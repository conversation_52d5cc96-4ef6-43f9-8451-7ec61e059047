!function(){var t,e={571:function(t,e,o){"use strict";function a(t,e){var o=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),o.push.apply(o,a)}return o}function n(t){for(var e=1;e<arguments.length;e++){var o=null!=arguments[e]?arguments[e]:{};e%2?a(Object(o),!0).forEach((function(e){i(t,e,o[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(o)):a(Object(o)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(o,e))}))}return t}function i(t,e,o){return e in t?Object.defineProperty(t,e,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[e]=o,t}function r(t,e){for(var o=0;o<e.length;o++){var a=e[o];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(t,a.key,a)}}o.r(e),o.d(e,{PluginHelper:function(){return c}});var c=function(t){var e=function(){function e(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e)}var o,a,i;return o=e,i=[{key:"GWPAdmin",value:function(){t().gwp_live_feed&&t().gwp_live_feed(),t().gwp_deactivate_popup&&t().gwp_deactivate_popup("woo-variation-swatches")}},{key:"PaginationAjax",value:function(e,o,a,n,i){var r={offset:n,product_id:e,attribute_id:o,attribute_name:a,_wpnonce:woo_variation_swatches_admin._wpnonce};t.ajax({global:!1,url:woo_variation_swatches_admin.wc_ajax_url.toString().replace("%%endpoint%%","woo_variation_swatches_load_product_terms"),method:"POST",data:r,beforeSend:function(t,e){i.block({message:null,overlayCSS:{background:"#DDDDDD",opacity:.6}})}}).fail((function(t,o){console.error("not available on: ".concat(e," ").concat($attribute_key,"."),o)})).always((function(){i.unblock()})).done((function(e){e&&(i.html(e),t(document.body).trigger("woo_variation_swatches_product_term_paging_done",i))}))}},{key:"MetaboxToggle",value:function(){t("#woo_variation_swatches_variation_product_options").on("click",".wc-metabox > h4",(function(e){var o=t(this).parent(".wc-metabox"),a=t(this).next(".wc-metabox-content");if(t(e.target).filter(":input, option, .sort, select, label, .select2-selection__rendered").length)return!1;o.hasClass("closed")?(o.removeClass("closed open").addClass("open"),a.slideDown()):(o.removeClass("closed open").addClass("closed"),a.slideUp())}))}},{key:"AttributeTypeSwitch",value:function(){t("#woo_variation_swatches_variation_product_options").on("change","select.woo_variation_swatches_attribute_type_switch",(function(e){var o=t(this).val();["select"].includes(o),["image","color","button"].includes(o)&&t(this).closest(".wc-metabox").find(".wc-metabox-content select.woo_variation_swatches_attribute_term_type_switch").val(o).trigger("change")})).on("change","select.woo_variation_swatches_attribute_term_type_switch",(function(e){var o=t(this).closest(".woo-variation-swatches-attribute-options-wrapper").find("select.woo_variation_swatches_attribute_type_switch").val();t(this).val()!==o&&t(this).closest(".woo-variation-swatches-attribute-options-wrapper").find("select.woo_variation_swatches_attribute_type_switch").val("mixed").trigger("change")}))}},{key:"SetAttributeTypePaging",value:function(e){var o=t(e).closest(".woo-variation-swatches-attribute-options-wrapper").find("select.woo_variation_swatches_attribute_type_switch").val();t(e).find("select.woo_variation_swatches_attribute_term_type_switch").hasClass("new-mode"),["image","color","button"].includes(o)&&t(e).find("select.woo_variation_swatches_attribute_term_type_switch.new-mode").val(o).trigger("change"),t(e).find("select.woo_variation_swatches_attribute_term_type_switch.new-mode").each((function(){t(this).val()}))}},{key:"__LoadProductAttributes",value:function(){t("#woocommerce-product-data").on("woocommerce_variations_loaded",(function(e){var o=t("#woo_variation_swatches_variation_product_options").data("product_id");t.ajax({global:!1,url:woo_variation_swatches_admin.wc_ajax_url.toString().replace("%%endpoint%%","woo_variation_swatches_load_product_options"),method:"POST",data:{product_id:o,_wpnonce:woo_variation_swatches_admin._wpnonce},beforeSend:function(e,o){t("#woo_variation_swatches_variation_product_options_inner").block({message:null,overlayCSS:{background:"#DDDDDD",opacity:.6}})}}).fail((function(t,e){console.error("not load option: ".concat(o,"."),e)})).always((function(){t("#woo_variation_swatches_variation_product_options_inner").unblock()})).done((function(e){t(document.body).trigger("woo_variation_swatches_variation_product_options_loaded",o)}))}))}},{key:"SaveProductAttributes",value:function(){var e=!1,o=t("#woo_variation_swatches_variation_product_options");o.on("change input color-changed",":input:not(.wvs-skip-field)",(function(){e||(window.onbeforeunload=function(){return woo_variation_swatches_admin.nav_warning},e=!0)})).on("click",".woo_variation_swatches_save_product_attributes, .woo_variation_swatches_reset_product_attributes",(function(){window.onbeforeunload=""})).on("click",".woo_variation_swatches_save_product_attributes",(function(e){e.preventDefault();var a,n=o.find(":input:not(.wvs-skip-field)").serializeJSON({disableColonTypes:!0}),i=Object.keys(n)?Object.keys(n).shift():"woo_variation_swatches_product_options",r=o.data("product_id");t.ajax({global:!1,url:woo_variation_swatches_admin.wc_ajax_url.toString().replace("%%endpoint%%","woo_variation_swatches_save_product_options"),method:"POST",data:{data:n[i],product_id:r,_wpnonce:woo_variation_swatches_admin._wpnonce},beforeSend:function(e,o){clearTimeout(a),t("#woo_variation_swatches_variation_product_options_inner").block({message:null,overlayCSS:{background:"#DDDDDD",opacity:.6}})}}).fail((function(t,e){console.error("not saved on: ".concat(r,"."),e)})).always((function(){t("#woo_variation_swatches_variation_product_options_inner").unblock()})).done((function(e){t("#saved-message").show(),a=setTimeout((function(){t("#saved-message").hide(600,(function(){t("#individual-swatches-info").removeClass("swatches-info-hide")}))}),5e3),t(document.body).trigger("woo_variation_swatches_variation_product_options_saved",r)}))})).on("click",".woo_variation_swatches_reset_product_attributes",(function(e){if(e.preventDefault(),confirm(woo_variation_swatches_admin.reset_notice)){var o=t(this).data("product_id");t.ajax({global:!1,url:woo_variation_swatches_admin.wc_ajax_url.toString().replace("%%endpoint%%","woo_variation_swatches_reset_product_options"),method:"POST",data:{product_id:o,_wpnonce:woo_variation_swatches_admin._wpnonce},beforeSend:function(e,o){t("#woo_variation_swatches_variation_product_options_inner").block({message:null,overlayCSS:{background:"#DDDDDD",opacity:.6}})}}).fail((function(t,e){console.error("not reset on: ".concat(o,"."),e)})).always((function(){t("#woo_variation_swatches_variation_product_options_inner").unblock()})).done((function(e){var a=t(e).find("#woo_variation_swatches_variation_product_options_inner").html();t("#woo_variation_swatches_variation_product_options_inner").html(a),t(document.body).trigger("woo_variation_swatches_variation_product_options_reset",o)}))}}))}},{key:"ResetProductAttributes",value:function(){t("#woo_variation_swatches_variation_product_options")}},{key:"Pagination",value:function(){var e=this,o=!1;t("#woo_variation_swatches_variation_product_options").on("change input color-changed",":input:not(.wvs-skip-field)",(function(t){o||(o=!0)})).on("click",".woo_variation_swatches_reset_product_attributes",(function(t){t.preventDefault(),o=!1})).on("click",".woo_variation_swatches_save_product_attributes",(function(t){t.preventDefault(),o=!1})).on("click",".first-page:not(.disabled), .prev-page:not(.disabled), .last-page:not(.disabled), .next-page:not(.disabled)",(function(t){o&&(t.preventDefault(),t.stopPropagation(),t.stopImmediatePropagation(),alert(woo_variation_swatches_admin.nav_warning))})).on("click",".first-page.disabled, .prev-page.disabled, .last-page.disabled, .next-page.disabled",(function(t){t.preventDefault()})).on("click",".first-page:not(.disabled)",(function(o){o.preventDefault();var a=t(o.currentTarget).closest(".product-term-label-settings").find(".product-term-label-settings-contents"),n=t(o.currentTarget).closest(".product-term-label-settings-pagination"),i=a.data("product_id"),r=a.data("attribute_id"),c=a.data("attribute_name");e.PaginationAjax(i,r,c,0,a),n.find(".next-page, .last-page").removeClass("disabled"),n.find(".current-page").text(1),a.data("current",1),n.find(".first-page, .prev-page").addClass("disabled")})).on("click",".prev-page:not(.disabled)",(function(o){o.preventDefault();var a=t(o.currentTarget).closest(".product-term-label-settings").find(".product-term-label-settings-contents"),n=t(o.currentTarget).closest(".product-term-label-settings-pagination");a.block({message:null,overlayCSS:{background:"#DDDDDD",opacity:.6}});var i=a.data("product_id"),r=(a.data("pages"),a.data("attribute_id")),c=a.data("attribute_name"),s=a.data("current"),d=a.data("limit"),l=(a.data("total"),(s-1)*d-d),u=s-1;e.PaginationAjax(i,r,c,l,a),n.find(".next-page, .last-page").removeClass("disabled"),n.find(".current-page").text(u),a.data("current",u),0===l&&n.find(".first-page, .prev-page").addClass("disabled")})).on("click",".next-page:not(.disabled)",(function(o){o.preventDefault();var a=t(o.currentTarget).closest(".product-term-label-settings").find(".product-term-label-settings-contents"),n=t(o.currentTarget).closest(".product-term-label-settings-pagination");a.block({message:null,overlayCSS:{background:"#DDDDDD",opacity:.6}});var i=a.data("product_id"),r=a.data("pages"),c=a.data("attribute_id"),s=a.data("attribute_name"),d=a.data("current"),l=a.data("limit"),u=(a.data("total"),d*l),_=d+1;e.PaginationAjax(i,c,s,u,a),n.find(".first-page, .prev-page").removeClass("disabled"),n.find(".current-page").text(_),a.data("current",_),r===_&&n.find(".next-page, .last-page").addClass("disabled")})).on("click",".last-page:not(.disabled)",(function(o){o.preventDefault();var a=t(o.currentTarget).closest(".product-term-label-settings").find(".product-term-label-settings-contents"),n=t(o.currentTarget).closest(".product-term-label-settings-pagination");a.block({message:null,overlayCSS:{background:"#DDDDDD",opacity:.6}});var i=a.data("product_id"),r=a.data("pages"),c=a.data("attribute_id"),s=a.data("attribute_name"),d=(a.data("current"),a.data("limit")),l=r*d-d;e.PaginationAjax(i,c,s,l,a),n.find(".first-page, .prev-page").removeClass("disabled"),n.find(".current-page").text(r),a.data("current",r),n.find(".next-page, .last-page").addClass("disabled")}))}},{key:"ResetAfterTermCreate",value:function(){t(document.body).on("woo_variation_swatches_admin_term_meta_added",this.ClearImagePicker),t(document.body).on("woo_variation_swatches_admin_term_meta_added",this.ClearColorPicker),t(document).ajaxComplete((function(e,o,a){try{var n=Object.fromEntries(new URLSearchParams(a.data));"add-tag"===n.action&&""===t("#tag-name").val()&&_.delay((function(){t(document.body).trigger("woo_variation_swatches_admin_term_meta_added",n)}),300)}catch(t){}}))}},{key:"ImageUploader",value:function(){t(document.body).off("click","button.wvs_upload_image_button"),t(document.body).on("click","button.wvs_upload_image_button",this.AddImage),t(document.body).on("click","button.wvs_remove_image_button",this.RemoveImage)}},{key:"AddImage",value:function(e){var o,a=this;if(e.preventDefault(),e.stopPropagation(),"undefined"!=typeof wp&&wp.media&&wp.media.editor){if(o)return void o.open();(o=wp.media.frames.select_image=wp.media({title:woo_variation_swatches_admin.media_title,button:{text:woo_variation_swatches_admin.button_title},multiple:!1})).on("select",(function(){var e=o.state().get("selection").first().toJSON();if(""!==t.trim(e.id)){var n=void 0===e.sizes.thumbnail?e.sizes.full.url:e.sizes.thumbnail.url;t(a).prev().val(e.id),t(a).closest(".meta-image-field-wrapper").find("img").attr("src",n),t(a).next().show()}})),o.on("open",(function(){var e=o.state().get("selection"),n=t(a).prev().val(),i=wp.media.attachment(n);i.fetch(),e.add(i?[i]:[])})),o.open()}}},{key:"RemoveImage",value:function(e){e.preventDefault(),e.stopPropagation();var o=t(this).closest(".meta-image-field-wrapper").find("img").data("placeholder");return t(this).closest(".meta-image-field-wrapper").find("img").attr("src",o),t(this).prev().prev().val(""),t(this).hide(),!1}},{key:"ClearImagePicker",value:function(){t("#addtag").find(".wvs_remove_image_button").trigger("click")}},{key:"InitTooltip",value:function(){t(document.body).trigger("init_tooltips")}},{key:"SelectWoo",value:function(){try{t(document.body).trigger("wc-enhanced-select-init")}catch(t){window.console.log(t)}}},{key:"ColorPicker",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"input.wvs-color-picker";try{t(document.body).on("woo_variation_swatches_color_picker_init",(function(o){t(e).wpColorPicker({change:function(o,a){t(e).trigger("color-changed")},clear:function(){t(e).trigger("color-changed")}})})).trigger("woo_variation_swatches_color_picker_init")}catch(t){window.console.log(t)}}},{key:"ClearColorPicker",value:function(){t("#addtag").find(".wp-picker-clear").trigger("click")}},{key:"FieldDependency",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"[data-gwp_dependency]";try{t(document.body).on("init_form_field_dependency",(function(){t(e).GWPFormFieldDependency()})).trigger("init_form_field_dependency")}catch(t){window.console.log(t)}}},{key:"FieldDependencyTrigger",value:function(){t(document.body).trigger("init_form_field_dependency")}},{key:"savingDialog",value:function(e,o,a){var i={},r="";if(o.find("input, select").each((function(){var e=t(this).attr("name"),o=t(this).val();e&&("tag_name"===e?r=o:i[e]=o,t(this).val(""))})),r){t(".product_attributes").block({message:null,overlayCSS:{background:"#FFFFFF",opacity:.6}});var c=n(n({},i),{},{action:"woocommerce_add_new_attribute",taxonomy:a,term:r,security:woocommerce_admin_meta_boxes.add_attribute_nonce});t.post(woocommerce_admin_meta_boxes.ajax_url,c,(function(o){o.error?window.alert(o.error):o.slug&&(e.find("select.attribute_values").append('<option value="'+o.term_id+'" selected="selected">'+o.name+"</option>"),e.find("select.attribute_values").change()),t(".product_attributes").unblock()}))}else t(".product_attributes").unblock()}},{key:"AttributeDialog",value:function(){var e=this;t(".product_attributes").on("click","button.wvs_add_new_attribute",(function(o){o.preventDefault();var a=t(this).closest(".woocommerce_attribute"),n=a.data("taxonomy"),i=t(this).data("dialog_title");t(".wvs-attribute-dialog-for-"+n).dialog({title:"",dialogClass:"wp-dialog wvs-attribute-dialog",classes:{"ui-dialog":"wp-dialog wvs-attribute-dialog"},autoOpen:!1,draggable:!0,width:"auto",modal:!0,resizable:!1,closeOnEscape:!0,position:{my:"center",at:"center",of:window},open:function(){t(".ui-widget-overlay").bind("click",(function(){t("#attribute-dialog").dialog("close")}))},create:function(){}}).dialog("option","title",i).dialog("option","buttons",[{text:woo_variation_swatches_admin.dialog_save,click:function(){e.savingDialog(a,t(this),n),t(this).dialog("close").dialog("destroy")}},{text:woo_variation_swatches_admin.dialog_cancel,click:function(){t(this).dialog("close").dialog("destroy")}}]).dialog("open")}))}}],(a=null)&&r(o.prototype,a),i&&r(o,i),Object.defineProperty(o,"prototype",{writable:!1}),e}();return e}(jQuery)},206:function(t,e,o){function a(t){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},a(t)}function n(t){if("function"!=typeof WeakMap)return null;var e=new WeakMap,o=new WeakMap;return(n=function(t){return t?o:e})(t)}jQuery((function(t){Promise.resolve().then((function(){return function(t,e){if(!e&&t&&t.__esModule)return t;if(null===t||"object"!==a(t)&&"function"!=typeof t)return{default:t};var o=n(e);if(o&&o.has(t))return o.get(t);var i={},r=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var c in t)if("default"!==c&&Object.prototype.hasOwnProperty.call(t,c)){var s=r?Object.getOwnPropertyDescriptor(t,c):null;s&&(s.get||s.set)?Object.defineProperty(i,c,s):i[c]=t[c]}return i.default=t,o&&o.set(t,i),i}(o(571))})).then((function(e){var o=e.PluginHelper;o.ResetAfterTermCreate(),o.ColorPicker(),o.ImageUploader(),o.FieldDependency(),o.Pagination(),o.MetaboxToggle(),o.AttributeTypeSwitch(),o.SaveProductAttributes(),t(document.body).on("woo_variation_swatches_variation_product_options_reset woo_variation_swatches_product_term_paging_done",(function(t,e){o.InitTooltip(),o.SelectWoo(),o.ColorPicker(),o.FieldDependencyTrigger(),o.SetAttributeTypePaging(e)}))}))}))},630:function(){},672:function(){}},o={};function a(t){var n=o[t];if(void 0!==n)return n.exports;var i=o[t]={exports:{}};return e[t](i,i.exports,a),i.exports}a.m=e,t=[],a.O=function(e,o,n,i){if(!o){var r=1/0;for(l=0;l<t.length;l++){o=t[l][0],n=t[l][1],i=t[l][2];for(var c=!0,s=0;s<o.length;s++)(!1&i||r>=i)&&Object.keys(a.O).every((function(t){return a.O[t](o[s])}))?o.splice(s--,1):(c=!1,i<r&&(r=i));if(c){t.splice(l--,1);var d=n();void 0!==d&&(e=d)}}return e}i=i||0;for(var l=t.length;l>0&&t[l-1][2]>i;l--)t[l]=t[l-1];t[l]=[o,n,i]},a.d=function(t,e){for(var o in e)a.o(e,o)&&!a.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:e[o]})},a.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},a.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},function(){var t={683:0,577:0,846:0};a.O.j=function(e){return 0===t[e]};var e=function(e,o){var n,i,r=o[0],c=o[1],s=o[2],d=0;if(r.some((function(e){return 0!==t[e]}))){for(n in c)a.o(c,n)&&(a.m[n]=c[n]);if(s)var l=s(a)}for(e&&e(o);d<r.length;d++)i=r[d],a.o(t,i)&&t[i]&&t[i][0](),t[i]=0;return a.O(l)},o=self.webpackChunkwoo_variation_swatches=self.webpackChunkwoo_variation_swatches||[];o.forEach(e.bind(null,0)),o.push=e.bind(null,o.push.bind(o))}(),a.O(void 0,[577,846],(function(){return a(206)})),a.O(void 0,[577,846],(function(){return a(630)}));var n=a.O(void 0,[577,846],(function(){return a(672)}));n=a.O(n)}();