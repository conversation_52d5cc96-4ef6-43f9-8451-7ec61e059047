# Advanced Custom Fields Translations are a combination of translate.wordpress.org contributions,
# combined with user contributed strings for the PRO version.
# Translations from translate.wordpress.org take priority over translations in this file.
# translate.wordpress.org contributions are synced at the time of each release.
#
# If you would like to contribute translations, please visit
# https://translate.wordpress.org/projects/wp-plugins/advanced-custom-fields/stable/
#
# For additional ACF PRO strings, please submit a pull request over on the ACF GitHub repo at
# http://github.com/advancedcustomfields/acf using the .pot (and any existing .po) files in /lang/pro/
#
# This file is distributed under the same license as Advanced Custom Fields.
msgid ""
msgstr ""
"PO-Revision-Date: 2025-07-21T18:29:30+00:00\n"
"Report-Msgid-Bugs-To: http://support.advancedcustomfields.com\n"
"Language: tr_TR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: gettext\n"
"Project-Id-Version: Advanced Custom Fields\n"

#: includes/fields/class-acf-field-select.php:471
msgid ""
"Save created options back to the \"Choices\" setting in the field definition."
msgstr ""

#: includes/fields/class-acf-field-select.php:470
msgid "Save Options"
msgstr ""

#: includes/fields/class-acf-field-select.php:448
msgid ""
"Allow content editors to create new options by typing in the Select input. "
"Multiple options can be created from a comma separated string."
msgstr ""

#: includes/fields/class-acf-field-select.php:447
msgid "Create Options"
msgstr ""

#: includes/admin/views/global/navigation.php:179
#: includes/admin/views/global/navigation.php:183
msgid "Edit ACF Field Groups"
msgstr ""

#: includes/admin/views/global/navigation.php:100
msgid "Get 4 months free on any WP Engine plan"
msgstr ""

#: src/Site_Health/Site_Health.php:522
msgid "Number of Field Groups with Blocks and Other Locations"
msgstr ""

#: src/Site_Health/Site_Health.php:517
msgid "Number of Field Groups with Multiple Block Locations"
msgstr ""

#: src/Site_Health/Site_Health.php:512
msgid "Number of Field Groups with a Single Block Location"
msgstr ""

#: src/Site_Health/Site_Health.php:481
msgid "All Location Rules"
msgstr ""

#: includes/validation.php:144
msgid "Learn more"
msgstr ""

#: includes/validation.php:133
msgid ""
"ACF was unable to perform validation because the provided nonce failed "
"verification."
msgstr ""

#: includes/validation.php:131
msgid ""
"ACF was unable to perform validation because no nonce was received by the "
"server."
msgstr ""

#. translators: This text is prepended by a link to ACF's website, and appended
#. by a link to WP Engine's website.
#: includes/admin/admin.php:324
msgid "are developed and maintained by"
msgstr ""

#: src/Site_Health/Site_Health.php:295
msgid "Update Source"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:850
#: includes/admin/views/acf-taxonomy/advanced-settings.php:810
msgid "By default only admin users can edit this setting."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:848
#: includes/admin/views/acf-taxonomy/advanced-settings.php:808
msgid "By default only super admin users can edit this setting."
msgstr ""

#: includes/admin/views/acf-field-group/field.php:322
msgid "Close and Add Field"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:804
msgid ""
"A PHP function name to be called to handle the content of a meta box on your "
"taxonomy. For security, this callback will be executed in a special context "
"without access to any superglobals like $_POST or $_GET."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:842
msgid ""
"A PHP function name to be called when setting up the meta boxes for the edit "
"screen. For security, this callback will be executed in a special context "
"without access to any superglobals like $_POST or $_GET."
msgstr ""

#: src/Site_Health/Site_Health.php:296
msgid "wordpress.org"
msgstr ""

#: includes/fields/class-acf-field.php:359
msgid "Allow Access to Value in Editor UI"
msgstr ""

#: includes/fields/class-acf-field.php:341
msgid "Learn more."
msgstr ""

#. translators: %s A "Learn More" link to documentation explaining the setting
#. further.
#: includes/fields/class-acf-field.php:340
msgid ""
"Allow content editors to access and display the field value in the editor UI "
"using Block Bindings or the ACF Shortcode. %s"
msgstr ""

#: src/Blocks/Bindings.php:67
msgid ""
"The requested ACF field type does not support output in Block Bindings or "
"the ACF shortcode."
msgstr ""

#: includes/api/api-template.php:1085 src/Blocks/Bindings.php:75
msgid ""
"The requested ACF field is not allowed to be output in bindings or the ACF "
"Shortcode."
msgstr ""

#: includes/api/api-template.php:1077
msgid ""
"The requested ACF field type does not support output in bindings or the ACF "
"Shortcode."
msgstr ""

#: includes/api/api-template.php:1054
msgid "[The ACF shortcode cannot display fields from non-public posts]"
msgstr ""
"[ACF kısa kodu herkese açık olmayan gönderilerdeki alanları görüntüleyemez]"

#: includes/api/api-template.php:1011
msgid "[The ACF shortcode is disabled on this site]"
msgstr "[ACF kısa kodu bu sitede devre dışı bırakılmıştır]"

#: includes/fields/class-acf-field-icon_picker.php:476
msgid "Businessman Icon"
msgstr "İş insanı simgesi"

#: includes/fields/class-acf-field-icon_picker.php:468
msgid "Forums Icon"
msgstr "Forumlar simgesi"

#: includes/fields/class-acf-field-icon_picker.php:747
msgid "YouTube Icon"
msgstr "YouTube simgesi"

#: includes/fields/class-acf-field-icon_picker.php:746
msgid "Yes (alt) Icon"
msgstr "Evet (alt) simgesi"

#: includes/fields/class-acf-field-icon_picker.php:744
msgid "Xing Icon"
msgstr "Xing simgesi"

#: includes/fields/class-acf-field-icon_picker.php:743
msgid "WordPress (alt) Icon"
msgstr "WordPress (alt) simgesi"

#: includes/fields/class-acf-field-icon_picker.php:741
msgid "WhatsApp Icon"
msgstr "WhatsApp simgesi"

#: includes/fields/class-acf-field-icon_picker.php:740
msgid "Write Blog Icon"
msgstr "Blog yaz simgesi"

#: includes/fields/class-acf-field-icon_picker.php:739
msgid "Widgets Menus Icon"
msgstr "Widget menüleri simgesi"

#: includes/fields/class-acf-field-icon_picker.php:738
msgid "View Site Icon"
msgstr "Siteyi görüntüle simgesi"

#: includes/fields/class-acf-field-icon_picker.php:737
msgid "Learn More Icon"
msgstr "Daha fazla öğren simgesi"

#: includes/fields/class-acf-field-icon_picker.php:735
msgid "Add Page Icon"
msgstr "Sayfa ekle simgesi"

#: includes/fields/class-acf-field-icon_picker.php:732
msgid "Video (alt3) Icon"
msgstr "Video (alt3) simgesi"

#: includes/fields/class-acf-field-icon_picker.php:731
msgid "Video (alt2) Icon"
msgstr "Video (alt2) simgesi"

#: includes/fields/class-acf-field-icon_picker.php:730
msgid "Video (alt) Icon"
msgstr "Video (alt) simgesi"

#: includes/fields/class-acf-field-icon_picker.php:727
msgid "Update (alt) Icon"
msgstr "Güncelle (alt) simgesi"

#: includes/fields/class-acf-field-icon_picker.php:724
msgid "Universal Access (alt) Icon"
msgstr "Evrensel erişim (alt) simgesi"

#: includes/fields/class-acf-field-icon_picker.php:721
msgid "Twitter (alt) Icon"
msgstr "Twitter (alt) simgesi"

#: includes/fields/class-acf-field-icon_picker.php:719
msgid "Twitch Icon"
msgstr "Twitch simgesi"

#: includes/fields/class-acf-field-icon_picker.php:716
msgid "Tide Icon"
msgstr "Tide simgesi"

#: includes/fields/class-acf-field-icon_picker.php:715
msgid "Tickets (alt) Icon"
msgstr "Biletler (alt) simgesi"

#: includes/fields/class-acf-field-icon_picker.php:711
msgid "Text Page Icon"
msgstr "Metin sayfası simgesi"

#: includes/fields/class-acf-field-icon_picker.php:705
msgid "Table Row Delete Icon"
msgstr "Tablo satırı silme simgesi"

#: includes/fields/class-acf-field-icon_picker.php:704
msgid "Table Row Before Icon"
msgstr "Tablo satırı önceki simgesi"

#: includes/fields/class-acf-field-icon_picker.php:703
msgid "Table Row After Icon"
msgstr "Tablo satırı sonraki simgesi"

#: includes/fields/class-acf-field-icon_picker.php:702
msgid "Table Col Delete Icon"
msgstr "Tablo sütunu silme simgesi"

#: includes/fields/class-acf-field-icon_picker.php:701
msgid "Table Col Before Icon"
msgstr "Tablo sütunu önceki simgesi"

#: includes/fields/class-acf-field-icon_picker.php:700
msgid "Table Col After Icon"
msgstr "Tablo sütunu sonraki simgesi"

#: includes/fields/class-acf-field-icon_picker.php:699
msgid "Superhero (alt) Icon"
msgstr "Süper kahraman (alt) simgesi"

#: includes/fields/class-acf-field-icon_picker.php:698
msgid "Superhero Icon"
msgstr "Süper kahraman simgesi"

#: includes/fields/class-acf-field-icon_picker.php:692
msgid "Spotify Icon"
msgstr "Spotify simgesi"

#: includes/fields/class-acf-field-icon_picker.php:686
msgid "Shortcode Icon"
msgstr "Kısa kod simgesi"

#: includes/fields/class-acf-field-icon_picker.php:685
msgid "Shield (alt) Icon"
msgstr "Kalkan (alt) simgesi"

#: includes/fields/class-acf-field-icon_picker.php:683
msgid "Share (alt2) Icon"
msgstr "Paylaş (alt2) simgesi"

#: includes/fields/class-acf-field-icon_picker.php:682
msgid "Share (alt) Icon"
msgstr "Paylaş (alt) simgesi"

#: includes/fields/class-acf-field-icon_picker.php:677
msgid "Saved Icon"
msgstr "Kaydedildi simgesi"

#: includes/fields/class-acf-field-icon_picker.php:676
msgid "RSS Icon"
msgstr "RSS simgesi"

#: includes/fields/class-acf-field-icon_picker.php:675
msgid "REST API Icon"
msgstr "REST API simgesi"

#: includes/fields/class-acf-field-icon_picker.php:674
msgid "Remove Icon"
msgstr "Kaldır simgesi"

#: includes/fields/class-acf-field-icon_picker.php:672
msgid "Reddit Icon"
msgstr "Reddit simgesi"

#: includes/fields/class-acf-field-icon_picker.php:669
msgid "Privacy Icon"
msgstr "Gizlilik simgesi"

#: includes/fields/class-acf-field-icon_picker.php:668
msgid "Printer Icon"
msgstr "Yazıcı simgesi"

#: includes/fields/class-acf-field-icon_picker.php:664
msgid "Podio Icon"
msgstr "Podio simgesi"

#: includes/fields/class-acf-field-icon_picker.php:663
msgid "Plus (alt2) Icon"
msgstr "Artı (alt2) simgesi"

#: includes/fields/class-acf-field-icon_picker.php:662
msgid "Plus (alt) Icon"
msgstr "Artı (alt) simgesi"

#: includes/fields/class-acf-field-icon_picker.php:660
msgid "Plugins Checked Icon"
msgstr "Eklentiler kontrol edildi simgesi"

#: includes/fields/class-acf-field-icon_picker.php:657
msgid "Pinterest Icon"
msgstr "Pinterest simgesi"

#: includes/fields/class-acf-field-icon_picker.php:655
msgid "Pets Icon"
msgstr "Evcil hayvanlar simgesi"

#: includes/fields/class-acf-field-icon_picker.php:653
msgid "PDF Icon"
msgstr "PDF simgesi"

#: includes/fields/class-acf-field-icon_picker.php:651
msgid "Palm Tree Icon"
msgstr "Palmiye ağacı simgesi"

#: includes/fields/class-acf-field-icon_picker.php:650
msgid "Open Folder Icon"
msgstr "Açık klasör simgesi"

#: includes/fields/class-acf-field-icon_picker.php:649
msgid "No (alt) Icon"
msgstr "Hayır (alt) simgesi"

#: includes/fields/class-acf-field-icon_picker.php:644
msgid "Money (alt) Icon"
msgstr "Para (alt) simgesi"

#: includes/fields/class-acf-field-icon_picker.php:639
msgid "Menu (alt3) Icon"
msgstr "Menü (alt3) simgesi"

#: includes/fields/class-acf-field-icon_picker.php:638
msgid "Menu (alt2) Icon"
msgstr "Menü (alt2) simgesi"

#: includes/fields/class-acf-field-icon_picker.php:637
msgid "Menu (alt) Icon"
msgstr "Menü (alt) simgesi"

#: includes/fields/class-acf-field-icon_picker.php:632
msgid "Spreadsheet Icon"
msgstr "Elektronik tablo simgesi"

#: includes/fields/class-acf-field-icon_picker.php:631
msgid "Interactive Icon"
msgstr "İnteraktif simgesi"

#: includes/fields/class-acf-field-icon_picker.php:630
msgid "Document Icon"
msgstr "Belge simgesi"

#: includes/fields/class-acf-field-icon_picker.php:629
msgid "Default Icon"
msgstr "Varsayılan simgesi"

#: includes/fields/class-acf-field-icon_picker.php:623
msgid "Location (alt) Icon"
msgstr "Konum (alt) simgesi"

#: includes/fields/class-acf-field-icon_picker.php:620
msgid "LinkedIn Icon"
msgstr "LinkedIn simgesi"

#: includes/fields/class-acf-field-icon_picker.php:615
msgid "Instagram Icon"
msgstr "Instagram simgesi"

#: includes/fields/class-acf-field-icon_picker.php:614
msgid "Insert Before Icon"
msgstr "Öncesine ekle simgesi"

#: includes/fields/class-acf-field-icon_picker.php:613
msgid "Insert After Icon"
msgstr "Sonrasına ekle simgesi"

#: includes/fields/class-acf-field-icon_picker.php:612
msgid "Insert Icon"
msgstr "Ekle simgesi"

#: includes/fields/class-acf-field-icon_picker.php:611
msgid "Info Outline Icon"
msgstr "Bilgi anahat simgesi"

#: includes/fields/class-acf-field-icon_picker.php:608
msgid "Images (alt2) Icon"
msgstr "Görseller (alt2) simgesi"

#: includes/fields/class-acf-field-icon_picker.php:607
msgid "Images (alt) Icon"
msgstr "Görseller (alt) simgesi"

#: includes/fields/class-acf-field-icon_picker.php:606
msgid "Rotate Right Icon"
msgstr "Sağa döndür simgesi"

#: includes/fields/class-acf-field-icon_picker.php:605
msgid "Rotate Left Icon"
msgstr "Sola döndür simgesi"

#: includes/fields/class-acf-field-icon_picker.php:604
msgid "Rotate Icon"
msgstr "Döndürme simgesi"

#: includes/fields/class-acf-field-icon_picker.php:603
msgid "Flip Vertical Icon"
msgstr "Dikey çevirme simgesi"

#: includes/fields/class-acf-field-icon_picker.php:602
msgid "Flip Horizontal Icon"
msgstr "Yatay çevirme simgesi"

#: includes/fields/class-acf-field-icon_picker.php:600
msgid "Crop Icon"
msgstr "Kırpma simgesi"

#: includes/fields/class-acf-field-icon_picker.php:599
msgid "ID (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:597
msgid "HTML Icon"
msgstr "HTML simgesi"

#: includes/fields/class-acf-field-icon_picker.php:596
msgid "Hourglass Icon"
msgstr "Kum saati simgesi"

#: includes/fields/class-acf-field-icon_picker.php:593
msgid "Heading Icon"
msgstr "Başlık simgesi"

#: includes/fields/class-acf-field-icon_picker.php:589
msgid "Google Icon"
msgstr "Google simgesi"

#: includes/fields/class-acf-field-icon_picker.php:588
msgid "Games Icon"
msgstr "Oyunlar simgesi"

#: includes/fields/class-acf-field-icon_picker.php:587
msgid "Fullscreen Exit (alt) Icon"
msgstr "Tam ekran çıkış (alt) simgesi"

#: includes/fields/class-acf-field-icon_picker.php:586
msgid "Fullscreen (alt) Icon"
msgstr "Tam ekran (alt) simgesi"

#: includes/fields/class-acf-field-icon_picker.php:583
msgid "Status Icon"
msgstr "Durum simgesi"

#: includes/fields/class-acf-field-icon_picker.php:581
msgid "Image Icon"
msgstr "Görsel simgesi"

#: includes/fields/class-acf-field-icon_picker.php:580
msgid "Gallery Icon"
msgstr "Galeri simgesi"

#: includes/fields/class-acf-field-icon_picker.php:579
msgid "Chat Icon"
msgstr "Sohbet simgesi"

#: includes/fields/class-acf-field-icon_picker.php:578
#: includes/fields/class-acf-field-icon_picker.php:627
msgid "Audio Icon"
msgstr "Ses simgesi"

#: includes/fields/class-acf-field-icon_picker.php:577
msgid "Aside Icon"
msgstr "Kenar simgesi"

#: includes/fields/class-acf-field-icon_picker.php:576
msgid "Food Icon"
msgstr "Yemek simgesi"

#: includes/fields/class-acf-field-icon_picker.php:569
msgid "Exit Icon"
msgstr "Çıkış simgesi"

#: includes/fields/class-acf-field-icon_picker.php:568
msgid "Excerpt View Icon"
msgstr "Özet görünümü simgesi"

#: includes/fields/class-acf-field-icon_picker.php:567
msgid "Embed Video Icon"
msgstr "Video gömme simgesi"

#: includes/fields/class-acf-field-icon_picker.php:566
msgid "Embed Post Icon"
msgstr "Yazı gömme simgesi"

#: includes/fields/class-acf-field-icon_picker.php:565
msgid "Embed Photo Icon"
msgstr "Fotoğraf gömme simgesi"

#: includes/fields/class-acf-field-icon_picker.php:564
msgid "Embed Generic Icon"
msgstr "Jenerik gömme simgesi"

#: includes/fields/class-acf-field-icon_picker.php:563
msgid "Embed Audio Icon"
msgstr "Ses gömme simgesi"

#: includes/fields/class-acf-field-icon_picker.php:562
msgid "Email (alt2) Icon"
msgstr "E-posta (alt2) simgesi"

#: includes/fields/class-acf-field-icon_picker.php:559
msgid "Ellipsis Icon"
msgstr "Elips simgesi"

#: includes/fields/class-acf-field-icon_picker.php:555
msgid "Unordered List Icon"
msgstr "Sırasız liste simgesi"

#: includes/fields/class-acf-field-icon_picker.php:550
msgid "RTL Icon"
msgstr "RTL simgesi"

#: includes/fields/class-acf-field-icon_picker.php:543
msgid "Ordered List RTL Icon"
msgstr "Sıralı liste RTL simgesi"

#: includes/fields/class-acf-field-icon_picker.php:542
msgid "Ordered List Icon"
msgstr "Sıralı liste simgesi"

#: includes/fields/class-acf-field-icon_picker.php:541
msgid "LTR Icon"
msgstr "LTR simgesi"

#: includes/fields/class-acf-field-icon_picker.php:533
msgid "Custom Character Icon"
msgstr "Özel karakter simgesi"

#: includes/fields/class-acf-field-icon_picker.php:525
msgid "Edit Page Icon"
msgstr "Sayfayı düzenle simgesi"

#: includes/fields/class-acf-field-icon_picker.php:524
msgid "Edit Large Icon"
msgstr "Büyük düzenle simgesi"

#: includes/fields/class-acf-field-icon_picker.php:522
msgid "Drumstick Icon"
msgstr "Baget simgesi"

#: includes/fields/class-acf-field-icon_picker.php:518
msgid "Database View Icon"
msgstr "Veritabanı görünümü simgesi"

#: includes/fields/class-acf-field-icon_picker.php:517
msgid "Database Remove Icon"
msgstr "Veritabanı kaldırma simgesi"

#: includes/fields/class-acf-field-icon_picker.php:516
msgid "Database Import Icon"
msgstr "Veritabanı içe aktarma simgesi"

#: includes/fields/class-acf-field-icon_picker.php:515
msgid "Database Export Icon"
msgstr "Veritabanı dışa aktarma simgesi"

#: includes/fields/class-acf-field-icon_picker.php:514
msgid "Database Add Icon"
msgstr "Veritabanı ekleme simgesi"

#: includes/fields/class-acf-field-icon_picker.php:513
msgid "Database Icon"
msgstr "Veritabanı simgesi"

#: includes/fields/class-acf-field-icon_picker.php:511
msgid "Cover Image Icon"
msgstr "Kapak görseli simgesi"

#: includes/fields/class-acf-field-icon_picker.php:510
msgid "Volume On Icon"
msgstr "Ses açık simgesi"

#: includes/fields/class-acf-field-icon_picker.php:509
msgid "Volume Off Icon"
msgstr "Ses kapalı simgesi"

#: includes/fields/class-acf-field-icon_picker.php:508
msgid "Skip Forward Icon"
msgstr "İleri atla simgesi"

#: includes/fields/class-acf-field-icon_picker.php:507
msgid "Skip Back Icon"
msgstr "Geri atla simgesi"

#: includes/fields/class-acf-field-icon_picker.php:506
msgid "Repeat Icon"
msgstr "Tekrarla simgesi"

#: includes/fields/class-acf-field-icon_picker.php:505
msgid "Play Icon"
msgstr "Oynat simgesi"

#: includes/fields/class-acf-field-icon_picker.php:504
msgid "Pause Icon"
msgstr "Duraklat simgesi"

#: includes/fields/class-acf-field-icon_picker.php:503
msgid "Forward Icon"
msgstr "İleri simgesi"

#: includes/fields/class-acf-field-icon_picker.php:502
msgid "Back Icon"
msgstr "Geri simgesi"

#: includes/fields/class-acf-field-icon_picker.php:501
msgid "Columns Icon"
msgstr "Sütunlar simgesi"

#: includes/fields/class-acf-field-icon_picker.php:500
msgid "Color Picker Icon"
msgstr "Renk seçici simgesi"

#: includes/fields/class-acf-field-icon_picker.php:499
msgid "Coffee Icon"
msgstr "Kahve simgesi"

#: includes/fields/class-acf-field-icon_picker.php:498
msgid "Code Standards Icon"
msgstr "Kod standartları simgesi"

#: includes/fields/class-acf-field-icon_picker.php:497
msgid "Cloud Upload Icon"
msgstr "Bulut yükleme simgesi"

#: includes/fields/class-acf-field-icon_picker.php:496
msgid "Cloud Saved Icon"
msgstr "Bulut kaydedildi simgesi"

#: includes/fields/class-acf-field-icon_picker.php:485
msgid "Car Icon"
msgstr "Araba simgesi"

#: includes/fields/class-acf-field-icon_picker.php:484
msgid "Camera (alt) Icon"
msgstr "Kamera (alt) simgesi"

#: includes/fields/class-acf-field-icon_picker.php:480
msgid "Calculator Icon"
msgstr "Hesap makinesi simgesi"

#: includes/fields/class-acf-field-icon_picker.php:479
msgid "Button Icon"
msgstr "Düğme simgesi"

#: includes/fields/class-acf-field-icon_picker.php:477
msgid "Businessperson Icon"
msgstr "İş i̇nsanı i̇konu"

#: includes/fields/class-acf-field-icon_picker.php:474
msgid "Tracking Icon"
msgstr "İzleme simgesi"

#: includes/fields/class-acf-field-icon_picker.php:473
msgid "Topics Icon"
msgstr "Konular simge"

#: includes/fields/class-acf-field-icon_picker.php:472
msgid "Replies Icon"
msgstr "Yanıtlar simgesi"

#: includes/fields/class-acf-field-icon_picker.php:471
msgid "PM Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:469
msgid "Friends Icon"
msgstr "Arkadaşlar simgesi"

#: includes/fields/class-acf-field-icon_picker.php:467
msgid "Community Icon"
msgstr "Topluluk simgesi"

#: includes/fields/class-acf-field-icon_picker.php:466
msgid "BuddyPress Icon"
msgstr "BuddyPress simgesi"

#: includes/fields/class-acf-field-icon_picker.php:465
msgid "bbPress Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:464
msgid "Activity Icon"
msgstr "Etkinlik simgesi"

#: includes/fields/class-acf-field-icon_picker.php:463
msgid "Book (alt) Icon"
msgstr "Kitap (alt) simgesi"

#: includes/fields/class-acf-field-icon_picker.php:461
msgid "Block Default Icon"
msgstr "Blok varsayılan simgesi"

#: includes/fields/class-acf-field-icon_picker.php:460
msgid "Bell Icon"
msgstr "Çan simgesi"

#: includes/fields/class-acf-field-icon_picker.php:459
msgid "Beer Icon"
msgstr "Bira simgesi"

#: includes/fields/class-acf-field-icon_picker.php:458
msgid "Bank Icon"
msgstr "Banka simgesi"

#: includes/fields/class-acf-field-icon_picker.php:454
msgid "Arrow Up (alt2) Icon"
msgstr "Yukarı ok (alt2) simgesi"

#: includes/fields/class-acf-field-icon_picker.php:453
msgid "Arrow Up (alt) Icon"
msgstr "Yukarı ok (alt) simgesi"

#: includes/fields/class-acf-field-icon_picker.php:451
msgid "Arrow Right (alt2) Icon"
msgstr "Sağ ok (alt2) simgesi"

#: includes/fields/class-acf-field-icon_picker.php:450
msgid "Arrow Right (alt) Icon"
msgstr "Sağ ok (alt) simgesi"

#: includes/fields/class-acf-field-icon_picker.php:448
msgid "Arrow Left (alt2) Icon"
msgstr "Sol ok (alt2) simgesi"

#: includes/fields/class-acf-field-icon_picker.php:447
msgid "Arrow Left (alt) Icon"
msgstr "Sol ok (alt) simgesi"

#: includes/fields/class-acf-field-icon_picker.php:445
msgid "Arrow Down (alt2) Icon"
msgstr "Aşağı ok (alt2) simgesi"

#: includes/fields/class-acf-field-icon_picker.php:444
msgid "Arrow Down (alt) Icon"
msgstr "Aşağı ok (alt) simgesi"

#: includes/fields/class-acf-field-icon_picker.php:440
msgid "Amazon Icon"
msgstr "Amazon simgesi"

#: includes/fields/class-acf-field-icon_picker.php:439
msgid "Align Wide Icon"
msgstr "Geniş hizala simgesi"

#: includes/fields/class-acf-field-icon_picker.php:437
msgid "Align Pull Right Icon"
msgstr "Sağa çek hizala simgesi"

#: includes/fields/class-acf-field-icon_picker.php:436
msgid "Align Pull Left Icon"
msgstr "Sola çek hizala simgesi"

#: includes/fields/class-acf-field-icon_picker.php:433
msgid "Align Full Width Icon"
msgstr "Tam genişlikte hizala simgesi"

#: includes/fields/class-acf-field-icon_picker.php:430
msgid "Airplane Icon"
msgstr "Uçak simgesi"

#: includes/fields/class-acf-field-icon_picker.php:427
msgid "Site (alt3) Icon"
msgstr "Site (alt3) simgesi"

#: includes/fields/class-acf-field-icon_picker.php:426
msgid "Site (alt2) Icon"
msgstr "Site (alt2) simgesi"

#: includes/fields/class-acf-field-icon_picker.php:425
msgid "Site (alt) Icon"
msgstr "Site (alt) simgesi"

#: includes/admin/views/options-page-preview.php:26
msgid "Upgrade to ACF PRO to create options pages in just a few clicks"
msgstr ""
"Sadece birkaç tıklamayla seçenek sayfaları oluşturmak için ACF PRO'ya "
"yükseltin"

#: includes/ajax/class-acf-ajax-query-users.php:24
msgid "Invalid request args."
msgstr "Geçersiz istek argümanları."

#: includes/ajax/class-acf-ajax-check-screen.php:37
#: includes/ajax/class-acf-ajax-local-json-diff.php:37
#: includes/ajax/class-acf-ajax-query-users.php:33
#: includes/ajax/class-acf-ajax-upgrade.php:24
#: includes/ajax/class-acf-ajax-user-setting.php:38
msgid "Sorry, you do not have permission to do that."
msgstr "Üzgünüm, bunu yapmak için izniniz yok."

#: src/Site_Health/Site_Health.php:720
msgid "Blocks Using Post Meta"
msgstr "Yazı metası kullanan bloklar"

#: includes/admin/views/acf-field-group/pro-features.php:25
#: includes/admin/views/acf-field-group/pro-features.php:27
#: includes/admin/views/global/header.php:27
msgid "ACF PRO logo"
msgstr "ACF PRO logosu"

#: includes/admin/views/acf-field-group/field.php:37
msgid "ACF PRO Logo"
msgstr "ACF PRO Logosu"

#. translators: %s - field/param name
#: includes/fields/class-acf-field-icon_picker.php:813
msgid "%s requires a valid attachment ID when type is set to media_library."
msgstr ""
"Tip media_library olarak ayarlandığında %s geçerli bir ek kimliği gerektirir."

#. translators: %s - field name
#: includes/fields/class-acf-field-icon_picker.php:797
msgid "%s is a required property of acf."
msgstr "%s acf'nin gerekli bir özelliğidir."

#: includes/fields/class-acf-field-icon_picker.php:773
msgid "The value of icon to save."
msgstr "Kaydedilecek simgenin değeri."

#: includes/fields/class-acf-field-icon_picker.php:767
msgid "The type of icon to save."
msgstr "Kaydedilecek simgenin türü."

#: includes/fields/class-acf-field-icon_picker.php:745
msgid "Yes Icon"
msgstr "Evet simgesi"

#: includes/fields/class-acf-field-icon_picker.php:742
msgid "WordPress Icon"
msgstr "WordPress simgesi"

#: includes/fields/class-acf-field-icon_picker.php:734
msgid "Warning Icon"
msgstr "Uyarı simgesi"

#: includes/fields/class-acf-field-icon_picker.php:733
msgid "Visibility Icon"
msgstr "Görünürlük simgesi"

#: includes/fields/class-acf-field-icon_picker.php:729
msgid "Vault Icon"
msgstr "Kasa simgesi"

#: includes/fields/class-acf-field-icon_picker.php:728
msgid "Upload Icon"
msgstr "Yükleme simgesi"

#: includes/fields/class-acf-field-icon_picker.php:726
msgid "Update Icon"
msgstr "Güncelleme simgesi"

#: includes/fields/class-acf-field-icon_picker.php:725
msgid "Unlock Icon"
msgstr "Kilit açma simgesi"

#: includes/fields/class-acf-field-icon_picker.php:723
msgid "Universal Access Icon"
msgstr "Evrensel erişim simgesi"

#: includes/fields/class-acf-field-icon_picker.php:722
msgid "Undo Icon"
msgstr "Geri al simgesi"

#: includes/fields/class-acf-field-icon_picker.php:720
msgid "Twitter Icon"
msgstr "Twitter simgesi"

#: includes/fields/class-acf-field-icon_picker.php:718
msgid "Trash Icon"
msgstr "Çöp kutusu simgesi"

#: includes/fields/class-acf-field-icon_picker.php:717
msgid "Translation Icon"
msgstr "Çeviri simgesi"

#: includes/fields/class-acf-field-icon_picker.php:714
msgid "Tickets Icon"
msgstr "Bilet simgesi"

#: includes/fields/class-acf-field-icon_picker.php:713
msgid "Thumbs Up Icon"
msgstr "Başparmak yukarı simgesi"

#: includes/fields/class-acf-field-icon_picker.php:712
msgid "Thumbs Down Icon"
msgstr "Başparmak aşağı simgesi"

#: includes/fields/class-acf-field-icon_picker.php:633
#: includes/fields/class-acf-field-icon_picker.php:710
msgid "Text Icon"
msgstr "Metin simgesi"

#: includes/fields/class-acf-field-icon_picker.php:709
msgid "Testimonial Icon"
msgstr "Referans simgesi"

#: includes/fields/class-acf-field-icon_picker.php:708
msgid "Tagcloud Icon"
msgstr "Etiket bulutu simgesi"

#: includes/fields/class-acf-field-icon_picker.php:707
msgid "Tag Icon"
msgstr "Etiket simgesi"

#: includes/fields/class-acf-field-icon_picker.php:706
msgid "Tablet Icon"
msgstr "Tablet simgesi"

#: includes/fields/class-acf-field-icon_picker.php:697
msgid "Store Icon"
msgstr "Mağaza simgesi"

#: includes/fields/class-acf-field-icon_picker.php:696
msgid "Sticky Icon"
msgstr "Yapışkan simge"

#: includes/fields/class-acf-field-icon_picker.php:695
msgid "Star Half Icon"
msgstr "Yıldız-yarım simgesi"

#: includes/fields/class-acf-field-icon_picker.php:694
msgid "Star Filled Icon"
msgstr "Yıldız dolu simge"

#: includes/fields/class-acf-field-icon_picker.php:693
msgid "Star Empty Icon"
msgstr "Yıldız-boş simgesi"

#: includes/fields/class-acf-field-icon_picker.php:691
msgid "Sos Icon"
msgstr "Sos simgesi"

#: includes/fields/class-acf-field-icon_picker.php:690
msgid "Sort Icon"
msgstr "Sıralama simgesi"

#: includes/fields/class-acf-field-icon_picker.php:689
msgid "Smiley Icon"
msgstr "Gülen yüz simgesi"

#: includes/fields/class-acf-field-icon_picker.php:688
msgid "Smartphone Icon"
msgstr "Akıllı telefon simgesi"

#: includes/fields/class-acf-field-icon_picker.php:687
msgid "Slides Icon"
msgstr "Slaytlar simgesi"

#: includes/fields/class-acf-field-icon_picker.php:684
msgid "Shield Icon"
msgstr "Kalkan Simgesi"

#: includes/fields/class-acf-field-icon_picker.php:681
msgid "Share Icon"
msgstr "Paylaş simgesi"

#: includes/fields/class-acf-field-icon_picker.php:680
msgid "Search Icon"
msgstr "Arama simgesi"

#: includes/fields/class-acf-field-icon_picker.php:679
msgid "Screen Options Icon"
msgstr "Ekran ayarları simgesi"

#: includes/fields/class-acf-field-icon_picker.php:678
msgid "Schedule Icon"
msgstr "Zamanlama simgesi"

#: includes/fields/class-acf-field-icon_picker.php:673
msgid "Redo Icon"
msgstr "İleri al simgesi"

#: includes/fields/class-acf-field-icon_picker.php:671
msgid "Randomize Icon"
msgstr "Rastgele simgesi"

#: includes/fields/class-acf-field-icon_picker.php:670
msgid "Products Icon"
msgstr "Ürünler simgesi"

#: includes/fields/class-acf-field-icon_picker.php:667
msgid "Pressthis Icon"
msgstr "Pressthis simgesi"

#: includes/fields/class-acf-field-icon_picker.php:666
msgid "Post Status Icon"
msgstr "Yazı-durumu simgesi"

#: includes/fields/class-acf-field-icon_picker.php:665
msgid "Portfolio Icon"
msgstr "Portföy simgesi"

#: includes/fields/class-acf-field-icon_picker.php:661
msgid "Plus Icon"
msgstr "Artı simgesi"

#: includes/fields/class-acf-field-icon_picker.php:659
msgid "Playlist Video Icon"
msgstr "Oynatma listesi-video simgesi"

#: includes/fields/class-acf-field-icon_picker.php:658
msgid "Playlist Audio Icon"
msgstr "Oynatma listesi-ses simgesi"

#: includes/fields/class-acf-field-icon_picker.php:656
msgid "Phone Icon"
msgstr "Telefon simgesi"

#: includes/fields/class-acf-field-icon_picker.php:654
msgid "Performance Icon"
msgstr "Performans simgesi"

#: includes/fields/class-acf-field-icon_picker.php:652
msgid "Paperclip Icon"
msgstr "Ataç simgesi"

#: includes/fields/class-acf-field-icon_picker.php:648
msgid "No Icon"
msgstr "Hayır simgesi"

#: includes/fields/class-acf-field-icon_picker.php:647
msgid "Networking Icon"
msgstr "Ağ simgesi"

#: includes/fields/class-acf-field-icon_picker.php:646
msgid "Nametag Icon"
msgstr "İsim etiketi simgesi"

#: includes/fields/class-acf-field-icon_picker.php:645
msgid "Move Icon"
msgstr "Taşıma simgesi"

#: includes/fields/class-acf-field-icon_picker.php:643
msgid "Money Icon"
msgstr "Para simgesi"

#: includes/fields/class-acf-field-icon_picker.php:642
msgid "Minus Icon"
msgstr "Eksi simgesi"

#: includes/fields/class-acf-field-icon_picker.php:641
msgid "Migrate Icon"
msgstr "Aktarma simgesi"

#: includes/fields/class-acf-field-icon_picker.php:640
msgid "Microphone Icon"
msgstr "Mikrofon simgesi"

#: includes/fields/class-acf-field-icon_picker.php:635
msgid "Megaphone Icon"
msgstr "Megafon simgesi"

#: includes/fields/class-acf-field-icon_picker.php:625
msgid "Marker Icon"
msgstr "İşaret simgesi"

#: includes/fields/class-acf-field-icon_picker.php:624
msgid "Lock Icon"
msgstr "Kilit simgesi"

#: includes/fields/class-acf-field-icon_picker.php:622
msgid "Location Icon"
msgstr "Konum simgesi"

#: includes/fields/class-acf-field-icon_picker.php:621
msgid "List View Icon"
msgstr "Liste-görünümü simgesi"

#: includes/fields/class-acf-field-icon_picker.php:619
msgid "Lightbulb Icon"
msgstr "Ampul simgesi"

#: includes/fields/class-acf-field-icon_picker.php:618
msgid "Left Right Icon"
msgstr "Sol-sağ simgesi"

#: includes/fields/class-acf-field-icon_picker.php:617
msgid "Layout Icon"
msgstr "Düzen simgesi"

#: includes/fields/class-acf-field-icon_picker.php:616
msgid "Laptop Icon"
msgstr "Dizüstü bilgisayar simgesi"

#: includes/fields/class-acf-field-icon_picker.php:610
msgid "Info Icon"
msgstr "Bilgi simgesi"

#: includes/fields/class-acf-field-icon_picker.php:609
msgid "Index Card Icon"
msgstr "Dizin-kartı simgesi"

#: includes/fields/class-acf-field-icon_picker.php:598
msgid "ID Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:595
msgid "Hidden Icon"
msgstr "Gizli simgesi"

#: includes/fields/class-acf-field-icon_picker.php:594
msgid "Heart Icon"
msgstr "Kalp Simgesi"

#: includes/fields/class-acf-field-icon_picker.php:592
msgid "Hammer Icon"
msgstr "Çekiç simgesi"

#: includes/fields/class-acf-field-icon_picker.php:470
#: includes/fields/class-acf-field-icon_picker.php:591
msgid "Groups Icon"
msgstr "Gruplar simgesi"

#: includes/fields/class-acf-field-icon_picker.php:590
msgid "Grid View Icon"
msgstr "Izgara-görünümü simgesi"

#: includes/fields/class-acf-field-icon_picker.php:585
msgid "Forms Icon"
msgstr "Formlar simgesi"

#: includes/fields/class-acf-field-icon_picker.php:575
msgid "Flag Icon"
msgstr "Bayrak simgesi"

#: includes/fields/class-acf-field-icon_picker.php:574
#: includes/fields/class-acf-field-icon_picker.php:601
msgid "Filter Icon"
msgstr "Filtre simgesi"

#: includes/fields/class-acf-field-icon_picker.php:573
msgid "Feedback Icon"
msgstr "Geri bildirim simgesi"

#: includes/fields/class-acf-field-icon_picker.php:572
msgid "Facebook (alt) Icon"
msgstr "Facebook alt simgesi"

#: includes/fields/class-acf-field-icon_picker.php:571
msgid "Facebook Icon"
msgstr "Facebook simgesi"

#: includes/fields/class-acf-field-icon_picker.php:570
msgid "External Icon"
msgstr "Harici simgesi"

#: includes/fields/class-acf-field-icon_picker.php:561
msgid "Email (alt) Icon"
msgstr "E-posta alt simgesi"

#: includes/fields/class-acf-field-icon_picker.php:560
msgid "Email Icon"
msgstr "E-posta simgesi"

#: includes/fields/class-acf-field-icon_picker.php:558
#: includes/fields/class-acf-field-icon_picker.php:584
#: includes/fields/class-acf-field-icon_picker.php:634
msgid "Video Icon"
msgstr "Video simgesi"

#: includes/fields/class-acf-field-icon_picker.php:557
msgid "Unlink Icon"
msgstr "Bağlantıyı kaldır simgesi"

#: includes/fields/class-acf-field-icon_picker.php:556
msgid "Underline Icon"
msgstr "Altı çizili simgesi"

#: includes/fields/class-acf-field-icon_picker.php:554
msgid "Text Color Icon"
msgstr "Metin rengi simgesi"

#: includes/fields/class-acf-field-icon_picker.php:553
msgid "Table Icon"
msgstr "Tablo simgesi"

#: includes/fields/class-acf-field-icon_picker.php:552
msgid "Strikethrough Icon"
msgstr "Üstü çizili simgesi"

#: includes/fields/class-acf-field-icon_picker.php:551
msgid "Spellcheck Icon"
msgstr "Yazım denetimi simgesi"

#: includes/fields/class-acf-field-icon_picker.php:549
msgid "Remove Formatting Icon"
msgstr "Biçimlendirme kaldır simgesi"

#: includes/fields/class-acf-field-icon_picker.php:548
#: includes/fields/class-acf-field-icon_picker.php:582
msgid "Quote Icon"
msgstr "Alıntı simgesi"

#: includes/fields/class-acf-field-icon_picker.php:547
msgid "Paste Word Icon"
msgstr "Kelime yapıştır simgesi"

#: includes/fields/class-acf-field-icon_picker.php:546
msgid "Paste Text Icon"
msgstr "Metin yapıştır simgesi"

#: includes/fields/class-acf-field-icon_picker.php:545
msgid "Paragraph Icon"
msgstr "Paragraf simgesi"

#: includes/fields/class-acf-field-icon_picker.php:544
msgid "Outdent Icon"
msgstr "Dışarı it simgesi"

#: includes/fields/class-acf-field-icon_picker.php:540
msgid "Kitchen Sink Icon"
msgstr "Mutfak lavabosu simgesi"

#: includes/fields/class-acf-field-icon_picker.php:539
msgid "Justify Icon"
msgstr "İki yana yasla simgesi"

#: includes/fields/class-acf-field-icon_picker.php:538
msgid "Italic Icon"
msgstr "Eğik simgesi"

#: includes/fields/class-acf-field-icon_picker.php:537
msgid "Insert More Icon"
msgstr "Daha fazla ekle simgesi"

#: includes/fields/class-acf-field-icon_picker.php:536
msgid "Indent Icon"
msgstr "Girinti simgesi"

#: includes/fields/class-acf-field-icon_picker.php:535
msgid "Help Icon"
msgstr "Yardım simgesi"

#: includes/fields/class-acf-field-icon_picker.php:534
msgid "Expand Icon"
msgstr "Genişletme simgesi"

#: includes/fields/class-acf-field-icon_picker.php:532
msgid "Contract Icon"
msgstr "Sözleşme simgesi"

#: includes/fields/class-acf-field-icon_picker.php:531
#: includes/fields/class-acf-field-icon_picker.php:628
msgid "Code Icon"
msgstr "Kod simgesi"

#: includes/fields/class-acf-field-icon_picker.php:530
msgid "Break Icon"
msgstr "Mola simgesi"

#: includes/fields/class-acf-field-icon_picker.php:529
msgid "Bold Icon"
msgstr "Kalın simgesi"

#: includes/fields/class-acf-field-icon_picker.php:523
msgid "Edit Icon"
msgstr "Düzenle simgesi"

#: includes/fields/class-acf-field-icon_picker.php:521
msgid "Download Icon"
msgstr "İndirme simgesi"

#: includes/fields/class-acf-field-icon_picker.php:520
msgid "Dismiss Icon"
msgstr "Kapat simgesi"

#: includes/fields/class-acf-field-icon_picker.php:519
msgid "Desktop Icon"
msgstr "Masaüstü simgesi"

#: includes/fields/class-acf-field-icon_picker.php:512
msgid "Dashboard Icon"
msgstr "Pano simgesi"

#: includes/fields/class-acf-field-icon_picker.php:495
msgid "Cloud Icon"
msgstr "Bulut simgesi"

#: includes/fields/class-acf-field-icon_picker.php:494
msgid "Clock Icon"
msgstr "Saat simgesi"

#: includes/fields/class-acf-field-icon_picker.php:493
msgid "Clipboard Icon"
msgstr "Pano simgesi"

#: includes/fields/class-acf-field-icon_picker.php:492
msgid "Chart Pie Icon"
msgstr "Pasta grafiği simgesi"

#: includes/fields/class-acf-field-icon_picker.php:491
msgid "Chart Line Icon"
msgstr "Grafik çizgisi simgesi"

#: includes/fields/class-acf-field-icon_picker.php:490
msgid "Chart Bar Icon"
msgstr "Grafik çubuğu simgesi"

#: includes/fields/class-acf-field-icon_picker.php:489
msgid "Chart Area Icon"
msgstr "Grafik alanı simgesi"

#: includes/fields/class-acf-field-icon_picker.php:488
msgid "Category Icon"
msgstr "Kategori simgesi"

#: includes/fields/class-acf-field-icon_picker.php:487
msgid "Cart Icon"
msgstr "Sepet simgesi"

#: includes/fields/class-acf-field-icon_picker.php:486
msgid "Carrot Icon"
msgstr "Havuç simgesi"

#: includes/fields/class-acf-field-icon_picker.php:483
msgid "Camera Icon"
msgstr "Kamera simgesi"

#: includes/fields/class-acf-field-icon_picker.php:482
msgid "Calendar (alt) Icon"
msgstr "Takvim alt simgesi"

#: includes/fields/class-acf-field-icon_picker.php:481
msgid "Calendar Icon"
msgstr "Takvim simgesi"

#: includes/fields/class-acf-field-icon_picker.php:478
msgid "Businesswoman Icon"
msgstr "İş adamı simgesi"

#: includes/fields/class-acf-field-icon_picker.php:475
msgid "Building Icon"
msgstr "Bina simgesi"

#: includes/fields/class-acf-field-icon_picker.php:462
msgid "Book Icon"
msgstr "Kitap simgesi"

#: includes/fields/class-acf-field-icon_picker.php:457
msgid "Backup Icon"
msgstr "Yedekleme simgesi"

#: includes/fields/class-acf-field-icon_picker.php:456
msgid "Awards Icon"
msgstr "Ödül simgesi"

#: includes/fields/class-acf-field-icon_picker.php:455
msgid "Art Icon"
msgstr "Sanat simgesi"

#: includes/fields/class-acf-field-icon_picker.php:452
msgid "Arrow Up Icon"
msgstr "Yukarı ok simgesi"

#: includes/fields/class-acf-field-icon_picker.php:449
msgid "Arrow Right Icon"
msgstr "Sağ ok simgesi"

#: includes/fields/class-acf-field-icon_picker.php:446
msgid "Arrow Left Icon"
msgstr "Sol ok simgesi"

#: includes/fields/class-acf-field-icon_picker.php:443
msgid "Arrow Down Icon"
msgstr "Aşağı ok simgesi"

#: includes/fields/class-acf-field-icon_picker.php:442
#: includes/fields/class-acf-field-icon_picker.php:626
msgid "Archive Icon"
msgstr "Arşiv simgesi"

#: includes/fields/class-acf-field-icon_picker.php:441
msgid "Analytics Icon"
msgstr "Analitik simgesi"

#: includes/fields/class-acf-field-icon_picker.php:438
#: includes/fields/class-acf-field-icon_picker.php:528
msgid "Align Right Icon"
msgstr "Hizalama-sağ simgesi"

#: includes/fields/class-acf-field-icon_picker.php:435
msgid "Align None Icon"
msgstr "Hizalama-yok simgesi"

#: includes/fields/class-acf-field-icon_picker.php:434
#: includes/fields/class-acf-field-icon_picker.php:527
msgid "Align Left Icon"
msgstr "Hizalama-sol simgesi"

#: includes/fields/class-acf-field-icon_picker.php:432
#: includes/fields/class-acf-field-icon_picker.php:526
msgid "Align Center Icon"
msgstr "Hizalama-ortala simgesi"

#: includes/fields/class-acf-field-icon_picker.php:431
msgid "Album Icon"
msgstr "Albüm simgesi"

#: includes/fields/class-acf-field-icon_picker.php:429
msgid "Users Icon"
msgstr "Kullanıcı simgesi"

#: includes/fields/class-acf-field-icon_picker.php:428
msgid "Tools Icon"
msgstr "Araçlar simgesi"

#: includes/fields/class-acf-field-icon_picker.php:424
msgid "Site Icon"
msgstr "Site simgesi"

#: includes/fields/class-acf-field-icon_picker.php:423
msgid "Settings Icon"
msgstr "Ayarlar simgesi"

#: includes/fields/class-acf-field-icon_picker.php:422
msgid "Post Icon"
msgstr "Yazı simgesi"

#: includes/fields/class-acf-field-icon_picker.php:421
msgid "Plugins Icon"
msgstr "Eklentiler simgesi"

#: includes/fields/class-acf-field-icon_picker.php:420
msgid "Page Icon"
msgstr "Sayfa simgesi"

#: includes/fields/class-acf-field-icon_picker.php:419
msgid "Network Icon"
msgstr "Ağ simgesi"

#: includes/fields/class-acf-field-icon_picker.php:418
msgid "Multisite Icon"
msgstr "Çoklu site simgesi"

#: includes/fields/class-acf-field-icon_picker.php:417
msgid "Media Icon"
msgstr "Ortam simgesi"

#: includes/fields/class-acf-field-icon_picker.php:416
msgid "Links Icon"
msgstr "Bağlantılar simgesi"

#: includes/fields/class-acf-field-icon_picker.php:415
msgid "Home Icon"
msgstr "Ana sayfa simgesi"

#: includes/fields/class-acf-field-icon_picker.php:413
msgid "Customizer Icon"
msgstr "Özelleştirici simgesi"

#: includes/fields/class-acf-field-icon_picker.php:412
#: includes/fields/class-acf-field-icon_picker.php:736
msgid "Comments Icon"
msgstr "Yorumlar simgesi"

#: includes/fields/class-acf-field-icon_picker.php:411
msgid "Collapse Icon"
msgstr "Daraltma simgesi"

#: includes/fields/class-acf-field-icon_picker.php:410
msgid "Appearance Icon"
msgstr "Görünüm simgesi"

#: includes/fields/class-acf-field-icon_picker.php:414
msgid "Generic Icon"
msgstr "Jenerik simgesi"

#: includes/fields/class-acf-field-icon_picker.php:346
msgid "Icon picker requires a value."
msgstr "Simge seçici bir değer gerektirir."

#: includes/fields/class-acf-field-icon_picker.php:341
msgid "Icon picker requires an icon type."
msgstr "Simge seçici bir simge türü gerektirir."

#: includes/fields/class-acf-field-icon_picker.php:310
msgid ""
"The available icons matching your search query have been updated in the icon "
"picker below."
msgstr ""
"Arama sorgunuzla eşleşen mevcut simgeler aşağıdaki simge seçicide "
"güncellenmiştir."

#: includes/fields/class-acf-field-icon_picker.php:309
msgid "No results found for that search term"
msgstr "Bu arama terimi için sonuç bulunamadı"

#: includes/fields/class-acf-field-icon_picker.php:291
msgid "Array"
msgstr "Dizi"

#: includes/fields/class-acf-field-icon_picker.php:290
msgid "String"
msgstr "Dize"

#. translators: %s - link to documentation
#: includes/fields/class-acf-field-icon_picker.php:278
msgid "Specify the return format for the icon. %s"
msgstr "Simge için dönüş biçimini belirtin. %s"

#: includes/fields/class-acf-field-icon_picker.php:263
msgid "Select where content editors can choose the icon from."
msgstr "İçerik editörlerinin simgeyi nereden seçebileceğini seçin."

#: includes/fields/class-acf-field-icon_picker.php:224
msgid "The URL to the icon you'd like to use, or svg as Data URI"
msgstr "Kullanmak istediğiniz simgenin web adresi veya Veri adresi olarak svg"

#: includes/fields/class-acf-field-icon_picker.php:207
msgid "Browse Media Library"
msgstr "Ortam kütüphanesine göz at"

#: includes/fields/class-acf-field-icon_picker.php:198
msgid "The currently selected image preview"
msgstr "Seçili görselin önizlemesi"

#: includes/fields/class-acf-field-icon_picker.php:189
msgid "Click to change the icon in the Media Library"
msgstr "Ortam kütüphanesinnde simgeyi değiştirmek için tıklayın"

#: includes/fields/class-acf-field-icon_picker.php:84
msgid "Search icons..."
msgstr "Arama simgeleri..."

#: includes/fields/class-acf-field-icon_picker.php:53
msgid "Media Library"
msgstr "Ortam kütüphanesi"

#: includes/fields/class-acf-field-icon_picker.php:49
msgid "Dashicons"
msgstr "Panel simgeleri"

#: includes/fields/class-acf-field-icon_picker.php:26
msgid ""
"An interactive UI for selecting an icon. Select from Dashicons, the media "
"library, or a standalone URL input."
msgstr ""
"Bir simge seçmek için etkileşimli bir kullanıcı arayüzü. Dashicons, ortam "
"kütüphanesi veya bağımsız bir adres girişi arasından seçim yapın."

#: includes/fields/class-acf-field-icon_picker.php:23
msgid "Icon Picker"
msgstr "Simge seçici"

#: src/Site_Health/Site_Health.php:781
msgid "JSON Load Paths"
msgstr "JSON yükleme yolları"

#: src/Site_Health/Site_Health.php:775
msgid "JSON Save Paths"
msgstr "JSON kaydetme yolları"

#: src/Site_Health/Site_Health.php:766
msgid "Registered ACF Forms"
msgstr "Kayıtlı ACF formları"

#: src/Site_Health/Site_Health.php:760
msgid "Shortcode Enabled"
msgstr "Kısa kod etkin"

#: src/Site_Health/Site_Health.php:752
msgid "Field Settings Tabs Enabled"
msgstr "Saha ayarları sekmeleri etkin"

#: src/Site_Health/Site_Health.php:744
msgid "Field Type Modal Enabled"
msgstr "Alan türü modali etkin"

#: src/Site_Health/Site_Health.php:736
msgid "Admin UI Enabled"
msgstr "Yönetici kullanıcı arayüzü etkin"

#: src/Site_Health/Site_Health.php:727
msgid "Block Preloading Enabled"
msgstr "Blok ön yükleme etkin"

#: src/Site_Health/Site_Health.php:715
msgid "Blocks Per ACF Block Version"
msgstr "Her bir ACF blok sürümü için bloklar"

#: src/Site_Health/Site_Health.php:710
msgid "Blocks Per API Version"
msgstr "Her bir API sürümü için bloklar"

#: src/Site_Health/Site_Health.php:683
msgid "Registered ACF Blocks"
msgstr "Kayıtlı ACF blokları"

#: src/Site_Health/Site_Health.php:677
msgid "Light"
msgstr "Açık"

#: src/Site_Health/Site_Health.php:677
msgid "Standard"
msgstr "Standart"

#: src/Site_Health/Site_Health.php:676
msgid "REST API Format"
msgstr "REST API biçimi"

#: src/Site_Health/Site_Health.php:668
msgid "Registered Options Pages (PHP)"
msgstr "Kayıtlı seçenekler sayfaları (PHP)"

#: src/Site_Health/Site_Health.php:654
msgid "Registered Options Pages (JSON)"
msgstr "Kayıtlı seçenekler sayfaları (JSON)"

#: src/Site_Health/Site_Health.php:649
msgid "Registered Options Pages (UI)"
msgstr "Kayıtlı seçenekler sayfaları (UI)"

#: src/Site_Health/Site_Health.php:619
msgid "Options Pages UI Enabled"
msgstr "Seçenekler sayfaları UI etkin"

#: src/Site_Health/Site_Health.php:611
msgid "Registered Taxonomies (JSON)"
msgstr "Kayıtlı taksonomiler (JSON)"

#: src/Site_Health/Site_Health.php:599
msgid "Registered Taxonomies (UI)"
msgstr "Kayıtlı taksonomiler (UI)"

#: src/Site_Health/Site_Health.php:587
msgid "Registered Post Types (JSON)"
msgstr "Kayıtlı yazı tipleri (JSON)"

#: src/Site_Health/Site_Health.php:575
msgid "Registered Post Types (UI)"
msgstr "Kayıtlı yazı tipleri (UI)"

#: src/Site_Health/Site_Health.php:562
msgid "Post Types and Taxonomies Enabled"
msgstr "Yazı tipleri ve taksonomiler etkinleştirildi"

#: src/Site_Health/Site_Health.php:555
msgid "Number of Third Party Fields by Field Type"
msgstr "Alan türüne göre üçüncü taraf alanlarının sayısı"

#: src/Site_Health/Site_Health.php:550
msgid "Number of Fields by Field Type"
msgstr "Alan türüne göre alan sayısı"

#: src/Site_Health/Site_Health.php:449
msgid "Field Groups Enabled for GraphQL"
msgstr "GraphQL için alan grupları etkinleştirildi"

#: src/Site_Health/Site_Health.php:436
msgid "Field Groups Enabled for REST API"
msgstr "REST API için alan grupları etkinleştirildi"

#: src/Site_Health/Site_Health.php:424
msgid "Registered Field Groups (JSON)"
msgstr "Kayıtlı alan grupları (JSON)"

#: src/Site_Health/Site_Health.php:412
msgid "Registered Field Groups (PHP)"
msgstr "Kayıtlı alan grupları (PHP)"

#: src/Site_Health/Site_Health.php:400
msgid "Registered Field Groups (UI)"
msgstr "Kayıtlı alan grupları (UI)"

#: src/Site_Health/Site_Health.php:388
msgid "Active Plugins"
msgstr "Etkin eklentiler"

#: src/Site_Health/Site_Health.php:362
msgid "Parent Theme"
msgstr "Ebeveyn tema"

#: src/Site_Health/Site_Health.php:351
msgid "Active Theme"
msgstr "Etkin tema"

#: src/Site_Health/Site_Health.php:342
msgid "Is Multisite"
msgstr "Çoklu site mi"

#: src/Site_Health/Site_Health.php:337
msgid "MySQL Version"
msgstr "MySQL sürümü"

#: src/Site_Health/Site_Health.php:332
msgid "WordPress Version"
msgstr "WordPress Sürümü"

#: src/Site_Health/Site_Health.php:325
msgid "Subscription Expiry Date"
msgstr "Abonelik sona erme tarihi"

#: src/Site_Health/Site_Health.php:317
msgid "License Status"
msgstr "Lisans durumu"

#: src/Site_Health/Site_Health.php:312
msgid "License Type"
msgstr "Lisans türü"

#: src/Site_Health/Site_Health.php:307
msgid "Licensed URL"
msgstr "Lisanslanan web adresi"

#: src/Site_Health/Site_Health.php:301
msgid "License Activated"
msgstr "Lisans etkinleştirildi"

#: src/Site_Health/Site_Health.php:290
msgid "Free"
msgstr "Ücretsiz"

#: src/Site_Health/Site_Health.php:289
msgid "Plugin Type"
msgstr "Eklenti tipi"

#: src/Site_Health/Site_Health.php:284
msgid "Plugin Version"
msgstr "Eklenti sürümü"

#: src/Site_Health/Site_Health.php:255
msgid ""
"This section contains debug information about your ACF configuration which "
"can be useful to provide to support."
msgstr ""
"Bu bölüm, ACF yapılandırmanız hakkında destek ekibine sağlamak için yararlı "
"olabilecek hata ayıklama bilgilerini içerir."

#: includes/assets.php:373
msgid "An ACF Block on this page requires attention before you can save."
msgstr ""
"Kaydetmeden önce bu sayfadaki bir ACF Bloğuna dikkat edilmesi gerekiyor."

#. translators: %s - The clear log button opening HTML tag. %s - The closing
#. HTML tag.
#: includes/admin/views/escaped-html-notice.php:63
msgid ""
"This data is logged as we detect values that have been changed during "
"output. %1$sClear log and dismiss%2$s after escaping the values in your "
"code. The notice will reappear if we detect changed values again."
msgstr ""
"Bu veriler, çıktı sırasında değiştirilen değerleri tespit ettiğimizde "
"günlüğe kaydedilir. Kodunuzdaki değerleri temizledikten sonra %1$sgünlüğü "
"temizleyin ve kapatın%2$s. Değişen değerleri tekrar tespit edersek bildirim "
"yeniden görünecektir."

#: includes/admin/views/escaped-html-notice.php:25
msgid "Dismiss permanently"
msgstr "Kalıcı olarak kapat"

#: includes/admin/views/acf-field-group/field.php:220
msgid "Instructions for content editors. Shown when submitting data."
msgstr "İçerik düzenleyicileri için talimatlar. Veri gönderirken gösterilir."

#: includes/admin/post-types/admin-field-group.php:143
msgid "Has no term selected"
msgstr "Seçilmiş bir terim yok"

#: includes/admin/post-types/admin-field-group.php:142
msgid "Has any term selected"
msgstr "Seçilmiş herhangi bir terim"

#: includes/admin/post-types/admin-field-group.php:141
msgid "Terms do not contain"
msgstr "Terimler şunları içermez"

#: includes/admin/post-types/admin-field-group.php:140
msgid "Terms contain"
msgstr "Terimler şunları içerir"

#: includes/admin/post-types/admin-field-group.php:139
msgid "Term is not equal to"
msgstr "Terim şuna eşit değildir"

#: includes/admin/post-types/admin-field-group.php:138
msgid "Term is equal to"
msgstr "Terim şuna eşittir"

#: includes/admin/post-types/admin-field-group.php:137
msgid "Has no user selected"
msgstr "Seçili kullanıcı yok"

#: includes/admin/post-types/admin-field-group.php:136
msgid "Has any user selected"
msgstr "Herhangi bir kullanıcı"

#: includes/admin/post-types/admin-field-group.php:135
msgid "Users do not contain"
msgstr "Şunları içermeyen kullanıcılar"

#: includes/admin/post-types/admin-field-group.php:134
msgid "Users contain"
msgstr "Şunları içeren kullanıcılar"

#: includes/admin/post-types/admin-field-group.php:133
msgid "User is not equal to"
msgstr "Şuna eşit olmayan kullanıcı"

#: includes/admin/post-types/admin-field-group.php:132
msgid "User is equal to"
msgstr "Şuna eşit olan kullanıcı"

#: includes/admin/post-types/admin-field-group.php:131
msgid "Has no page selected"
msgstr "Seçili sayfa yok"

#: includes/admin/post-types/admin-field-group.php:130
msgid "Has any page selected"
msgstr "Seçili herhangi bir sayfa"

#: includes/admin/post-types/admin-field-group.php:129
msgid "Pages do not contain"
msgstr "Şunu içermeyen sayfalar"

#: includes/admin/post-types/admin-field-group.php:128
msgid "Pages contain"
msgstr "Şunu içeren sayfalar"

#: includes/admin/post-types/admin-field-group.php:127
msgid "Page is not equal to"
msgstr "Şuna eşit olmayan sayfa"

#: includes/admin/post-types/admin-field-group.php:126
msgid "Page is equal to"
msgstr "Şuna eşit olan sayfa"

#: includes/admin/post-types/admin-field-group.php:125
msgid "Has no relationship selected"
msgstr "Seçili llişkisi olmayan"

#: includes/admin/post-types/admin-field-group.php:124
msgid "Has any relationship selected"
msgstr "Herhangi bir ilişki seçilmiş olan"

#: includes/admin/post-types/admin-field-group.php:123
msgid "Has no post selected"
msgstr "Seçili hiç bir yazısı olmayan"

#: includes/admin/post-types/admin-field-group.php:122
msgid "Has any post selected"
msgstr "Seçili herhangi bir yazısı olan"

#: includes/admin/post-types/admin-field-group.php:121
msgid "Posts do not contain"
msgstr "Şunu içermeyen yazı"

#: includes/admin/post-types/admin-field-group.php:120
msgid "Posts contain"
msgstr "Şunu içeren yazı"

#: includes/admin/post-types/admin-field-group.php:119
msgid "Post is not equal to"
msgstr "Şuna eşit olmayan yazı"

#: includes/admin/post-types/admin-field-group.php:118
msgid "Post is equal to"
msgstr "Şuna eşit olan yazı"

#: includes/admin/post-types/admin-field-group.php:117
msgid "Relationships do not contain"
msgstr "Şunu içermeyen ilişliler"

#: includes/admin/post-types/admin-field-group.php:116
msgid "Relationships contain"
msgstr "Şunu içeren ilişliler"

#: includes/admin/post-types/admin-field-group.php:115
msgid "Relationship is not equal to"
msgstr "Şuna eşit olmayan ilişkiler"

#: includes/admin/post-types/admin-field-group.php:114
msgid "Relationship is equal to"
msgstr "Şuna eşit olan ilişkiler"

#: src/Blocks/Bindings.php:38
msgctxt "The core ACF block binding source name for fields on the current page"
msgid "ACF Fields"
msgstr "ACF alanları"

#: includes/admin/views/browse-fields-modal.php:14
msgid "ACF PRO Feature"
msgstr "ACF PRO Özelliği"

#: includes/admin/views/browse-fields-modal.php:10
msgid "Renew PRO to Unlock"
msgstr "Kilidi açmak için PRO'yu yenileyin"

#: includes/admin/views/browse-fields-modal.php:8
msgid "Renew PRO License"
msgstr "PRO lisansını yenile"

#: includes/admin/views/acf-field-group/field.php:41
msgid "PRO fields cannot be edited without an active license."
msgstr "PRO alanları etkin bir lisans olmadan düzenlenemez."

#: includes/admin/admin-internal-post-type-list.php:232
msgid ""
"Please activate your ACF PRO license to edit field groups assigned to an ACF "
"Block."
msgstr ""
"Bir ACF bloğuna atanan alan gruplarını düzenlemek için lütfen ACF PRO "
"lisansınızı etkinleştirin."

#: includes/admin/admin-internal-post-type-list.php:231
msgid "Please activate your ACF PRO license to edit this options page."
msgstr ""
"Bu seçenekler sayfasını düzenlemek için lütfen ACF PRO lisansınızı "
"etkinleştirin."

#: includes/api/api-template.php:385 includes/api/api-template.php:439
msgid ""
"Returning escaped HTML values is only possible when format_value is also "
"true. The field values have not been returned for security."
msgstr ""
"Temizlenmiş HTML değerlerinin döndürülmesi yalnızca format_value da true "
"olduğunda mümkündür. Alan değerleri güvenlik için döndürülmemiştir."

#: includes/api/api-template.php:46 includes/api/api-template.php:251
#: includes/api/api-template.php:947
msgid ""
"Returning an escaped HTML value is only possible when format_value is also "
"true. The field value has not been returned for security."
msgstr ""
"Temizlenmiş bir HTML değerinin döndürülmesi yalnızca format_value da true "
"olduğunda mümkündür. Alan değeri güvenlik için döndürülmemiştir."

#. translators: %1$s - name of the ACF plugin. %2$s - Link to documentation.
#: includes/admin/views/escaped-html-notice.php:32
msgid ""
"%1$s ACF now automatically escapes unsafe HTML when rendered by "
"<code>the_field</code> or the ACF shortcode. We've detected the output of "
"some of your fields has been modified by this change, but this may not be a "
"breaking change. %2$s."
msgstr ""
"%1$s ACF artık <code>the_field</code> veya ACF kısa kodu tarafından "
"işlendiğinde güvenli olmayan HTML’i otomatik olarak temizliyor. Bazı "
"alanlarınızın çıktısının bu değişiklikle değiştirildiğini tespit ettik, "
"ancak bu kritik bir değişiklik olmayabilir. %2$s."

#: includes/admin/views/escaped-html-notice.php:27
msgid "Please contact your site administrator or developer for more details."
msgstr ""
"Daha fazla ayrıntı için lütfen site yöneticinize veya geliştiricinize "
"başvurun."

#: includes/admin/views/escaped-html-notice.php:5
msgid "Learn&nbsp;more"
msgstr "Daha&nbsp;fazlasını&nbsp;öğren"

#: includes/admin/admin.php:63
msgid "Hide&nbsp;details"
msgstr "Ayrıntıları&nbsp;gizle"

#: includes/admin/admin.php:62 includes/admin/views/escaped-html-notice.php:24
msgid "Show&nbsp;details"
msgstr "Detayları&nbsp;göster"

#. translators: %1$s - The selector used  %2$s The field name  3%$s The parent
#. function name
#: includes/admin/views/escaped-html-notice.php:49
msgid "%1$s (%2$s) - rendered via %3$s"
msgstr "%1$s (%2$s) - %3$s aracılığıyla işlenir"

#: includes/admin/views/global/navigation.php:229
msgid "Renew ACF PRO License"
msgstr "ACF PRO lisansını yenile"

#: includes/admin/views/acf-field-group/pro-features.php:17
msgid "Renew License"
msgstr "Lisansı yenile"

#: includes/admin/views/acf-field-group/pro-features.php:14
msgid "Manage License"
msgstr "Lisansı yönet"

#: includes/admin/views/acf-field-group/options.php:102
msgid "'High' position not supported in the Block Editor"
msgstr "'Yüksek' konum blok düzenleyicide desteklenmiyor"

#: includes/admin/views/options-page-preview.php:30
msgid "Upgrade to ACF PRO"
msgstr "ACF PRO'ya yükseltin"

#. translators: %s URL to ACF options pages documentation
#: includes/admin/views/options-page-preview.php:7
msgid ""
"ACF <a href=\"%s\" target=\"_blank\">options pages</a> are custom admin "
"pages for managing global settings via fields. You can create multiple pages "
"and sub-pages."
msgstr ""
"ACF <a href=\"%s\" target=\"_blank\">seçenek sayfaları</a>, alanlar "
"aracılığıyla genel ayarları yönetmeye yönelik özel yönetici sayfalarıdır. "
"Birden fazla sayfa ve alt sayfa oluşturabilirsiniz."

#: includes/admin/views/global/header.php:35
msgid "Add Options Page"
msgstr "Seçenekler sayfası ekle"

#: includes/admin/views/acf-post-type/advanced-settings.php:708
msgid "In the editor used as the placeholder of the title."
msgstr "Editörde başlığın yer tutucusu olarak kullanılır."

#: includes/admin/views/acf-post-type/advanced-settings.php:707
msgid "Title Placeholder"
msgstr "Başlık yer tutucusu"

#: includes/admin/views/global/navigation.php:97
msgid "4 Months Free"
msgstr "4 Ay ücretsiz"

#. translators: %s - A singular label for a post type or taxonomy.
#: includes/admin/views/global/form-top.php:59
msgid "(Duplicated from %s)"
msgstr "(%s öğesinden çoğaltıldı)"

#: includes/admin/tools/class-acf-admin-tool-export.php:289
msgid "Select Options Pages"
msgstr "Seçenekler sayfalarını seçin"

#: includes/admin/post-types/admin-taxonomy.php:107
msgid "Duplicate taxonomy"
msgstr "Taksonomiyi çoğalt"

#: includes/admin/post-types/admin-post-type.php:106
#: includes/admin/post-types/admin-taxonomy.php:106
msgid "Create taxonomy"
msgstr "Taksonomi oluştur"

#: includes/admin/post-types/admin-post-type.php:105
msgid "Duplicate post type"
msgstr "Yazı tipini çoğalt"

#: includes/admin/post-types/admin-post-type.php:104
#: includes/admin/post-types/admin-taxonomy.php:108
msgid "Create post type"
msgstr "Yazı tipi oluştur"

#: includes/admin/post-types/admin-post-type.php:103
#: includes/admin/post-types/admin-taxonomy.php:105
msgid "Link field groups"
msgstr "Alan gruplarını bağla"

#: includes/admin/post-types/admin-post-type.php:102
#: includes/admin/post-types/admin-taxonomy.php:104
msgid "Add fields"
msgstr "Alan ekle"

#: includes/admin/post-types/admin-field-group.php:147
msgid "This Field"
msgstr "Bu alan"

#: includes/admin/admin.php:361
msgid "ACF PRO"
msgstr "ACF PRO"

#: includes/admin/admin.php:359
msgid "Feedback"
msgstr "Geri bildirim"

#: includes/admin/admin.php:357
msgid "Support"
msgstr "Destek"

#. translators: This text is prepended by a link to ACF's website, and appended
#. by a link to WP Engine's website.
#: includes/admin/admin.php:332
msgid "is developed and maintained by"
msgstr "geliştiren ve devam ettiren"

#. translators: %s - either "post type" or "taxonomy"
#: includes/admin/admin-internal-post-type.php:313
msgid "Add this %s to the location rules of the selected field groups."
msgstr "Bu %s öğesini seçilen alan gruplarının konum kurallarına ekleyin."

#. translators: %s the URL to ACF's bidirectional relationship documentation
#: includes/acf-bidirectional-functions.php:272
msgid ""
"Enabling the bidirectional setting allows you to update a value in the "
"target fields for each value selected for this field, adding or removing the "
"Post ID, Taxonomy ID or User ID of the item being updated. For more "
"information, please read the <a href=\"%s\" target=\"_blank\">documentation</"
"a>."
msgstr ""
"Çift yönlü ayarın etkinleştirilmesi, güncellenen öğenin Posta Kimliğini, "
"Sınıflandırma Kimliğini veya Kullanıcı Kimliğini ekleyerek veya kaldırarak, "
"bu alan için seçilen her değer için hedef alanlardaki bir değeri "
"güncellemenize olanak tanır. Daha fazla bilgi için lütfen <a href=\"%s\" "
"target=\"_blank\">belgeleri</a> okuyun."

#: includes/acf-bidirectional-functions.php:248
msgid ""
"Select field(s) to store the reference back to the item being updated. You "
"may select this field. Target fields must be compatible with where this "
"field is being displayed. For example, if this field is displayed on a "
"Taxonomy, your target field should be of type Taxonomy"
msgstr ""
"Referansı güncellenmekte olan öğeye geri depolamak için alanları seçin. Bu "
"alanı seçebilirsiniz. Hedef alanlar bu alanın görüntülendiği yerle uyumlu "
"olmalıdır. Örneğin, bu alan bir Taksonomide görüntüleniyorsa, hedef alanınız "
"Taksonomi türünde olmalıdır"

#: includes/acf-bidirectional-functions.php:247
msgid "Target Field"
msgstr "Hedef alan"

#: includes/acf-bidirectional-functions.php:221
msgid "Update a field on the selected values, referencing back to this ID"
msgstr "Seçilen değerlerdeki bir alanı bu kimliğe referans vererek güncelleyin"

#: includes/acf-bidirectional-functions.php:220
msgid "Bidirectional"
msgstr "Çift yönlü"

#. translators: %s A field type name, such as "Relationship"
#: includes/acf-bidirectional-functions.php:193
msgid "%s Field"
msgstr "%s alanı"

#: includes/fields/class-acf-field-page_link.php:498
#: includes/fields/class-acf-field-post_object.php:411
#: includes/fields/class-acf-field-select.php:378
#: includes/fields/class-acf-field-user.php:111
msgid "Select Multiple"
msgstr "Birden çok seçin"

#: includes/admin/views/global/navigation.php:241
msgid "WP Engine logo"
msgstr "WP Engine logosu"

#: includes/admin/views/acf-taxonomy/basic-settings.php:58
msgid "Lower case letters, underscores and dashes only, Max 32 characters."
msgstr ""
"Yalnızca küçük harfler, alt çizgiler ve kısa çizgiler, en fazla 32 karakter."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1156
msgid "The capability name for assigning terms of this taxonomy."
msgstr "Bu sınıflandırmanın terimlerini atamak için kullanılan yetenek adı."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1155
msgid "Assign Terms Capability"
msgstr "Terim yeteneği ata"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1139
msgid "The capability name for deleting terms of this taxonomy."
msgstr "Bu sınıflandırmanın terimlerini silmeye yönelik yetenek adı."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1138
msgid "Delete Terms Capability"
msgstr "Terim yeteneği sil"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1122
msgid "The capability name for editing terms of this taxonomy."
msgstr "Bu sınıflandırmanın terimlerini düzenlemeye yönelik yetenek adı."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1121
msgid "Edit Terms Capability"
msgstr "Terim yeteneği düzenle"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1105
msgid "The capability name for managing terms of this taxonomy."
msgstr "Bu sınıflandırmanın terimlerini yönetmeye yönelik yetenek adı."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1104
msgid "Manage Terms Capability"
msgstr "Terim yeteneği yönet"

#: includes/admin/views/acf-post-type/advanced-settings.php:929
msgid ""
"Sets whether posts should be excluded from search results and taxonomy "
"archive pages."
msgstr ""
"Yazıların arama sonuçlarından ve sınıflandırma arşivi sayfalarından hariç "
"tutulup tutulmayacağını ayarlar."

#: includes/admin/views/acf-field-group/pro-features.php:78
msgid "More Tools from WP Engine"
msgstr "WP Engine'den daha fazla araç"

#. translators: %s - WP Engine logo
#: includes/admin/views/acf-field-group/pro-features.php:73
msgid "Built for those that build with WordPress, by the team at %s"
msgstr "WordPress ile geliştirenler için, %s ekibi tarafından oluşturuldu"

#: includes/admin/views/acf-field-group/pro-features.php:6
msgid "View Pricing & Upgrade"
msgstr "Fiyatlandırmayı görüntüle ve yükselt"

#: includes/admin/views/acf-field-group/pro-features.php:3
#: includes/admin/views/options-page-preview.php:29
#: includes/fields/class-acf-field-icon_picker.php:273
msgid "Learn More"
msgstr "Daha fazla bilgi"

#: includes/admin/views/acf-field-group/pro-features.php:28
msgid ""
"Speed up your workflow and develop better websites with features like ACF "
"Blocks and Options Pages, and sophisticated field types like Repeater, "
"Flexible Content, Clone, and Gallery."
msgstr ""
"ACF blokları ve seçenek sayfaları gibi özelliklerin yanı sıra tekrarlayıcı, "
"esnek içerik, klonlama ve galeri gibi gelişmiş alan türleriyle iş akışınızı "
"hızlandırın ve daha iyi web siteleri geliştirin."

#: includes/admin/views/acf-field-group/pro-features.php:2
msgid "Unlock Advanced Features and Build Even More with ACF PRO"
msgstr ""
"ACF PRO ile gelişmiş özelliklerin kilidini açın ve daha fazlasını oluşturun"

#. translators: %s - singular label of post type/taxonomy, i.e. "Movie"/"Genre"
#: includes/admin/views/global/form-top.php:19
msgid "%s fields"
msgstr "%s alanları"

#: includes/admin/post-types/admin-taxonomies.php:267
msgid "No terms"
msgstr "Terim yok"

#: includes/admin/post-types/admin-taxonomies.php:240
msgid "No post types"
msgstr "Yazı tipi yok"

#: includes/admin/post-types/admin-post-types.php:264
msgid "No posts"
msgstr "Yazı yok"

#: includes/admin/post-types/admin-post-types.php:238
msgid "No taxonomies"
msgstr "Taksonomi yok"

#: includes/admin/post-types/admin-post-types.php:183
#: includes/admin/post-types/admin-taxonomies.php:182
msgid "No field groups"
msgstr "Alan grubu yok"

#: includes/admin/post-types/admin-field-groups.php:255
msgid "No fields"
msgstr "Alan yok"

#: includes/admin/post-types/admin-field-groups.php:128
#: includes/admin/post-types/admin-post-types.php:147
#: includes/admin/post-types/admin-taxonomies.php:146
msgid "No description"
msgstr "Açıklama yok"

#: includes/fields/class-acf-field-page_link.php:465
#: includes/fields/class-acf-field-post_object.php:374
#: includes/fields/class-acf-field-relationship.php:573
msgid "Any post status"
msgstr "Herhangi bir yazı durumu"

#: includes/post-types/class-acf-taxonomy.php:288
msgid ""
"This taxonomy key is already in use by another taxonomy registered outside "
"of ACF and cannot be used."
msgstr ""
"Bu taksonomi anahtarı, ACF dışında kayıtlı başka bir taksonomi tarafından "
"zaten kullanılıyor ve kullanılamaz."

#: includes/post-types/class-acf-taxonomy.php:284
msgid ""
"This taxonomy key is already in use by another taxonomy in ACF and cannot be "
"used."
msgstr ""
"Bu taksonomi anahtarı ACF'deki başka bir taksonomi tarafından zaten "
"kullanılıyor ve kullanılamaz."

#: includes/post-types/class-acf-taxonomy.php:256
msgid ""
"The taxonomy key must only contain lower case alphanumeric characters, "
"underscores or dashes."
msgstr ""
"Taksonomi anahtarı yalnızca küçük harf alfasayısal karakterler, alt çizgiler "
"veya kısa çizgiler içermelidir."

#: includes/post-types/class-acf-taxonomy.php:251
msgid "The taxonomy key must be under 32 characters."
msgstr "Taksonomi anahtarı 32 karakterden kısa olmalıdır."

#: includes/post-types/class-acf-taxonomy.php:99
msgid "No Taxonomies found in Trash"
msgstr "Çöp kutusunda taksonomi bulunamadı"

#: includes/post-types/class-acf-taxonomy.php:98
msgid "No Taxonomies found"
msgstr "Taksonomi bulunamadı"

#: includes/post-types/class-acf-taxonomy.php:97
msgid "Search Taxonomies"
msgstr "Taksonomilerde ara"

#: includes/post-types/class-acf-taxonomy.php:96
msgid "View Taxonomy"
msgstr "Taksonomi görüntüle"

#: includes/post-types/class-acf-taxonomy.php:95
msgid "New Taxonomy"
msgstr "Yeni taksonomi"

#: includes/post-types/class-acf-taxonomy.php:94
msgid "Edit Taxonomy"
msgstr "Taksonomi düzenle"

#: includes/post-types/class-acf-taxonomy.php:93
msgid "Add New Taxonomy"
msgstr "Yeni taksonomi ekle"

#: includes/post-types/class-acf-post-type.php:100
msgid "No Post Types found in Trash"
msgstr "Çöp kutusunda yazı tipi bulunamadı"

#: includes/post-types/class-acf-post-type.php:99
msgid "No Post Types found"
msgstr "Yazı tipi bulunamadı"

#: includes/post-types/class-acf-post-type.php:98
msgid "Search Post Types"
msgstr "Yazı tiplerinde ara"

#: includes/post-types/class-acf-post-type.php:97
msgid "View Post Type"
msgstr "Yazı tipini görüntüle"

#: includes/post-types/class-acf-post-type.php:96
msgid "New Post Type"
msgstr "Yeni yazı tipi"

#: includes/post-types/class-acf-post-type.php:95
msgid "Edit Post Type"
msgstr "Yazı tipini düzenle"

#: includes/post-types/class-acf-post-type.php:94
msgid "Add New Post Type"
msgstr "Yeni yazı tipi ekle"

#: includes/post-types/class-acf-post-type.php:366
msgid ""
"This post type key is already in use by another post type registered outside "
"of ACF and cannot be used."
msgstr ""
"Bu yazı tipi anahtarı, ACF dışında kayıtlı başka bir yazı tipi tarafından "
"zaten kullanılıyor ve kullanılamaz."

#: includes/post-types/class-acf-post-type.php:361
msgid ""
"This post type key is already in use by another post type in ACF and cannot "
"be used."
msgstr ""
"Bu yazı tipi anahtarı, ACF'deki başka bir yazı tipi tarafından zaten "
"kullanılıyor ve kullanılamaz."

#. translators: %s a link to WordPress.org's Reserved Terms page
#: includes/post-types/class-acf-post-type.php:339
#: includes/post-types/class-acf-taxonomy.php:262
msgid ""
"This field must not be a WordPress <a href=\"%s\" target=\"_blank\">reserved "
"term</a>."
msgstr ""
"Bu alan, WordPress'e <a href=\"%s\" target=\"_blank\">ayrılmış bir terim</a> "
"olmamalıdır."

#: includes/post-types/class-acf-post-type.php:333
msgid ""
"The post type key must only contain lower case alphanumeric characters, "
"underscores or dashes."
msgstr ""
"Yazı tipi anahtarı yalnızca küçük harf alfasayısal karakterler, alt çizgiler "
"veya kısa çizgiler içermelidir."

#: includes/post-types/class-acf-post-type.php:328
msgid "The post type key must be under 20 characters."
msgstr "Yazı tipi anahtarı 20 karakterin altında olmalıdır."

#: includes/fields/class-acf-field-wysiwyg.php:24
msgid "We do not recommend using this field in ACF Blocks."
msgstr "Bu alanın ACF bloklarında kullanılmasını önermiyoruz."

#: includes/fields/class-acf-field-wysiwyg.php:24
msgid ""
"Displays the WordPress WYSIWYG editor as seen in Posts and Pages allowing "
"for a rich text-editing experience that also allows for multimedia content."
msgstr ""
"WordPress WYSIWYG düzenleyicisini yazılar ve sayfalarda görüldüğü gibi "
"görüntüler ve multimedya içeriğine de olanak tanıyan zengin bir metin "
"düzenleme deneyimi sunar."

#: includes/fields/class-acf-field-wysiwyg.php:22
msgid "WYSIWYG Editor"
msgstr "WYSIWYG düzenleyicisi"

#: includes/fields/class-acf-field-user.php:17
msgid ""
"Allows the selection of one or more users which can be used to create "
"relationships between data objects."
msgstr ""
"Veri nesneleri arasında ilişkiler oluşturmak için kullanılabilecek bir veya "
"daha fazla kullanıcının seçilmesine olanak tanır."

#: includes/fields/class-acf-field-url.php:20
msgid "A text input specifically designed for storing web addresses."
msgstr ""
"Web adreslerini depolamak için özel olarak tasarlanmış bir metin girişi."

#: includes/fields/class-acf-field-icon_picker.php:56
#: includes/fields/class-acf-field-url.php:19
msgid "URL"
msgstr "Web adresi"

#: includes/fields/class-acf-field-true_false.php:24
msgid ""
"A toggle that allows you to pick a value of 1 or 0 (on or off, true or "
"false, etc). Can be presented as a stylized switch or checkbox."
msgstr ""
"1 veya 0 değerini seçmenizi sağlayan bir geçiş (açık veya kapalı, doğru veya "
"yanlış vb.). Stilize edilmiş bir anahtar veya onay kutusu olarak sunulabilir."

#: includes/fields/class-acf-field-time_picker.php:24
msgid ""
"An interactive UI for picking a time. The time format can be customized "
"using the field settings."
msgstr ""
"Zaman seçmek için etkileşimli bir kullanıcı arayüzü. Saat formatı alan "
"ayarları kullanılarak özelleştirilebilir."

#: includes/fields/class-acf-field-textarea.php:23
msgid "A basic textarea input for storing paragraphs of text."
msgstr "Metin paragraflarını depolamak için temel bir metin alanı girişi."

#: includes/fields/class-acf-field-text.php:23
msgid "A basic text input, useful for storing single string values."
msgstr ""
"Tek dize değerlerini depolamak için kullanışlı olan temel bir metin girişi."

#: includes/fields/class-acf-field-taxonomy.php:22
msgid ""
"Allows the selection of one or more taxonomy terms based on the criteria and "
"options specified in the fields settings."
msgstr ""
"Alan ayarlarında belirtilen kriterlere ve seçeneklere göre bir veya daha "
"fazla sınıflandırma teriminin seçilmesine olanak tanır."

#: includes/fields/class-acf-field-tab.php:25
msgid ""
"Allows you to group fields into tabbed sections in the edit screen. Useful "
"for keeping fields organized and structured."
msgstr ""
"Düzenleme ekranında alanları sekmeli bölümler halinde gruplandırmanıza "
"olanak tanır. Alanları düzenli ve yapılandırılmış tutmak için kullanışlıdır."

#: includes/fields/class-acf-field-select.php:18
msgid "A dropdown list with a selection of choices that you specify."
msgstr "Belirttiğiniz seçeneklerin yer aldığı açılır liste."

#: includes/fields/class-acf-field-relationship.php:19
msgid ""
"A dual-column interface to select one or more posts, pages, or custom post "
"type items to create a relationship with the item that you're currently "
"editing. Includes options to search and filter."
msgstr ""
"Şu anda düzenlemekte olduğunuz öğeyle bir ilişki oluşturmak amacıyla bir "
"veya daha fazla gönderiyi, sayfayı veya özel gönderi türü öğesini seçmek "
"için çift sütunlu bir arayüz. Arama ve filtreleme seçeneklerini içerir."

#: includes/fields/class-acf-field-range.php:23
msgid ""
"An input for selecting a numerical value within a specified range using a "
"range slider element."
msgstr ""
"Bir aralık kaydırıcı öğesi kullanılarak belirli bir aralıktaki sayısal bir "
"değerin seçilmesine yönelik bir giriş."

#: includes/fields/class-acf-field-radio.php:24
msgid ""
"A group of radio button inputs that allows the user to make a single "
"selection from values that you specify."
msgstr ""
"Kullanıcının belirttiğiniz değerlerden tek bir seçim yapmasına olanak "
"tanıyan bir grup radyo düğmesi girişi."

#: includes/fields/class-acf-field-post_object.php:17
msgid ""
"An interactive and customizable UI for picking one or many posts, pages or "
"post type items with the option to search. "
msgstr ""
"Bir veya daha fazla yazıyı, sayfayı veya yazı tipi öğesini arama seçeneğiyle "
"birlikte seçmek için etkileşimli ve özelleştirilebilir bir kullanıcı "
"arayüzü. "

#: includes/fields/class-acf-field-password.php:23
msgid "An input for providing a password using a masked field."
msgstr "Maskelenmiş bir alan kullanarak parola sağlamaya yönelik bir giriş."

#: includes/fields/class-acf-field-page_link.php:457
#: includes/fields/class-acf-field-post_object.php:366
#: includes/fields/class-acf-field-relationship.php:565
msgid "Filter by Post Status"
msgstr "Yazı durumuna göre filtrele"

#: includes/fields/class-acf-field-page_link.php:24
msgid ""
"An interactive dropdown to select one or more posts, pages, custom post type "
"items or archive URLs, with the option to search."
msgstr ""
"Arama seçeneğiyle birlikte bir veya daha fazla yazıyı, sayfayı, özel yazı "
"tipi ögesini veya arşiv adresini seçmek için etkileşimli bir açılır menü."

#: includes/fields/class-acf-field-oembed.php:24
msgid ""
"An interactive component for embedding videos, images, tweets, audio and "
"other content by making use of the native WordPress oEmbed functionality."
msgstr ""
"Yerel WordPress oEmbed işlevselliğini kullanarak video, resim, tweet, ses ve "
"diğer içerikleri gömmek için etkileşimli bir bileşen."

#: includes/fields/class-acf-field-number.php:23
msgid "An input limited to numerical values."
msgstr "Sayısal değerlerle sınırlı bir giriş."

#: includes/fields/class-acf-field-message.php:25
msgid ""
"Used to display a message to editors alongside other fields. Useful for "
"providing additional context or instructions around your fields."
msgstr ""
"Diğer alanların yanında editörlere bir mesaj görüntülemek için kullanılır. "
"Alanlarınızla ilgili ek bağlam veya talimatlar sağlamak için kullanışlıdır."

#: includes/fields/class-acf-field-link.php:24
msgid ""
"Allows you to specify a link and its properties such as title and target "
"using the WordPress native link picker."
msgstr ""
"WordPress yerel bağlantı seçiciyi kullanarak bir bağlantıyı ve onun başlık "
"ve hedef gibi özelliklerini belirtmenize olanak tanır."

#: includes/fields/class-acf-field-image.php:24
msgid "Uses the native WordPress media picker to upload, or choose images."
msgstr ""
"Görselleri yüklemek veya seçmek için yerel WordPress ortam seçicisini "
"kullanır."

#: includes/fields/class-acf-field-group.php:24
msgid ""
"Provides a way to structure fields into groups to better organize the data "
"and the edit screen."
msgstr ""
"Verileri ve düzenleme ekranını daha iyi organize etmek için alanları gruplar "
"halinde yapılandırmanın bir yolunu sağlar."

#: includes/fields/class-acf-field-google-map.php:24
msgid ""
"An interactive UI for selecting a location using Google Maps. Requires a "
"Google Maps API key and additional configuration to display correctly."
msgstr ""
"Google Haritalar'ı kullanarak konum seçmek için etkileşimli bir kullanıcı "
"arayüzü. Doğru şekilde görüntülenmesi için bir Google Haritalar API anahtarı "
"ve ek yapılandırma gerekir."

#: includes/fields/class-acf-field-file.php:24
msgid "Uses the native WordPress media picker to upload, or choose files."
msgstr ""
"Dosyaları yüklemek veya seçmek için yerel WordPress ortam seçicisini "
"kullanır."

#: includes/fields/class-acf-field-email.php:23
msgid "A text input specifically designed for storing email addresses."
msgstr ""
"E-posta adreslerini saklamak için özel olarak tasarlanmış bir metin girişi."

#: includes/fields/class-acf-field-date_time_picker.php:24
msgid ""
"An interactive UI for picking a date and time. The date return format can be "
"customized using the field settings."
msgstr ""
"Tarih ve saat seçmek için etkileşimli bir kullanıcı arayüzü. Tarih dönüş "
"biçimi alan ayarları kullanılarak özelleştirilebilir."

#: includes/fields/class-acf-field-date_picker.php:24
msgid ""
"An interactive UI for picking a date. The date return format can be "
"customized using the field settings."
msgstr ""
"Tarih seçmek için etkileşimli bir kullanıcı arayüzü. Tarih dönüş biçimi alan "
"ayarları kullanılarak özelleştirilebilir."

#: includes/fields/class-acf-field-color_picker.php:24
msgid "An interactive UI for selecting a color, or specifying a Hex value."
msgstr ""
"Bir renk seçmek veya Hex değerini belirtmek için etkileşimli bir kullanıcı "
"arayüzü."

#: includes/fields/class-acf-field-checkbox.php:24
msgid ""
"A group of checkbox inputs that allow the user to select one, or multiple "
"values that you specify."
msgstr ""
"Kullanıcının belirttiğiniz bir veya daha fazla değeri seçmesine olanak "
"tanıyan bir grup onay kutusu girişi."

#: includes/fields/class-acf-field-button-group.php:25
msgid ""
"A group of buttons with values that you specify, users can choose one option "
"from the values provided."
msgstr ""
"Belirlediğiniz değerlere sahip bir grup düğme, kullanıcılar sağlanan "
"değerlerden bir seçeneği seçebilir."

#: includes/fields/class-acf-field-accordion.php:26
msgid ""
"Allows you to group and organize custom fields into collapsable panels that "
"are shown while editing content. Useful for keeping large datasets tidy."
msgstr ""
"Özel alanları, içeriği düzenlerken gösterilen daraltılabilir paneller "
"halinde gruplandırmanıza ve düzenlemenize olanak tanır. Büyük veri "
"kümelerini düzenli tutmak için kullanışlıdır."

#: includes/fields.php:449
msgid ""
"This provides a solution for repeating content such as slides, team members, "
"and call-to-action tiles, by acting as a parent to a set of subfields which "
"can be repeated again and again."
msgstr ""
"Bu, tekrar tekrar tekrarlanabilecek bir dizi alt alanın üst öğesi olarak "
"hareket ederek slaytlar, ekip üyeleri ve harekete geçirici mesaj parçaları "
"gibi yinelenen içeriklere yönelik bir çözüm sağlar."

#: includes/fields.php:439
msgid ""
"This provides an interactive interface for managing a collection of "
"attachments. Most settings are similar to the Image field type. Additional "
"settings allow you to specify where new attachments are added in the gallery "
"and the minimum/maximum number of attachments allowed."
msgstr ""
"Bu, bir ek koleksiyonunu yönetmek için etkileşimli bir arayüz sağlar. Çoğu "
"ayar görsel alanı türüne benzer. Ek ayarlar, galeride yeni eklerin nereye "
"ekleneceğini ve izin verilen minimum/maksimum ek sayısını belirtmenize "
"olanak tanır."

#: includes/fields.php:429
msgid ""
"This provides a simple, structured, layout-based editor. The Flexible "
"Content field allows you to define, create and manage content with total "
"control by using layouts and subfields to design the available blocks."
msgstr ""
"Bu, basit, yapılandırılmış, yerleşim tabanlı bir düzenleyici sağlar. Esnek "
"içerik alanı, mevcut blokları tasarlamak için yerleşimleri ve alt alanları "
"kullanarak içeriği tam kontrolle tanımlamanıza, oluşturmanıza ve yönetmenize "
"olanak tanır."

#: includes/fields.php:419
msgid ""
"This allows you to select and display existing fields. It does not duplicate "
"any fields in the database, but loads and displays the selected fields at "
"run-time. The Clone field can either replace itself with the selected fields "
"or display the selected fields as a group of subfields."
msgstr ""
"Bu, mevcut alanları seçmenize ve görüntülemenize olanak tanır. "
"Veritabanındaki hiçbir alanı çoğaltmaz, ancak seçilen alanları çalışma "
"zamanında yükler ve görüntüler. Klon alanı kendisini seçilen alanlarla "
"değiştirebilir veya seçilen alanları bir alt alan grubu olarak "
"görüntüleyebilir."

#: includes/fields.php:416
msgctxt "noun"
msgid "Clone"
msgstr "Çoğalt"

#: includes/admin/views/global/navigation.php:86 includes/fields.php:331
#: src/Site_Health/Site_Health.php:290
msgid "PRO"
msgstr "PRO"

#: includes/fields.php:329 includes/fields.php:386
msgid "Advanced"
msgstr "Gelişmiş"

#: includes/ajax/class-acf-ajax-local-json-diff.php:90
msgid "JSON (newer)"
msgstr "JSON (daha yeni)"

#: includes/ajax/class-acf-ajax-local-json-diff.php:86
msgid "Original"
msgstr "Orijinal"

#: includes/ajax/class-acf-ajax-local-json-diff.php:60
msgid "Invalid post ID."
msgstr "Geçersiz yazı kimliği."

#: includes/ajax/class-acf-ajax-local-json-diff.php:52
msgid "Invalid post type selected for review."
msgstr "İnceleme için geçersiz yazı tipi seçildi."

#: includes/admin/views/global/navigation.php:192
msgid "More"
msgstr "Daha fazlası"

#: includes/admin/views/browse-fields-modal.php:96
msgid "Tutorial"
msgstr "Başlangıç Kılavuzu"

#: includes/admin/views/browse-fields-modal.php:73
msgid "Select Field"
msgstr "Alan seç"

#. translators: %s: A link to the popular fields used in ACF
#: includes/admin/views/browse-fields-modal.php:60
msgid "Try a different search term or browse %s"
msgstr "Farklı bir arama terimi deneyin veya %s göz atın"

#: includes/admin/views/browse-fields-modal.php:57
msgid "Popular fields"
msgstr "Popüler alanlar"

#. translators: %s: The invalid search term
#: includes/admin/views/browse-fields-modal.php:50
#: includes/fields/class-acf-field-icon_picker.php:97
msgid "No search results for '%s'"
msgstr "'%s' sorgusu için arama sonucu yok"

#: includes/admin/views/browse-fields-modal.php:23
msgid "Search fields..."
msgstr "Arama alanları..."

#: includes/admin/views/browse-fields-modal.php:21
msgid "Select Field Type"
msgstr "Alan tipi seçin"

#: includes/admin/views/browse-fields-modal.php:4
msgid "Popular"
msgstr "Popüler"

#: includes/admin/views/acf-taxonomy/list-empty.php:15
msgid "Add Taxonomy"
msgstr "Sınıflandırma ekle"

#: includes/admin/views/acf-taxonomy/list-empty.php:14
msgid "Create custom taxonomies to classify post type content"
msgstr ""
"Yazı tipi içeriğini sınıflandırmak için özel sınıflandırmalar oluşturun"

#: includes/admin/views/acf-taxonomy/list-empty.php:13
msgid "Add Your First Taxonomy"
msgstr "İlk sınıflandırmanızı ekleyin"

#: includes/admin/views/acf-taxonomy/basic-settings.php:122
msgid "Hierarchical taxonomies can have descendants (like categories)."
msgstr "Hiyerarşik taksonomilerin alt ögeleri (kategoriler gibi) olabilir."

#: includes/admin/views/acf-taxonomy/basic-settings.php:107
msgid "Makes a taxonomy visible on the frontend and in the admin dashboard."
msgstr ""
"Ön uçta ve yönetici kontrol panelinde bir sınıflandırmayı görünür hale "
"getirir."

#: includes/admin/views/acf-taxonomy/basic-settings.php:91
msgid "One or many post types that can be classified with this taxonomy."
msgstr ""
"Bu sınıflandırmayla sınıflandırılabilecek bir veya daha fazla yazı tipi."

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:60
msgid "genre"
msgstr "tür"

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:42
msgid "Genre"
msgstr "Tür"

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:25
msgid "Genres"
msgstr "Türler"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1231
msgid ""
"Optional custom controller to use instead of `WP_REST_Terms_Controller `."
msgstr ""
"'WP_REST_Terms_Controller' yerine kullanılacak isteğe bağlı özel denetleyici."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1175
msgid "Expose this post type in the REST API."
msgstr "Bu yazı tipini REST API'sinde gösterin."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1075
msgid "Customize the query variable name"
msgstr "Sorgu değişkeni adını özelleştirin"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1048
msgid ""
"Terms can be accessed using the non-pretty permalink, e.g., {query_var}"
"={term_slug}."
msgstr ""
"Terimlere, güzel olmayan kalıcı bağlantı kullanılarak erişilebilir, örneğin, "
"{query_var}={term_slug}."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1001
msgid "Parent-child terms in URLs for hierarchical taxonomies."
msgstr "Hiyerarşik sınıflandırmalar için web adreslerindeki üst-alt terimler."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:961
msgid "Customize the slug used in the URL"
msgstr "Adreste kullanılan kısa ismi özelleştir"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:944
msgid "Permalinks for this taxonomy are disabled."
msgstr "Bu sınıflandırmaya ilişkin kalıcı bağlantılar devre dışı bırakıldı."

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-taxonomy/advanced-settings.php:941
msgid ""
"Rewrite the URL using the taxonomy key as the slug. Your permalink structure "
"will be"
msgstr ""
"Kısa isim olarak sınıflandırma anahtarını kullanarak adresi yeniden yazın. "
"Kalıcı bağlantı yapınız şöyle olacak"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:933
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1050
#: includes/admin/views/acf-taxonomy/basic-settings.php:57
msgid "Taxonomy Key"
msgstr "Sınıflandırma anahtarı"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:931
msgid "Select the type of permalink to use for this taxonomy."
msgstr "Bu sınıflandırma için kullanılacak kalıcı bağlantı türünü seçin."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:916
msgid "Display a column for the taxonomy on post type listing screens."
msgstr ""
"Yazı tipi listeleme ekranlarında sınıflandırma için bir sütun görüntüleyin."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:915
msgid "Show Admin Column"
msgstr "Yönetici sütununu göster"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:902
msgid "Show the taxonomy in the quick/bulk edit panel."
msgstr "Hızlı/toplu düzenleme panelinde sınıflandırmayı göster."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:901
msgid "Quick Edit"
msgstr "Hızlı düzenle"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:888
msgid "List the taxonomy in the Tag Cloud Widget controls."
msgstr "Etiket bulutu gereç kontrollerindeki sınıflandırmayı listele."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:887
msgid "Tag Cloud"
msgstr "Etiket bulutu"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:842
msgid ""
"A PHP function name to be called for sanitizing taxonomy data saved from a "
"meta box."
msgstr ""
"Bir meta kutusundan kaydedilen sınıflandırma verilerinin temizlenmesi için "
"çağrılacak bir PHP işlev adı."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:841
msgid "Meta Box Sanitization Callback"
msgstr "Meta kutusu temizleme geri çağrısı"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:821
msgid "Register Meta Box Callback"
msgstr "Meta kutusu geri çağrısını kaydet"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:752
msgid "No Meta Box"
msgstr "Meta mutusu yok"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:751
msgid "Custom Meta Box"
msgstr "Özel meta kutusu"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:768
msgid ""
"Controls the meta box on the content editor screen. By default, the "
"Categories meta box is shown for hierarchical taxonomies, and the Tags meta "
"box is shown for non-hierarchical taxonomies."
msgstr ""
"İçerik düzenleyici ekranındaki meta kutusunu kontrol eder. Varsayılan "
"olarak, hiyerarşik sınıflandırmalar için Kategoriler meta kutusu gösterilir "
"ve hiyerarşik olmayan sınıflandırmalar için Etiketler meta kutusu gösterilir."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:767
msgid "Meta Box"
msgstr "Meta kutusu"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:746
#: includes/admin/views/acf-taxonomy/advanced-settings.php:773
msgid "Categories Meta Box"
msgstr "Kategoriler meta kutusu"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:745
#: includes/admin/views/acf-taxonomy/advanced-settings.php:772
msgid "Tags Meta Box"
msgstr "Etiketler meta kutusu"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:704
msgid "A link to a tag"
msgstr "Bir etikete bağlantı"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:703
msgid "Describes a navigation link block variation used in the block editor."
msgstr ""
"Blok düzenleyicide kullanılan bir gezinme bağlantısı bloğu varyasyonunu "
"açıklar."

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:698
msgid "A link to a %s"
msgstr "Bir %s bağlantısı"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:683
msgid "Tag Link"
msgstr "Etiket bağlantısı"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:682
msgid ""
"Assigns a title for navigation link block variation used in the block editor."
msgstr ""
"Blok düzenleyicide kullanılan gezinme bağlantısı blok varyasyonu için bir "
"başlık atar."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:663
msgid "← Go to tags"
msgstr "← Etiketlere git"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:662
msgid ""
"Assigns the text used to link back to the main index after updating a term."
msgstr ""
"Bir terimi güncelleştirdikten sonra ana dizine geri bağlanmak için "
"kullanılan metni atar."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:661
msgid "Back To Items"
msgstr "Öğelere geri dön"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:657
msgid "← Go to %s"
msgstr "← %s git"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:642
msgid "Tags list"
msgstr "Etiket listesi"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:641
msgid "Assigns text to the table hidden heading."
msgstr "Tablonun gizli başlığına metin atası yapar."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:622
msgid "Tags list navigation"
msgstr "Etiket listesi dolaşımı"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:621
msgid "Assigns text to the table pagination hidden heading."
msgstr "Tablo sayfalandırma gizli başlığına metin ataması yapar."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:597
msgid "Filter by category"
msgstr "Kategoriye göre filtrele"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:596
msgid "Assigns text to the filter button in the posts lists table."
msgstr "Yazı listeleri tablosundaki filtre düğmesine metin ataması yapar."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:595
msgid "Filter By Item"
msgstr "Ögeye göre filtrele"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:591
msgid "Filter by %s"
msgstr "%s ile filtrele"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:575
#: includes/admin/views/acf-taxonomy/advanced-settings.php:576
msgid ""
"The description is not prominent by default; however, some themes may show "
"it."
msgstr ""
"Tanım bölümü varsayılan olarak ön planda değildir, yine de bazı temalar bu "
"bölümü gösterebilir."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:574
msgid "Describes the Description field on the Edit Tags screen."
msgstr "Etiketleri düzenle ekranındaki açıklama alanını tanımlar."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:573
msgid "Description Field Description"
msgstr "Açıklama alanı tanımı"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:554
#: includes/admin/views/acf-taxonomy/advanced-settings.php:555
msgid ""
"Assign a parent term to create a hierarchy. The term Jazz, for example, "
"would be the parent of Bebop and Big Band"
msgstr ""
"Hiyerarşi oluşturmak için bir üst terim atayın. Örneğin Jazz terimi Bebop ve "
"Big Band'in üst öğesi olacaktır"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:553
msgid "Describes the Parent field on the Edit Tags screen."
msgstr "Etiketleri düzenle ekranındaki ana alanı açıklar."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:552
msgid "Parent Field Description"
msgstr "Ana alan açıklaması"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:538
#: includes/admin/views/acf-taxonomy/advanced-settings.php:539
msgid ""
"The \"slug\" is the URL-friendly version of the name. It is usually all "
"lower case and contains only letters, numbers, and hyphens."
msgstr ""
"\"Kısa isim\", adın web adresi dostu sürümüdür. Genellikle tamamı küçük "
"harftir ve yalnızca harf, sayı ve kısa çizgi içerir."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:537
msgid "Describes the Slug field on the Edit Tags screen."
msgstr "Etiketleri düzenle ekranındaki kısa isim alanını tanımlar."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:536
msgid "Slug Field Description"
msgstr "Kısa isim alanı açıklaması"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:522
#: includes/admin/views/acf-taxonomy/advanced-settings.php:523
msgid "The name is how it appears on your site"
msgstr "Ad, sitenizde nasıl göründüğüdür"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:521
msgid "Describes the Name field on the Edit Tags screen."
msgstr "Etiketleri düzenle ekranındaki ad alanını açıklar."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:520
msgid "Name Field Description"
msgstr "Ad alan açıklaması"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:507
msgid "No tags"
msgstr "Etiket yok"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:506
msgid ""
"Assigns the text displayed in the posts and media list tables when no tags "
"or categories are available."
msgstr ""
"Hiçbir etiket veya kategori bulunmadığında gönderilerde ve ortam listesi "
"tablolarında görüntülenen metni atar."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:505
msgid "No Terms"
msgstr "Terim yok"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:501
msgid "No %s"
msgstr "%s yok"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:486
msgid "No tags found"
msgstr "Etiket bulunamadı"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:485
msgid ""
"Assigns the text displayed when clicking the 'choose from most used' text in "
"the taxonomy meta box when no tags are available, and assigns the text used "
"in the terms list table when there are no items for a taxonomy."
msgstr ""
"Hiçbir etiket mevcut olmadığında sınıflandırma meta kutusundaki 'en çok "
"kullanılanlardan seç' metnine tıklandığında görüntülenen metnin atamasını "
"yapar ve bir sınıflandırma için hiçbir öge olmadığında terimler listesi "
"tablosunda kullanılan metnin atamasını yapar."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:484
msgid "Not Found"
msgstr "Bulunamadı"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:463
msgid "Assigns text to the Title field of the Most Used tab."
msgstr "En Çok Kullanılan sekmesinin başlık alanına metin atar."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:462
#: includes/admin/views/acf-taxonomy/advanced-settings.php:464
#: includes/admin/views/acf-taxonomy/advanced-settings.php:465
msgid "Most Used"
msgstr "En çok kullanılan"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:444
msgid "Choose from the most used tags"
msgstr "En çok kullanılan etiketler arasından seç"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:443
msgid ""
"Assigns the 'choose from most used' text used in the meta box when "
"JavaScript is disabled. Only used on non-hierarchical taxonomies."
msgstr ""
"JavaScript devre dışı bırakıldığında meta kutusunda kullanılan 'en çok "
"kullanılandan seç' metninin atamasını yapar. Yalnızca hiyerarşik olmayan "
"sınıflandırmalarda kullanılır."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:442
msgid "Choose From Most Used"
msgstr "En çok kullanılanlardan seç"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:438
msgid "Choose from the most used %s"
msgstr "En çok kullanılan %s arasından seçim yapın"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:418
msgid "Add or remove tags"
msgstr "Etiket ekle ya da kaldır"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:417
msgid ""
"Assigns the add or remove items text used in the meta box when JavaScript is "
"disabled. Only used on non-hierarchical taxonomies"
msgstr ""
"JavaScript devre dışı olduğunda meta kutusunda kullanılan öğe ekleme veya "
"kaldırma metninin atamasını yapar. Sadece hiyerarşik olmayan "
"sınıflandırmalarda kullanılır."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:416
msgid "Add Or Remove Items"
msgstr "Öğeleri ekle veya kaldır"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:412
msgid "Add or remove %s"
msgstr "%s ekle veya kaldır"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:392
msgid "Separate tags with commas"
msgstr "Etiketleri virgül ile ayırın"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:391
msgid ""
"Assigns the separate item with commas text used in the taxonomy meta box. "
"Only used on non-hierarchical taxonomies."
msgstr ""
"Ayrı öğeyi sınıflandırma meta kutusunda kullanılan virgül metniyle atar. "
"Yalnızca hiyerarşik olmayan sınıflandırmalarda kullanılır."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:390
msgid "Separate Items With Commas"
msgstr "Ögeleri virgülle ayırın"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:386
msgid "Separate %s with commas"
msgstr "%s içeriklerini virgülle ayırın"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:366
msgid "Popular Tags"
msgstr "Popüler etiketler"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:365
msgid "Assigns popular items text. Only used for non-hierarchical taxonomies."
msgstr ""
"Popüler ögeler metninin atamasını yapar. Yalnızca hiyerarşik olmayan "
"sınıflandırmalar için kullanılır."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:364
msgid "Popular Items"
msgstr "Popüler ögeler"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:361
msgid "Popular %s"
msgstr "Popüler %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:347
msgid "Search Tags"
msgstr "Etiketlerde ara"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:346
msgid "Assigns search items text."
msgstr "Arama öğeleri metninin atamasını yapar."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:323
msgid "Parent Category:"
msgstr "Üst kategori:"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:322
msgid "Assigns parent item text, but with a colon (:) added to the end."
msgstr ""
"Üst öğe metninin atamasını yapar ancak sonuna iki nokta üst üste (:) eklenir."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:321
msgid "Parent Item With Colon"
msgstr "İki nokta üst üste ile ana öğe"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:298
msgid "Parent Category"
msgstr "Üst kategori"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:297
msgid "Assigns parent item text. Only used on hierarchical taxonomies."
msgstr ""
"Üst öğe metnini belirler. Sadece hiyerarşik sınıflandırmalarda kullanılır."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:296
msgid "Parent Item"
msgstr "Ana öge"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:293
msgid "Parent %s"
msgstr "Ebeveyn %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:278
msgid "New Tag Name"
msgstr "Yeni etiket ismi"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:277
msgid "Assigns the new item name text."
msgstr "Yeni öğe adı metninin atamasını yapar."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:276
msgid "New Item Name"
msgstr "Yeni öge adı"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:273
msgid "New %s Name"
msgstr "Yeni %s adı"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:258
msgid "Add New Tag"
msgstr "Yeni etiket ekle"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:257
msgid "Assigns the add new item text."
msgstr "Yeni öğe ekleme metninin atamasını yapar."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:238
msgid "Update Tag"
msgstr "Etiketi güncelle"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:237
msgid "Assigns the update item text."
msgstr "Güncelleme öğesi metninin atamasını yapar."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:236
msgid "Update Item"
msgstr "Öğeyi güncelle"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:233
msgid "Update %s"
msgstr "%s güncelle"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:218
msgid "View Tag"
msgstr "Etiketi görüntüle"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:217
msgid "In the admin bar to view term during editing."
msgstr "Düzenleme sırasında terimi görüntülemek için yönetici çubuğunda."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:198
msgid "Edit Tag"
msgstr "Etiketi düzenle"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:197
msgid "At the top of the editor screen when editing a term."
msgstr "Bir terimi düzenlerken editör ekranının üst kısmında."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:178
msgid "All Tags"
msgstr "Tüm etiketler"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:177
msgid "Assigns the all items text."
msgstr "Tüm öğelerin metninin atamasını yapar."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:158
msgid "Assigns the menu name text."
msgstr "Menü adı metninin atamasını yapar."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:157
msgid "Menu Label"
msgstr "Menü etiketi"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:131
msgid "Active taxonomies are enabled and registered with WordPress."
msgstr "Aktif sınıflandırmalar WordPress'te etkinleştirilir ve kaydedilir."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:115
msgid "A descriptive summary of the taxonomy."
msgstr "Sınıflandırmanın açıklayıcı bir özeti."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:95
msgid "A descriptive summary of the term."
msgstr "Terimin açıklayıcı bir özeti."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:94
msgid "Term Description"
msgstr "Terim açıklaması"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:76
msgid "Single word, no spaces. Underscores and dashes allowed."
msgstr "Tek kelime, boşluksuz. Alt çizgi ve tireye izin var"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:75
msgid "Term Slug"
msgstr "Terim kısa adı"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:56
msgid "The name of the default term."
msgstr "Varsayılan terimin adı."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:55
msgid "Term Name"
msgstr "Terim adı"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:41
msgid ""
"Create a term for the taxonomy that cannot be deleted. It will not be "
"selected for posts by default."
msgstr ""
"Sınıflandırma için silinemeyecek bir terim oluşturun. Varsayılan olarak "
"yazılar için seçilmeyecektir."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:40
msgid "Default Term"
msgstr "Varsayılan terim"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:28
msgid ""
"Whether terms in this taxonomy should be sorted in the order they are "
"provided to `wp_set_object_terms()`."
msgstr ""
"Bu sınıflandırmadaki terimlerin `wp_set_object_terms()` işlevine "
"sağlandıkları sıraya göre sıralanıp sıralanmayacağı."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:27
msgid "Sort Terms"
msgstr "Terimleri sırala"

#: includes/admin/views/acf-post-type/list-empty.php:14
msgid "Add Post Type"
msgstr "Yazı tipi ekle"

#: includes/admin/views/acf-post-type/list-empty.php:13
msgid ""
"Expand the functionality of WordPress beyond standard posts and pages with "
"custom post types."
msgstr ""
"Özel yazı türleri ile WordPress'in işlevselliğini standart yazılar ve "
"sayfaların ötesine genişletin."

#: includes/admin/views/acf-post-type/list-empty.php:12
msgid "Add Your First Post Type"
msgstr "İlk yazı türünüzü ekleyin"

#: includes/admin/views/acf-post-type/basic-settings.php:136
#: includes/admin/views/acf-taxonomy/basic-settings.php:135
msgid "I know what I'm doing, show me all the options."
msgstr "Ne yaptığımı biliyorum, baba tüm seçenekleri göster."

#: includes/admin/views/acf-post-type/basic-settings.php:135
#: includes/admin/views/acf-taxonomy/basic-settings.php:134
msgid "Advanced Configuration"
msgstr "Gelişmiş yapılandırma"

#: includes/admin/views/acf-post-type/basic-settings.php:123
msgid "Hierarchical post types can have descendants (like pages)."
msgstr "Hiyerarşik yazı türleri, alt öğelere (sayfalar gibi) sahip olabilir."

#: includes/admin/views/acf-post-type/basic-settings.php:122
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1000
#: includes/admin/views/acf-taxonomy/basic-settings.php:121
msgid "Hierarchical"
msgstr "Hiyerarşik"

#: includes/admin/views/acf-post-type/basic-settings.php:107
msgid "Visible on the frontend and in the admin dashboard."
msgstr "Kulanıcı görünümü ve yönetim panelinde görünür."

#: includes/admin/views/acf-post-type/basic-settings.php:106
#: includes/admin/views/acf-taxonomy/basic-settings.php:106
msgid "Public"
msgstr "Herkese açık"

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:59
msgid "movie"
msgstr "film"

#: includes/admin/views/acf-post-type/basic-settings.php:57
msgid "Lower case letters, underscores and dashes only, Max 20 characters."
msgstr "Sadece küçük harfler, alt çizgi ve tireler, En fazla 20 karakter."

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:41
msgid "Movie"
msgstr "Film"

#: includes/admin/views/acf-post-type/basic-settings.php:39
#: includes/admin/views/acf-taxonomy/basic-settings.php:40
msgid "Singular Label"
msgstr "Tekil etiket"

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:24
msgid "Movies"
msgstr "Filmler"

#: includes/admin/views/acf-post-type/basic-settings.php:22
#: includes/admin/views/acf-taxonomy/basic-settings.php:23
msgid "Plural Label"
msgstr "Çoğul etiket"

#: includes/admin/views/acf-post-type/advanced-settings.php:1313
msgid ""
"Optional custom controller to use instead of `WP_REST_Posts_Controller`."
msgstr ""
"`WP_REST_Posts_Controller` yerine kullanılacak isteğe bağlı özel denetleyici."

#: includes/admin/views/acf-post-type/advanced-settings.php:1312
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1230
msgid "Controller Class"
msgstr "Denetleyici sınıfı"

#: includes/admin/views/acf-post-type/advanced-settings.php:1294
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1211
msgid "The namespace part of the REST API URL."
msgstr "REST API adresinin ad alanı bölümü."

#: includes/admin/views/acf-post-type/advanced-settings.php:1293
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1210
msgid "Namespace Route"
msgstr "Ad alanı yolu"

#: includes/admin/views/acf-post-type/advanced-settings.php:1275
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1192
msgid "The base URL for the post type REST API URLs."
msgstr "Yazı tipi REST API adresleri için temel web adresi."

#: includes/admin/views/acf-post-type/advanced-settings.php:1274
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1191
msgid "Base URL"
msgstr "Temel web adresi"

#: includes/admin/views/acf-post-type/advanced-settings.php:1260
msgid ""
"Exposes this post type in the REST API. Required to use the block editor."
msgstr ""
"Bu yazı türünü REST API'de görünür hale getirir. Blok düzenleyiciyi "
"kullanmak için gereklidir."

#: includes/admin/views/acf-post-type/advanced-settings.php:1259
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1174
msgid "Show In REST API"
msgstr "REST API'de göster"

#: includes/admin/views/acf-post-type/advanced-settings.php:1238
msgid "Customize the query variable name."
msgstr "Sorgu değişken adını özelleştir."

#: includes/admin/views/acf-post-type/advanced-settings.php:1237
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1074
msgid "Query Variable"
msgstr "Sorgu değişkeni"

#: includes/admin/views/acf-post-type/advanced-settings.php:1215
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1052
msgid "No Query Variable Support"
msgstr "Sorgu değişkeni desteği yok"

#: includes/admin/views/acf-post-type/advanced-settings.php:1214
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1051
msgid "Custom Query Variable"
msgstr "Özel sorgu değişkeni"

#: includes/admin/views/acf-post-type/advanced-settings.php:1211
msgid ""
"Items can be accessed using the non-pretty permalink, eg. {post_type}"
"={post_slug}."
msgstr ""
"Ögelere güzel olmayan kalıcı bağlantı kullanılarak erişilebilir, örn. "
"{post_type}={post_slug}."

#: includes/admin/views/acf-post-type/advanced-settings.php:1210
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1047
msgid "Query Variable Support"
msgstr "Sorgu değişken desteği"

#: includes/admin/views/acf-post-type/advanced-settings.php:1185
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1023
msgid "URLs for an item and items can be accessed with a query string."
msgstr "Bir öğe ve öğeler için adreslere bir sorgu dizesi ile erişilebilir."

#: includes/admin/views/acf-post-type/advanced-settings.php:1184
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1022
msgid "Publicly Queryable"
msgstr "Herkese açık olarak sorgulanabilir"

#: includes/admin/views/acf-post-type/advanced-settings.php:1163
msgid "Custom slug for the Archive URL."
msgstr "Arşiv adresi için özel kısa isim"

#: includes/admin/views/acf-post-type/advanced-settings.php:1162
msgid "Archive Slug"
msgstr "Arşiv kısa ismi"

#: includes/admin/views/acf-post-type/advanced-settings.php:1149
msgid ""
"Has an item archive that can be customized with an archive template file in "
"your theme."
msgstr ""
"Temanızdaki bir arşiv şablon dosyası ile özelleştirilebilen bir öğe arşivine "
"sahiptir."

#: includes/admin/views/acf-post-type/advanced-settings.php:1148
msgid "Archive"
msgstr "Arşiv"

#: includes/admin/views/acf-post-type/advanced-settings.php:1128
msgid "Pagination support for the items URLs such as the archives."
msgstr "Arşivler gibi öğe adresleri için sayfalama desteği."

#: includes/admin/views/acf-post-type/advanced-settings.php:1127
msgid "Pagination"
msgstr "Sayfalama"

#: includes/admin/views/acf-post-type/advanced-settings.php:1110
msgid "RSS feed URL for the post type items."
msgstr "Yazı türü öğeleri için RSS akış adresi."

#: includes/admin/views/acf-post-type/advanced-settings.php:1109
msgid "Feed URL"
msgstr "Akış adresi"

#: includes/admin/views/acf-post-type/advanced-settings.php:1091
#: includes/admin/views/acf-taxonomy/advanced-settings.php:981
msgid ""
"Alters the permalink structure to add the `WP_Rewrite::$front` prefix to "
"URLs."
msgstr ""
"Adreslere WP_Rewrite::$front önekini eklemek için kalıcı bağlantı yapısını "
"değiştirir."

#: includes/admin/views/acf-post-type/advanced-settings.php:1090
#: includes/admin/views/acf-taxonomy/advanced-settings.php:980
msgid "Front URL Prefix"
msgstr "Ön adres öneki"

#: includes/admin/views/acf-post-type/advanced-settings.php:1071
msgid "Customize the slug used in the URL."
msgstr "Adreste kullanılan kısa ismi özelleştirin."

#: includes/admin/views/acf-post-type/advanced-settings.php:1070
#: includes/admin/views/acf-taxonomy/advanced-settings.php:960
msgid "URL Slug"
msgstr "Adres kısa adı"

#: includes/admin/views/acf-post-type/advanced-settings.php:1054
msgid "Permalinks for this post type are disabled."
msgstr "Bu yazı türü için kalıcı bağlantılar devre dışı bırakıldı."

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-post-type/advanced-settings.php:1053
#: includes/admin/views/acf-taxonomy/advanced-settings.php:943
msgid ""
"Rewrite the URL using a custom slug defined in the input below. Your "
"permalink structure will be"
msgstr ""
"Aşağıdaki girişte tanımlanan özel kısa ismi kullanarak adresi yeniden yazın. "
"Kalıcı bağlantı yapınız şu şekilde olacaktır"

#: includes/admin/views/acf-post-type/advanced-settings.php:1045
#: includes/admin/views/acf-taxonomy/advanced-settings.php:935
msgid "No Permalink (prevent URL rewriting)"
msgstr "Kalıcı bağlantı yok (URL biçimlendirmeyi önle)"

#: includes/admin/views/acf-post-type/advanced-settings.php:1044
#: includes/admin/views/acf-taxonomy/advanced-settings.php:934
msgid "Custom Permalink"
msgstr "Özel kalıcı bağlantı"

#: includes/admin/views/acf-post-type/advanced-settings.php:1043
#: includes/admin/views/acf-post-type/advanced-settings.php:1213
#: includes/admin/views/acf-post-type/basic-settings.php:56
msgid "Post Type Key"
msgstr "Yazı türü anahtarı"

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-post-type/advanced-settings.php:1041
#: includes/admin/views/acf-post-type/advanced-settings.php:1051
msgid ""
"Rewrite the URL using the post type key as the slug. Your permalink "
"structure will be"
msgstr ""
"Yazı türü anahtarını kısa isim olarak kullanarak adresi yeniden yazın. "
"Kalıcı bağlantı yapınız şu şekilde olacaktır"

#: includes/admin/views/acf-post-type/advanced-settings.php:1039
#: includes/admin/views/acf-taxonomy/advanced-settings.php:930
msgid "Permalink Rewrite"
msgstr "Kalıcı bağlantı yeniden yazım"

#: includes/admin/views/acf-post-type/advanced-settings.php:1025
msgid "Delete items by a user when that user is deleted."
msgstr "Bir kullanıcı silindiğinde öğelerini sil."

#: includes/admin/views/acf-post-type/advanced-settings.php:1024
msgid "Delete With User"
msgstr "Kullanıcı ile sil."

#: includes/admin/views/acf-post-type/advanced-settings.php:1010
msgid "Allow the post type to be exported from 'Tools' > 'Export'."
msgstr ""
"Yazı türünün 'Araçlar' > 'Dışa Aktar' kısmından dışa aktarılmasına izin "
"verir."

#: includes/admin/views/acf-post-type/advanced-settings.php:1009
msgid "Can Export"
msgstr "Dışarı aktarılabilir"

#: includes/admin/views/acf-post-type/advanced-settings.php:978
msgid "Optionally provide a plural to be used in capabilities."
msgstr "Yetkinliklerde kullanılacak bir çoğul ad sağlayın (isteğe bağlı)."

#: includes/admin/views/acf-post-type/advanced-settings.php:977
msgid "Plural Capability Name"
msgstr "Çoğul yetkinlik adı"

#: includes/admin/views/acf-post-type/advanced-settings.php:959
msgid "Choose another post type to base the capabilities for this post type."
msgstr ""
"Bu yazı türü için yeteneklerin temel alınacağı başka bir yazı türü seçin."

#: includes/admin/views/acf-post-type/advanced-settings.php:958
msgid "Singular Capability Name"
msgstr "Tekil yetkinlik adı"

#: includes/admin/views/acf-post-type/advanced-settings.php:944
msgid ""
"By default the capabilities of the post type will inherit the 'Post' "
"capability names, eg. edit_post, delete_posts. Enable to use post type "
"specific capabilities, eg. edit_{singular}, delete_{plural}."
msgstr ""
"Varsayılan olarak, yazı türünün yetenekleri 'Yazı' yetenek isimlerini "
"devralacaktır, örneğin edit_post, delete_posts. Yazı türüne özgü yetenekleri "
"kullanmak için etkinleştirin, örneğin edit_{singular}, delete_{plural}."

#: includes/admin/views/acf-post-type/advanced-settings.php:943
msgid "Rename Capabilities"
msgstr "Yetkinlikleri yeniden adlandır"

#: includes/admin/views/acf-post-type/advanced-settings.php:928
msgid "Exclude From Search"
msgstr "Aramadan hariç tut"

#: includes/admin/views/acf-post-type/advanced-settings.php:915
#: includes/admin/views/acf-taxonomy/advanced-settings.php:874
msgid ""
"Allow items to be added to menus in the 'Appearance' > 'Menus' screen. Must "
"be turned on in 'Screen options'."
msgstr ""
"Öğelerin 'Görünüm' > 'Menüler' ekranında menülere eklenmesine izin verin. "
"'Ekran seçenekleri'nde etkinleştirilmesi gerekir."

#: includes/admin/views/acf-post-type/advanced-settings.php:914
#: includes/admin/views/acf-taxonomy/advanced-settings.php:873
msgid "Appearance Menus Support"
msgstr "Görünüm menüleri desteği"

#: includes/admin/views/acf-post-type/advanced-settings.php:896
msgid "Appears as an item in the 'New' menu in the admin bar."
msgstr "Yönetim çubuğundaki 'Yeni' menüsünde bir öğe olarak görünür."

#: includes/admin/views/acf-post-type/advanced-settings.php:895
msgid "Show In Admin Bar"
msgstr "Yönetici araç çubuğunda göster"

#: includes/admin/views/acf-post-type/advanced-settings.php:861
msgid "Custom Meta Box Callback"
msgstr "Özel meta kutusu geri çağrısı"

#: includes/admin/views/acf-post-type/advanced-settings.php:822
#: includes/fields/class-acf-field-icon_picker.php:636
msgid "Menu Icon"
msgstr "Menü Simgesi"

#: includes/admin/views/acf-post-type/advanced-settings.php:778
msgid "The position in the sidebar menu in the admin dashboard."
msgstr "Yönetim panosundaki kenar çubuğu menüsündeki konum."

#: includes/admin/views/acf-post-type/advanced-settings.php:777
msgid "Menu Position"
msgstr "Menü pozisyonu"

#: includes/admin/views/acf-post-type/advanced-settings.php:759
msgid ""
"By default the post type will get a new top level item in the admin menu. If "
"an existing top level item is supplied here, the post type will be added as "
"a submenu item under it."
msgstr ""
"Varsayılan olarak, yazı türü yönetim menüsünde yeni bir üst düzey öğe olarak "
"görünecektir. Buraya mevcut bir üst düzey öğe sağlanırsa, yazı türü onun alt "
"menü öğesi olarak eklenir."

#: includes/admin/views/acf-post-type/advanced-settings.php:758
msgid "Admin Menu Parent"
msgstr "Admin menüsü üst ögesi"

#: includes/admin/views/acf-post-type/advanced-settings.php:739
#: includes/admin/views/acf-taxonomy/advanced-settings.php:734
msgid "Admin editor navigation in the sidebar menu."
msgstr "Kenar çubuğu menüsünde yönetici editörü dolaşımı."

#: includes/admin/views/acf-post-type/advanced-settings.php:738
#: includes/admin/views/acf-taxonomy/advanced-settings.php:733
msgid "Show In Admin Menu"
msgstr "Admin menüsünde göster"

#: includes/admin/views/acf-post-type/advanced-settings.php:725
#: includes/admin/views/acf-taxonomy/advanced-settings.php:719
msgid "Items can be edited and managed in the admin dashboard."
msgstr "Öğeler yönetim panelinde düzenlenebilir ve yönetilebilir."

#: includes/admin/views/acf-post-type/advanced-settings.php:724
#: includes/admin/views/acf-taxonomy/advanced-settings.php:718
msgid "Show In UI"
msgstr "Kullanıcı arayüzünde göster"

#: includes/admin/views/acf-post-type/advanced-settings.php:694
msgid "A link to a post."
msgstr "Yazıya bir bağlantı."

#: includes/admin/views/acf-post-type/advanced-settings.php:693
msgid "Description for a navigation link block variation."
msgstr "Bir gezinme bağlantısı blok varyasyonu için açıklama."

#: includes/admin/views/acf-post-type/advanced-settings.php:692
#: includes/admin/views/acf-taxonomy/advanced-settings.php:702
msgid "Item Link Description"
msgstr "Öğe listesi açıklaması"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:688
msgid "A link to a %s."
msgstr "%s yazı türüne bir bağlantı."

#: includes/admin/views/acf-post-type/advanced-settings.php:673
msgid "Post Link"
msgstr "Yazı bağlantısı"

#: includes/admin/views/acf-post-type/advanced-settings.php:672
msgid "Title for a navigation link block variation."
msgstr "Bir gezinme bağlantısı blok varyasyonu için başlık."

#: includes/admin/views/acf-post-type/advanced-settings.php:671
#: includes/admin/views/acf-taxonomy/advanced-settings.php:681
msgid "Item Link"
msgstr "Öğe bağlantısı"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:668
#: includes/admin/views/acf-taxonomy/advanced-settings.php:678
msgid "%s Link"
msgstr "%s bağlantısı"

#: includes/admin/views/acf-post-type/advanced-settings.php:653
msgid "Post updated."
msgstr "Yazı güncellendi."

#: includes/admin/views/acf-post-type/advanced-settings.php:652
msgid "In the editor notice after an item is updated."
msgstr "Bir öğe güncellendikten sonra düzenleyici bildiriminde."

#: includes/admin/views/acf-post-type/advanced-settings.php:651
msgid "Item Updated"
msgstr "Öğe güncellendi"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:648
msgid "%s updated."
msgstr "%s güncellendi."

#: includes/admin/views/acf-post-type/advanced-settings.php:633
msgid "Post scheduled."
msgstr "Yazı zamanlandı."

#: includes/admin/views/acf-post-type/advanced-settings.php:632
msgid "In the editor notice after scheduling an item."
msgstr "Bir öğe zamanlandıktan sonra düzenleyici bildiriminde."

#: includes/admin/views/acf-post-type/advanced-settings.php:631
msgid "Item Scheduled"
msgstr "Öğe zamanlandı"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:628
msgid "%s scheduled."
msgstr "%s zamanlandı."

#: includes/admin/views/acf-post-type/advanced-settings.php:613
msgid "Post reverted to draft."
msgstr "Yazı taslağa geri dönüştürüldü."

#: includes/admin/views/acf-post-type/advanced-settings.php:612
msgid "In the editor notice after reverting an item to draft."
msgstr "Bir öğe taslağa döndürüldükten sonra düzenleyici bildiriminde."

#: includes/admin/views/acf-post-type/advanced-settings.php:611
msgid "Item Reverted To Draft"
msgstr "Öğe taslağa döndürüldü."

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:608
msgid "%s reverted to draft."
msgstr "%s taslağa çevrildi."

#: includes/admin/views/acf-post-type/advanced-settings.php:593
msgid "Post published privately."
msgstr "Yazı özel olarak yayımlandı."

#: includes/admin/views/acf-post-type/advanced-settings.php:592
msgid "In the editor notice after publishing a private item."
msgstr "Özel bir öğe yayımlandıktan sonra düzenleyici bildiriminde."

#: includes/admin/views/acf-post-type/advanced-settings.php:591
msgid "Item Published Privately"
msgstr "Öğe özel olarak yayımlandı."

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:588
msgid "%s published privately."
msgstr "%s özel olarak yayımlandı."

#: includes/admin/views/acf-post-type/advanced-settings.php:573
msgid "Post published."
msgstr "Yazı yayınlandı."

#: includes/admin/views/acf-post-type/advanced-settings.php:572
msgid "In the editor notice after publishing an item."
msgstr "Bir öğe yayımlandıktan sonra düzenleyici bildiriminde."

#: includes/admin/views/acf-post-type/advanced-settings.php:571
msgid "Item Published"
msgstr "Öğe yayımlandı."

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:568
msgid "%s published."
msgstr "%s yayımlandı."

#: includes/admin/views/acf-post-type/advanced-settings.php:553
msgid "Posts list"
msgstr "Yazı listesi"

#: includes/admin/views/acf-post-type/advanced-settings.php:552
msgid "Used by screen readers for the items list on the post type list screen."
msgstr ""
"Yazı türü liste ekranındaki öğeler listesi için ekran okuyucular tarafından "
"kullanılır."

#: includes/admin/views/acf-post-type/advanced-settings.php:551
#: includes/admin/views/acf-taxonomy/advanced-settings.php:640
msgid "Items List"
msgstr "Öğe listesi"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:548
#: includes/admin/views/acf-taxonomy/advanced-settings.php:637
msgid "%s list"
msgstr "%s listesi"

#: includes/admin/views/acf-post-type/advanced-settings.php:533
msgid "Posts list navigation"
msgstr "Yazı listesi dolaşımı"

#: includes/admin/views/acf-post-type/advanced-settings.php:532
msgid ""
"Used by screen readers for the filter list pagination on the post type list "
"screen."
msgstr ""
"Yazı türü liste ekranındaki süzme listesi sayfalandırması için ekran "
"okuyucular tarafından kullanılır."

#: includes/admin/views/acf-post-type/advanced-settings.php:531
#: includes/admin/views/acf-taxonomy/advanced-settings.php:620
msgid "Items List Navigation"
msgstr "Öğe listesi gezinmesi"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:528
#: includes/admin/views/acf-taxonomy/advanced-settings.php:617
msgid "%s list navigation"
msgstr "%s listesi gezinmesi"

#: includes/admin/views/acf-post-type/advanced-settings.php:512
msgid "Filter posts by date"
msgstr "Yazıları tarihe göre süz"

#: includes/admin/views/acf-post-type/advanced-settings.php:511
msgid ""
"Used by screen readers for the filter by date heading on the post type list "
"screen."
msgstr ""
"Yazı türü liste ekranındaki tarihe göre süzme başlığı için ekran okuyucular "
"tarafından kullanılır."

#: includes/admin/views/acf-post-type/advanced-settings.php:510
msgid "Filter Items By Date"
msgstr "Öğeleri tarihe göre süz"

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:506
msgid "Filter %s by date"
msgstr "%s öğelerini tarihe göre süz"

#: includes/admin/views/acf-post-type/advanced-settings.php:491
msgid "Filter posts list"
msgstr "Yazı listesini filtrele"

#: includes/admin/views/acf-post-type/advanced-settings.php:490
msgid ""
"Used by screen readers for the filter links heading on the post type list "
"screen."
msgstr ""
"Yazı türü liste ekranındaki süzme bağlantıları başlığı için ekran okuyucular "
"tarafından kullanılır."

#: includes/admin/views/acf-post-type/advanced-settings.php:489
msgid "Filter Items List"
msgstr "Öğe listesini süz"

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:485
msgid "Filter %s list"
msgstr "%s listesini süz"

#: includes/admin/views/acf-post-type/advanced-settings.php:469
msgid "In the media modal showing all media uploaded to this item."
msgstr ""
"Bu öğeye yüklenen tüm ortam dosyalraını gösteren ortam açılır ekranında."

#: includes/admin/views/acf-post-type/advanced-settings.php:468
msgid "Uploaded To This Item"
msgstr "Bu öğeye yüklendi"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:464
msgid "Uploaded to this %s"
msgstr "Bu %s öğesine yüklendi"

#: includes/admin/views/acf-post-type/advanced-settings.php:449
msgid "Insert into post"
msgstr "Yazıya ekle"

#: includes/admin/views/acf-post-type/advanced-settings.php:448
msgid "As the button label when adding media to content."
msgstr "İçeriğie ortam eklerken düğme etiketi olarak."

#: includes/admin/views/acf-post-type/advanced-settings.php:447
msgid "Insert Into Media Button"
msgstr "Ortam düğmesine ekle"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:443
msgid "Insert into %s"
msgstr "%s içine ekle"

#: includes/admin/views/acf-post-type/advanced-settings.php:428
msgid "Use as featured image"
msgstr "Öne çıkan görsel olarak kullan"

#: includes/admin/views/acf-post-type/advanced-settings.php:427
msgid ""
"As the button label for selecting to use an image as the featured image."
msgstr "Bir görseli öne çıkan görsel olarak seçme düğmesi etiketi olarak."

#: includes/admin/views/acf-post-type/advanced-settings.php:426
msgid "Use Featured Image"
msgstr "Öne çıkan görseli kullan"

#: includes/admin/views/acf-post-type/advanced-settings.php:413
msgid "Remove featured image"
msgstr "Öne çıkan görseli kaldır"

#: includes/admin/views/acf-post-type/advanced-settings.php:412
msgid "As the button label when removing the featured image."
msgstr "Öne çıkan görseli kaldırma düğmesi etiketi olarak."

#: includes/admin/views/acf-post-type/advanced-settings.php:411
msgid "Remove Featured Image"
msgstr "Öne çıkarılan görseli kaldır"

#: includes/admin/views/acf-post-type/advanced-settings.php:398
msgid "Set featured image"
msgstr "Öne çıkan görsel seç"

#: includes/admin/views/acf-post-type/advanced-settings.php:397
msgid "As the button label when setting the featured image."
msgstr "Öne çıkan görseli ayarlarken düğme etiketi olarak."

#: includes/admin/views/acf-post-type/advanced-settings.php:396
msgid "Set Featured Image"
msgstr "Öne çıkan görsel belirle"

#: includes/admin/views/acf-post-type/advanced-settings.php:383
msgid "Featured image"
msgstr "Öne çıkan görsel"

#: includes/admin/views/acf-post-type/advanced-settings.php:382
msgid "In the editor used for the title of the featured image meta box."
msgstr "Öne çıkan görsel meta kutusunun başlığı için düzenleyicide kullanılır."

#: includes/admin/views/acf-post-type/advanced-settings.php:381
msgid "Featured Image Meta Box"
msgstr "Öne çıkan görsel meta kutusu"

#: includes/admin/views/acf-post-type/advanced-settings.php:368
msgid "Post Attributes"
msgstr "Yazı özellikleri"

#: includes/admin/views/acf-post-type/advanced-settings.php:367
msgid "In the editor used for the title of the post attributes meta box."
msgstr ""
"Yazı öznitelikleri meta kutusunun başlığı için kullanılan düzenleyicide."

#: includes/admin/views/acf-post-type/advanced-settings.php:366
msgid "Attributes Meta Box"
msgstr "Öznitelikler meta kutusu"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:363
msgid "%s Attributes"
msgstr "%s öznitelikleri"

#: includes/admin/views/acf-post-type/advanced-settings.php:348
msgid "Post Archives"
msgstr "Yazı arşivleri"

#: includes/admin/views/acf-post-type/advanced-settings.php:347
msgid ""
"Adds 'Post Type Archive' items with this label to the list of posts shown "
"when adding items to an existing menu in a CPT with archives enabled. Only "
"appears when editing menus in 'Live Preview' mode and a custom archive slug "
"has been provided."
msgstr ""
"Arşivlerin etkin olduğu bir ÖYT'nde mevcut bir menüye öge eklerken "
"gösterilen yazılar listesine bu etikete sahip 'Yazı tipi arşivi' ögelerini "
"ekler. Yalnızca 'Canlı önizleme' modunda menüler düzenlenirken ve özel bir "
"arşiv kısa adı sağlandığında görünür."

#: includes/admin/views/acf-post-type/advanced-settings.php:346
msgid "Archives Nav Menu"
msgstr "Arşivler dolaşım menüsü"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:343
msgid "%s Archives"
msgstr "%s arşivleri"

#: includes/admin/views/acf-post-type/advanced-settings.php:328
msgid "No posts found in Trash"
msgstr "Çöp kutusunda yazı bulunamadı."

#: includes/admin/views/acf-post-type/advanced-settings.php:327
msgid ""
"At the top of the post type list screen when there are no posts in the trash."
msgstr "Çöp kutusunda yazı olmadığında yazı türü liste ekranının üstünde."

#: includes/admin/views/acf-post-type/advanced-settings.php:326
msgid "No Items Found in Trash"
msgstr "Çöp kutusunda öğe bulunamadı."

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:322
msgid "No %s found in Trash"
msgstr "Çöpte %s bulunamadı"

#: includes/admin/views/acf-post-type/advanced-settings.php:307
msgid "No posts found"
msgstr "Yazı bulunamadı"

#: includes/admin/views/acf-post-type/advanced-settings.php:306
msgid ""
"At the top of the post type list screen when there are no posts to display."
msgstr "Gösterilecek yazı olmadığında yazı türü liste ekranının üstünde."

#: includes/admin/views/acf-post-type/advanced-settings.php:305
msgid "No Items Found"
msgstr "Öğe bulunamadı"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:301
#: includes/admin/views/acf-taxonomy/advanced-settings.php:480
msgid "No %s found"
msgstr "%s bulunamadı"

#: includes/admin/views/acf-post-type/advanced-settings.php:286
msgid "Search Posts"
msgstr "Yazılarda ara"

#: includes/admin/views/acf-post-type/advanced-settings.php:285
msgid "At the top of the items screen when searching for an item."
msgstr "Bir öğe ararken öğeler ekranının üstünde."

#: includes/admin/views/acf-post-type/advanced-settings.php:284
#: includes/admin/views/acf-taxonomy/advanced-settings.php:345
msgid "Search Items"
msgstr "Öğeleri ara"

#. translators: %s Singular form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:281
#: includes/admin/views/acf-taxonomy/advanced-settings.php:342
msgid "Search %s"
msgstr "Ara %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:266
msgid "Parent Page:"
msgstr "Ebeveyn sayfa:"

#: includes/admin/views/acf-post-type/advanced-settings.php:265
msgid "For hierarchical types in the post type list screen."
msgstr "Hiyerarşik türler için yazı türü liste ekranında."

#: includes/admin/views/acf-post-type/advanced-settings.php:264
msgid "Parent Item Prefix"
msgstr "Üst öğe ön eki"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:261
#: includes/admin/views/acf-taxonomy/advanced-settings.php:318
msgid "Parent %s:"
msgstr "Ebeveyn %s:"

#: includes/admin/views/acf-post-type/advanced-settings.php:246
msgid "New Post"
msgstr "Yeni yazı"

#: includes/admin/views/acf-post-type/advanced-settings.php:244
msgid "New Item"
msgstr "Yeni öğe"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:241
msgid "New %s"
msgstr "Yeni %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:206
#: includes/admin/views/acf-post-type/advanced-settings.php:226
msgid "Add New Post"
msgstr "Yeni yazı ekle"

#: includes/admin/views/acf-post-type/advanced-settings.php:205
msgid "At the top of the editor screen when adding a new item."
msgstr "Yeni bir öğe eklerken düzenleyici ekranının üstünde."

#: includes/admin/views/acf-post-type/advanced-settings.php:204
#: includes/admin/views/acf-taxonomy/advanced-settings.php:256
msgid "Add New Item"
msgstr "Yeni öğe ekle"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:201
#: includes/admin/views/acf-post-type/advanced-settings.php:221
#: includes/admin/views/acf-taxonomy/advanced-settings.php:253
msgid "Add New %s"
msgstr "Yeni %s ekle"

#: includes/admin/views/acf-post-type/advanced-settings.php:186
msgid "View Posts"
msgstr "Yazıları görüntüle"

#: includes/admin/views/acf-post-type/advanced-settings.php:185
msgid ""
"Appears in the admin bar in the 'All Posts' view, provided the post type "
"supports archives and the home page is not an archive of that post type."
msgstr ""
"Yazı türü arşivleri destekliyorsa ve ana sayfa o yazı türünün bir arşivi "
"değilse yönetim çubuğunda 'Tüm Yazılar' görünümünde görünür."

#: includes/admin/views/acf-post-type/advanced-settings.php:184
msgid "View Items"
msgstr "Öğeleri göster"

#: includes/admin/views/acf-post-type/advanced-settings.php:166
msgid "View Post"
msgstr "Yazıyı görüntüle"

#: includes/admin/views/acf-post-type/advanced-settings.php:165
msgid "In the admin bar to view item when editing it."
msgstr "Öğeyi düzenlerken görüntülemek için yönetim çubuğunda."

#: includes/admin/views/acf-post-type/advanced-settings.php:164
#: includes/admin/views/acf-taxonomy/advanced-settings.php:216
msgid "View Item"
msgstr "Öğeyi göster"

#. translators: %s Singular form of post type name
#. translators: %s Plural form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:161
#: includes/admin/views/acf-post-type/advanced-settings.php:181
#: includes/admin/views/acf-taxonomy/advanced-settings.php:213
msgid "View %s"
msgstr "%s görüntüle"

#: includes/admin/views/acf-post-type/advanced-settings.php:146
msgid "Edit Post"
msgstr "Yazıyı düzenle"

#: includes/admin/views/acf-post-type/advanced-settings.php:145
msgid "At the top of the editor screen when editing an item."
msgstr "Bir öğeyi düzenlerken düzenleyici ekranının üstünde."

#: includes/admin/views/acf-post-type/advanced-settings.php:144
#: includes/admin/views/acf-taxonomy/advanced-settings.php:196
msgid "Edit Item"
msgstr "Öğeyi düzenle"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:141
#: includes/admin/views/acf-taxonomy/advanced-settings.php:193
msgid "Edit %s"
msgstr "%s düzenle"

#: includes/admin/views/acf-post-type/advanced-settings.php:126
msgid "All Posts"
msgstr "Tüm yazılar"

#: includes/admin/views/acf-post-type/advanced-settings.php:125
#: includes/admin/views/acf-post-type/advanced-settings.php:225
#: includes/admin/views/acf-post-type/advanced-settings.php:245
msgid "In the post type submenu in the admin dashboard."
msgstr "Başlangıç ekranında yazı türü alt menüsünde."

#: includes/admin/views/acf-post-type/advanced-settings.php:124
#: includes/admin/views/acf-taxonomy/advanced-settings.php:176
msgid "All Items"
msgstr "Tüm öğeler"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:121
#: includes/admin/views/acf-taxonomy/advanced-settings.php:173
msgid "All %s"
msgstr "Tüm %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:105
msgid "Admin menu name for the post type."
msgstr "Yazı türü için yönetici menüsü adı."

#: includes/admin/views/acf-post-type/advanced-settings.php:104
msgid "Menu Name"
msgstr "Menü ismi"

#: includes/admin/views/acf-post-type/advanced-settings.php:90
#: includes/admin/views/acf-taxonomy/advanced-settings.php:142
msgid "Regenerate all labels using the Singular and Plural labels"
msgstr "Tekil ve Çoğul etiketleri kullanarak tüm etiketleri yeniden oluşturun."

#: includes/admin/views/acf-post-type/advanced-settings.php:88
#: includes/admin/views/acf-taxonomy/advanced-settings.php:140
msgid "Regenerate"
msgstr "Yeniden oluştur"

#: includes/admin/views/acf-post-type/advanced-settings.php:79
msgid "Active post types are enabled and registered with WordPress."
msgstr "Aktif yazı türleri etkinleştirilmiş ve WordPress'e kaydedilmiştir."

#: includes/admin/views/acf-post-type/advanced-settings.php:63
msgid "A descriptive summary of the post type."
msgstr "Yazı türünün açıklayıcı bir özeti."

#: includes/admin/views/acf-post-type/advanced-settings.php:48
msgid "Add Custom"
msgstr "Özel ekle"

#: includes/admin/views/acf-post-type/advanced-settings.php:42
msgid "Enable various features in the content editor."
msgstr "İçerik düzenleyicide çeşitli özellikleri etkinleştirin."

#: includes/admin/views/acf-post-type/advanced-settings.php:31
msgid "Post Formats"
msgstr "Yazı biçimleri"

#: includes/admin/views/acf-post-type/advanced-settings.php:25
msgid "Editor"
msgstr "Editör"

#: includes/admin/views/acf-post-type/advanced-settings.php:24
msgid "Trackbacks"
msgstr "Geri izlemeler"

#: includes/admin/views/acf-post-type/basic-settings.php:87
msgid "Select existing taxonomies to classify items of the post type."
msgstr ""
"Yazı türü öğelerini sınıflandırmak için mevcut sınıflandırmalardan seçin."

#: includes/admin/views/acf-field-group/field.php:158
msgid "Browse Fields"
msgstr "Alanlara Göz At"

#: includes/admin/tools/class-acf-admin-tool-import.php:287
msgid "Nothing to import"
msgstr "İçeri aktarılacak bir şey yok"

#: includes/admin/tools/class-acf-admin-tool-import.php:282
msgid ". The Custom Post Type UI plugin can be deactivated."
msgstr ". Custom Post Type UI eklentisi etkisizleştirilebilir."

#. translators: %d - number of items imported from CPTUI
#: includes/admin/tools/class-acf-admin-tool-import.php:273
msgid "Imported %d item from Custom Post Type UI -"
msgid_plural "Imported %d items from Custom Post Type UI -"
msgstr[0] "Custom Post Type UI'dan %d öğe içe aktarıldı -"
msgstr[1] "Custom Post Type UI'dan %d öğe içe aktarıldı -"

#: includes/admin/tools/class-acf-admin-tool-import.php:257
msgid "Failed to import taxonomies."
msgstr "Sınıflandırmalar içeri aktarılamadı."

#: includes/admin/tools/class-acf-admin-tool-import.php:239
msgid "Failed to import post types."
msgstr "Yazı türleri içeri aktarılamadı."

#: includes/admin/tools/class-acf-admin-tool-import.php:228
msgid "Nothing from Custom Post Type UI plugin selected for import."
msgstr ""
"Custom Post Type UI eklentisinden içe aktarmak için hiçbir şey seçilmedi."

#: includes/admin/tools/class-acf-admin-tool-import.php:204
msgid "Imported 1 item"
msgid_plural "Imported %s items"
msgstr[0] "Bir öğe içeri aktarıldı"
msgstr[1] "%s öğe içeri aktarıldı"

#: includes/admin/tools/class-acf-admin-tool-import.php:119
msgid ""
"Importing a Post Type or Taxonomy with the same key as one that already "
"exists will overwrite the settings for the existing Post Type or Taxonomy "
"with those of the import."
msgstr ""
"Aynı anahtara sahip bir yazı türü veya sınıflandırmayı içe aktarmak, içe "
"aktarılanın ayarlarının mevcut yazı türü veya sınıflandırma ayarlarının "
"üzerine yazılmasına neden olacaktır."

#: includes/admin/tools/class-acf-admin-tool-import.php:108
#: includes/admin/tools/class-acf-admin-tool-import.php:124
msgid "Import from Custom Post Type UI"
msgstr "Custom Post Type UI'dan İçe Aktar"

#: includes/admin/tools/class-acf-admin-tool-export.php:354
msgid ""
"The following code can be used to register a local version of the selected "
"items. Storing field groups, post types, or taxonomies locally can provide "
"many benefits such as faster load times, version control & dynamic fields/"
"settings. Simply copy and paste the following code to your theme's "
"functions.php file or include it within an external file, then deactivate or "
"delete the items from the ACF admin."
msgstr ""
"Aşağıdaki kod, seçilen öğelerin yerel bir sürümünü kaydetmek için "
"kullanılabilir. Alan gruplarını, yazı türlerini veya sınıflandırmaları yerel "
"olarak depolamak, daha hızlı yükleme süreleri, sürüm kontrolü ve dinamik "
"alanlar/ayarlar gibi birçok avantaj sağlayabilir. Aşağıdaki kodu temanızın "
"functions.php dosyasına kopyalayıp yapıştırmanız veya harici bir dosya "
"içinde dahil etmeniz yeterlidir, ardından ACF yönetim panelinden öğeleri "
"devre dışı bırakın veya silin."

#: includes/admin/tools/class-acf-admin-tool-export.php:353
msgid "Export - Generate PHP"
msgstr "Dışa Aktar - PHP Oluştur"

#: includes/admin/tools/class-acf-admin-tool-export.php:330
msgid "Export"
msgstr "Dışa aktar"

#: includes/admin/tools/class-acf-admin-tool-export.php:264
msgid "Select Taxonomies"
msgstr "Sınıflandırmaları seç"

#: includes/admin/tools/class-acf-admin-tool-export.php:239
msgid "Select Post Types"
msgstr "Yazı türlerini seç"

#: includes/admin/tools/class-acf-admin-tool-export.php:155
msgid "Exported 1 item."
msgid_plural "Exported %s items."
msgstr[0] "Bir öğe dışa aktarıldı."
msgstr[1] "%s öğe dışa aktarıldı."

#: includes/admin/post-types/admin-taxonomy.php:127
msgid "Category"
msgstr "Kategori"

#: includes/admin/post-types/admin-taxonomy.php:125
msgid "Tag"
msgstr "Etiket"

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:82
msgid "%s taxonomy created"
msgstr "%s taksonomisi oluşturuldu"

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:76
msgid "%s taxonomy updated"
msgstr "%s taksonomisi güncellendi"

#: includes/admin/post-types/admin-taxonomy.php:56
msgid "Taxonomy draft updated."
msgstr "Taksonomi taslağı güncellendi."

#: includes/admin/post-types/admin-taxonomy.php:55
msgid "Taxonomy scheduled for."
msgstr "Taksonomi planlandı."

#: includes/admin/post-types/admin-taxonomy.php:54
msgid "Taxonomy submitted."
msgstr "Sınıflandırma gönderildi."

#: includes/admin/post-types/admin-taxonomy.php:53
msgid "Taxonomy saved."
msgstr "Sınıflandırma kaydedildi."

#: includes/admin/post-types/admin-taxonomy.php:49
msgid "Taxonomy deleted."
msgstr "Sınıflandırma silindi."

#: includes/admin/post-types/admin-taxonomy.php:48
msgid "Taxonomy updated."
msgstr "Sınıflandırma güncellendi."

#: includes/admin/post-types/admin-taxonomies.php:351
#: includes/admin/post-types/admin-taxonomy.php:153
msgid ""
"This taxonomy could not be registered because its key is in use by another "
"taxonomy registered by another plugin or theme."
msgstr ""
"Bu sınıflandırma kaydedilemedi çünkü anahtarı başka bir eklenti veya tema "
"tarafından kaydedilen başka bir sınıflandırma tarafından kullanılıyor."

#. translators: %s number of taxonomies synchronized
#: includes/admin/post-types/admin-taxonomies.php:333
msgid "Taxonomy synchronized."
msgid_plural "%s taxonomies synchronized."
msgstr[0] "Taksonomi senkronize edildi."
msgstr[1] "%s taksonomi senkronize edildi."

#. translators: %s number of taxonomies duplicated
#: includes/admin/post-types/admin-taxonomies.php:326
msgid "Taxonomy duplicated."
msgid_plural "%s taxonomies duplicated."
msgstr[0] "Taksonomi çoğaltıldı."
msgstr[1] "%s taksonomi çoğaltıldı."

#. translators: %s number of taxonomies deactivated
#: includes/admin/post-types/admin-taxonomies.php:319
msgid "Taxonomy deactivated."
msgid_plural "%s taxonomies deactivated."
msgstr[0] "Sınıflandırma devre dışı bırakıldı."
msgstr[1] "%s sınıflandırma devre dışı bırakıldı."

#. translators: %s number of taxonomies activated
#: includes/admin/post-types/admin-taxonomies.php:312
msgid "Taxonomy activated."
msgid_plural "%s taxonomies activated."
msgstr[0] "Taksonomi etkinleştirildi."
msgstr[1] "%s aksonomi etkinleştirildi."

#: includes/admin/post-types/admin-taxonomies.php:113
msgid "Terms"
msgstr "Terimler"

#. translators: %s number of post types synchronized
#: includes/admin/post-types/admin-post-types.php:327
msgid "Post type synchronized."
msgid_plural "%s post types synchronized."
msgstr[0] "Yazı türü senkronize edildi."
msgstr[1] "%s yazı türü senkronize edildi."

#. translators: %s number of post types duplicated
#: includes/admin/post-types/admin-post-types.php:320
msgid "Post type duplicated."
msgid_plural "%s post types duplicated."
msgstr[0] "Yazı türü çoğaltıldı."
msgstr[1] "%s yazı türü çoğaltıldı."

#. translators: %s number of post types deactivated
#: includes/admin/post-types/admin-post-types.php:313
msgid "Post type deactivated."
msgid_plural "%s post types deactivated."
msgstr[0] "Yazı türü etkisizleştirildi."
msgstr[1] "%s yazı türü etkisizleştirildi."

#. translators: %s number of post types activated
#: includes/admin/post-types/admin-post-types.php:306
msgid "Post type activated."
msgid_plural "%s post types activated."
msgstr[0] "Yazı türü etkinleştirildi."
msgstr[1] "%s yazı türü etkinleştirildi."

#: includes/admin/post-types/admin-post-types.php:87
#: includes/admin/post-types/admin-taxonomies.php:111
#: includes/admin/tools/class-acf-admin-tool-import.php:79
#: includes/admin/views/acf-taxonomy/basic-settings.php:82
#: includes/post-types/class-acf-post-type.php:91
msgid "Post Types"
msgstr "Yazı tipleri"

#: includes/admin/post-types/admin-post-type.php:158
#: includes/admin/post-types/admin-taxonomy.php:160
msgid "Advanced Settings"
msgstr "Gelişmiş ayarlar"

#: includes/admin/post-types/admin-post-type.php:157
#: includes/admin/post-types/admin-taxonomy.php:159
msgid "Basic Settings"
msgstr "Temel ayarlar"

#: includes/admin/post-types/admin-post-type.php:151
#: includes/admin/post-types/admin-post-types.php:345
msgid ""
"This post type could not be registered because its key is in use by another "
"post type registered by another plugin or theme."
msgstr ""
"Bu yazı türü kaydedilemedi çünkü anahtarı başka bir eklenti veya tema "
"tarafından kaydedilen başka bir yazı türü tarafından kullanılıyor."

#: includes/admin/post-types/admin-post-type.php:126
msgid "Pages"
msgstr "Sayfalar"

#: includes/admin/admin-internal-post-type.php:347
msgid "Link Existing Field Groups"
msgstr "Mevcut alan gruplarını bağlayın"

#. translators: %s post type name
#: includes/admin/post-types/admin-post-type.php:80
msgid "%s post type created"
msgstr "%s yazı tipi oluşturuldu"

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:78
msgid "Add fields to %s"
msgstr "%s taksonomisine alan ekle"

#. translators: %s post type name
#: includes/admin/post-types/admin-post-type.php:76
msgid "%s post type updated"
msgstr "%s yazı tipi güncellendi"

#: includes/admin/post-types/admin-post-type.php:56
msgid "Post type draft updated."
msgstr "Yazı türü taslağı güncellendi."

#: includes/admin/post-types/admin-post-type.php:55
msgid "Post type scheduled for."
msgstr "Yazı türü zamanlandı."

#: includes/admin/post-types/admin-post-type.php:54
msgid "Post type submitted."
msgstr "Yazı türü gönderildi."

#: includes/admin/post-types/admin-post-type.php:53
msgid "Post type saved."
msgstr "Yazı türü kaydedildi"

#: includes/admin/post-types/admin-post-type.php:50
msgid "Post type updated."
msgstr "Yazı türü güncellendi."

#: includes/admin/post-types/admin-post-type.php:49
msgid "Post type deleted."
msgstr "Yazı türü silindi"

#: includes/admin/post-types/admin-field-group.php:146
msgid "Type to search..."
msgstr "Aramak için yazın..."

#: includes/admin/post-types/admin-field-group.php:101
msgid "PRO Only"
msgstr "Sadece PRO'da"

#: includes/admin/post-types/admin-field-group.php:93
msgid "Field groups linked successfully."
msgstr "Alan grubu başarıyla bağlandı."

#. translators: %s - URL to ACF tools page.
#: includes/admin/admin.php:199
msgid ""
"Import Post Types and Taxonomies registered with Custom Post Type UI and "
"manage them with ACF. <a href=\"%s\">Get Started</a>."
msgstr ""
"Custom Post Type UI ile kaydedilen yazı türleri ve sınıflandırmaları içeri "
"aktarın ve ACF ile yönetin. <a href=\"%s\">Başlayın</a>."

#: includes/admin/admin.php:46 includes/admin/admin.php:361
#: src/Site_Health/Site_Health.php:254
msgid "ACF"
msgstr "ACF"

#: includes/admin/admin-internal-post-type.php:314
msgid "taxonomy"
msgstr "taksonomi"

#: includes/admin/admin-internal-post-type.php:314
msgid "post type"
msgstr "yazı tipi"

#: includes/admin/admin-internal-post-type.php:338
msgid "Done"
msgstr "Tamamlandı"

#: includes/admin/admin-internal-post-type.php:324
msgid "Field Group(s)"
msgstr "Alan grup(lar)ı"

#: includes/admin/admin-internal-post-type.php:323
msgid "Select one or many field groups..."
msgstr "Bir veya daha fazla alan grubu seçin..."

#: includes/admin/admin-internal-post-type.php:322
msgid "Please select the field groups to link."
msgstr "Lütfen bağlanacak alan gruplarını seçin."

#: includes/admin/admin-internal-post-type.php:280
msgid "Field group linked successfully."
msgid_plural "Field groups linked successfully."
msgstr[0] "Alan grubu başarıyla bağlandı."
msgstr[1] "Alan grubu başarıyla bağlandı."

#: includes/admin/admin-internal-post-type-list.php:277
#: includes/admin/post-types/admin-post-types.php:346
#: includes/admin/post-types/admin-taxonomies.php:352
msgctxt "post status"
msgid "Registration Failed"
msgstr "Kayıt başarısız"

#: includes/admin/admin-internal-post-type-list.php:276
msgid ""
"This item could not be registered because its key is in use by another item "
"registered by another plugin or theme."
msgstr ""
"Bu öğe kaydedilemedi çünkü anahtarı başka bir eklenti veya tema tarafından "
"kaydedilen başka bir öğe tarafından kullanılıyor."

#: includes/acf-internal-post-type-functions.php:509
#: includes/acf-internal-post-type-functions.php:538
msgid "REST API"
msgstr "REST API"

#: includes/acf-internal-post-type-functions.php:508
#: includes/acf-internal-post-type-functions.php:537
#: includes/acf-internal-post-type-functions.php:564
msgid "Permissions"
msgstr "İzinler"

#: includes/acf-internal-post-type-functions.php:507
#: includes/acf-internal-post-type-functions.php:536
msgid "URLs"
msgstr "URL'ler"

#: includes/acf-internal-post-type-functions.php:506
#: includes/acf-internal-post-type-functions.php:535
#: includes/acf-internal-post-type-functions.php:562
msgid "Visibility"
msgstr "Görünürlük"

#: includes/acf-internal-post-type-functions.php:505
#: includes/acf-internal-post-type-functions.php:534
#: includes/acf-internal-post-type-functions.php:563
msgid "Labels"
msgstr "Etiketler"

#: includes/admin/post-types/admin-field-group.php:279
msgid "Field Settings Tabs"
msgstr "Alan ayarı sekmeleri"

#. Author URI of the plugin
#: acf.php
msgid ""
"https://wpengine.com/?"
"utm_source=wordpress.org&utm_medium=referral&utm_campaign=plugin_directory&utm_content=advanced_custom_fields"
msgstr ""
"https://wpengine.com/?"
"utm_source=wordpress.org&utm_medium=referral&utm_campaign=plugin_directory&utm_content=advanced_custom_fields"

#: includes/api/api-template.php:1027
msgid "[ACF shortcode value disabled for preview]"
msgstr "[ACF kısa kod değeri ön izleme için devre dışı bırakıldı]"

#: includes/admin/admin-internal-post-type.php:290
#: includes/admin/post-types/admin-field-group.php:572
msgid "Close Modal"
msgstr "Pencereyi kapat"

#: includes/admin/post-types/admin-field-group.php:92
msgid "Field moved to other group"
msgstr "Alan başka bir gruba taşındı."

#: includes/admin/post-types/admin-field-group.php:91
msgid "Close modal"
msgstr "Modalı kapat"

#: includes/fields/class-acf-field-tab.php:122
msgid "Start a new group of tabs at this tab."
msgstr "Bu sekmede yeni bir sekme grubu başlatın."

#: includes/fields/class-acf-field-tab.php:121
msgid "New Tab Group"
msgstr "Yeni sekme grubu"

#: includes/fields/class-acf-field-select.php:421
#: includes/fields/class-acf-field-true_false.php:188
msgid "Use a stylized checkbox using select2"
msgstr "Select2 kullanarak biçimlendirilmiş bir seçim kutusu kullan"

#: includes/fields/class-acf-field-radio.php:250
msgid "Save Other Choice"
msgstr "Diğer seçeneğini kaydet"

#: includes/fields/class-acf-field-radio.php:239
msgid "Allow Other Choice"
msgstr "Diğer seçeneğine izin ver"

#: includes/fields/class-acf-field-checkbox.php:420
msgid "Add Toggle All"
msgstr "Tümünü aç/kapat ekle"

#: includes/fields/class-acf-field-checkbox.php:379
msgid "Save Custom Values"
msgstr "Özel değerleri kaydet"

#: includes/fields/class-acf-field-checkbox.php:368
msgid "Allow Custom Values"
msgstr "Özel değerlere izin ver"

#: includes/fields/class-acf-field-checkbox.php:134
msgid "Checkbox custom values cannot be empty. Uncheck any empty values."
msgstr ""
"Onay kutusu özel değerleri boş olamaz. Boş değerlerin seçimini kaldırın."

#: includes/admin/views/global/navigation.php:256
msgid "Updates"
msgstr "Güncellemeler"

#: includes/admin/views/global/navigation.php:180
#: includes/admin/views/global/navigation.php:184
msgid "Advanced Custom Fields logo"
msgstr "Advanced Custom Fields logosu"

#: includes/admin/views/global/form-top.php:92
msgid "Save Changes"
msgstr "Değişiklikleri kaydet"

#: includes/admin/views/global/form-top.php:79
msgid "Field Group Title"
msgstr "Alan grubu başlığı"

#: includes/admin/views/acf-post-type/advanced-settings.php:709
#: includes/admin/views/global/form-top.php:3
msgid "Add title"
msgstr "Başlık ekle"

#. translators: %s url to getting started guide
#: includes/admin/views/acf-field-group/list-empty.php:30
#: includes/admin/views/acf-post-type/list-empty.php:20
#: includes/admin/views/acf-taxonomy/list-empty.php:21
#: includes/admin/views/options-page-preview.php:13
msgid ""
"New to ACF? Take a look at our <a href=\"%s\" target=\"_blank\">getting "
"started guide</a>."
msgstr ""
"ACF'de yeni misiniz? <a href=\"%s\" target=\"_blank\">başlangıç "
"kılavuzumuza</a> göz atın."

#: includes/admin/views/acf-field-group/list-empty.php:24
msgid "Add Field Group"
msgstr "Alan grubu ekle"

#. translators: %s url to creating a field group page
#: includes/admin/views/acf-field-group/list-empty.php:18
msgid ""
"ACF uses <a href=\"%s\" target=\"_blank\">field groups</a> to group custom "
"fields together, and then attach those fields to edit screens."
msgstr ""
"ACF, özel alanları bir araya toplamak için <a href=\"%s\" "
"target=\"_blank\">alan gruplarını</a> kullanır ve akabinde bu alanları "
"düzenleme ekranlarına ekler."

#: includes/admin/views/acf-field-group/list-empty.php:12
msgid "Add Your First Field Group"
msgstr "İlk alan grubunuzu ekleyin"

#: includes/admin/admin-options-pages-preview.php:28
#: includes/admin/views/acf-field-group/pro-features.php:58
#: includes/admin/views/global/navigation.php:86
#: includes/admin/views/global/navigation.php:258
msgid "Options Pages"
msgstr "Seçenekler sayfası"

#: includes/admin/views/acf-field-group/pro-features.php:54
msgid "ACF Blocks"
msgstr "ACF blokları"

#: includes/admin/views/acf-field-group/pro-features.php:62
msgid "Gallery Field"
msgstr "Galeri alanı"

#: includes/admin/views/acf-field-group/pro-features.php:42
msgid "Flexible Content Field"
msgstr "Esnek içerik alanı"

#: includes/admin/views/acf-field-group/pro-features.php:46
msgid "Repeater Field"
msgstr "Tekrarlayıcı alanı"

#: includes/admin/views/global/navigation.php:218
msgid "Unlock Extra Features with ACF PRO"
msgstr "ACF PRO ile ek özelikler açın"

#: includes/admin/views/acf-field-group/options.php:267
msgid "Delete Field Group"
msgstr "Alan grubunu sil"

#. translators: 1: Post creation date 2: Post creation time
#: includes/admin/views/acf-field-group/options.php:261
msgid "Created on %1$s at %2$s"
msgstr "%1$s tarihinde %2$s saatinde oluşturuldu"

#: includes/acf-field-group-functions.php:497
msgid "Group Settings"
msgstr "Grup ayarları"

#: includes/acf-field-group-functions.php:495
msgid "Location Rules"
msgstr "Konum kuralları"

#. translators: %s url to field types list
#: includes/admin/views/acf-field-group/fields.php:73
msgid ""
"Choose from over 30 field types. <a href=\"%s\" target=\"_blank\">Learn "
"more</a>."
msgstr ""
"30'dan fazla alan türünden seçim yapın. <a href=\"%s\" "
"target=\"_blank\">Daha fazla bilgi edinin</a>."

#: includes/admin/views/acf-field-group/fields.php:65
msgid ""
"Get started creating new custom fields for your posts, pages, custom post "
"types and other WordPress content."
msgstr ""
"Yazılarınız, sayfalarınız, özel yazı tipleriniz ve diğer WordPress "
"içerikleriniz için yeni özel alanlar oluşturmaya başlayın."

#: includes/admin/views/acf-field-group/fields.php:64
msgid "Add Your First Field"
msgstr "İlk alanınızı ekleyin"

#. translators: A symbol (or text, if not available in your locale) meaning
#. "Order Number", in terms of positional placement.
#: includes/admin/views/acf-field-group/fields.php:43
msgid "#"
msgstr "#"

#: includes/admin/views/acf-field-group/fields.php:33
#: includes/admin/views/acf-field-group/fields.php:67
#: includes/admin/views/acf-field-group/fields.php:101
#: includes/admin/views/global/form-top.php:88
msgid "Add Field"
msgstr "Alan ekle"

#: includes/acf-field-group-functions.php:496 includes/fields.php:384
msgid "Presentation"
msgstr "Sunum"

#: includes/fields.php:383
msgid "Validation"
msgstr "Doğrulama"

#: includes/acf-internal-post-type-functions.php:504
#: includes/acf-internal-post-type-functions.php:533 includes/fields.php:382
msgid "General"
msgstr "Genel"

#: includes/admin/tools/class-acf-admin-tool-import.php:67
msgid "Import JSON"
msgstr "JSON'u içe aktar"

#: includes/admin/tools/class-acf-admin-tool-export.php:338
msgid "Export As JSON"
msgstr "JSON olarak dışa aktar"

#. translators: %s number of field groups deactivated
#: includes/admin/post-types/admin-field-groups.php:360
msgid "Field group deactivated."
msgid_plural "%s field groups deactivated."
msgstr[0] "Alan grubu silindi."
msgstr[1] "%s alan grubu silindi."

#. translators: %s number of field groups activated
#: includes/admin/post-types/admin-field-groups.php:353
msgid "Field group activated."
msgid_plural "%s field groups activated."
msgstr[0] "Alan grubu kaydedildi."
msgstr[1] "%s alan grubu kaydedildi."

#: includes/admin/admin-internal-post-type-list.php:470
#: includes/admin/admin-internal-post-type-list.php:496
msgid "Deactivate"
msgstr "Devre dışı bırak"

#: includes/admin/admin-internal-post-type-list.php:470
msgid "Deactivate this item"
msgstr "Bu öğeyi devre dışı bırak"

#: includes/admin/admin-internal-post-type-list.php:466
#: includes/admin/admin-internal-post-type-list.php:495
msgid "Activate"
msgstr "Etkinleştir"

#: includes/admin/admin-internal-post-type-list.php:466
msgid "Activate this item"
msgstr "Bu öğeyi etkinleştir"

#: includes/admin/post-types/admin-field-group.php:88
msgid "Move field group to trash?"
msgstr "Alan grubu çöp kutusuna taşınsın mı?"

#: acf.php:520 includes/admin/admin-internal-post-type-list.php:264
#: includes/admin/post-types/admin-field-group.php:304
#: includes/admin/post-types/admin-post-type.php:282
#: includes/admin/post-types/admin-taxonomy.php:284
msgctxt "post status"
msgid "Inactive"
msgstr "Devre dışı"

#. Author of the plugin
#: acf.php includes/admin/views/global/navigation.php:240
msgid "WP Engine"
msgstr "WP Engine"

#: acf.php:578
msgid ""
"Advanced Custom Fields and Advanced Custom Fields PRO should not be active "
"at the same time. We've automatically deactivated Advanced Custom Fields PRO."
msgstr ""
"Advanced Custom Fields ve Advanced Custom Fields PRO aynı anda etkin "
"olmamalıdır. Advanced Custom Fields PRO eklentisini otomatik olarak devre "
"dışı bıraktık."

#: acf.php:576
msgid ""
"Advanced Custom Fields and Advanced Custom Fields PRO should not be active "
"at the same time. We've automatically deactivated Advanced Custom Fields."
msgstr ""
"Advanced Custom Fields ve Advanced Custom Fields PRO aynı anda etkin "
"olmamalıdır. Advanced Custom Fields eklentisini otomatik olarak devre dışı "
"bıraktık."

#: includes/fields/class-acf-field-user.php:578
msgid "%1$s must have a user with the %2$s role."
msgid_plural "%1$s must have a user with one of the following roles: %2$s"
msgstr[0] "%1$s %2$s rolüne sahip bir kullanıcıya sahip olmalıdır."
msgstr[1] "%1$s şu rollerden birine ait bir kullanıcıya sahip olmalıdır: %2$s"

#: includes/fields/class-acf-field-user.php:569
msgid "%1$s must have a valid user ID."
msgstr "%1$s geçerli bir kullanıcı kimliğine sahip olmalıdır."

#: includes/fields/class-acf-field-user.php:408
msgid "Invalid request."
msgstr "Geçersiz istek."

#: includes/fields/class-acf-field-select.php:689
msgid "%1$s is not one of %2$s"
msgstr "%1$s bir %2$s değil"

#: includes/fields/class-acf-field-post_object.php:660
msgid "%1$s must have term %2$s."
msgid_plural "%1$s must have one of the following terms: %2$s"
msgstr[0] "%1$s %2$s terimine sahip olmalı."
msgstr[1] "%1$s şu terimlerden biri olmalı: %2$s"

#: includes/fields/class-acf-field-post_object.php:644
msgid "%1$s must be of post type %2$s."
msgid_plural "%1$s must be of one of the following post types: %2$s"
msgstr[0] "%1$s %2$s yazı tipinde olmalıdır."
msgstr[1] "%1$s şu yazı tiplerinden birinde olmalıdır: %2$s"

#: includes/fields/class-acf-field-post_object.php:635
msgid "%1$s must have a valid post ID."
msgstr "%1$s geçerli bir yazı kimliği olmalıdır."

#: includes/fields/class-acf-field-file.php:447
msgid "%s requires a valid attachment ID."
msgstr "%s geçerli bir ek kimliği gerektirir."

#: includes/admin/views/acf-field-group/options.php:233
msgid "Show in REST API"
msgstr "REST API'da göster"

#: includes/fields/class-acf-field-color_picker.php:156
msgid "Enable Transparency"
msgstr "Saydamlığı etkinleştir"

#: includes/fields/class-acf-field-color_picker.php:175
msgid "RGBA Array"
msgstr "RGBA dizisi"

#: includes/fields/class-acf-field-color_picker.php:92
msgid "RGBA String"
msgstr "RGBA metni"

#: includes/fields/class-acf-field-color_picker.php:91
#: includes/fields/class-acf-field-color_picker.php:174
msgid "Hex String"
msgstr "Hex metin"

#: includes/admin/views/browse-fields-modal.php:12
msgid "Upgrade to PRO"
msgstr "Pro sürüme yükselt"

#: includes/admin/post-types/admin-field-group.php:304
#: includes/admin/post-types/admin-post-type.php:282
#: includes/admin/post-types/admin-taxonomy.php:284
msgctxt "post status"
msgid "Active"
msgstr "Etkin"

#: includes/fields/class-acf-field-email.php:166
msgid "'%s' is not a valid email address"
msgstr "'%s' geçerli bir e-posta adresi değil"

#: includes/fields/class-acf-field-color_picker.php:70
msgid "Color value"
msgstr "Renk değeri"

#: includes/fields/class-acf-field-color_picker.php:68
msgid "Select default color"
msgstr "Varsayılan rengi seç"

#: includes/fields/class-acf-field-color_picker.php:66
msgid "Clear color"
msgstr "Rengi temizle"

#: includes/acf-wp-functions.php:90
msgid "Blocks"
msgstr "Bloklar"

#: includes/acf-wp-functions.php:86
msgid "Options"
msgstr "Ayarlar"

#: includes/acf-wp-functions.php:82
msgid "Users"
msgstr "Kullanıcılar"

#: includes/acf-wp-functions.php:78
msgid "Menu items"
msgstr "Menü ögeleri"

#: includes/acf-wp-functions.php:70
msgid "Widgets"
msgstr "Bileşenler"

#: includes/acf-wp-functions.php:62
msgid "Attachments"
msgstr "Dosya ekleri"

#: includes/acf-wp-functions.php:57
#: includes/admin/post-types/admin-post-types.php:112
#: includes/admin/post-types/admin-taxonomies.php:86
#: includes/admin/tools/class-acf-admin-tool-import.php:90
#: includes/admin/views/acf-post-type/basic-settings.php:86
#: includes/post-types/class-acf-taxonomy.php:90
#: includes/post-types/class-acf-taxonomy.php:91
msgid "Taxonomies"
msgstr "Taksonomiler"

#: includes/acf-wp-functions.php:44
#: includes/admin/post-types/admin-post-type.php:124
#: includes/admin/post-types/admin-post-types.php:114
#: includes/admin/views/acf-post-type/advanced-settings.php:106
msgid "Posts"
msgstr "İletiler"

#: includes/ajax/class-acf-ajax-local-json-diff.php:81
msgid "Last updated: %s"
msgstr "Son güncellenme: %s"

#: includes/ajax/class-acf-ajax-local-json-diff.php:75
msgid "Sorry, this post is unavailable for diff comparison."
msgstr "Üzgünüz, bu alan grubu fark karşılaştırma için uygun değil."

#: includes/ajax/class-acf-ajax-local-json-diff.php:47
msgid "Invalid field group parameter(s)."
msgstr "Geçersiz alan grubu parametresi/leri."

#: includes/admin/admin-internal-post-type-list.php:429
msgid "Awaiting save"
msgstr "Kayıt edilmeyi bekliyor"

#: includes/admin/admin-internal-post-type-list.php:426
msgid "Saved"
msgstr "Kaydedildi"

#: includes/admin/admin-internal-post-type-list.php:422
#: includes/admin/tools/class-acf-admin-tool-import.php:46
msgid "Import"
msgstr "İçe aktar"

#: includes/admin/admin-internal-post-type-list.php:418
msgid "Review changes"
msgstr "Değişiklikleri incele"

#: includes/admin/admin-internal-post-type-list.php:394
msgid "Located in: %s"
msgstr "Konumu: %s"

#: includes/admin/admin-internal-post-type-list.php:391
msgid "Located in plugin: %s"
msgstr "Eklenti içinde konumlu: %s"

#: includes/admin/admin-internal-post-type-list.php:388
msgid "Located in theme: %s"
msgstr "Tema içinde konumlu: %s"

#: includes/admin/post-types/admin-field-groups.php:235
msgid "Various"
msgstr "Çeşitli"

#: includes/admin/admin-internal-post-type-list.php:230
#: includes/admin/admin-internal-post-type-list.php:503
msgid "Sync changes"
msgstr "Değişiklikleri eşitle"

#: includes/admin/admin-internal-post-type-list.php:229
msgid "Loading diff"
msgstr "Fark yükleniyor"

#: includes/admin/admin-internal-post-type-list.php:228
msgid "Review local JSON changes"
msgstr "Yerel JSON değişikliklerini incele"

#: includes/admin/admin.php:174
msgid "Visit website"
msgstr "Web sitesini ziyaret et"

#: includes/admin/admin.php:173
msgid "View details"
msgstr "Ayrıntıları görüntüle"

#: includes/admin/admin.php:172
msgid "Version %s"
msgstr "Sürüm %s"

#: includes/admin/admin.php:171
msgid "Information"
msgstr "Bilgi"

#: includes/admin/admin.php:162
msgid ""
"<a href=\"%s\" target=\"_blank\">Help Desk</a>. The support professionals on "
"our Help Desk will assist with your more in depth, technical challenges."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Yardım masası</a>. Yardım masamızdaki "
"profesyonel destek çalışanlarımızı daha derin, teknik sorunların üstesinden "
"gelmenize yardımcı olabilirler."

#: includes/admin/admin.php:158
msgid ""
"<a href=\"%s\" target=\"_blank\">Discussions</a>. We have an active and "
"friendly community on our Community Forums who may be able to help you "
"figure out the 'how-tos' of the ACF world."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Tartışmalar</a>. Topluluk forumlarımızda "
"etkin ve dost canlısı bir topluluğumuz var, sizi ACF dünyasının 'nasıl "
"yaparım'ları ile ilgili yardımcı olabilirler."

#: includes/admin/admin.php:154
msgid ""
"<a href=\"%s\" target=\"_blank\">Documentation</a>. Our extensive "
"documentation contains references and guides for most situations you may "
"encounter."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Belgeler</a>. Karşınıza çıkabilecek bir çok "
"konu hakkında geniş içerikli belgelerimize baş vurabilirsiniz."

#: includes/admin/admin.php:151
msgid ""
"We are fanatical about support, and want you to get the best out of your "
"website with ACF. If you run into any difficulties, there are several places "
"you can find help:"
msgstr ""
"Destek konusunu çok ciddiye alıyoruz ve size ACF ile sitenizde en iyi "
"çözümlere ulaşmanızı istiyoruz. Eğer bir sorunla karşılaşırsanız yardım "
"alabileceğiniz bir kaç yer var:"

#: includes/admin/admin.php:148 includes/admin/admin.php:150
msgid "Help & Support"
msgstr "Yardım ve destek"

#: includes/admin/admin.php:139
msgid ""
"Please use the Help & Support tab to get in touch should you find yourself "
"requiring assistance."
msgstr ""
"İşin içinden çıkamadığınızda lütfen Yardım ve destek sekmesinden irtibata "
"geçin."

#: includes/admin/admin.php:136
msgid ""
"Before creating your first Field Group, we recommend first reading our <a "
"href=\"%s\" target=\"_blank\">Getting started</a> guide to familiarize "
"yourself with the plugin's philosophy and best practises."
msgstr ""
"İlk alan grubunuzu oluşturmadan önce <a href=\"%s\" "
"target=\"_blank\">Başlarken</a> rehberimize okumanızı öneririz, bu sayede "
"eklentinin filozofisini daha iyi anlayabilir ve en iyi çözümleri "
"öğrenebilirsiniz."

#: includes/admin/admin.php:134
msgid ""
"The Advanced Custom Fields plugin provides a visual form builder to "
"customize WordPress edit screens with extra fields, and an intuitive API to "
"display custom field values in any theme template file."
msgstr ""
"The Advanced Custom Fields eklentisi bir görsel form oluşturucu ile "
"WordPress düzenleme ekranlarını ek alanlarla özelleştirme imkanı sağlıyor, "
"ve sezgisel API ile her türlü tema şablon dosyasında bu özel alanlar "
"gösterilebiliyor."

#: includes/admin/admin.php:131 includes/admin/admin.php:133
msgid "Overview"
msgstr "Genel görünüm"

#. translators: %s the name of the location type
#: includes/locations.php:38
msgid "Location type \"%s\" is already registered."
msgstr "Konum türü \"%s\" zaten kayıtlı."

#. translators: %s class name for a location that could not be found
#: includes/locations.php:26
msgid "Class \"%s\" does not exist."
msgstr "\"%s\" sınıfı mevcut değil."

#: includes/ajax/class-acf-ajax-query-users.php:43
#: includes/ajax/class-acf-ajax.php:157
msgid "Invalid nonce."
msgstr "Geçersiz nonce."

#: includes/fields/class-acf-field-user.php:400
msgid "Error loading field."
msgstr "Alan yükleme sırasında hata."

#: includes/forms/form-user.php:328
msgid "<strong>Error</strong>: %s"
msgstr "<strong>Hata</strong>: %s"

#: includes/locations/class-acf-location-widget.php:22
msgid "Widget"
msgstr "Bileşen"

#: includes/locations/class-acf-location-user-role.php:24
msgid "User Role"
msgstr "Kullanıcı rolü"

#: includes/locations/class-acf-location-comment.php:22
msgid "Comment"
msgstr "Yorum"

#: includes/locations/class-acf-location-post-format.php:22
msgid "Post Format"
msgstr "Yazı biçimi"

#: includes/locations/class-acf-location-nav-menu-item.php:22
msgid "Menu Item"
msgstr "Menü ögesi"

#: includes/locations/class-acf-location-post-status.php:22
msgid "Post Status"
msgstr "Yazı durumu"

#: includes/acf-wp-functions.php:74
#: includes/locations/class-acf-location-nav-menu.php:89
msgid "Menus"
msgstr "Menüler"

#: includes/locations/class-acf-location-nav-menu.php:80
msgid "Menu Locations"
msgstr "Menü konumları"

#: includes/locations/class-acf-location-nav-menu.php:22
msgid "Menu"
msgstr "Menü"

#: includes/locations/class-acf-location-post-taxonomy.php:22
msgid "Post Taxonomy"
msgstr "Yazı taksonomisi"

#: includes/locations/class-acf-location-page-type.php:114
msgid "Child Page (has parent)"
msgstr "Alt sayfa (ebeveyni olan)"

#: includes/locations/class-acf-location-page-type.php:113
msgid "Parent Page (has children)"
msgstr "Üst sayfa (alt sayfası olan)"

#: includes/locations/class-acf-location-page-type.php:112
msgid "Top Level Page (no parent)"
msgstr "Üst düzey sayfa (ebeveynsiz)"

#: includes/locations/class-acf-location-page-type.php:111
msgid "Posts Page"
msgstr "Yazılar sayfası"

#: includes/locations/class-acf-location-page-type.php:110
msgid "Front Page"
msgstr "Ön Sayfa"

#: includes/locations/class-acf-location-page-type.php:22
msgid "Page Type"
msgstr "Sayfa tipi"

#: includes/locations/class-acf-location-current-user.php:73
msgid "Viewing back end"
msgstr "Arka yüz görüntüleniyor"

#: includes/locations/class-acf-location-current-user.php:72
msgid "Viewing front end"
msgstr "Ön yüz görüntüleniyor"

#: includes/locations/class-acf-location-current-user.php:71
msgid "Logged in"
msgstr "Giriş yapıldı"

#: includes/locations/class-acf-location-current-user.php:22
msgid "Current User"
msgstr "Şu anki kullanıcı"

#: includes/locations/class-acf-location-page-template.php:22
msgid "Page Template"
msgstr "Sayfa şablonu"

#: includes/locations/class-acf-location-user-form.php:74
msgid "Register"
msgstr "Kayıt ol"

#: includes/locations/class-acf-location-user-form.php:73
msgid "Add / Edit"
msgstr "Ekle / düzenle"

#: includes/locations/class-acf-location-user-form.php:22
msgid "User Form"
msgstr "Kullanıcı formu"

#: includes/locations/class-acf-location-page-parent.php:22
msgid "Page Parent"
msgstr "Sayfa ebeveyni"

#: includes/locations/class-acf-location-current-user-role.php:77
msgid "Super Admin"
msgstr "Süper yönetici"

#: includes/locations/class-acf-location-current-user-role.php:22
msgid "Current User Role"
msgstr "Şu anki kullanıcı rolü"

#: includes/locations/class-acf-location-page-template.php:73
#: includes/locations/class-acf-location-post-template.php:85
msgid "Default Template"
msgstr "Varsayılan şablon"

#: includes/locations/class-acf-location-post-template.php:22
msgid "Post Template"
msgstr "Yazı şablonu"

#: includes/locations/class-acf-location-post-category.php:22
msgid "Post Category"
msgstr "Yazı kategorisi"

#: includes/locations/class-acf-location-attachment.php:84
msgid "All %s formats"
msgstr "Tüm %s biçimleri"

#: includes/locations/class-acf-location-attachment.php:22
msgid "Attachment"
msgstr "Eklenti"

#: includes/validation.php:323
msgid "%s value is required"
msgstr "%s değeri gerekli"

#: includes/admin/views/acf-field-group/conditional-logic.php:64
msgid "Show this field if"
msgstr "Alanı bu şart gerçekleşirse göster"

#: includes/admin/views/acf-field-group/conditional-logic.php:25
#: includes/admin/views/acf-field-group/field.php:122 includes/fields.php:385
msgid "Conditional Logic"
msgstr "Koşullu mantık"

#: includes/admin/views/acf-field-group/conditional-logic.php:169
#: includes/admin/views/acf-field-group/location-rule.php:84
msgid "and"
msgstr "ve"

#: includes/admin/post-types/admin-field-groups.php:97
#: includes/admin/post-types/admin-post-types.php:118
#: includes/admin/post-types/admin-taxonomies.php:117
msgid "Local JSON"
msgstr "Yerel JSON"

#: includes/admin/views/acf-field-group/pro-features.php:50
msgid "Clone Field"
msgstr "Çoğaltma alanı"

#. translators: %s a list of plugin
#: includes/admin/views/upgrade/notice.php:32
msgid ""
"Please also check all premium add-ons (%s) are updated to the latest version."
msgstr ""
"Lütfen ayrıca premium eklentilerin de (%s) en üst sürüme güncellendiğinden "
"emin olun."

#: includes/admin/views/upgrade/notice.php:29
msgid ""
"This version contains improvements to your database and requires an upgrade."
msgstr ""
"Bu sürüm veritabanınız için iyileştirmeler içeriyor ve yükseltme "
"gerektiriyor."

#. translators: %1 plugin name, %2 version number
#: includes/admin/views/upgrade/notice.php:28
msgid "Thank you for updating to %1$s v%2$s!"
msgstr "%1$s v%2$s sürümüne güncellediğiniz için teşekkür ederiz!"

#: includes/admin/views/upgrade/notice.php:26
msgid "Database Upgrade Required"
msgstr "Veritabanı yükseltmesi gerekiyor"

#: includes/admin/post-types/admin-field-group.php:159
#: includes/admin/views/upgrade/notice.php:17
msgid "Options Page"
msgstr "Seçenekler sayfası"

#: includes/admin/views/upgrade/notice.php:14 includes/fields.php:436
msgid "Gallery"
msgstr "Galeri"

#: includes/admin/views/upgrade/notice.php:11 includes/fields.php:426
msgid "Flexible Content"
msgstr "Esnek içerik"

#: includes/admin/views/upgrade/notice.php:8 includes/fields.php:446
msgid "Repeater"
msgstr "Tekrarlayıcı"

#: includes/admin/views/tools/tools.php:16
msgid "Back to all tools"
msgstr "Tüm araçlara geri dön"

#: includes/admin/views/acf-field-group/options.php:195
msgid ""
"If multiple field groups appear on an edit screen, the first field group's "
"options will be used (the one with the lowest order number)"
msgstr ""
"Eğer düzenleme ekranında birden çok alan grubu ortaya çıkarsa, ilk alan "
"grubunun seçenekleri kullanılır (en düşük sıralama numarasına sahip olan)"

#: includes/admin/views/acf-field-group/options.php:195
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr "Düzenleme ekranından <b>gizlemek</b> istediğiniz ögeleri <b>seçin</b>."

#: includes/admin/views/acf-field-group/options.php:194
msgid "Hide on screen"
msgstr "Ekranda gizle"

#: includes/admin/views/acf-field-group/options.php:186
msgid "Send Trackbacks"
msgstr "Geri izlemeleri gönder"

#: includes/admin/post-types/admin-taxonomy.php:126
#: includes/admin/views/acf-field-group/options.php:185
#: includes/admin/views/acf-taxonomy/advanced-settings.php:159
msgid "Tags"
msgstr "Etiketler"

#: includes/admin/post-types/admin-taxonomy.php:128
#: includes/admin/views/acf-field-group/options.php:184
msgid "Categories"
msgstr "Kategoriler"

#: includes/admin/views/acf-field-group/options.php:182
#: includes/admin/views/acf-post-type/advanced-settings.php:28
msgid "Page Attributes"
msgstr "Sayfa özellikleri"

#: includes/admin/views/acf-field-group/options.php:181
msgid "Format"
msgstr "Biçim"

#: includes/admin/views/acf-field-group/options.php:180
#: includes/admin/views/acf-post-type/advanced-settings.php:22
msgid "Author"
msgstr "Yazar"

#: includes/admin/views/acf-field-group/options.php:179
msgid "Slug"
msgstr "Kısa isim"

#: includes/admin/views/acf-field-group/options.php:178
#: includes/admin/views/acf-post-type/advanced-settings.php:27
msgid "Revisions"
msgstr "Sürümler"

#: includes/acf-wp-functions.php:66
#: includes/admin/views/acf-field-group/options.php:177
#: includes/admin/views/acf-post-type/advanced-settings.php:23
msgid "Comments"
msgstr "Yorumlar"

#: includes/admin/views/acf-field-group/options.php:176
msgid "Discussion"
msgstr "Tartışma"

#: includes/admin/views/acf-field-group/options.php:174
#: includes/admin/views/acf-post-type/advanced-settings.php:26
msgid "Excerpt"
msgstr "Özet"

#: includes/admin/views/acf-field-group/options.php:173
msgid "Content Editor"
msgstr "İçerik düzenleyici"

#: includes/admin/views/acf-field-group/options.php:172
msgid "Permalink"
msgstr "Kalıcı bağlantı"

#: includes/admin/views/acf-field-group/options.php:250
msgid "Shown in field group list"
msgstr "Alan grubu listesinde görüntülenir"

#: includes/admin/views/acf-field-group/options.php:157
msgid "Field groups with a lower order will appear first"
msgstr "Daha düşük sıralamaya sahip alan grupları daha önce görünür"

#: includes/admin/views/acf-field-group/options.php:156
msgid "Order No."
msgstr "Sipariş No."

#: includes/admin/views/acf-field-group/options.php:147
msgid "Below fields"
msgstr "Alanlarının altında"

#: includes/admin/views/acf-field-group/options.php:146
msgid "Below labels"
msgstr "Etiketlerin altında"

#: includes/admin/views/acf-field-group/options.php:139
msgid "Instruction Placement"
msgstr "Yönerge yerleştirme"

#: includes/admin/views/acf-field-group/options.php:122
msgid "Label Placement"
msgstr "Etiket yerleştirme"

#: includes/admin/views/acf-field-group/options.php:110
msgid "Side"
msgstr "Yan"

#: includes/admin/views/acf-field-group/options.php:109
msgid "Normal (after content)"
msgstr "Normal (içerikten sonra)"

#: includes/admin/views/acf-field-group/options.php:108
msgid "High (after title)"
msgstr "Yüksek (başlıktan sonra)"

#: includes/admin/views/acf-field-group/options.php:101
msgid "Position"
msgstr "Konum"

#: includes/admin/views/acf-field-group/options.php:92
msgid "Seamless (no metabox)"
msgstr "Pürüzsüz (metabox yok)"

#: includes/admin/views/acf-field-group/options.php:91
msgid "Standard (WP metabox)"
msgstr "Standart (WP metabox)"

#: includes/admin/views/acf-field-group/options.php:84
msgid "Style"
msgstr "Stil"

#: includes/admin/views/acf-field-group/fields.php:55
msgid "Type"
msgstr "Tür"

#: includes/admin/post-types/admin-field-groups.php:91
#: includes/admin/post-types/admin-post-types.php:111
#: includes/admin/post-types/admin-taxonomies.php:110
#: includes/admin/views/acf-field-group/fields.php:54
msgid "Key"
msgstr "Anahtar"

#. translators: Hidden accessibility text for the positional order number of
#. the field.
#: includes/admin/views/acf-field-group/fields.php:48
msgid "Order"
msgstr "Düzen"

#: includes/admin/views/acf-field-group/field.php:321
msgid "Close Field"
msgstr "Alanı kapat"

#: includes/admin/views/acf-field-group/field.php:252
msgid "id"
msgstr "id"

#: includes/admin/views/acf-field-group/field.php:236
msgid "class"
msgstr "sınıf"

#: includes/admin/views/acf-field-group/field.php:278
msgid "width"
msgstr "genişlik"

#: includes/admin/views/acf-field-group/field.php:272
msgid "Wrapper Attributes"
msgstr "Kapsayıcı öznitelikleri"

#: includes/fields/class-acf-field.php:312
msgid "Required"
msgstr "Gerekli"

#: includes/admin/views/acf-field-group/field.php:219
msgid "Instructions"
msgstr "Yönergeler"

#: includes/admin/views/acf-field-group/field.php:142
msgid "Field Type"
msgstr "Alan tipi"

#: includes/admin/views/acf-field-group/field.php:183
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr "Tek kelime, boşluksuz. Alt çizgi ve tireye izin var"

#: includes/admin/views/acf-field-group/field.php:182
msgid "Field Name"
msgstr "Alan adı"

#: includes/admin/views/acf-field-group/field.php:170
msgid "This is the name which will appear on the EDIT page"
msgstr "Bu isim DÜZENLEME sayfasında görüntülenecek isimdir"

#: includes/admin/views/acf-field-group/field.php:169
#: includes/admin/views/browse-fields-modal.php:69
msgid "Field Label"
msgstr "Alan etiketi"

#: includes/admin/views/acf-field-group/field.php:94
msgid "Delete"
msgstr "Sil"

#: includes/admin/views/acf-field-group/field.php:94
msgid "Delete field"
msgstr "Sil alanı"

#: includes/admin/views/acf-field-group/field.php:92
msgid "Move"
msgstr "Taşı"

#: includes/admin/views/acf-field-group/field.php:92
msgid "Move field to another group"
msgstr "Alanı başka gruba taşı"

#: includes/admin/views/acf-field-group/field.php:90
msgid "Duplicate field"
msgstr "Alanı çoğalt"

#: includes/admin/views/acf-field-group/field.php:86
#: includes/admin/views/acf-field-group/field.php:89
msgid "Edit field"
msgstr "Alanı düzenle"

#: includes/admin/views/acf-field-group/field.php:82
msgid "Drag to reorder"
msgstr "Yeniden düzenlemek için sürükleyin"

#: includes/admin/post-types/admin-field-group.php:99
#: includes/admin/views/acf-field-group/location-group.php:3
msgid "Show this field group if"
msgstr "Bu alan grubunu şu koşulda göster"

#: includes/admin/views/upgrade/upgrade.php:93
#: includes/ajax/class-acf-ajax-upgrade.php:34
msgid "No updates available."
msgstr "Güncelleme yok."

#. translators: %s the url to the field group page.
#: includes/admin/views/upgrade/upgrade.php:32
msgid "Database upgrade complete. <a href=\"%s\">See what's new</a>"
msgstr "Veritabanı yükseltme tamamlandı. <a href=\"%s\">Yenilikler </a>"

#: includes/admin/views/upgrade/upgrade.php:27
msgid "Reading upgrade tasks..."
msgstr "Yükseltme görevlerini okuyor..."

#: includes/admin/views/upgrade/network.php:165
#: includes/admin/views/upgrade/upgrade.php:64
msgid "Upgrade failed."
msgstr "Yükseltme başarısız oldu."

#: includes/admin/views/upgrade/network.php:162
msgid "Upgrade complete."
msgstr "Yükseltme başarılı."

#. translators: %s the version being upgraded to.
#. translators: %s the new ACF version
#: includes/admin/views/upgrade/network.php:148
#: includes/admin/views/upgrade/upgrade.php:29
msgid "Upgrading data to version %s"
msgstr "Veri %s sürümüne yükseltiliyor"

#: includes/admin/views/upgrade/network.php:120
#: includes/admin/views/upgrade/notice.php:46
msgid ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"
msgstr ""
"Devam etmeden önce veritabanınızı yedeklemeniz önemle önerilir. "
"Güncelleştiriciyi şimdi çalıştırmak istediğinizden emin misiniz?"

#: includes/admin/views/upgrade/network.php:116
msgid "Please select at least one site to upgrade."
msgstr "Lütfen yükseltmek için en az site seçin."

#. translators: %s admin dashboard url page
#: includes/admin/views/upgrade/network.php:96
msgid ""
"Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr ""
"Veritabanı güncellemesi tamamlandı. <a href=\"%s\">Ağ panosuna geri dön</a>"

#: includes/admin/views/upgrade/network.php:79
msgid "Site is up to date"
msgstr "Site güncel"

#. translators: %1 current db version, %2 available db version
#: includes/admin/views/upgrade/network.php:77
msgid "Site requires database upgrade from %1$s to %2$s"
msgstr "Site %1$s sürümünden %2$s sürümüne veritabanı yükseltmesi gerektiriyor"

#: includes/admin/views/upgrade/network.php:34
#: includes/admin/views/upgrade/network.php:45
msgid "Site"
msgstr "Site"

#. translators: %s The button label name, translated seperately
#: includes/admin/views/upgrade/network.php:24
#: includes/admin/views/upgrade/network.php:25
#: includes/admin/views/upgrade/network.php:94
msgid "Upgrade Sites"
msgstr "Siteleri yükselt"

#. translators: %s The button label name, translated seperately
#: includes/admin/views/upgrade/network.php:24
msgid ""
"The following sites require a DB upgrade. Check the ones you want to update "
"and then click %s."
msgstr ""
"Şu siteler için VT güncellemesi gerekiyor. Güncellemek istediklerinizi "
"işaretleyin ve %s tuşuna basın."

#: includes/admin/views/acf-field-group/conditional-logic.php:184
#: includes/admin/views/acf-field-group/locations.php:37
msgid "Add rule group"
msgstr "Kural grubu ekle"

#: includes/admin/views/acf-field-group/locations.php:10
msgid ""
"Create a set of rules to determine which edit screens will use these "
"advanced custom fields"
msgstr ""
"Bu gelişmiş özel alanları hangi düzenleme ekranlarının kullanacağını "
"belirlemek için bir kural seti oluşturun"

#: includes/admin/views/acf-field-group/locations.php:9
msgid "Rules"
msgstr "Kurallar"

#: includes/admin/tools/class-acf-admin-tool-export.php:449
msgid "Copied"
msgstr "Kopyalandı"

#: includes/admin/tools/class-acf-admin-tool-export.php:425
msgid "Copy to clipboard"
msgstr "Panoya kopyala"

#: includes/admin/tools/class-acf-admin-tool-export.php:331
msgid ""
"Select the items you would like to export and then select your export "
"method. Export As JSON to export to a .json file which you can then import "
"to another ACF installation. Generate PHP to export to PHP code which you "
"can place in your theme."
msgstr ""
"Dışa aktarma ve sonra dışa aktarma yöntemini seçtikten sonra alan gruplarını "
"seçin. Sonra başka bir ACF yükleme içe bir .json dosyaya vermek için indirme "
"düğmesini kullanın. Tema yerleştirebilirsiniz PHP kodu aktarma düğmesini "
"kullanın."

#: includes/admin/tools/class-acf-admin-tool-export.php:215
msgid "Select Field Groups"
msgstr "Alan gruplarını seç"

#: includes/admin/tools/class-acf-admin-tool-export.php:88
#: includes/admin/tools/class-acf-admin-tool-export.php:121
msgid "No field groups selected"
msgstr "Hiç alan grubu seçilmemiş"

#: includes/admin/tools/class-acf-admin-tool-export.php:38
#: includes/admin/tools/class-acf-admin-tool-export.php:339
#: includes/admin/tools/class-acf-admin-tool-export.php:363
msgid "Generate PHP"
msgstr "PHP oluştur"

#: includes/admin/tools/class-acf-admin-tool-export.php:34
msgid "Export Field Groups"
msgstr "Alan gruplarını dışarı aktar"

#: includes/admin/tools/class-acf-admin-tool-import.php:172
msgid "Import file empty"
msgstr "İçe aktarılan dosya boş"

#: includes/admin/tools/class-acf-admin-tool-import.php:163
msgid "Incorrect file type"
msgstr "Geçersiz dosya tipi"

#: includes/admin/tools/class-acf-admin-tool-import.php:158
msgid "Error uploading file. Please try again"
msgstr "Dosya yüklenirken hata oluştu. Lütfen tekrar deneyin"

#: includes/admin/tools/class-acf-admin-tool-import.php:47
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the items in that file."
msgstr ""
"İçeri aktarmak istediğiniz Advanced Custom Fields JSON dosyasını seçin. "
"Aşağıdaki içeri aktar tuşuna bastığınızda ACF alan gruplarını içeri "
"aktaracak."

#: includes/admin/tools/class-acf-admin-tool-import.php:26
msgid "Import Field Groups"
msgstr "Alan gruplarını içeri aktar"

#: includes/admin/admin-internal-post-type-list.php:417
msgid "Sync"
msgstr "Eşitle"

#. translators: %s: field group title
#: includes/admin/admin-internal-post-type-list.php:960
msgid "Select %s"
msgstr "Seç %s"

#: includes/admin/admin-internal-post-type-list.php:460
#: includes/admin/admin-internal-post-type-list.php:492
#: includes/admin/views/acf-field-group/field.php:90
msgid "Duplicate"
msgstr "Çoğalt"

#: includes/admin/admin-internal-post-type-list.php:460
msgid "Duplicate this item"
msgstr "Bu ögeyi çoğalt"

#: includes/admin/views/acf-post-type/advanced-settings.php:41
msgid "Supports"
msgstr "Destek"

#: includes/admin/admin.php:355
#: includes/admin/views/browse-fields-modal.php:102
msgid "Documentation"
msgstr "Belgeler"

#: includes/admin/post-types/admin-field-groups.php:90
#: includes/admin/post-types/admin-post-types.php:110
#: includes/admin/post-types/admin-taxonomies.php:109
#: includes/admin/views/acf-field-group/options.php:249
#: includes/admin/views/acf-post-type/advanced-settings.php:62
#: includes/admin/views/acf-taxonomy/advanced-settings.php:114
#: includes/admin/views/upgrade/network.php:36
#: includes/admin/views/upgrade/network.php:47
msgid "Description"
msgstr "Açıklama"

#: includes/admin/admin-internal-post-type-list.php:414
#: includes/admin/admin-internal-post-type-list.php:832
msgid "Sync available"
msgstr "Eşitleme mevcut"

#. translators: %s number of field groups synchronized
#: includes/admin/post-types/admin-field-groups.php:374
msgid "Field group synchronized."
msgid_plural "%s field groups synchronized."
msgstr[0] "Alan grubu eşitlendi."
msgstr[1] "%s alan grubu eşitlendi."

#. translators: %s number of field groups duplicated
#: includes/admin/post-types/admin-field-groups.php:367
msgid "Field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] "Alan grubu çoğaltıldı."
msgstr[1] "%s alan grubu çoğaltıldı."

#: includes/admin/admin-internal-post-type-list.php:155
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] "Etkin <span class=\"count\">(%s)</span>"
msgstr[1] "Etkin <span class=\"count\">(%s)</span>"

#: includes/admin/admin-upgrade.php:251
msgid "Review sites & upgrade"
msgstr "Siteleri incele ve güncelle"

#: includes/admin/admin-upgrade.php:59 includes/admin/admin-upgrade.php:90
#: includes/admin/admin-upgrade.php:91 includes/admin/admin-upgrade.php:227
#: includes/admin/views/upgrade/network.php:21
#: includes/admin/views/upgrade/upgrade.php:23
msgid "Upgrade Database"
msgstr "Veritabanını güncelle"

#: includes/admin/views/acf-field-group/options.php:175
#: includes/admin/views/acf-post-type/advanced-settings.php:30
msgid "Custom Fields"
msgstr "Ek alanlar"

#: includes/admin/post-types/admin-field-group.php:609
msgid "Move Field"
msgstr "Alanı taşı"

#: includes/admin/post-types/admin-field-group.php:602
#: includes/admin/post-types/admin-field-group.php:606
msgid "Please select the destination for this field"
msgstr "Lütfen bu alan için bir hedef seçin"

#. translators: Confirmation message once a field has been moved to a different
#. field group.
#: includes/admin/post-types/admin-field-group.php:568
msgid "The %1$s field can now be found in the %2$s field group"
msgstr "%1$s alanı artık %2$s alan grubunda bulunabilir"

#: includes/admin/post-types/admin-field-group.php:565
msgid "Move Complete."
msgstr "Taşıma tamamlandı."

#: includes/admin/views/acf-field-group/field.php:52
#: includes/admin/views/acf-field-group/options.php:217
#: includes/admin/views/acf-post-type/advanced-settings.php:78
#: includes/admin/views/acf-taxonomy/advanced-settings.php:130
msgid "Active"
msgstr "Etkin"

#: includes/admin/post-types/admin-field-group.php:276
msgid "Field Keys"
msgstr "Alan anahtarları"

#: includes/admin/post-types/admin-field-group.php:180
msgid "Settings"
msgstr "Ayarlar"

#: includes/admin/post-types/admin-field-groups.php:92
msgid "Location"
msgstr "Konum"

#: includes/admin/post-types/admin-field-group.php:100
msgid "Null"
msgstr "Boş"

#: includes/admin/post-types/admin-field-group.php:97
#: includes/class-acf-internal-post-type.php:728
#: includes/post-types/class-acf-field-group.php:345
msgid "copy"
msgstr "kopyala"

#: includes/admin/post-types/admin-field-group.php:96
msgid "(this field)"
msgstr "(bu alan)"

#: includes/admin/post-types/admin-field-group.php:94
msgid "Checked"
msgstr "İşaretlendi"

#: includes/admin/post-types/admin-field-group.php:90
msgid "Move Custom Field"
msgstr "Özel alanı taşı"

#: includes/admin/post-types/admin-field-group.php:89
msgid "No toggle fields available"
msgstr "Kullanılabilir aç-kapa alan yok"

#: includes/admin/post-types/admin-field-group.php:87
msgid "Field group title is required"
msgstr "Alan grubu başlığı gerekli"

#: includes/admin/post-types/admin-field-group.php:86
msgid "This field cannot be moved until its changes have been saved"
msgstr "Bu alan, üzerinde yapılan değişiklikler kaydedilene kadar taşınamaz"

#: includes/admin/post-types/admin-field-group.php:85
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr "Artık alan isimlerinin başlangıcında “field_” kullanılmayacak"

#: includes/admin/post-types/admin-field-group.php:69
msgid "Field group draft updated."
msgstr "Alan grubu taslağı güncellendi."

#: includes/admin/post-types/admin-field-group.php:68
msgid "Field group scheduled for."
msgstr "Alan grubu zamanlandı."

#: includes/admin/post-types/admin-field-group.php:67
msgid "Field group submitted."
msgstr "Alan grubu gönderildi."

#: includes/admin/post-types/admin-field-group.php:66
msgid "Field group saved."
msgstr "Alan grubu kaydedildi."

#: includes/admin/post-types/admin-field-group.php:65
msgid "Field group published."
msgstr "Alan grubu yayımlandı."

#: includes/admin/post-types/admin-field-group.php:62
msgid "Field group deleted."
msgstr "Alan grubu silindi."

#: includes/admin/post-types/admin-field-group.php:60
#: includes/admin/post-types/admin-field-group.php:61
#: includes/admin/post-types/admin-field-group.php:63
msgid "Field group updated."
msgstr "Alan grubu güncellendi."

#: includes/admin/admin-tools.php:107
#: includes/admin/views/global/navigation.php:254
#: includes/admin/views/tools/tools.php:14
msgid "Tools"
msgstr "Araçlar"

#: includes/locations/abstract-acf-location.php:105
msgid "is not equal to"
msgstr "eşit değilse"

#: includes/locations/abstract-acf-location.php:104
msgid "is equal to"
msgstr "eşitse"

#: includes/locations.php:104
msgid "Forms"
msgstr "Formlar"

#: includes/admin/post-types/admin-post-type.php:125 includes/locations.php:102
#: includes/locations/class-acf-location-page.php:22
msgid "Page"
msgstr "Sayfa"

#: includes/admin/post-types/admin-post-type.php:123 includes/locations.php:101
#: includes/locations/class-acf-location-post.php:22
msgid "Post"
msgstr "Yazı"

#: includes/fields.php:328
msgid "Relational"
msgstr "İlişkisel"

#: includes/fields.php:327
msgid "Choice"
msgstr "Seçim"

#: includes/fields.php:325
msgid "Basic"
msgstr "Basit"

#: includes/fields.php:276
msgid "Unknown"
msgstr "Bilinmeyen"

#: includes/fields.php:276
msgid "Field type does not exist"
msgstr "Var olmayan alan tipi"

#: includes/forms/form-front.php:220
msgid "Spam Detected"
msgstr "İstenmeyen tespit edildi"

#: includes/forms/form-front.php:103
msgid "Post updated"
msgstr "Yazı güncellendi"

#: includes/forms/form-front.php:102
msgid "Update"
msgstr "Güncelleme"

#: includes/forms/form-front.php:63
msgid "Validate Email"
msgstr "E-postayı doğrula"

#: includes/fields.php:326 includes/forms/form-front.php:55
msgid "Content"
msgstr "İçerik"

#: includes/admin/views/acf-post-type/advanced-settings.php:21
#: includes/forms/form-front.php:46
msgid "Title"
msgstr "Başlık"

#: includes/assets.php:376 includes/forms/form-comment.php:140
msgid "Edit field group"
msgstr "Alan grubunu düzenle"

#: includes/admin/post-types/admin-field-group.php:113
msgid "Selection is less than"
msgstr "Seçim daha az"

#: includes/admin/post-types/admin-field-group.php:112
msgid "Selection is greater than"
msgstr "Seçin daha büyük"

#: includes/admin/post-types/admin-field-group.php:111
msgid "Value is less than"
msgstr "Değer daha az"

#: includes/admin/post-types/admin-field-group.php:110
msgid "Value is greater than"
msgstr "Değer daha büyük"

#: includes/admin/post-types/admin-field-group.php:109
msgid "Value contains"
msgstr "Değer içeriyor"

#: includes/admin/post-types/admin-field-group.php:108
msgid "Value matches pattern"
msgstr "Değer bir desenle eşleşir"

#: includes/admin/post-types/admin-field-group.php:107
msgid "Value is not equal to"
msgstr "Değer eşit değilse"

#: includes/admin/post-types/admin-field-group.php:106
msgid "Value is equal to"
msgstr "Değer eşitse"

#: includes/admin/post-types/admin-field-group.php:105
msgid "Has no value"
msgstr "Hiçbir değer"

#: includes/admin/post-types/admin-field-group.php:104
msgid "Has any value"
msgstr "Herhangi bir değer"

#: includes/admin/admin-internal-post-type.php:337
#: includes/admin/views/browse-fields-modal.php:72 includes/assets.php:354
msgid "Cancel"
msgstr "Vazgeç"

#: includes/assets.php:350
msgid "Are you sure?"
msgstr "Emin misiniz?"

#: includes/assets.php:370
msgid "%d fields require attention"
msgstr "%d alan dikkatinizi gerektiriyor"

#: includes/assets.php:369
msgid "1 field requires attention"
msgstr "1 alan dikkatinizi gerektiriyor"

#: includes/assets.php:368 includes/validation.php:257
#: includes/validation.php:265
msgid "Validation failed"
msgstr "Doğrulama başarısız"

#: includes/assets.php:367
msgid "Validation successful"
msgstr "Doğrulama başarılı"

#: includes/media.php:54
msgid "Restricted"
msgstr "Kısıtlı"

#: includes/media.php:53
msgid "Collapse Details"
msgstr "Detayları daralt"

#: includes/media.php:52
msgid "Expand Details"
msgstr "Ayrıntıları genişlet"

#: includes/admin/views/acf-post-type/advanced-settings.php:470
#: includes/media.php:51
msgid "Uploaded to this post"
msgstr "Bu yazıya yüklenmiş"

#: includes/media.php:50
msgctxt "verb"
msgid "Update"
msgstr "Güncelleme"

#: includes/media.php:49
msgctxt "verb"
msgid "Edit"
msgstr "Düzenle"

#: includes/assets.php:364
msgid "The changes you made will be lost if you navigate away from this page"
msgstr ""
"Bu sayfadan başka bir sayfaya geçerseniz yaptığınız değişiklikler kaybolacak"

#: includes/api/api-helpers.php:3000
msgid "File type must be %s."
msgstr "Dosya tipi %s olmalı."

#: includes/admin/post-types/admin-field-group.php:98
#: includes/admin/views/acf-field-group/conditional-logic.php:64
#: includes/admin/views/acf-field-group/conditional-logic.php:182
#: includes/admin/views/acf-field-group/location-group.php:3
#: includes/admin/views/acf-field-group/locations.php:35
#: includes/api/api-helpers.php:2997
msgid "or"
msgstr "veya"

#: includes/api/api-helpers.php:2973
msgid "File size must not exceed %s."
msgstr "Dosya boyutu %s boyutunu geçmemeli."

#: includes/api/api-helpers.php:2969
msgid "File size must be at least %s."
msgstr "Dosya boyutu en az %s olmalı."

#: includes/api/api-helpers.php:2956
msgid "Image height must not exceed %dpx."
msgstr "Görsel yüksekliği %dpx değerini geçmemeli."

#: includes/api/api-helpers.php:2952
msgid "Image height must be at least %dpx."
msgstr "Görsel yüksekliği en az %dpx olmalı."

#: includes/api/api-helpers.php:2940
msgid "Image width must not exceed %dpx."
msgstr "Görsel genişliği %dpx değerini geçmemeli."

#: includes/api/api-helpers.php:2936
msgid "Image width must be at least %dpx."
msgstr "Görsel genişliği en az %dpx olmalı."

#: includes/api/api-helpers.php:1425 includes/api/api-term.php:140
msgid "(no title)"
msgstr "(başlık yok)"

#: includes/api/api-helpers.php:781
msgid "Full Size"
msgstr "Tam boyut"

#: includes/api/api-helpers.php:746
msgid "Large"
msgstr "Büyük"

#: includes/api/api-helpers.php:745
msgid "Medium"
msgstr "Orta"

#: includes/api/api-helpers.php:744
msgid "Thumbnail"
msgstr "Küçük resim"

#: includes/acf-field-functions.php:854
#: includes/admin/post-types/admin-field-group.php:95
msgid "(no label)"
msgstr "(etiket yok)"

#: includes/fields/class-acf-field-textarea.php:135
msgid "Sets the textarea height"
msgstr "Metin alanı yüksekliğini ayarla"

#: includes/fields/class-acf-field-textarea.php:134
msgid "Rows"
msgstr "Satırlar"

#: includes/fields/class-acf-field-textarea.php:22
msgid "Text Area"
msgstr "Metin alanı"

#: includes/fields/class-acf-field-checkbox.php:421
msgid "Prepend an extra checkbox to toggle all choices"
msgstr ""
"En başa tüm seçimleri tersine çevirmek için ekstra bir seçim kutusu ekle"

#: includes/fields/class-acf-field-checkbox.php:383
msgid "Save 'custom' values to the field's choices"
msgstr "‘Özel’ değerleri alanın seçenekleri arasına kaydet"

#: includes/fields/class-acf-field-checkbox.php:372
msgid "Allow 'custom' values to be added"
msgstr "‘Özel’ alanların eklenebilmesine izin ver"

#: includes/fields/class-acf-field-checkbox.php:35
msgid "Add new choice"
msgstr "Yeni seçenek ekle"

#: includes/fields/class-acf-field-checkbox.php:157
msgid "Toggle All"
msgstr "Tümünü aç/kapat"

#: includes/fields/class-acf-field-page_link.php:487
msgid "Allow Archives URLs"
msgstr "Arşivler adresine izin ver"

#: includes/fields/class-acf-field-page_link.php:196
msgid "Archives"
msgstr "Arşivler"

#: includes/fields/class-acf-field-page_link.php:22
msgid "Page Link"
msgstr "Sayfa bağlantısı"

#: includes/fields/class-acf-field-taxonomy.php:884
#: includes/locations/class-acf-location-user-form.php:72
msgid "Add"
msgstr "Ekle"

#: includes/admin/views/acf-field-group/fields.php:53
#: includes/fields/class-acf-field-taxonomy.php:854
msgid "Name"
msgstr "Bağlantı ismi"

#: includes/fields/class-acf-field-taxonomy.php:839
msgid "%s added"
msgstr "%s eklendi"

#: includes/fields/class-acf-field-taxonomy.php:803
msgid "%s already exists"
msgstr "%s zaten mevcut"

#: includes/fields/class-acf-field-taxonomy.php:791
msgid "User unable to add new %s"
msgstr "Kullanıcı yeni %s ekleyemiyor"

#: includes/fields/class-acf-field-taxonomy.php:678
msgid "Term ID"
msgstr "Terim no"

#: includes/fields/class-acf-field-taxonomy.php:677
msgid "Term Object"
msgstr "Terim nesnesi"

#: includes/fields/class-acf-field-taxonomy.php:662
msgid "Load value from posts terms"
msgstr "Yazının terimlerinden değerleri yükle"

#: includes/fields/class-acf-field-taxonomy.php:661
msgid "Load Terms"
msgstr "Terimleri yükle"

#: includes/fields/class-acf-field-taxonomy.php:651
msgid "Connect selected terms to the post"
msgstr "Seçilmiş terimleri yazıya bağla"

#: includes/fields/class-acf-field-taxonomy.php:650
msgid "Save Terms"
msgstr "Terimleri kaydet"

#: includes/fields/class-acf-field-taxonomy.php:640
msgid "Allow new terms to be created whilst editing"
msgstr "Düzenlenirken yeni terimlerin oluşabilmesine izin ver"

#: includes/fields/class-acf-field-taxonomy.php:639
msgid "Create Terms"
msgstr "Terimleri oluştur"

#: includes/fields/class-acf-field-taxonomy.php:698
msgid "Radio Buttons"
msgstr "Tekli seçim"

#: includes/fields/class-acf-field-taxonomy.php:697
msgid "Single Value"
msgstr "Tek değer"

#: includes/fields/class-acf-field-taxonomy.php:695
msgid "Multi Select"
msgstr "Çoklu seçim"

#: includes/fields/class-acf-field-checkbox.php:22
#: includes/fields/class-acf-field-taxonomy.php:694
msgid "Checkbox"
msgstr "İşaret kutusu"

#: includes/fields/class-acf-field-taxonomy.php:693
msgid "Multiple Values"
msgstr "Çoklu değer"

#: includes/fields/class-acf-field-taxonomy.php:688
msgid "Select the appearance of this field"
msgstr "Bu alanın görünümünü seçin"

#: includes/fields/class-acf-field-taxonomy.php:687
msgid "Appearance"
msgstr "Görünüm"

#: includes/fields/class-acf-field-taxonomy.php:629
msgid "Select the taxonomy to be displayed"
msgstr "Görüntülenecek taksonomiyi seçin"

#: includes/fields/class-acf-field-taxonomy.php:593
msgctxt "No Terms"
msgid "No %s"
msgstr "%s yok"

#: includes/fields/class-acf-field-number.php:240
msgid "Value must be equal to or lower than %d"
msgstr "Değer %d değerine eşit ya da daha küçük olmalı"

#: includes/fields/class-acf-field-number.php:235
msgid "Value must be equal to or higher than %d"
msgstr "Değer %d değerine eşit ya da daha büyük olmalı"

#: includes/fields/class-acf-field-number.php:223
msgid "Value must be a number"
msgstr "Değer bir sayı olmalı"

#: includes/fields/class-acf-field-number.php:22
msgid "Number"
msgstr "Numara"

#: includes/fields/class-acf-field-radio.php:254
msgid "Save 'other' values to the field's choices"
msgstr "‘Diğer’ değerlerini alanın seçenekleri arasına kaydet"

#: includes/fields/class-acf-field-radio.php:243
msgid "Add 'other' choice to allow for custom values"
msgstr "Özel değerlere izin vermek için 'diğer' seçeneği ekle"

#: includes/admin/views/global/navigation.php:202
msgid "Other"
msgstr "Diğer"

#: includes/fields/class-acf-field-radio.php:22
msgid "Radio Button"
msgstr "Radyo düğmesi"

#: includes/fields/class-acf-field-accordion.php:106
msgid ""
"Define an endpoint for the previous accordion to stop. This accordion will "
"not be visible."
msgstr ""
"Önceki akordeonun durması için bir son nokta tanımlayın. Bu akordeon "
"görüntülenmeyecek."

#: includes/fields/class-acf-field-accordion.php:95
msgid "Allow this accordion to open without closing others."
msgstr "Bu akordeonun diğerlerini kapatmadan açılmasını sağla."

#: includes/fields/class-acf-field-accordion.php:94
msgid "Multi-Expand"
msgstr "Çoklu genişletme"

#: includes/fields/class-acf-field-accordion.php:84
msgid "Display this accordion as open on page load."
msgstr "Sayfa yüklemesi sırasında bu akordeonu açık olarak görüntüle."

#: includes/fields/class-acf-field-accordion.php:83
msgid "Open"
msgstr "Açık"

#: includes/fields/class-acf-field-accordion.php:24
msgid "Accordion"
msgstr "Akordiyon"

#: includes/fields/class-acf-field-file.php:253
#: includes/fields/class-acf-field-file.php:265
msgid "Restrict which files can be uploaded"
msgstr "Yüklenebilecek dosyaları sınırlandırın"

#: includes/fields/class-acf-field-file.php:207
msgid "File ID"
msgstr "Dosya no"

#: includes/fields/class-acf-field-file.php:206
msgid "File URL"
msgstr "Dosya adresi"

#: includes/fields/class-acf-field-file.php:205
msgid "File Array"
msgstr "Dosya dizisi"

#: includes/fields/class-acf-field-file.php:176
msgid "Add File"
msgstr "Dosya Ekle"

#: includes/admin/tools/class-acf-admin-tool-import.php:151
#: includes/fields/class-acf-field-file.php:176
msgid "No file selected"
msgstr "Dosya seçilmedi"

#: includes/fields/class-acf-field-file.php:140
msgid "File name"
msgstr "Dosya adı"

#: includes/fields/class-acf-field-file.php:57
msgid "Update File"
msgstr "Dosyayı güncelle"

#: includes/fields/class-acf-field-file.php:56
msgid "Edit File"
msgstr "Dosya düzenle"

#: includes/admin/tools/class-acf-admin-tool-import.php:55
#: includes/fields/class-acf-field-file.php:55
msgid "Select File"
msgstr "Dosya seç"

#: includes/fields/class-acf-field-file.php:22
msgid "File"
msgstr "Dosya"

#: includes/fields/class-acf-field-password.php:22
msgid "Password"
msgstr "Parola"

#: includes/fields/class-acf-field-select.php:363
msgid "Specify the value returned"
msgstr "Dönecek değeri belirt"

#: includes/fields/class-acf-field-select.php:431
msgid "Use AJAX to lazy load choices?"
msgstr "Seçimlerin tembel yüklenmesi için AJAX kullanılsın mı?"

#: includes/fields/class-acf-field-checkbox.php:333
#: includes/fields/class-acf-field-select.php:352
msgid "Enter each default value on a new line"
msgstr "Her satıra bir değer girin"

#: includes/fields/class-acf-field-select.php:217 includes/media.php:48
msgctxt "verb"
msgid "Select"
msgstr "Seçim"

#: includes/fields/class-acf-field-select.php:95
msgctxt "Select2 JS load_fail"
msgid "Loading failed"
msgstr "Yükleme başarısız oldu"

#: includes/fields/class-acf-field-select.php:94
msgctxt "Select2 JS searching"
msgid "Searching&hellip;"
msgstr "Aranıyor&hellip;"

#: includes/fields/class-acf-field-select.php:93
msgctxt "Select2 JS load_more"
msgid "Loading more results&hellip;"
msgstr "Daha fazla sonuç yükleniyor&hellip;"

#. translators: %d - maximum number of items that can be selected in the select
#. field
#: includes/fields/class-acf-field-select.php:92
msgctxt "Select2 JS selection_too_long_n"
msgid "You can only select %d items"
msgstr "Sadece %d öge seçebilirsiniz"

#: includes/fields/class-acf-field-select.php:90
msgctxt "Select2 JS selection_too_long_1"
msgid "You can only select 1 item"
msgstr "Sadece 1 öge seçebilirsiniz"

#. translators: %d - number of characters that should be removed from select
#. field
#: includes/fields/class-acf-field-select.php:89
msgctxt "Select2 JS input_too_long_n"
msgid "Please delete %d characters"
msgstr "Lütfen %d karakter silin"

#: includes/fields/class-acf-field-select.php:87
msgctxt "Select2 JS input_too_long_1"
msgid "Please delete 1 character"
msgstr "Lütfen 1 karakter silin"

#. translators: %d - number of characters to enter into select field input
#: includes/fields/class-acf-field-select.php:86
msgctxt "Select2 JS input_too_short_n"
msgid "Please enter %d or more characters"
msgstr "Lütfen %d veya daha fazla karakter girin"

#: includes/fields/class-acf-field-select.php:84
msgctxt "Select2 JS input_too_short_1"
msgid "Please enter 1 or more characters"
msgstr "Lütfen 1 veya daha fazla karakter girin"

#: includes/fields/class-acf-field-select.php:83
msgctxt "Select2 JS matches_0"
msgid "No matches found"
msgstr "Eşleşme yok"

#. translators: %d - number of results available in select field
#: includes/fields/class-acf-field-select.php:82
msgctxt "Select2 JS matches_n"
msgid "%d results are available, use up and down arrow keys to navigate."
msgstr "%d sonuç bulundu. Dolaşmak için yukarı ve aşağı okları kullanın."

#: includes/fields/class-acf-field-select.php:80
msgctxt "Select2 JS matches_1"
msgid "One result is available, press enter to select it."
msgstr "Bir sonuç bulundu, seçmek için enter tuşuna basın."

#: includes/fields/class-acf-field-select.php:16
#: includes/fields/class-acf-field-taxonomy.php:699
msgctxt "noun"
msgid "Select"
msgstr "Seçim"

#: includes/fields/class-acf-field-user.php:102
msgid "User ID"
msgstr "Kullanıcı No"

#: includes/fields/class-acf-field-user.php:101
msgid "User Object"
msgstr "Kullanıcı nesnesi"

#: includes/fields/class-acf-field-user.php:100
msgid "User Array"
msgstr "Kullanıcı dizisi"

#: includes/fields/class-acf-field-user.php:88
msgid "All user roles"
msgstr "Bütün kullanıcı rolleri"

#: includes/fields/class-acf-field-user.php:80
msgid "Filter by Role"
msgstr "Role göre filtrele"

#: includes/fields/class-acf-field-user.php:15 includes/locations.php:103
msgid "User"
msgstr "Kullanıcı"

#: includes/fields/class-acf-field-separator.php:22
msgid "Separator"
msgstr "Ayırıcı"

#: includes/fields/class-acf-field-color_picker.php:69
msgid "Select Color"
msgstr "Renk seç"

#: includes/admin/post-types/admin-post-type.php:127
#: includes/admin/post-types/admin-taxonomy.php:129
#: includes/fields/class-acf-field-color_picker.php:67
msgid "Default"
msgstr "Varsayılan"

#: includes/admin/views/acf-post-type/advanced-settings.php:89
#: includes/admin/views/acf-taxonomy/advanced-settings.php:141
#: includes/fields/class-acf-field-color_picker.php:65
msgid "Clear"
msgstr "Temizle"

#: includes/fields/class-acf-field-color_picker.php:22
msgid "Color Picker"
msgstr "Renk seçici"

#: includes/fields/class-acf-field-date_time_picker.php:82
msgctxt "Date Time Picker JS pmTextShort"
msgid "P"
msgstr "P"

#: includes/fields/class-acf-field-date_time_picker.php:81
msgctxt "Date Time Picker JS pmText"
msgid "PM"
msgstr "PM"

#: includes/fields/class-acf-field-date_time_picker.php:78
msgctxt "Date Time Picker JS amTextShort"
msgid "A"
msgstr "A"

#: includes/fields/class-acf-field-date_time_picker.php:77
msgctxt "Date Time Picker JS amText"
msgid "AM"
msgstr "AM"

#: includes/fields/class-acf-field-date_time_picker.php:75
msgctxt "Date Time Picker JS selectText"
msgid "Select"
msgstr "Seçim"

#: includes/fields/class-acf-field-date_time_picker.php:74
msgctxt "Date Time Picker JS closeText"
msgid "Done"
msgstr "Bitti"

#: includes/fields/class-acf-field-date_time_picker.php:73
msgctxt "Date Time Picker JS currentText"
msgid "Now"
msgstr "Şimdi"

#: includes/fields/class-acf-field-date_time_picker.php:72
msgctxt "Date Time Picker JS timezoneText"
msgid "Time Zone"
msgstr "Zaman dilimi"

#: includes/fields/class-acf-field-date_time_picker.php:71
msgctxt "Date Time Picker JS microsecText"
msgid "Microsecond"
msgstr "Mikrosaniye"

#: includes/fields/class-acf-field-date_time_picker.php:70
msgctxt "Date Time Picker JS millisecText"
msgid "Millisecond"
msgstr "Milisaniye"

#: includes/fields/class-acf-field-date_time_picker.php:69
msgctxt "Date Time Picker JS secondText"
msgid "Second"
msgstr "İkinci"

#: includes/fields/class-acf-field-date_time_picker.php:68
msgctxt "Date Time Picker JS minuteText"
msgid "Minute"
msgstr "Dakika"

#: includes/fields/class-acf-field-date_time_picker.php:67
msgctxt "Date Time Picker JS hourText"
msgid "Hour"
msgstr "Saat"

#: includes/fields/class-acf-field-date_time_picker.php:66
msgctxt "Date Time Picker JS timeText"
msgid "Time"
msgstr "Zaman"

#: includes/fields/class-acf-field-date_time_picker.php:65
msgctxt "Date Time Picker JS timeOnlyTitle"
msgid "Choose Time"
msgstr "Zamanı se"

#: includes/fields/class-acf-field-date_time_picker.php:22
msgid "Date Time Picker"
msgstr "Tarih zaman seçici"

#: includes/fields/class-acf-field-accordion.php:105
msgid "Endpoint"
msgstr "Uç nokta"

#: includes/admin/views/acf-field-group/options.php:130
#: includes/fields/class-acf-field-tab.php:112
msgid "Left aligned"
msgstr "Sola hizalı"

#: includes/admin/views/acf-field-group/options.php:129
#: includes/fields/class-acf-field-tab.php:111
msgid "Top aligned"
msgstr "Üste hizalı"

#: includes/fields/class-acf-field-tab.php:107
msgid "Placement"
msgstr "Konumlandırma"

#: includes/fields/class-acf-field-tab.php:23
msgid "Tab"
msgstr "Sekme"

#: includes/fields/class-acf-field-url.php:138
msgid "Value must be a valid URL"
msgstr "Değer geçerli bir web adresi olmalı"

#: includes/fields/class-acf-field-link.php:153
msgid "Link URL"
msgstr "Bağlantı adresi"

#: includes/fields/class-acf-field-link.php:152
msgid "Link Array"
msgstr "Bağlantı dizisi"

#: includes/fields/class-acf-field-link.php:124
msgid "Opens in a new window/tab"
msgstr "Yeni pencerede/sekmede açılır"

#: includes/fields/class-acf-field-link.php:119
msgid "Select Link"
msgstr "Bağlantı seç"

#: includes/fields/class-acf-field-link.php:22
msgid "Link"
msgstr "Link"

#: includes/fields/class-acf-field-email.php:22
msgid "Email"
msgstr "EPosta"

#: includes/fields/class-acf-field-number.php:173
#: includes/fields/class-acf-field-range.php:206
msgid "Step Size"
msgstr "Adım boyutu"

#: includes/fields/class-acf-field-number.php:143
#: includes/fields/class-acf-field-range.php:184
msgid "Maximum Value"
msgstr "En fazla değer"

#: includes/fields/class-acf-field-number.php:133
#: includes/fields/class-acf-field-range.php:173
msgid "Minimum Value"
msgstr "En az değer"

#: includes/fields/class-acf-field-range.php:22
msgid "Range"
msgstr "Aralık"

#: includes/fields/class-acf-field-button-group.php:165
#: includes/fields/class-acf-field-checkbox.php:350
#: includes/fields/class-acf-field-radio.php:210
#: includes/fields/class-acf-field-select.php:370
msgid "Both (Array)"
msgstr "İkisi de (Dizi)"

#: includes/admin/views/acf-field-group/fields.php:52
#: includes/fields/class-acf-field-button-group.php:164
#: includes/fields/class-acf-field-checkbox.php:349
#: includes/fields/class-acf-field-radio.php:209
#: includes/fields/class-acf-field-select.php:369
msgid "Label"
msgstr "Etiket"

#: includes/fields/class-acf-field-button-group.php:163
#: includes/fields/class-acf-field-checkbox.php:348
#: includes/fields/class-acf-field-radio.php:208
#: includes/fields/class-acf-field-select.php:368
msgid "Value"
msgstr "Değer"

#: includes/fields/class-acf-field-button-group.php:211
#: includes/fields/class-acf-field-checkbox.php:411
#: includes/fields/class-acf-field-radio.php:282
msgid "Vertical"
msgstr "Dikey"

#: includes/fields/class-acf-field-button-group.php:210
#: includes/fields/class-acf-field-checkbox.php:412
#: includes/fields/class-acf-field-radio.php:283
msgid "Horizontal"
msgstr "Yatay"

#: includes/fields/class-acf-field-button-group.php:138
#: includes/fields/class-acf-field-checkbox.php:323
#: includes/fields/class-acf-field-radio.php:183
#: includes/fields/class-acf-field-select.php:341
msgid "red : Red"
msgstr "kirmizi : Kırmızı"

#: includes/fields/class-acf-field-button-group.php:138
#: includes/fields/class-acf-field-checkbox.php:323
#: includes/fields/class-acf-field-radio.php:183
#: includes/fields/class-acf-field-select.php:341
msgid "For more control, you may specify both a value and label like this:"
msgstr ""
"Daha fazla kontrol için, hem bir değeri hem de bir etiketi şu şekilde "
"belirtebilirsiniz:"

#: includes/fields/class-acf-field-button-group.php:138
#: includes/fields/class-acf-field-checkbox.php:323
#: includes/fields/class-acf-field-radio.php:183
#: includes/fields/class-acf-field-select.php:341
msgid "Enter each choice on a new line."
msgstr "Her seçeneği yeni bir satıra girin."

#: includes/fields/class-acf-field-button-group.php:137
#: includes/fields/class-acf-field-checkbox.php:322
#: includes/fields/class-acf-field-radio.php:182
#: includes/fields/class-acf-field-select.php:340
msgid "Choices"
msgstr "Seçimler"

#: includes/fields/class-acf-field-button-group.php:23
msgid "Button Group"
msgstr "Tuş grubu"

#: includes/fields/class-acf-field-button-group.php:183
#: includes/fields/class-acf-field-page_link.php:519
#: includes/fields/class-acf-field-post_object.php:432
#: includes/fields/class-acf-field-radio.php:228
#: includes/fields/class-acf-field-select.php:399
#: includes/fields/class-acf-field-taxonomy.php:708
#: includes/fields/class-acf-field-user.php:132
msgid "Allow Null"
msgstr "Null değere izin ver"

#: includes/fields/class-acf-field-page_link.php:273
#: includes/fields/class-acf-field-post_object.php:254
#: includes/fields/class-acf-field-taxonomy.php:872
msgid "Parent"
msgstr "Ana"

#: includes/fields/class-acf-field-wysiwyg.php:367
msgid "TinyMCE will not be initialized until field is clicked"
msgstr "Alan tıklanana kadar TinyMCE hazırlanmayacaktır"

#: includes/fields/class-acf-field-wysiwyg.php:366
msgid "Delay Initialization"
msgstr "Hazırlık geciktirme"

#: includes/fields/class-acf-field-wysiwyg.php:355
msgid "Show Media Upload Buttons"
msgstr "Ortam yükleme tuşları gösterilsin"

#: includes/fields/class-acf-field-wysiwyg.php:339
msgid "Toolbar"
msgstr "Araç çubuğu"

#: includes/fields/class-acf-field-wysiwyg.php:331
msgid "Text Only"
msgstr "Sadece Metin"

#: includes/fields/class-acf-field-wysiwyg.php:330
msgid "Visual Only"
msgstr "Sadece görsel"

#: includes/fields/class-acf-field-wysiwyg.php:329
msgid "Visual & Text"
msgstr "Görsel ve metin"

#: includes/fields/class-acf-field-icon_picker.php:262
#: includes/fields/class-acf-field-wysiwyg.php:324
msgid "Tabs"
msgstr "Seklemeler"

#: includes/fields/class-acf-field-wysiwyg.php:268
msgid "Click to initialize TinyMCE"
msgstr "TinyMCE hazırlamak için tıklayın"

#: includes/fields/class-acf-field-wysiwyg.php:262
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr "Metin"

#: includes/fields/class-acf-field-wysiwyg.php:261
msgid "Visual"
msgstr "Görsel"

#: includes/fields/class-acf-field-text.php:181
#: includes/fields/class-acf-field-textarea.php:217
msgid "Value must not exceed %d characters"
msgstr "Değer %d karakteri geçmemelidir"

#: includes/fields/class-acf-field-text.php:116
#: includes/fields/class-acf-field-textarea.php:114
msgid "Leave blank for no limit"
msgstr "Limit olmaması için boş bırakın"

#: includes/fields/class-acf-field-text.php:115
#: includes/fields/class-acf-field-textarea.php:113
msgid "Character Limit"
msgstr "Karakter limiti"

#: includes/fields/class-acf-field-email.php:144
#: includes/fields/class-acf-field-number.php:194
#: includes/fields/class-acf-field-password.php:95
#: includes/fields/class-acf-field-range.php:228
#: includes/fields/class-acf-field-text.php:156
msgid "Appears after the input"
msgstr "Girdi alanından sonra görünür"

#: includes/fields/class-acf-field-email.php:143
#: includes/fields/class-acf-field-number.php:193
#: includes/fields/class-acf-field-password.php:94
#: includes/fields/class-acf-field-range.php:227
#: includes/fields/class-acf-field-text.php:155
msgid "Append"
msgstr "Sonuna ekle"

#: includes/fields/class-acf-field-email.php:134
#: includes/fields/class-acf-field-number.php:184
#: includes/fields/class-acf-field-password.php:85
#: includes/fields/class-acf-field-range.php:218
#: includes/fields/class-acf-field-text.php:146
msgid "Appears before the input"
msgstr "Girdi alanından önce görünür"

#: includes/fields/class-acf-field-email.php:133
#: includes/fields/class-acf-field-number.php:183
#: includes/fields/class-acf-field-password.php:84
#: includes/fields/class-acf-field-range.php:217
#: includes/fields/class-acf-field-text.php:145
msgid "Prepend"
msgstr "Önüne ekle"

#: includes/fields/class-acf-field-email.php:124
#: includes/fields/class-acf-field-number.php:164
#: includes/fields/class-acf-field-password.php:75
#: includes/fields/class-acf-field-text.php:136
#: includes/fields/class-acf-field-textarea.php:146
#: includes/fields/class-acf-field-url.php:105
msgid "Appears within the input"
msgstr "Girdi alanının içinde görünür"

#: includes/fields/class-acf-field-email.php:123
#: includes/fields/class-acf-field-number.php:163
#: includes/fields/class-acf-field-password.php:74
#: includes/fields/class-acf-field-text.php:135
#: includes/fields/class-acf-field-textarea.php:145
#: includes/fields/class-acf-field-url.php:104
msgid "Placeholder Text"
msgstr "Yer tutucu metin"

#: includes/fields/class-acf-field-button-group.php:148
#: includes/fields/class-acf-field-email.php:104
#: includes/fields/class-acf-field-number.php:114
#: includes/fields/class-acf-field-radio.php:193
#: includes/fields/class-acf-field-range.php:154
#: includes/fields/class-acf-field-text.php:96
#: includes/fields/class-acf-field-textarea.php:94
#: includes/fields/class-acf-field-url.php:85
#: includes/fields/class-acf-field-wysiwyg.php:292
msgid "Appears when creating a new post"
msgstr "Yeni bir yazı oluştururken görünür"

#: includes/fields/class-acf-field-text.php:22
msgid "Text"
msgstr "Metin"

#: includes/fields/class-acf-field-relationship.php:753
msgid "%1$s requires at least %2$s selection"
msgid_plural "%1$s requires at least %2$s selections"
msgstr[0] "%1$s en az %2$s seçim gerektirir"
msgstr[1] "%1$s en az %2$s seçim gerektirir"

#: includes/fields/class-acf-field-post_object.php:402
#: includes/fields/class-acf-field-relationship.php:616
msgid "Post ID"
msgstr "Yazı ID"

#: includes/fields/class-acf-field-post_object.php:15
#: includes/fields/class-acf-field-post_object.php:401
#: includes/fields/class-acf-field-relationship.php:615
msgid "Post Object"
msgstr "Yazı nesnesi"

#: includes/fields/class-acf-field-relationship.php:648
msgid "Maximum Posts"
msgstr "En fazla yazı"

#: includes/fields/class-acf-field-relationship.php:638
msgid "Minimum Posts"
msgstr "En az gönderi"

#: includes/admin/views/acf-field-group/options.php:183
#: includes/admin/views/acf-post-type/advanced-settings.php:29
#: includes/fields/class-acf-field-relationship.php:673
msgid "Featured Image"
msgstr "Öne çıkan görsel"

#: includes/fields/class-acf-field-relationship.php:669
msgid "Selected elements will be displayed in each result"
msgstr "Her sonuç içinde seçilmiş elemanlar görüntülenir"

#: includes/fields/class-acf-field-relationship.php:668
msgid "Elements"
msgstr "Elemanlar"

#: includes/fields/class-acf-field-relationship.php:602
#: includes/fields/class-acf-field-taxonomy.php:20
#: includes/fields/class-acf-field-taxonomy.php:628
#: includes/locations/class-acf-location-taxonomy.php:22
msgid "Taxonomy"
msgstr "Etiketleme"

#: includes/fields/class-acf-field-relationship.php:601
#: includes/locations/class-acf-location-post-type.php:22
#: includes/post-types/class-acf-post-type.php:92
msgid "Post Type"
msgstr "Yazı tipi"

#: includes/fields/class-acf-field-relationship.php:595
msgid "Filters"
msgstr "Filtreler"

#: includes/fields/class-acf-field-page_link.php:480
#: includes/fields/class-acf-field-post_object.php:389
#: includes/fields/class-acf-field-relationship.php:588
msgid "All taxonomies"
msgstr "Tüm taksonomiler"

#: includes/fields/class-acf-field-page_link.php:472
#: includes/fields/class-acf-field-post_object.php:381
#: includes/fields/class-acf-field-relationship.php:580
msgid "Filter by Taxonomy"
msgstr "Taksonomiye göre filtre"

#: includes/fields/class-acf-field-page_link.php:450
#: includes/fields/class-acf-field-post_object.php:359
#: includes/fields/class-acf-field-relationship.php:558
msgid "All post types"
msgstr "Tüm yazı tipleri"

#: includes/fields/class-acf-field-page_link.php:442
#: includes/fields/class-acf-field-post_object.php:351
#: includes/fields/class-acf-field-relationship.php:550
msgid "Filter by Post Type"
msgstr "Yazı tipine göre filtre"

#: includes/fields/class-acf-field-relationship.php:450
msgid "Search..."
msgstr "Ara..."

#: includes/fields/class-acf-field-relationship.php:380
msgid "Select taxonomy"
msgstr "Taksonomi seç"

#: includes/fields/class-acf-field-relationship.php:372
msgid "Select post type"
msgstr "Yazı tipi seç"

#: includes/fields/class-acf-field-relationship.php:78
msgid "No matches found"
msgstr "Eşleşme yok"

#: includes/fields/class-acf-field-relationship.php:77
msgid "Loading"
msgstr "Yükleniyor"

#: includes/fields/class-acf-field-relationship.php:76
msgid "Maximum values reached ( {max} values )"
msgstr "En yüksek değerlere ulaşıldı ({max} değerleri)"

#: includes/fields/class-acf-field-relationship.php:17
msgid "Relationship"
msgstr "İlişkili"

#: includes/fields/class-acf-field-file.php:277
#: includes/fields/class-acf-field-image.php:307
msgid "Comma separated list. Leave blank for all types"
msgstr "Virgül ile ayrılmış liste. Tüm tipler için boş bırakın"

#: includes/fields/class-acf-field-file.php:276
#: includes/fields/class-acf-field-image.php:306
msgid "Allowed File Types"
msgstr "İzin verilen dosya tipleri"

#: includes/fields/class-acf-field-file.php:264
#: includes/fields/class-acf-field-image.php:270
msgid "Maximum"
msgstr "En fazla"

#: includes/fields/class-acf-field-file.php:144
#: includes/fields/class-acf-field-file.php:256
#: includes/fields/class-acf-field-file.php:268
#: includes/fields/class-acf-field-image.php:261
#: includes/fields/class-acf-field-image.php:297
msgid "File size"
msgstr "Dosya boyutu"

#: includes/fields/class-acf-field-image.php:235
#: includes/fields/class-acf-field-image.php:271
msgid "Restrict which images can be uploaded"
msgstr "Hangi görsellerin yüklenebileceğini sınırlandırın"

#: includes/fields/class-acf-field-file.php:252
#: includes/fields/class-acf-field-image.php:234
msgid "Minimum"
msgstr "En az"

#: includes/fields/class-acf-field-file.php:222
#: includes/fields/class-acf-field-image.php:200
msgid "Uploaded to post"
msgstr "Yazıya yüklendi"

#: includes/fields/class-acf-field-file.php:221
#: includes/fields/class-acf-field-image.php:199
#: includes/locations/class-acf-location-attachment.php:73
#: includes/locations/class-acf-location-comment.php:61
#: includes/locations/class-acf-location-nav-menu.php:74
#: includes/locations/class-acf-location-taxonomy.php:63
#: includes/locations/class-acf-location-user-form.php:71
#: includes/locations/class-acf-location-user-role.php:78
#: includes/locations/class-acf-location-widget.php:65
msgid "All"
msgstr "Tümü"

#: includes/fields/class-acf-field-file.php:216
#: includes/fields/class-acf-field-image.php:194
msgid "Limit the media library choice"
msgstr "Ortam kitaplığı seçimini sınırlayın"

#: includes/fields/class-acf-field-file.php:215
#: includes/fields/class-acf-field-image.php:193
msgid "Library"
msgstr "Kitaplık"

#: includes/fields/class-acf-field-image.php:326
msgid "Preview Size"
msgstr "Önizleme boyutu"

#: includes/fields/class-acf-field-image.php:185
msgid "Image ID"
msgstr "Görsel no"

#: includes/fields/class-acf-field-image.php:184
msgid "Image URL"
msgstr "Resim Adresi"

#: includes/fields/class-acf-field-image.php:183
msgid "Image Array"
msgstr "Görsel dizisi"

#: includes/fields/class-acf-field-button-group.php:158
#: includes/fields/class-acf-field-checkbox.php:343
#: includes/fields/class-acf-field-file.php:200
#: includes/fields/class-acf-field-link.php:147
#: includes/fields/class-acf-field-radio.php:203
msgid "Specify the returned value on front end"
msgstr "Ön yüzden dönecek değeri belirleyin"

#: includes/fields/class-acf-field-button-group.php:157
#: includes/fields/class-acf-field-checkbox.php:342
#: includes/fields/class-acf-field-file.php:199
#: includes/fields/class-acf-field-link.php:146
#: includes/fields/class-acf-field-radio.php:202
#: includes/fields/class-acf-field-taxonomy.php:672
msgid "Return Value"
msgstr "Dönüş değeri"

#: includes/fields/class-acf-field-image.php:155
msgid "Add Image"
msgstr "Görsel ekle"

#: includes/fields/class-acf-field-image.php:155
msgid "No image selected"
msgstr "Resim seçilmedi"

#: includes/assets.php:353 includes/fields/class-acf-field-file.php:152
#: includes/fields/class-acf-field-image.php:135
#: includes/fields/class-acf-field-link.php:124
msgid "Remove"
msgstr "Kaldır"

#: includes/admin/views/acf-field-group/field.php:89
#: includes/fields/class-acf-field-file.php:150
#: includes/fields/class-acf-field-image.php:133
#: includes/fields/class-acf-field-link.php:124
msgid "Edit"
msgstr "Düzenle"

#: includes/fields/class-acf-field-image.php:63 includes/media.php:55
msgid "All images"
msgstr "Tüm görseller"

#: includes/fields/class-acf-field-image.php:62
msgid "Update Image"
msgstr "Görseli güncelle"

#: includes/fields/class-acf-field-image.php:61
msgid "Edit Image"
msgstr "Resmi düzenle"

#: includes/fields/class-acf-field-image.php:60
msgid "Select Image"
msgstr "Resim Seç"

#: includes/fields/class-acf-field-image.php:22
msgid "Image"
msgstr "Görsel"

#: includes/fields/class-acf-field-message.php:113
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr "Görünür metin olarak HTML kodlamasının görüntülenmesine izin ver"

#: includes/fields/class-acf-field-message.php:112
msgid "Escape HTML"
msgstr "HTML’i güvenli hale getir"

#: includes/fields/class-acf-field-message.php:104
#: includes/fields/class-acf-field-textarea.php:162
msgid "No Formatting"
msgstr "Biçimlendirme yok"

#: includes/fields/class-acf-field-message.php:103
#: includes/fields/class-acf-field-textarea.php:161
msgid "Automatically add &lt;br&gt;"
msgstr "Otomatik ekle &lt;br&gt;"

#: includes/fields/class-acf-field-message.php:102
#: includes/fields/class-acf-field-textarea.php:160
msgid "Automatically add paragraphs"
msgstr "Otomatik paragraf ekle"

#: includes/fields/class-acf-field-message.php:98
#: includes/fields/class-acf-field-textarea.php:156
msgid "Controls how new lines are rendered"
msgstr "Yeni satırların nasıl görüntüleneceğini denetler"

#: includes/fields/class-acf-field-message.php:97
#: includes/fields/class-acf-field-textarea.php:155
msgid "New Lines"
msgstr "Yeni satırlar"

#: includes/fields/class-acf-field-date_picker.php:221
#: includes/fields/class-acf-field-date_time_picker.php:208
msgid "Week Starts On"
msgstr "Hafta başlangıcı"

#: includes/fields/class-acf-field-date_picker.php:190
msgid "The format used when saving a value"
msgstr "Bir değer kaydedilirken kullanılacak biçim"

#: includes/fields/class-acf-field-date_picker.php:189
msgid "Save Format"
msgstr "Biçimi kaydet"

#: includes/fields/class-acf-field-date_picker.php:61
msgctxt "Date Picker JS weekHeader"
msgid "Wk"
msgstr "Hf"

#: includes/fields/class-acf-field-date_picker.php:60
msgctxt "Date Picker JS prevText"
msgid "Prev"
msgstr "Önceki"

#: includes/fields/class-acf-field-date_picker.php:59
msgctxt "Date Picker JS nextText"
msgid "Next"
msgstr "Sonraki"

#: includes/fields/class-acf-field-date_picker.php:58
msgctxt "Date Picker JS currentText"
msgid "Today"
msgstr "Bugün"

#: includes/fields/class-acf-field-date_picker.php:57
msgctxt "Date Picker JS closeText"
msgid "Done"
msgstr "Bitti"

#: includes/fields/class-acf-field-date_picker.php:22
msgid "Date Picker"
msgstr "Tarih seçici"

#: includes/fields/class-acf-field-image.php:238
#: includes/fields/class-acf-field-image.php:274
#: includes/fields/class-acf-field-oembed.php:241
msgid "Width"
msgstr "Genişlik"

#: includes/fields/class-acf-field-oembed.php:238
#: includes/fields/class-acf-field-oembed.php:250
msgid "Embed Size"
msgstr "Gömme boyutu"

#: includes/fields/class-acf-field-oembed.php:198
msgid "Enter URL"
msgstr "Adres girin"

#: includes/fields/class-acf-field-oembed.php:22
msgid "oEmbed"
msgstr "oEmbed"

#: includes/fields/class-acf-field-true_false.php:172
msgid "Text shown when inactive"
msgstr "Etkin değilken görüntülenen metin"

#: includes/fields/class-acf-field-true_false.php:171
msgid "Off Text"
msgstr "Kapalı metni"

#: includes/fields/class-acf-field-true_false.php:156
msgid "Text shown when active"
msgstr "Etkinken görüntülenen metin"

#: includes/fields/class-acf-field-true_false.php:155
msgid "On Text"
msgstr "Açık metni"

#: includes/fields/class-acf-field-select.php:420
#: includes/fields/class-acf-field-true_false.php:187
msgid "Stylized UI"
msgstr "Stilize edilmiş kullanıcı arabirimi"

#: includes/fields/class-acf-field-button-group.php:147
#: includes/fields/class-acf-field-checkbox.php:332
#: includes/fields/class-acf-field-color_picker.php:144
#: includes/fields/class-acf-field-email.php:103
#: includes/fields/class-acf-field-number.php:113
#: includes/fields/class-acf-field-radio.php:192
#: includes/fields/class-acf-field-range.php:153
#: includes/fields/class-acf-field-select.php:351
#: includes/fields/class-acf-field-text.php:95
#: includes/fields/class-acf-field-textarea.php:93
#: includes/fields/class-acf-field-true_false.php:135
#: includes/fields/class-acf-field-url.php:84
#: includes/fields/class-acf-field-wysiwyg.php:291
msgid "Default Value"
msgstr "Varsayılan değer"

#: includes/fields/class-acf-field-true_false.php:126
msgid "Displays text alongside the checkbox"
msgstr "İşaret kutusunun yanında görüntülenen metin"

#: includes/fields/class-acf-field-message.php:23
#: includes/fields/class-acf-field-message.php:87
#: includes/fields/class-acf-field-true_false.php:125
msgid "Message"
msgstr "Mesaj"

#: includes/assets.php:352 includes/fields/class-acf-field-true_false.php:79
#: includes/fields/class-acf-field-true_false.php:175
#: src/Site_Health/Site_Health.php:281 src/Site_Health/Site_Health.php:343
msgid "No"
msgstr "Hayır"

#: includes/assets.php:351 includes/fields/class-acf-field-true_false.php:76
#: includes/fields/class-acf-field-true_false.php:159
#: src/Site_Health/Site_Health.php:280 src/Site_Health/Site_Health.php:343
msgid "Yes"
msgstr "Evet"

#: includes/fields/class-acf-field-true_false.php:22
msgid "True / False"
msgstr "Doğru / yanlış"

#: includes/fields/class-acf-field-group.php:415
msgid "Row"
msgstr "Satır"

#: includes/fields/class-acf-field-group.php:414
msgid "Table"
msgstr "Tablo"

#: includes/admin/post-types/admin-field-group.php:158
#: includes/fields/class-acf-field-group.php:413
msgid "Block"
msgstr "Blok"

#: includes/fields/class-acf-field-group.php:408
msgid "Specify the style used to render the selected fields"
msgstr "Seçili alanları görüntülemek için kullanılacak stili belirtin"

#: includes/fields.php:330 includes/fields/class-acf-field-button-group.php:204
#: includes/fields/class-acf-field-checkbox.php:405
#: includes/fields/class-acf-field-group.php:407
#: includes/fields/class-acf-field-radio.php:276
msgid "Layout"
msgstr "Yerleşim"

#: includes/fields/class-acf-field-group.php:391
msgid "Sub Fields"
msgstr "Alt alanlar"

#: includes/fields/class-acf-field-group.php:22
msgid "Group"
msgstr "Grup"

#: includes/fields/class-acf-field-google-map.php:222
msgid "Customize the map height"
msgstr "Harita yüksekliğini özelleştir"

#: includes/fields/class-acf-field-google-map.php:221
#: includes/fields/class-acf-field-image.php:249
#: includes/fields/class-acf-field-image.php:285
#: includes/fields/class-acf-field-oembed.php:253
msgid "Height"
msgstr "Ağırlık"

#: includes/fields/class-acf-field-google-map.php:210
msgid "Set the initial zoom level"
msgstr "Temel yaklaşma seviyesini belirle"

#: includes/fields/class-acf-field-google-map.php:209
msgid "Zoom"
msgstr "Yakınlaşma"

#: includes/fields/class-acf-field-google-map.php:183
#: includes/fields/class-acf-field-google-map.php:196
msgid "Center the initial map"
msgstr "Haritayı ortala"

#: includes/fields/class-acf-field-google-map.php:182
#: includes/fields/class-acf-field-google-map.php:195
msgid "Center"
msgstr "Merkez"

#: includes/fields/class-acf-field-google-map.php:154
msgid "Search for address..."
msgstr "Adres arayın…"

#: includes/fields/class-acf-field-google-map.php:151
msgid "Find current location"
msgstr "Şu anki konumu bul"

#: includes/fields/class-acf-field-google-map.php:150
msgid "Clear location"
msgstr "Konumu temizle"

#: includes/fields/class-acf-field-google-map.php:149
#: includes/fields/class-acf-field-relationship.php:600
msgid "Search"
msgstr "Ara"

#: includes/fields/class-acf-field-google-map.php:57
msgid "Sorry, this browser does not support geolocation"
msgstr "Üzgünüz, bu tarayıcı konumlandırma desteklemiyor"

#: includes/fields/class-acf-field-google-map.php:22
msgid "Google Map"
msgstr "Google haritası"

#: includes/fields/class-acf-field-date_picker.php:201
#: includes/fields/class-acf-field-date_time_picker.php:189
#: includes/fields/class-acf-field-time_picker.php:122
msgid "The format returned via template functions"
msgstr "Tema işlevlerinden dönen biçim"

#: includes/fields/class-acf-field-color_picker.php:168
#: includes/fields/class-acf-field-date_picker.php:200
#: includes/fields/class-acf-field-date_time_picker.php:188
#: includes/fields/class-acf-field-icon_picker.php:285
#: includes/fields/class-acf-field-image.php:177
#: includes/fields/class-acf-field-post_object.php:396
#: includes/fields/class-acf-field-relationship.php:610
#: includes/fields/class-acf-field-select.php:362
#: includes/fields/class-acf-field-time_picker.php:121
#: includes/fields/class-acf-field-user.php:95
msgid "Return Format"
msgstr "Dönüş biçimi"

#: includes/fields/class-acf-field-date_picker.php:179
#: includes/fields/class-acf-field-date_picker.php:210
#: includes/fields/class-acf-field-date_time_picker.php:180
#: includes/fields/class-acf-field-date_time_picker.php:198
#: includes/fields/class-acf-field-time_picker.php:113
#: includes/fields/class-acf-field-time_picker.php:129
msgid "Custom:"
msgstr "Özel:"

#: includes/fields/class-acf-field-date_picker.php:171
#: includes/fields/class-acf-field-date_time_picker.php:171
#: includes/fields/class-acf-field-time_picker.php:106
msgid "The format displayed when editing a post"
msgstr "Bir yazı düzenlenirken görüntülenecek biçim"

#: includes/fields/class-acf-field-date_picker.php:170
#: includes/fields/class-acf-field-date_time_picker.php:170
#: includes/fields/class-acf-field-time_picker.php:105
msgid "Display Format"
msgstr "Gösterim biçimi"

#: includes/fields/class-acf-field-time_picker.php:22
msgid "Time Picker"
msgstr "Zaman seçici"

#. translators: counts for inactive field groups
#: acf.php:526
msgid "Inactive <span class=\"count\">(%s)</span>"
msgid_plural "Inactive <span class=\"count\">(%s)</span>"
msgstr[0] "Devre dışı <span class=\"count\">(%s)</span>"
msgstr[1] "Devre dışı <span class=\"count\">(%s)</span>"

#: acf.php:487
msgid "No Fields found in Trash"
msgstr "Çöpte alan bulunamadı"

#: acf.php:486
msgid "No Fields found"
msgstr "Hiç alan bulunamadı"

#: acf.php:485
msgid "Search Fields"
msgstr "Alanlarda ara"

#: acf.php:484
msgid "View Field"
msgstr "Alanı görüntüle"

#: acf.php:483 includes/admin/views/acf-field-group/fields.php:113
msgid "New Field"
msgstr "Yeni alan"

#: acf.php:482
msgid "Edit Field"
msgstr "Alanı düzenle"

#: acf.php:481
msgid "Add New Field"
msgstr "Yeni elan ekle"

#: acf.php:479
msgid "Field"
msgstr "Alan"

#: acf.php:478 includes/admin/post-types/admin-field-group.php:179
#: includes/admin/post-types/admin-field-groups.php:93
#: includes/admin/views/acf-field-group/fields.php:32
msgid "Fields"
msgstr "Alanlar"

#: acf.php:453
msgid "No Field Groups found in Trash"
msgstr "Çöpte alan grubu bulunamadı"

#: acf.php:452
msgid "No Field Groups found"
msgstr "Hiç alan grubu bulunamadı"

#: acf.php:451
msgid "Search Field Groups"
msgstr "Alan gruplarında ara"

#: acf.php:450
msgid "View Field Group"
msgstr "Alan grubunu görüntüle"

#: acf.php:449
msgid "New Field Group"
msgstr "Yeni alan grubu"

#: acf.php:448
msgid "Edit Field Group"
msgstr "Alan grubunu düzenle"

#: acf.php:447
msgid "Add New Field Group"
msgstr "Yeni alan grubu ekle"

#: acf.php:446 acf.php:480
#: includes/admin/views/acf-post-type/advanced-settings.php:224
#: includes/post-types/class-acf-post-type.php:93
#: includes/post-types/class-acf-taxonomy.php:92
msgid "Add New"
msgstr "Yeni ekle"

#: acf.php:445
msgid "Field Group"
msgstr "Alan grubu"

#: acf.php:444 includes/admin/post-types/admin-field-groups.php:55
#: includes/admin/post-types/admin-post-types.php:113
#: includes/admin/post-types/admin-taxonomies.php:112
msgid "Field Groups"
msgstr "Alan grupları"

#. Description of the plugin
#: acf.php
msgid "Customize WordPress with powerful, professional and intuitive fields."
msgstr "Güçlü, profesyonel ve sezgisel alanlar ile WordPress'i özelleştirin."

#. Plugin URI of the plugin
#: acf.php
msgid "https://www.advancedcustomfields.com"
msgstr "https://www.advancedcustomfields.com"

#. Plugin Name of the plugin
#: acf.php acf.php:290
msgid "Advanced Custom Fields"
msgstr "Advanced Custom Fields"

#: pro/acf-pro.php:27
msgid "Advanced Custom Fields PRO"
msgstr "Advanced Custom Fields PRO"

#: pro/blocks.php:170
msgid "Block type name is required."
msgstr "Blok türü adı gereklidir."

#. translators: The name of the block type
#: pro/blocks.php:178
msgid "Block type \"%s\" is already registered."
msgstr "Blok türü \"%s\" zaten kayıtlı."

#: pro/blocks.php:726
msgid "Switch to Edit"
msgstr "Düzenlemeye geç"

#: pro/blocks.php:727
msgid "Switch to Preview"
msgstr "Önizlemeye geç"

#: pro/blocks.php:728
msgid "Change content alignment"
msgstr "İçerik hizalamasını değiştir"

#. translators: %s: Block type title
#: pro/blocks.php:731
msgid "%s settings"
msgstr "%s ayarları"

#: pro/blocks.php:936
msgid "This block contains no editable fields."
msgstr ""

#. translators: %s: an admin URL to the field group edit screen
#: pro/blocks.php:942
msgid ""
"Assign a <a href=\"%s\" target=\"_blank\">field group</a> to add fields to "
"this block."
msgstr ""

#: pro/options-page.php:78
msgid "Options Updated"
msgstr "Seçenekler güncellendi"

#: pro/updates.php:99
msgid ""
"To enable updates, please enter your license key on the <a "
"href=\"%1$s\">Updates</a> page. If you don't have a licence key, please see "
"<a href=\"%2$s\" target=\"_blank\">details & pricing</a>."
msgstr ""
"Güncellemeleri etkinleştirmek için lütfen <a href=\"%1$s\">Güncellemeler</a> "
"sayfasında lisans anahtarınızı girin. Eğer bir lisans anahtarınız yoksa "
"lütfen <a href=\"%2$s\">detaylar ve fiyatlama</a> sayfasına bakın."

#: pro/updates.php:159
msgid ""
"<b>ACF Activation Error</b>. Your defined license key has changed, but an "
"error occurred when deactivating your old licence"
msgstr ""
"<b>ACF etkinleştirme hatası</b>. Tanımlı lisans anahtarınız değişti, ancak "
"eski lisansınızı devre dışı bırakırken bir hata oluştu"

#: pro/updates.php:154
msgid ""
"<b>ACF Activation Error</b>. Your defined license key has changed, but an "
"error occurred when connecting to activation server"
msgstr ""
"<b>ACF etkinleştirme hatası</b>. Tanımlı lisans anahtarınız değişti, ancak "
"etkinleştirme sunucusuna bağlanırken bir hata oluştu"

#: pro/updates.php:192
msgid "<b>ACF Activation Error</b>"
msgstr "<b>ACF etkinleştirme hatası</b>"

#: pro/updates.php:187
msgid ""
"<b>ACF Activation Error</b>. An error occurred when connecting to activation "
"server"
msgstr ""
"<b>ACF etkinleştirme hatası</b>. Etkinleştirme sunucusuna bağlanırken bir "
"hata oluştu"

#: pro/updates.php:279
msgid "Check Again"
msgstr "Tekrar kontrol et"

#: pro/updates.php:593
msgid "<b>ACF Activation Error</b>. Could not connect to activation server"
msgstr ""
"<b>ACF etkinleştirme hatası</b>. Etkinleştirme sunucusu ile bağlantı "
"kurulamadı"

#: pro/admin/admin-options-page.php:195
msgid "Publish"
msgstr "Yayımla"

#: pro/admin/admin-options-page.php:199
msgid ""
"No Custom Field Groups found for this options page. <a href=\"%s\">Create a "
"Custom Field Group</a>"
msgstr ""
"Bu seçenekler sayfası için hiç özel alan grubu bulunamadı. <a "
"href=\"%s\">Bir özel alan grubu oluştur</a>"

#: pro/admin/admin-updates.php:52
msgid "<b>Error</b>. Could not connect to update server"
msgstr "<b> Hata</b>. Güncelleme sunucusu ile bağlantı kurulamadı"

#: pro/admin/admin-updates.php:212
msgid ""
"<b>Error</b>. Could not authenticate update package. Please check again or "
"deactivate and reactivate your ACF PRO license."
msgstr ""
"<b>Hata</b>. Güncelleme paketi için kimlik doğrulaması yapılamadı. Lütfen "
"ACF PRO lisansınızı kontrol edin ya da lisansınızı etkisizleştirip, tekrar "
"etkinleştirin."

#: pro/admin/admin-updates.php:199
msgid ""
"<b>Error</b>. Your license for this site has expired or been deactivated. "
"Please reactivate your ACF PRO license."
msgstr ""
"<b>Hata</b>. Bu sitenin lisansının süresi dolmuş veya devre dışı bırakılmış. "
"Lütfen ACF PRO lisansınızı yeniden etkinleştirin."

#: pro/fields/class-acf-field-clone.php:27,
#: pro/fields/class-acf-field-repeater.php:31
msgid ""
"Allows you to select and display existing fields. It does not duplicate any "
"fields in the database, but loads and displays the selected fields at run-"
"time. The Clone field can either replace itself with the selected fields or "
"display the selected fields as a group of subfields."
msgstr ""

#: pro/fields/class-acf-field-clone.php:819
msgid "Select one or more fields you wish to clone"
msgstr "Çoğaltmak için bir ya da daha fazla alan seçin"

#: pro/fields/class-acf-field-clone.php:838
msgid "Display"
msgstr "Görüntüle"

#: pro/fields/class-acf-field-clone.php:839
msgid "Specify the style used to render the clone field"
msgstr "Çoğaltılacak alanın görünümü için stili belirleyin"

#: pro/fields/class-acf-field-clone.php:844
msgid "Group (displays selected fields in a group within this field)"
msgstr "Grup (bu alanın içinde seçili alanları grup olarak gösterir)"

#: pro/fields/class-acf-field-clone.php:845
msgid "Seamless (replaces this field with selected fields)"
msgstr "Pürüzsüz (bu alanı seçişmiş olan alanlarla değiştirir)"

#: pro/fields/class-acf-field-clone.php:868
msgid "Labels will be displayed as %s"
msgstr "Etiketler %s olarak görüntülenir"

#: pro/fields/class-acf-field-clone.php:873
msgid "Prefix Field Labels"
msgstr "Alan etiketlerine ön ek ekle"

#: pro/fields/class-acf-field-clone.php:883
msgid "Values will be saved as %s"
msgstr "Değerler %s olarak kaydedilecek"

#: pro/fields/class-acf-field-clone.php:888
msgid "Prefix Field Names"
msgstr "Alan isimlerine ön ek ekle"

#: pro/fields/class-acf-field-clone.php:1005
msgid "Unknown field"
msgstr "Bilinmeyen alan"

#: pro/fields/class-acf-field-clone.php:1042
msgid "Unknown field group"
msgstr "Bilinmeyen alan grubu"

#: pro/fields/class-acf-field-clone.php:1046
msgid "All fields from %s field group"
msgstr "%s alan grubundaki tüm alanlar"

#: pro/fields/class-acf-field-flexible-content.php:27
msgid ""
"Allows you to define, create and manage content with total control by "
"creating layouts that contain subfields that content editors can choose from."
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:36,
#: pro/fields/class-acf-field-repeater.php:103,
#: pro/fields/class-acf-field-repeater.php:297
msgid "Add Row"
msgstr "Satır ekle"

#: pro/fields/class-acf-field-flexible-content.php:76,
#: pro/fields/class-acf-field-flexible-content.php:943,
#: pro/fields/class-acf-field-flexible-content.php:1022
msgid "layout"
msgid_plural "layouts"
msgstr[0] "yerleşim"
msgstr[1] "yerleşimler"

#: pro/fields/class-acf-field-flexible-content.php:77
msgid "layouts"
msgstr "yerleşimler"

#: pro/fields/class-acf-field-flexible-content.php:81,
#: pro/fields/class-acf-field-flexible-content.php:942,
#: pro/fields/class-acf-field-flexible-content.php:1021
msgid "This field requires at least {min} {label} {identifier}"
msgstr "Bu alan için en az gereken {min} {label} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:82
msgid "This field has a limit of {max} {label} {identifier}"
msgstr "Bu alan için sınır {max} {label} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:85
msgid "{available} {label} {identifier} available (max {max})"
msgstr "{available} {label} {identifier} kullanılabilir (en fazla {max})"

#: pro/fields/class-acf-field-flexible-content.php:86
msgid "{required} {label} {identifier} required (min {min})"
msgstr "{required} {label} {identifier} gerekli (min {min})"

#: pro/fields/class-acf-field-flexible-content.php:89
msgid "Flexible Content requires at least 1 layout"
msgstr "Esnek içerik, en az 1 yerleşim gerektirir"

#: pro/fields/class-acf-field-flexible-content.php:282
msgid "Click the \"%s\" button below to start creating your layout"
msgstr ""
"Kendi yerleşiminizi oluşturmaya başlamak için aşağıdaki \"%s \" tuşuna "
"tıklayın"

#: pro/fields/class-acf-field-flexible-content.php:423
msgid "Add layout"
msgstr "Yerleşim ekle"

#: pro/fields/class-acf-field-flexible-content.php:424
msgid "Duplicate layout"
msgstr "Düzeni çoğalt"

#: pro/fields/class-acf-field-flexible-content.php:425
msgid "Remove layout"
msgstr "Yerleşimi çıkar"

#: pro/fields/class-acf-field-flexible-content.php:426,
#: pro/fields/class-acf-repeater-table.php:382
msgid "Click to toggle"
msgstr "Geçiş yapmak için tıklayın"

#: pro/fields/class-acf-field-flexible-content.php:562
msgid "Delete Layout"
msgstr "Yerleşimi sil"

#: pro/fields/class-acf-field-flexible-content.php:563
msgid "Duplicate Layout"
msgstr "Yerleşimi çoğalt"

#: pro/fields/class-acf-field-flexible-content.php:564
msgid "Add New Layout"
msgstr "Yeni yerleşim ekle"

#: pro/fields/class-acf-field-flexible-content.php:564
#, fuzzy
#| msgid "Add layout"
msgid "Add Layout"
msgstr "Yerleşim ekle"

#: pro/fields/class-acf-field-flexible-content.php:647
msgid "Min"
msgstr "En düşük"

#: pro/fields/class-acf-field-flexible-content.php:662
msgid "Max"
msgstr "En yüksek"

#: pro/fields/class-acf-field-flexible-content.php:705
msgid "Minimum Layouts"
msgstr "En az yerleşim"

#: pro/fields/class-acf-field-flexible-content.php:716
msgid "Maximum Layouts"
msgstr "En fazla yerleşim"

#: pro/fields/class-acf-field-flexible-content.php:727,
#: pro/fields/class-acf-field-repeater.php:293
msgid "Button Label"
msgstr "Tuş etiketi"

#: pro/fields/class-acf-field-flexible-content.php:1710,
#: pro/fields/class-acf-field-repeater.php:918
msgid "%s must be of type array or null."
msgstr "%s dizi veya null türünde olmalıdır."

#: pro/fields/class-acf-field-flexible-content.php:1721
msgid "%1$s must contain at least %2$s %3$s layout."
msgid_plural "%1$s must contain at least %2$s %3$s layouts."
msgstr[0] "%1$s en az %2$s %3$s düzen içermelidir."
msgstr[1] "%1$s en az %2$s %3$s düzen içermelidir."

#: pro/fields/class-acf-field-flexible-content.php:1737
msgid "%1$s must contain at most %2$s %3$s layout."
msgid_plural "%1$s must contain at most %2$s %3$s layouts."
msgstr[0] "%1$s en fazla %2$s %3$s düzeni içermelidir."
msgstr[1] "%1$s en fazla %2$s %3$s düzeni içermelidir."

#: pro/fields/class-acf-field-gallery.php:27
msgid ""
"An interactive interface for managing a collection of attachments, such as "
"images."
msgstr ""

#: pro/fields/class-acf-field-gallery.php:77
msgid "Add Image to Gallery"
msgstr "Galeriye görsel ekle"

#: pro/fields/class-acf-field-gallery.php:78
msgid "Maximum selection reached"
msgstr "En fazla seçim aşıldı"

#: pro/fields/class-acf-field-gallery.php:324
msgid "Length"
msgstr "Uzunluk"

#: pro/fields/class-acf-field-gallery.php:368
msgid "Caption"
msgstr "Başlık"

#: pro/fields/class-acf-field-gallery.php:380
msgid "Alt Text"
msgstr "Alternatif metin"

#: pro/fields/class-acf-field-gallery.php:504
msgid "Add to gallery"
msgstr "Galeriye ekle"

#: pro/fields/class-acf-field-gallery.php:508
msgid "Bulk actions"
msgstr "Toplu eylemler"

#: pro/fields/class-acf-field-gallery.php:509
msgid "Sort by date uploaded"
msgstr "Yüklenme tarihine göre sırala"

#: pro/fields/class-acf-field-gallery.php:510
msgid "Sort by date modified"
msgstr "Değiştirme tarihine göre sırala"

#: pro/fields/class-acf-field-gallery.php:511
msgid "Sort by title"
msgstr "Başlığa göre sırala"

#: pro/fields/class-acf-field-gallery.php:512
msgid "Reverse current order"
msgstr "Sıralamayı ters çevir"

#: pro/fields/class-acf-field-gallery.php:524
msgid "Close"
msgstr "Kapat"

#: pro/fields/class-acf-field-gallery.php:615
msgid "Minimum Selection"
msgstr "En az seçim"

#: pro/fields/class-acf-field-gallery.php:625
msgid "Maximum Selection"
msgstr "En fazla seçim"

#: pro/fields/class-acf-field-gallery.php:707
msgid "Allowed file types"
msgstr "İzin verilen dosya tipleri"

#: pro/fields/class-acf-field-gallery.php:727
msgid "Insert"
msgstr "Ekle"

#: pro/fields/class-acf-field-gallery.php:728
msgid "Specify where new attachments are added"
msgstr "Yeni eklerin nereye ekleneceğini belirtin"

#: pro/fields/class-acf-field-gallery.php:732
msgid "Append to the end"
msgstr "Sona ekle"

#: pro/fields/class-acf-field-gallery.php:733
msgid "Prepend to the beginning"
msgstr "En başa ekleyin"

#: pro/fields/class-acf-field-repeater.php:66,
#: pro/fields/class-acf-field-repeater.php:463
#, fuzzy
#| msgid "Minimum rows reached ({min} rows)"
msgid "Minimum rows not reached ({min} rows)"
msgstr "En az satır sayısına ulaşıldı ({min} satır)"

#: pro/fields/class-acf-field-repeater.php:67
msgid "Maximum rows reached ({max} rows)"
msgstr "En fazla satır değerine ulaşıldı ({max} satır)"

#: pro/fields/class-acf-field-repeater.php:68
msgid "Error loading page"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:69
msgid "Order will be assigned upon save"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:196
msgid "Useful for fields with a large number of rows."
msgstr ""

#: pro/fields/class-acf-field-repeater.php:207
msgid "Rows Per Page"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:208
msgid "Set the number of rows to be displayed on a page."
msgstr ""

#: pro/fields/class-acf-field-repeater.php:240
msgid "Minimum Rows"
msgstr "En az satır"

#: pro/fields/class-acf-field-repeater.php:251
msgid "Maximum Rows"
msgstr "En fazla satır"

#: pro/fields/class-acf-field-repeater.php:281
msgid "Collapsed"
msgstr "Daraltılmış"

#: pro/fields/class-acf-field-repeater.php:282
msgid "Select a sub field to show when row is collapsed"
msgstr "Satır toparlandığında görüntülenecek alt alanı seçin"

#: pro/fields/class-acf-field-repeater.php:1060
#, fuzzy
#| msgid "Invalid field group ID."
msgid "Invalid field key or name."
msgstr "Geçersiz alan grup no."

#: pro/fields/class-acf-field-repeater.php:1069
msgid "There was an error retrieving the field."
msgstr ""

#: pro/fields/class-acf-repeater-table.php:369
#, fuzzy
#| msgid "Drag to reorder"
msgid "Click to reorder"
msgstr "Yeniden düzenlemek için sürükleyin"

#: pro/fields/class-acf-repeater-table.php:402
msgid "Add row"
msgstr "Satır ekle"

#: pro/fields/class-acf-repeater-table.php:403
msgid "Duplicate row"
msgstr "Satırı çoğalt"

#: pro/fields/class-acf-repeater-table.php:404
msgid "Remove row"
msgstr "Satır çıkar"

#: pro/fields/class-acf-repeater-table.php:448,
#: pro/fields/class-acf-repeater-table.php:465,
#: pro/fields/class-acf-repeater-table.php:466
msgid "Current Page"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:456,
#: pro/fields/class-acf-repeater-table.php:457
#, fuzzy
#| msgid "Front Page"
msgid "First Page"
msgstr "Ön sayfa"

#: pro/fields/class-acf-repeater-table.php:460,
#: pro/fields/class-acf-repeater-table.php:461
#, fuzzy
#| msgid "Posts Page"
msgid "Previous Page"
msgstr "Yazılar sayfası"

#. translators: 1: Current page, 2: Total pages.
#: pro/fields/class-acf-repeater-table.php:470
msgctxt "paging"
msgid "%1$s of %2$s"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:477,
#: pro/fields/class-acf-repeater-table.php:478
#, fuzzy
#| msgid "Front Page"
msgid "Next Page"
msgstr "Ön sayfa"

#: pro/fields/class-acf-repeater-table.php:481,
#: pro/fields/class-acf-repeater-table.php:482
#, fuzzy
#| msgid "Posts Page"
msgid "Last Page"
msgstr "Yazılar sayfası"

#: pro/locations/class-acf-location-block.php:71
msgid "No block types exist"
msgstr "Hiç blok tipi yok"

#: pro/locations/class-acf-location-options-page.php:70
msgid "No options pages exist"
msgstr "Seçenekler sayfayı mevcut değil"

#: pro/admin/views/html-settings-updates.php:6
msgid "Deactivate License"
msgstr "Lisansı devre dışı bırak"

#: pro/admin/views/html-settings-updates.php:6
msgid "Activate License"
msgstr "Lisansı etkinleştir"

#: pro/admin/views/html-settings-updates.php:16
msgid "License Information"
msgstr "Lisans bilgisi"

#: pro/admin/views/html-settings-updates.php:34
msgid ""
"To unlock updates, please enter your license key below. If you don't have a "
"licence key, please see <a href=\"%s\" target=\"_blank\">details & pricing</"
"a>."
msgstr ""
"Güncellemeleri açmak için lisans anahtarınızı aşağıya girin. Eğer bir lisans "
"anahtarınız yoksa lütfen <a href=\"%s\" target=“_blank”>detaylar ve "
"fiyatlama</a> sayfasına bakın."

#: pro/admin/views/html-settings-updates.php:37
msgid "License Key"
msgstr "Lisans anahtarı"

#: pro/admin/views/html-settings-updates.php:22
msgid "Your license key is defined in wp-config.php."
msgstr "Lisans anahtarınız wp-config.php içinde tanımlanmış."

#: pro/admin/views/html-settings-updates.php:29
msgid "Retry Activation"
msgstr "Etkinleştirmeyi yeniden dene"

#: pro/admin/views/html-settings-updates.php:61
msgid "Update Information"
msgstr "Güncelleme bilgisi"

#: pro/admin/views/html-settings-updates.php:68
msgid "Current Version"
msgstr "Mevcut sürüm"

#: pro/admin/views/html-settings-updates.php:76
msgid "Latest Version"
msgstr "En son sürüm"

#: pro/admin/views/html-settings-updates.php:84
msgid "Update Available"
msgstr "Güncelleme mevcut"

#: pro/admin/views/html-settings-updates.php:98
msgid "Upgrade Notice"
msgstr "Yükseltme bildirimi"

#: pro/admin/views/html-settings-updates.php:126
msgid "Check For Updates"
msgstr ""

#: pro/admin/views/html-settings-updates.php:121
#, fuzzy
#| msgid "Please enter your license key above to unlock updates"
msgid "Enter your license key to unlock updates"
msgstr ""
"Güncelleştirmelerin kilidini açmak için yukardaki alana lisans anahtarını "
"girin"

#: pro/admin/views/html-settings-updates.php:119
msgid "Update Plugin"
msgstr "Eklentiyi güncelle"

#: pro/admin/views/html-settings-updates.php:117
msgid "Please reactivate your license to unlock updates"
msgstr ""
"Güncellemelerin kilidini açmak için lütfen lisansınızı yeniden etkinleştirin"
