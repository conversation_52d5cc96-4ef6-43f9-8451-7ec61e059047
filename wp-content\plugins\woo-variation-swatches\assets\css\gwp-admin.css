/*!
 * Variation Swatches for WooCommerce v1.1.19 
 * 
 * Author: <PERSON><PERSON> ( <EMAIL> ) 
 * Date: 07/09/2021
 * Released under the GPLv3 license.
 */
/**
  * GWP Backbone modal dialog
  */

.gwp-backbone-modal * {
  box-sizing: border-box;
}

.gwp-backbone-modal .gwp-backbone-modal-content {
  position: fixed;
  background: #FFFFFF;
  z-index: 100000;
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  max-width: 100%;
  min-width: 300px;
}

.gwp-backbone-modal .gwp-backbone-modal-content article {
  overflow: auto;
}

.gwp-backbone-modal .gwp-backbone-modal-content {
  width: 40%;
}

.gwp-backbone-modal .gwp-feedback-dialog-form-body h2 {
  font-weight: 700;
  font-size: 15px;
  color: #495157;
  margin: 0 0 20px 0;
}

.gwp-backbone-modal .gwp-feedback-dialog-form-body .feedback-dialog-form-body .feedback-dialog-input-wrapper {
  line-height: 1.3;
  overflow: hidden;
  margin-bottom: 15px;
}

.gwp-backbone-modal .gwp-feedback-dialog-form-body .feedback-dialog-form-body .feedback-dialog-input {
  float: left;
  margin: 0 10px 0 0;
  box-shadow: none;
}

.gwp-backbone-modal .gwp-feedback-dialog-form-body .feedback-dialog-form-body .feedback-text {
  margin: 10px 0 0 25px;
  padding: 5px;
  font-size: 13px;
  box-shadow: none;
  background-color: #FFFFFF;
  width: 92%;
  display: none;
}

.gwp-backbone-modal .gwp-feedback-dialog-form-body .feedback-dialog-form-body .feedback-alert {
  color: #0273A9;
  background-color: #FFFFFF;
  font-weight: 600;
}

.gwp-backbone-modal .gwp-feedback-dialog-form-body .feedback-dialog-form-body .feedback-dialog-label {
  display: block;
  font-size: 13px;
  color: #6D7882;
}

.gwp-backbone-modal footer {
  margin-top: 30px;
  overflow: hidden;
  clear: both;
}

.gwp-backbone-modal footer .feedback-dialog-form-button-send {
  background-color: #56BFD4;
  border-radius: 3px;
  color: #FFFFFF;
  line-height: 1;
  padding: 12px 20px;
  font-size: 13px;
  height: 40px;
  border: 0;
  box-shadow: none;
  text-shadow: none;
  float: left;
}

.gwp-backbone-modal footer .feedback-dialog-form-button-send:hover {
  background: #389DB1;
}

.gwp-backbone-modal footer .feedback-dialog-form-button-send:active {
  -webkit-transform: translateY(0);
          transform: translateY(0);
}

.gwp-backbone-modal footer .feedback-dialog-form-button-skip {
  font-size: 12px;
  color: #A4AFB7;
  background: none;
  float: right;
  width: auto;
  cursor: pointer;
  padding: 10px 0;
  outline: 0;
  text-decoration: none;
}

.gwp-backbone-modal footer .feedback-dialog-form-button-skip:focus {
  box-shadow: none;
}

.gwp-backbone-modal footer .feedback-dialog-form-button-skip:hover {
  text-decoration: underline;
}

.woo-variation-gallery-message {
  background-color: #7f54b3;
  color: #fff;
  padding: 5px 10px;
  border-radius: 5px;
  display: none;
}

.woo-variation-gallery-message a {
  color: #fff;
}

.woo-variation-gallery-message.enable {
  display: block;
}

.woo-variation-gallery-message.loading {
  display: block;
}

@media screen and (max-width: 782px) {
  .gwp-backbone-modal .gwp-backbone-modal-content {
    width: 100%;
    height: 100%;
    min-width: 100%;
  }
}

.gwp-backbone-modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  min-height: 360px;
  background: #000000;
  opacity: 0.7;
  z-index: 99900;
}

.gwp-backbone-modal-main {
  padding-bottom: 55px;
}

.gwp-backbone-modal-main header,
.gwp-backbone-modal-main article {
  display: block;
  position: relative;
}

.gwp-backbone-modal-main .gwp-backbone-modal-header {
  height: auto;
  background: #FCFCFC;
  padding: 1em 1.5em;
  border-bottom: 1px solid #DDDDDD;
}

.gwp-backbone-modal-main .gwp-backbone-modal-header h1 {
  margin: 0;
  color: #495157;
  font-weight: 700;
  font-size: 14px;
  background: transparent;
  line-height: 2em;
  text-transform: uppercase;
  border: 0;
}

.gwp-backbone-modal-main .gwp-backbone-modal-header .modal-close-link {
  cursor: pointer;
  color: #777777;
  height: 54px;
  width: 54px;
  padding: 0;
  position: absolute;
  top: 0;
  right: 0;
  text-align: center;
  border: 0;
  border-left: 1px solid #DDDDDD;
  background-color: transparent;
  transition: color 0.1s ease-in-out, background 0.1s ease-in-out;
}

.gwp-backbone-modal-main .gwp-backbone-modal-header .modal-close-link::before {
  font: normal 22px/50px "dashicons" !important;
  color: #666666;
  display: block;
  content: "\F335";
  font-weight: 300;
}

.gwp-backbone-modal-main .gwp-backbone-modal-header .modal-close-link:hover,
.gwp-backbone-modal-main .gwp-backbone-modal-header .modal-close-link:focus {
  background: #DDDDDD;
  border-color: #CCCCCC;
  color: #000000;
}

.gwp-backbone-modal-main .gwp-backbone-modal-header .modal-close-link:focus {
  outline: none;
}

.gwp-backbone-modal-main article {
  padding: 1.5em;
}

.gwp-backbone-modal-main article p {
  margin: 1.5em 0;
}

.gwp-backbone-modal-main article p:first-child {
  margin-top: 0;
}

.gwp-backbone-modal-main article p:last-child {
  margin-bottom: 0;
}

.gwp-backbone-modal-main article .pagination {
  padding: 10px 0 0;
  text-align: center;
}

.gwp-backbone-modal-main article table.widefat {
  margin: 0;
  width: 100%;
  border: 0;
  box-shadow: none;
}

.gwp-backbone-modal-main article table.widefat thead th {
  padding: 0 1em 1em 1em;
  text-align: left;
}

.gwp-backbone-modal-main article table.widefat thead th:first-child {
  padding-left: 0;
}

.gwp-backbone-modal-main article table.widefat thead th:last-child {
  padding-right: 0;
  text-align: right;
}

.gwp-backbone-modal-main article table.widefat tbody td,
.gwp-backbone-modal-main article table.widefat tbody th {
  padding: 1em;
  text-align: left;
  vertical-align: middle;
}

.gwp-backbone-modal-main article table.widefat tbody td:first-child,
.gwp-backbone-modal-main article table.widefat tbody th:first-child {
  padding-left: 0;
}

.gwp-backbone-modal-main article table.widefat tbody td:last-child,
.gwp-backbone-modal-main article table.widefat tbody th:last-child {
  padding-right: 0;
  text-align: right;
}

.gwp-backbone-modal-main article table.widefat tbody td select,
.gwp-backbone-modal-main article table.widefat tbody td .select2-container,
.gwp-backbone-modal-main article table.widefat tbody th select,
.gwp-backbone-modal-main article table.widefat tbody th .select2-container {
  width: 100%;
}

.gwp-backbone-modal-main footer {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 100;
  padding: 1em 1.5em;
  background: #FCFCFC;
  border-top: 1px solid #DFDFDF;
  box-shadow: 0 -4px 4px -4px rgba(0, 0, 0, 0.1);
}

.gwp-backbone-modal-main footer .inner {
  text-align: right;
  line-height: 23px;
}

.gwp-backbone-modal-main footer .inner .button {
  margin-bottom: 0;
}

.gwp-backbone-modal-main footer .inner .gwp-action-button-group {
  display: inline-block;
  float: left;
}

.gwp-backbone-modal-main footer .inner .button.button-large {
  margin-left: 10px;
  padding: 0 10px !important;
  line-height: 28px;
  height: auto;
  display: inline-block;
}

.gwp-action-button-group {
  vertical-align: middle;
  line-height: 26px;
  text-align: left;
}

.gwp-action-button-group .gwp-action-button-group__items {
  display: inline-flex;
  flex-flow: row wrap;
  align-content: flex-start;
  justify-content: flex-start;
}

.gwp-action-button-group .gwp-action-button {
  margin: 0 0 0 -1px !important;
  border: 1px solid #CCCCCC;
  padding: 0 10px !important;
  border-radius: 0 !important;
  float: none;
  line-height: 28px;
  height: auto;
  z-index: 1;
  position: relative;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1 0 auto;
  box-sizing: border-box;
  text-align: center;
  white-space: nowrap;
}

.gwp-action-button-group .gwp-action-button:hover,
.gwp-action-button-group .gwp-action-button:focus {
  border: 1px solid #999999;
  z-index: 2;
}

.gwp-action-button-group .gwp-action-button:first-child {
  margin-left: 0 !important;
  border-top-left-radius: 3px !important;
  border-bottom-left-radius: 3px !important;
}

.gwp-action-button-group .gwp-action-button:last-child {
  border-top-right-radius: 3px !important;
  border-bottom-right-radius: 3px !important;
}

.gwp-action-button-group .spinner {
  margin: 10px;
}

.gwp-action-button-group .spinner.visible {
  visibility: visible;
}