<?php
/**
 * The template for displaying product content in the single-product.php template
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/content-single-product.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see     https://woocommerce.com/document/template-structure/
 * @package WooCommerce\Templates
 * @version 3.6.0
 */

defined( 'ABSPATH' ) || exit;

global $product;

/**
 * Hook: woocommerce_before_single_product.
 *
 * @hooked woocommerce_output_all_notices - 10
 */
do_action( 'woocommerce_before_single_product' );

if ( post_password_required() ) {
	echo get_the_password_form(); // WPCS: XSS ok.
	return;
}
?>
<div id="product-<?php the_ID(); ?>" <?php wc_product_class( '', $product ); ?>>

	<?php
	/**
	 * Hook: woocommerce_before_single_product_summary.
	 *
	 * @hooked woocommerce_show_product_sale_flash - 10
	 * @hooked woocommerce_show_product_images - 20
	 */
	do_action( 'woocommerce_before_single_product_summary' );
	?>

	<div class="summary entry-summary">
		<?php
		/**
		 * Hook: woocommerce_single_product_summary.
		 *
		 * @hooked woocommerce_template_single_title - 5
		 * @hooked woocommerce_template_single_rating - 10
		 * @hooked woocommerce_template_single_price - 10
		 * @hooked woocommerce_template_single_excerpt - 20
		 * @hooked woocommerce_template_single_add_to_cart - 30
		 * @hooked woocommerce_template_single_meta - 40
		 * @hooked woocommerce_template_single_sharing - 50
		 * @hooked WC_Structured_Data::generate_product_data() - 60
		 */
		do_action( 'woocommerce_single_product_summary' );
		?>
	</div>

	<?php
	/**
	 * Hook: woocommerce_after_single_product_summary.
	 *
	 * @hooked woocommerce_output_product_data_tabs - 10
	 * @hooked woocommerce_upsell_display - 15
	 * @hooked woocommerce_output_related_products - 20
	 */
	do_action( 'woocommerce_after_single_product_summary' );
	?>
</div>

<?php do_action( 'woocommerce_after_single_product' ); ?>

<style>
#customSizeForm {
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    background-color: #f9f9f9;
    margin-top: 20px;
}

#customSizeForm h4 {
    margin-bottom: 20px;
    color: #333;
    font-weight: 600;
}

#customSizeForm h5 {
    margin-bottom: 15px;
    color: #555;
    font-weight: 500;
}

.customSizeDiagramBox {
    margin-bottom: 15px;
}

.customSizeDiagram img {
    max-width: 100%;
    height: auto;
}

.customSizeFields .form-floating {
    margin-bottom: 10px;
}

.customSizeFields .form-control {
    border: 1px solid #ccc;
    border-radius: 4px;
    padding: 10px;
    font-size: 14px;
}

.customSizeFields .form-control:focus {
    border-color: #000;
    box-shadow: 0 0 0 0.2rem rgba(0, 0, 0, 0.1);
}

.diagramLabel {
    position: absolute;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    font-weight: bold;
    pointer-events: none;
}
</style>

<script>
jQuery(document).ready(function($) {
    // Custom Size Integration
    function initCustomSizeIntegration() {
        // Find the size variation dropdown/select
        const sizeSelectors = [
            'select[name*="pa_size"]',
            'select[name*="attribute_pa_size"]',
            '.size-variable-items-wrapper',
            '.variations tr:has([name*="pa_size"])'
        ];
        
        let sizeWrapper = null;
        for (let selector of sizeSelectors) {
            sizeWrapper = $(selector);
            if (sizeWrapper.length > 0) break;
        }
        
        if (sizeWrapper.length === 0) {
            // Fallback: insert after variations table
            const variationsTable = $('.variations');
            if (variationsTable.length > 0) {
                variationsTable.after('<div id="customSizeWrapper"></div>');
            } else {
                // Last resort: insert after add to cart form
                $('.single_variation_wrap').after('<div id="customSizeWrapper"></div>');
            }
        } else {
            sizeWrapper.after('<div id="customSizeWrapper"></div>');
        }
        
        // Load custom size form
        $('#customSizeWrapper').html(`
            <div id="customSizeForm" style="display:none; margin-top:20px;">
                <h4 style="margin-bottom:15px;">Custom Size Measurements</h4>
                <div class="custom-size-fields mt-3">
                    <?php 
                    $custom_fields = array(); // Initialize for template
                    ob_start();
                    include get_template_directory() . '/template-parts/product/product-custom-size.php';
                    $custom_size_html = ob_get_clean();
                    echo addslashes(str_replace(array("\r", "\n", "\t"), '', $custom_size_html));
                    ?>
                </div>
            </div>
        `);

        // Listen for changes in size selection
        function checkSizeSelection() {
            let selectedValue = '';

            // Check regular dropdown
            const sizeSelect = $('select[name*="pa_size"], select[name*="attribute_pa_size"]');
            if (sizeSelect.length > 0) {
                selectedValue = sizeSelect.val();
            }

            // Check variation swatches
            const activeSwatch = $('.variable-item[data-attribute*="pa_size"].selected');
            if (activeSwatch.length > 0) {
                selectedValue = activeSwatch.attr('data-value');
            }

            if (selectedValue === 'custom-size') {
                $('#customSizeForm').slideDown();
            } else {
                $('#customSizeForm').slideUp();
            }
        }

        // Bind to size selection changes
        $(document).on('change', 'select[name*="pa_size"], select[name*="attribute_pa_size"]', checkSizeSelection);

        // Also listen for variation swatches clicks if using swatches plugin
        $(document).on('click', '.variable-item[data-attribute*="pa_size"]', function() {
            setTimeout(checkSizeSelection, 100);
        });

        // Listen for WooCommerce variation events
        $(document).on('woocommerce_variation_has_changed', checkSizeSelection);

        // Check initial state
        setTimeout(checkSizeSelection, 500);

        // Add validation for custom size
        $('.variations_form').on('submit', function(e) {
            const sizeSelect = $('select[name*="pa_size"], select[name*="attribute_pa_size"]');
            const activeSwatch = $('.variable-item[data-attribute*="pa_size"].selected');
            let selectedValue = '';

            if (sizeSelect.length > 0) {
                selectedValue = sizeSelect.val();
            } else if (activeSwatch.length > 0) {
                selectedValue = activeSwatch.attr('data-value');
            }

            if (selectedValue === 'custom-size') {
                // Check if at least one measurement is provided
                const measurements = $('#customSizeForm input[type="text"]');
                let hasValue = false;

                measurements.each(function() {
                    if ($(this).val().trim() !== '') {
                        hasValue = true;
                        return false; // break loop
                    }
                });

                if (!hasValue) {
                    e.preventDefault();
                    alert('Please provide at least one custom measurement.');
                    return false;
                }
            }
        });
    }

    // Initialize when variations form is ready
    if ($('.variations_form').length > 0) {
        initCustomSizeIntegration();
    } else {
        // Wait for variations form to load
        $(document).on('wc_variation_form', initCustomSizeIntegration);
    }
});
</script>
