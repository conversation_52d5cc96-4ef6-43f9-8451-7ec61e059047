=== Variation Swatches for WooCommerce ===
Contributors: EmranAhmed, getwooplugins
Tags: woocommerce variation swatches, woocommerce variation, woocommerce, variation swatches, woocommerce attributes, product attributes, product color, product size, variable product attributes, variation product swatches, color variation swatch, image variation swatch, printful color swatches
Requires PHP: 7.4
Requires at least: 5.9
Tested up to: 6.7
WC requires at least: 7.5
WC tested up to: 9.6
Stable tag: 2.2.0
License: GPLv3 or later
License URI: http://www.gnu.org/licenses/gpl-3.0.html

Beautiful Color, Image and Buttons Variation Swatches For WooCommerce Product Attributes 

== Description ==

=== How To Install Product Variation Swatches Plugin for WooCommerce Variable Products ===

[youtube https://www.youtube.com/watch?v=4uIZjvWiSf0]

WooCommerce Variation Swatches makes selecting product options easier and more enjoyable. Say goodbye to dull dropdown menus—your customers can now choose sizes, colors, styles, and more using vibrant swatches, images, and labels.

This transformation turns the shopping experience into something fun and visually appealing while efficiently supporting popular WordPress themes. Plus, it seamlessly integrates with product quick views, allows you to customize shapes into <strong>round or square</strong>, and even enables you to disable the plugin’s CSS to tailor it to your theme’s stylesheet.

The best part? WooCommerce Variation Swatches automatically convert all variation select dropdowns into buttons right after installation—only a quick two-step configuration is needed for color and image swatches.


## Key Feature Comes with Variation Swatches For WooCommerce Plugin ##

👉 Auto Convert All Variation Dropdowns to Button Swatch by Default

  You can easily convert the variation select options from drop-down to buttons just by installing this WooCommerce Variation Swatches plugin. It will automatically convert your product variation select dropdowns to button swatches once the plugin is installed. 

  [Live Demo & Documentation](http://j.mp/automatic-button-swatches-readme)
<hr />

👉 Enable Color Swatches For Variable Product Attribute Variations

For selling variable products, adding color variations is one of the best ways to bring more life into eCommerce stores. When you enable Color Swatches instead of traditional variation dropdown, you are making your customers easier to choose their desired products. 

  [Live Demo & Documentation](http://bit.ly/3rd-demo-color-swatches-readme)
<hr />
👉 Enable Image Swatches For Variable Product Attribute Variations

Image swatches will provide a pleasant way to display variations of a product.This Image Variation Swatches plugin helps to display product variations easier and more stylishly. 

 [Live Demo & Documentation](http://bit.ly/3rd-image-demo-swatches-readme)
 <hr />
👉 Enable Label/Text/Button Swatches For Variable Product Attribute Variations 

For showing available product variation-related details (such as colors and sizes), label/text/button variation swatches extensively boost conversion. It is one of the most effective ways to make all the available options visible for products.

  [Live Demo & Documentation](http://bit.ly/3rd-label-demo-readme)
<hr />

👉 Convert Globally Created Attribute Variations Into Color, Image, and Label Swatches

Globally created product attributes are used for many different products. With these Variation Swatches for WooCommerce plugin, you can globally create attributes into color, image, and label swatches. So, you don’t need to get inside each product of your store and enable variation swatches for them separately. 

  [Video Tutorial & Preview](http://bit.ly/label-swatch-free-read-youtube)
<hr />
👉 Option to Globally Select ROUNDED Attribute Variation Swatches Shape.

The round shape is one of the unique and helpful features of this swatches plugin. You could grab the attention of buyers by using rounded attribute swatches. The feature helps convert potential buyers into sales.

  [Live Demo & Documentation](http://bit.ly/3rd-round-shape-readme) 
<hr />
👉 Option to Globally Select SQUARED Attribute Variation Swatches Shape.

There are many scenarios where you need to show more than two or three characters in swatches or add color and image swatch exclusively. Instead of Round, using a Squared shape is the best practice in such situations.

  [Live Demo & Documentation](http://bit.ly/3rd-demo-square-readme) 
<hr />
👉 Show Cross Sign For Out of Stock Variation Swatches (Up To 30 Variations)

By default, WooCommerce doesn't cross out any variations that are out of stock. However,this plugin would help if you avoided user frustration by never letting them pick any variation product they can't purchase.

  [Live Demo & Documentation](http://bit.ly/3rd-demo-cross-out-of-stock-readme)
<hr />
👉 Blur For Out of Stock Variation Swatches (Up To 30 Variations)

WooCommerce allows users to select the variation first, then let them know it's out of stock. But, that's time-consuming.  When you use this swatches plugin, you can make blur for out-of-stock variation swatches up to 30 variations.

  [Live Demo & Documentation](http://bit.ly/3rd-demo-blur-readme)
<hr />
👉 Hide For Out of Stock Variation Swatches (Up To 30 Variations)

Showing out-of-stock variation seems unnecessary. So most of the store owners want to hide out-of-stock variation products. This swatches plugin is helpful, as it has the option to hide out-of-stock variation swatches products. 

  [Live Demo & Documentation](http://bit.ly/3rd-demo-hide-out-stock-readme) 
<hr />
👉 Product Page Swatches Size Control

The Variation Swatches plugin has the option to control the size of the product page swatches. It will help you to present product variants more engagingly and appealingly. 

 [Live Demo & Documentation](http://bit.ly/3rd-demo-size-control-readme) 
<hr />
👉 Show Selected Variation Name Beside Label

This Variable Swatches plugin can show selected single color, image, and button attribute swatches name beside the label. Displaying selected variation names beside labels will help users to know about the product in a better way.

 [Live Demo & Documentation](http://bit.ly/show-selected-variation-name-beside-label) 
<hr />
👉 Flexible Swatches Tooltip Insert and Display settings 

The plugin has awesome tooltip features. You can show extra information about the swatches. It helps the users to understand about the variation a more elaborately. Consequently, it helps to boost sales and conversion exponentially. 

 [Live Demo & Documentation](https://bit.ly/tooltip-swatches-disable)  
<hr />

👉 Convert Buttons Swatches to Dropdown (If It's needed)

If you want to convert variation select dropdown into buttons. You don’t need to configure anything. Just you install the plugin, and you can notice variation select dropdown is converted to buttons. You can disable this feature on your requirement when you need. 

  [Live Demo & Documentation](http://j.mp/button-to-dropdown-readme)
<hr />
👉 Compatible With Elementor Page Builder Plugin

Elementor works well with all the themes and plugins which respect the coding. The WooCommerce Variation Swatches plugin is compatible with the Elementor page builder plugin.

[Video Demo & Documentation](http://j.mp/elementor-swatches)
<hr />
👉 Compatible With Printful WooCommerce Plugin 

The Variation Swatches plugin is compatible with the Printful WooCommerce plugin. You can enable color swatches with Printful Technology when customizing your shop.

[Video Demo & Documentation](http://j.mp/printful-swatches)
<hr />
👉 Compatible With Dokan MultiVendor WooCommerce Plugin 

Dokan Multivendor WooCommerce plugin and its functionalities are compatible with beautiful color, image, and button Variation Swatches for WooCommerce Product Attributes.

[Video Demo & Documentation](http://j.mp/swatches-dokan)
<hr />
👉 Compatible With AliDropship WooCommerce Plugin 
[Video Demo & Documentation](http://j.mp/swatches-alidropship)
<hr />
👉 Reorder Product Attribute Variation Swatches

The Attribute Variation Swatches plugin expands your possibilities for displaying variation swatches options. You can reorder product attribute variation swatches with ease. 

👉 Work on Variable Product Quick View Popup   

With the help of the WooCommerce Attribute Swatches plugin, you can show swatches on the product details page and archive page. In addition, you can provide shoppers with a “quick view” of the product.

👉 Compatible with popular WooCommerce Themes 

It is the only Variation Swatches plugin in the market that runs over 200K WooCommerce stores .The plugin is compatible with major themes in the market, including OceanWP, Flatsome, Divi and many more.

👉 Frequently Updated And Secured Codebase 

Our plugins are frequently updated with new features. We try our best to ensure that our plugin runs correctly on your site and keeps your site functional and secure. 

👉 Option to disable default plugin stylesheet for theme developer

Some plugins have styles that are not that easy to override. However, we provide the option to disable our default plugin's stylesheet and use your own stylesheet instead. 

👉 Compatible With Popular WooCommerce Plugins

There are many other useful plugins that serve different purposes, and many users will install at least a few. Our Variation Swatches plugin is compatible with the most popular WooCommerce plugins. 

## 🏆 Users' Feedback For Variation Swatches For WooCommerce ##

[Tim Cumming, Founder at DevisionUK](https://wordpress.org/support/topic/outstanding-support-118): 
> 'Really can’t rate this developer high enough!'

>Not only is the plugin super cool but their support, is utterly mind-blowing! I had reason to ask a question and, without hesitation, Ahmed logged into our site, had a look around and responded with a solution, all within the space of 5 minutes!!

>It you’re looking for a great product with superlative support then look no further!'

[Ruben Zuidervaart](https://wordpress.org/support/topic/does-what-it-should-with-no-issues/): 
> 'It just works, and the support is also pretty damn good for something that they offer for free.'

> 'Leaving a 5/5 is the least I could do 🙂'

[cl4udio](https://wordpress.org/support/topic/good-support-278/): 
> 'I like this plugin! Works great and the customer support is the best. Love it!'


##  PREMIUM FEATURES OF VARIATION SWATCHES FOR WOOCOMMERCE ##

[Live Demo](http://bit.ly/3rd-main-demo-readme) | [Upgrade to PRO](http://bit.ly/upgrade-to-pro-readme) | [Documentation](https://getwooplugins.com/documentation/woocommerce-variation-swatches/?utm_source=wordpress.org&utm_medium=README&utm_campaign=woo-variation-swatches) | [Support](https://getwooplugins.com/tickets/?utm_source=wordpress.org&utm_medium=README&utm_campaign=woo-variation-swatches)

=== How To Create Product Variation Swatches From Custom Product Level Attribute ===

[youtube https://www.youtube.com/watch?v=Ny9QBY_x9cA&t]

=== How To Show WooCommerce Variation Swatches on Shop Page ===

[youtube https://www.youtube.com/watch?v=1IhEZiGzJHs]

Besides adding swatches on the product page, you can show [WooCommerce variation swatches on shop page](https://getwooplugins.com/plugins/woocommerce-variation-swatches/)

👉 Auto Convert All Dropdowns to Image Swatch If Variation Has a Featured Image Set

If you have already set featured images for variations, then it will automatically convert all dropdowns to image swatch. That means you don’t need the hassle to do it manually which in return will save your valuable time. 

   [Live Demo & Documentation](http://j.mp/auto-image-swatches-insideplugin)
<hr />
👉 Multi Color Variation Swatches

Multi Color Variation Swatches allow you to insert dual color for a single variation product. The plugin is going to be handy in such situations where you have product variations in two colors.

  [Live Demo & Documentation](http://j.mp/dual-color-doc-inside-plugin)
<hr />
👉 Show Entire Color, Image, Label And Radio Attributes Swatches In Catalog/ Category / Archive / Store/  Shop Pages

Your variable product may have plenty of attributes. This Attribute Swatches plugin is very helpful for showing distinct color, image or label attribute swatches in catalog, category, archive or shop pages.  

  [Live Demo & Documentation](http://bit.ly/3rd-demo-acrhive-page-readme)
<hr />
👉 Show Selected Single Color or Image Or Label Attribute Swatches In Catalog/ Category / Archive / Store / Shop Pages

With the help of WooCommerce Variation Swatches plugin, you can show select attributes on archive page. So, don't worry if you have too many variations on the product page. 

  [Live Demo & Documentation](http://bit.ly/3rd-demo-readme-catalog-mode)
<hr />
👉 Convert attribute variations into radio button

The radio button is a quicker way for the user to scan all the available options. That’s definitely a better choice for selecting from all the variation products. The plugin has the feature to convert product attribute variations into radio buttons. 

[Live Demo & Documentation](http://bit.ly/3rd-demo-radio-readme)
<hr />
👉 Product Basis Attribute Swatches Type Change

This is another effective feature of the WooCommerce Variation Swatches plugin. For example, if you decide to change the swatch type of color attribute into an image, you can do it easily with this Attribute Swatches plugin.   

[Live Demo & Documentation](http://bit.ly/3rd-demo-product-basis-product-type-change-readme)
<hr />
👉 Show All Color, Image & Button Swatches Type In the Same Attribute

You can choose your desired attribute swatches type in the same attribute from the drop-down menu. This Attribute Swatches plugin allows you to show any swatch attribute type, such as, color, image, or button in the same attribute list instead of dropdown. 

[Live Demo & Documentation](http://bit.ly/3rd-readme-demo-show-all-variation-type-in-the-same-variation)
<hr />
👉 Insert New Image Replacing Globally Added Image For Image Variation Swatches In Each Product

WooCommerce Variation Swatch plugin offers global swatches.
It has the feature to insert a new image by replacing your globally added image. That means, you can replace your globally added images for variation swatches in each product. 

[Live Demo & Documentation](http://bit.ly/3rd-demo-product-basis-image-change-readme)
<hr />
👉 Insert New Color Replacing Globally Added Color In Color Variation Swatches For Each Product

The Variation Swatches plugin also allows you to change your globally added color. You can insert new color replacing globally added color in color variation swatches for each product. 

[Live Demo & Documentation](http://bit.ly/3rd-demo-product-basis-color-change-readme)
<hr />
👉 Convert Manually Created Attribute Variations Into Color, Image, and Label Swatches

WooCommerce by default has the option to show manually created attribute variations. However, the Attribute Variation Swatches plugin helps to convert manually created attribute variations into beautiful swatches like color, image and label swatches.

  [Live Demo & Documentation](http://bit.ly/manual-attribute-readme-video)
<hr />
👉 Change Variation Product Gallery After Selecting Single Attribute Like Amazon Or AliExpress

The shoppers don’t need to match the entire attribute variation when this WooCommerce Swatches plugin is used. Like Amazon or AliExpress, variation products gallery will be changed after selecting a single attribute. 

  [Live Demo & Documentation](http://bit.ly/3rd-readme-demo-amazon)
<hr />
👉 Generate Selected Attribute Variation Link 

This feature is useful when you want to send any specific link for variation products. Your customers may ask you to send them a direct link to a specific variation product link. It will help in making a quick purchase and save their valuable time. 

  [Live Demo & Documentation](http://bit.ly/3rd-demo-generate-link-readme)
<hr />
👉 Option to Select ROUNDED and SQUARED Attribute Variation Swatches Shape In the Same Product.

In the WooCommerce Variation Swatches plugin,there are options for styling swatches, such as rounded and squared attribute swatches shape. The round and square shapes are one of the unique features of this plugin. 

  [Live Demo & Documentation](http://bit.ly/3rd-readme-demo-round-square)
<hr />
👉 Show Cross Sign For Out of Stock Variation Swatches (Unlimited Variations Without hiding out of stock item from catalog)

The free version of those Swatches plugin enables you to show cross signs up to 30 variations that are currently out of stock. However, the advanced version will allow to show cross signs for unlimited variations.

  [Live Demo & Documentation](http://bit.ly/3rd-readme-demo-cross-out-of-stock-variations-more-than-30-variations)
<hr />
👉 Blur Out of Stock Variation Swatches (Unlimited Variations Without hiding out of stock item from catalog)

The advanced version of the WooCommerce Variation Swatches plugin is useful for displaying out of stock variation swatches as blur. You can show unlimited variations without hiding stock items from the catalog.

  [Live Demo & Documentation](http://bit.ly/3rd-demo-readme-blur-out-of-stock-variations-more-than-30-variations)
<hr />
👉 Hide Out of Stock Variation Swatches (Unlimited Variations Without hiding out of stock item from catalog)

Default WooCommerce settings will hide out-of-stock items everywhere on your online store. But, this WooCommerce Swatches plugin enables you to hide out of stock for unlimited variation products without hiding from the catalog. 

  [Live Demo & Documentation](http://bit.ly/3rd-demo-readme-hide-out-of-stock-variations-more-than-30-variations)
<hr />
👉 Shop Page Swatches Size Control

You will have control over swatch size when you are using this WooCommerce Variation Swatches plugin. You can decide how large or small the swatches will be on the product page. That means, you can set height, or width for the products for the shop page swatches.  

 [Live Demo & Documentation](http://bit.ly/3rd-readme-demo-shop-swatches-size) 
<hr />
👉 Make Selected Attribute Variation Swatches Size Larger Than Other Default Attribute Variations

You may want to keep a single attribute standout from plenty of WooCommerce Variable Product. This plugin allows you to make your desired attribute variation swatches size larger than other default attribute variations. 

 [Live Demo & Documentation](http://bit.ly/3rd-demo-readme-swatches-size-special-attribute)
<hr />
👉 Keep Attribute Variation Selected on Product Page After Choosing from Catalog page like Walmart

This Variation Swatches plugin has the option to keep attribute variation selected on the product page after choosing from the catalog page. When you choose a product from the catalog page, it will be selected on the product page like Walmart marketplace. 

 [Live Demo & Documentation](http://bit.ly/keep-attribute-variation-selected-on-product-page-after-choosing-from-catalog-page)
<hr />
👉 Show Custom Text in Variation Tooltip

It’s a good practice to provide extra descriptive product info to customers.To make online stores more user-friendly, this Product Variation Swatches plugin has brought the feature to show custom text in variation tooltip. 

 [Live Demo & Documentation](http://bit.ly/3rd-demo-readme-how-to-enable-text-tooltip)
<hr />
👉 Show Custom Image in Variation Tooltip

The image tooltip option plays a vital role by showing the image as tooltip in WooCommerce attribute variation. This Variation Swatches plugin helps you to show a custom image in variation tooltip. It is a fantastic way to present variation products.

 [Live Demo & Documentation](http://bit.ly/3rd-demo-readme-image-tooltip)
<hr />
👉 Swatches Border Styling

You can make the swatches border styling for each attribute item custom by using this Variation Swatches plugin. The plugin allows you to decide your desired products border color, size, text color, and item background color. 

 [Live Demo & Documentation](http://bit.ly/3rd-demo-readme-swatches-border-styling)
<hr />
👉 Hover Swatches Border Styling

Using this Variation Swatches plugin for WooCommerce, you can set hover swatches border styling for each WooCommerce product variation. The plugin will let you customize hover swatches border that will affect while hovering the mouse. 

 [Live Demo & Documentation](http://bit.ly/3rd-demo-readme-hover-swatches-border-styling)
<hr />
👉 Active Swatches Border Styling

When you click on a swatch, the product will be selected. That's known as active swatches.  With the help of the Product Attribute Swatches, you can define border styling for active swatches which will affect when a product selection is made.  

 [Live Demo & Documentation](https://bit.ly/active-border-styling)
<hr />
👉 Archive Page Swatches Alignment

When all swatches are shown in product archive pages, sometimes it may create misalignment issues. The Variation Swatches WooCommerce plugin allows you to set swatches alignment on left, center, or right on the archive page. 

 [Live Demo & Documentation](http://bit.ly/3rd-demo-readme-shop-alignment)
<hr />
👉 Show Variation Product Stock Notice

If you wish to let your customer know about the minimum stock of your product is left. This Variation Swatches for WooCommerce swatch plugin is very effective. It has the option to show all the remaining stock for variation products. 

 [Live Demo & Documentation](http://bit.ly/show-variation-product-stock-notice)
<hr />
<hr />
👉 WooCommerce Filter Widget

The Variation Swatches for WooCommerce plugin converts filter products by attribute widget into color swatches. This feature of the Swatch plugin will reduce the number of results that the user has to look through. 

 [Live Demo & Documentation](http://bit.ly/filter-widget-link-doc)
<hr />

👉 Create Categories & Grouped Variation Swatches

Users may feel confused to select desired variation swatches among many options. Category and Group swatches feature makes it easy to select the right product variation with ease.

 [Live Demo & Documentation](https://getwooplugins.com/documentation/woocommerce-variation-swatches/#category-group-swatches)
<hr />

👉 Compatible with Printful, Alidrop, and Multivendor Plugins

This Variation Swatches plugin is compatible with major themes and most popular plugins for WooCommerce. On top of that, it is also compatible with printful, alidrop and multivendor plugins.

👉 Customize Tooltip Text and Background Color

WooCommerce Variation Swatches plugin allows customizing the title text and tooltip background from the admin backend. It also has the option to change text background color. 

👉 Enable / Disable Ajax Variation

When WooCommerce starts to use ajax to load your selected variations, this changes the way of selecting products. That time you can enable or disable ajax variation from our plugin settings. 

👉 Archive page swatches positioning

The plugin not only allows you to show swatches on the archive page. At the same time,  you can place swatches before and after the add to cart button in the store from Archive Swatches.

👉 Archive page swatches alignment

The plugin not only allows you to show swatches on the archive page. At the same time,  you can place swatches before and after the add to cart button in the store from Archive Swatches.

👉 Tooltip display setting on archive/shop page

Variation Swatches for WooCommerce plugin offers a simple setting to enable and disable swatches on archive pages. You can show or hide tooltips and clear links on archive pages.

👉 Variation clear button display setting.

Clear button allows you to clear the selected attribute variation. The Woocommerce Variation Swatches plugin offers the option to enable and disable clear links on the archive page.

👉 Customize Tooltip Text and Background Color
👉 Customize swatches colors, background and border sizes
👉 Automatic updates

Keeping your plugin updated is crucial for security and stability of your site. We try to update our [variation swatches for woocommerce pro](https://wordpress.org/support/topic/the-best-support-ever-thank-you-guys/) plugin regularly. You can enable auto update for this swatch plugin directly from your WordPress dashboard.

👉 Exclusive technical support

We provide exclusive technical support to our customers. Our plugin has plenty of 5-star customer reviews and positive customer feedback on the WordPress plugin directory for its extraordinary support. 


## 🏆 Customers' Feedback For Variation Swatches For WooCommerce ##

[tysonfrantz](https://wordpress.org/support/topic/great-plugin-awesome-support-127): 
> 'This is one of the best swatches plugin available. The thing that separates it from the rest is the amazing customer support. I had an issue with my website, and the friendly GetWooPlugins team was able to get it resolved quickly!'

[nevadauser1](https://wordpress.org/support/topic/the-best-support-ever-thank-you-guys/): 
> 'These guys went above and beyond trying to help me out, I had an issue with my theme and one of the plugins after a woocommerce update, and they really did the best they could to help me solve the problem! Thank you so much guys specially Shamser!'

[jeromepernin](https://wordpress.org/support/topic/top-587/): 
> 'Super plugin! He literally saved 🙂 a huge thank you to Ahmed who provides extraordinary support. Congratulations to the whole team!'

>Will be buying from you again!'

= Sites Built With Variation Swatches for WooCommerce Plugin =

<blockquote>
<ul>
<li> 
   <a target="_blank" href="https://getwooplugins.com/showcase-swatches">Built With: Flatsome WooCommerce Theme</a>
</li>

<li> 
   <a target="_blank" href="https://getwooplugins.com/showcase-swatches">Built With: Avada WooCommerce Multi-Purpose Theme</a>
</li>

<li> 
   <a target="_blank" href="https://getwooplugins.com/showcase-swatches">Built With: Divi by Elegant Themes</a>
</li>

<li> 
   <a target="_blank" href="https://getwooplugins.com/showcase-swatches">Built With: Enfold - WooCommerce Multi-Purpose Theme</a>
</li>

<li> 
   <a target="_blank" href="https://getwooplugins.com/showcase-swatches">Built With: Astra WooCommerce Theme</a>
</li>

<li> 
   <a target="_blank" href="https://getwooplugins.com/showcase-swatches">Built With: Salient WooCommerce Theme</a>
</li>

<li> 
   <a target="_blank" href="https://getwooplugins.com/showcase-swatches">Built With: Twenty Seventen Theme</a>
</li>

<li> 
   <a target="_blank" href="https://getwooplugins.com/showcase-swatches">Built With: TheGem WooCommerce Theme</a>
</li>

<li> 
   <a target="_blank" href="https://getwooplugins.com/showcase-swatches">Built With: Kalium WooCommerce Professionals Theme</a>
</li>

<li> 
   <a target="_blank" href="https://getwooplugins.com/showcase-swatches">Built With: Jevelin WooCommerce Theme</a>
</li>

<li> 
   <a target="_blank" href="https://getwooplugins.com/showcase-swatches">Built With: Nitro WooCommerce Theme</a>
</li>

<li> 
    <a target="_blank" href="https://getwooplugins.com/showcase-swatches">Built With: KuteShop WooCommerce Theme</a>
</li>

<li> 
    <a target="_blank" href="https://getwooplugins.com/showcase-swatches">Built With: Domino WooCommerce Theme</a>
</li>
</ul>
</blockquote>

= Officially tested plugins =

* WPML
* <a target="_blank" href="https://wordpress.org/plugins/polylang/">Polylang</a>
* <a target="_blank" href="https://wordpress.org/plugins/loco-translate/">Loco Translate</a>

= Compatible WooCommerce Plugin =

<blockquote>

<ul>
<li><a target="_blank" href="http://bit.ly/woovs-org-item-page">Additional Variation Image Gallery For WooCommerce</a></li>
</ul>
<ul>
<li><a target="_blank" href="http://bit.ly/swatches-link-to-duplicator-readme">Variation Duplicator For WooCommerce</a></li>
</ul>
<ul>
<li><a target="_blank" href="https://wordpress.org/plugins/woo-cart-redirect-to-checkout-page">Add to Cart Redirect for WooCommerce</a></li>
</ul>

</blockquote>


= Forum and Feature Request =

<blockquote>
<h4>Pro Version</h4>
<ul>
<li><a target="_blank" href="https://getwooplugins.com/plugins/woocommerce-variation-swatches/?utm_source=wordpress.org&utm_medium=README&utm_campaign=woo-variation-swatches">Pro Version</a></li>
</ul>
<ul>
<li><a target="_blank" href="https://getwooplugins.com/documentation/woocommerce-variation-swatches/?utm_source=wordpress.org&utm_medium=README&utm_campaign=woo-variation-swatches">Documentation</a></li>
</ul>
<h4>For Quick Support, feature request and bug reporting</h4>
<ul>
<li><a target="_blank" href="http://bit.ly/getwoopluginsgroup">Join Our Facebook Group</a></li>
</ul>
<h4>For more information</h4>
<ul>
<li><a target="_blank" href="https://getwooplugins.com/?utm_source=wordpress.org&utm_medium=README&utm_campaign=woo-variation-swatches">Visit Our Official Website</a></li>
</ul>
</blockquote>

= Featured on Popular Blog Sites =

<blockquote>

<ul>
<li><a target="_blank" href="https://wpcred.com">wpcred.com</a></li>
</ul>
<ul>
<li><a target="_blank" href="https://storepress.com">storepress.com</a></li>
</ul>

</blockquote>

== Installation ==

### Automatic Installation From WordPress Dashboard

1. Login to your admin panel
2. Navigate to Plugins -> Add New
3. Search **Variation Swatches for WooCommerce**
4. Click install and activate respectively.

### Manual Installation From WordPress Dashboard

If your server is not connected to the Internet, then you can use this method-

1. Download the plugin by clicking on the red button above. A ZIP file will be downloaded.
2. Login to your site's admin panel and navigate to Plugins -> Add New -> Upload.
3. Click choose file, select the plugin file and click install

### Install Using FTP

If you are unable to use any of the methods due to internet connectivity and file permission issues, then you can use this method-

1. Download the plugin by clicking on the red button above. A ZIP file will be downloaded.
2. Unzip the file.
3. Launch your favorite FTP client. Such as FileZilla, FireFTP, CyberDuck etc. If you are a more advanced user, then you can use SSH too.
4. Upload the folder to `wp-content/plugins/`
5. Log in to your WordPress dashboard.
6. Navigate to Plugins -> Installed
7. Activate the plugin

== Frequently Asked Questions ==

= How can I configure attributes? =

Even this plugin has been installed and activated on your site, variable products will still show dropdowns if you’ve not configured product attributes.

1. Log in to your WordPress dashboard, navigate to the Products menu and click Attributes.
2. Click attribute name to edit an exists attribute or in the Add New Attribute form you will see the default Type selector.
3. Click that Type selector to change attribute’s type. Besides default options Select and Text, there are more 3 options Color, Image, Button/Label to choose.
4. Select the suitable type for your attribute and click Save Change/Add attribute
5. Go back to manage attributes screen. Click the cog icon on the right side of attribute to start editing terms.
6. Start adding new terms or editing exists terms. There will be a new option at the end of the form that allows you to choose color from colorpicker, upload image or choose as a button for those terms.

= Is it compatible with any kinds of WooCommerce Theme? =

Yes, it's compatible with any WooCommerce theme including OceanWP / Astra / Flatsome / X-Theme / Avada / Uncode / Storefront / Labomba / WR Nitro / Divi / BeTheme. But sometimes it may require small css tweak.

For more details. Check this <a target="_blank" href="https://bit.ly/100swatches"> 100+ WooCommerce Themes that support Variation Swatches plugin</a>

= Does it show in product QuickView? =

Yes, it supports any kinds of product quick view.

= Does it work on MultiSite? =

Yes, it is.

= How to use it on ajax load more? =

If your theme follow wordpress 'post-load' event with `document` then you don't have to do anything.
Here is the details about it: https://codex.wordpress.org/AJAX_in_Plugins#The_post-load_JavaScript_Event

But if you don't then just call this javascript function on ajax load event
```
$('.variations_form').each(function(){
    $(this).wc_variation_form();
});
```
And your are ready to go.

== Screenshots ==

1. Variation Color Swatch Preview
2. Variation Image Swatch Preview
3. Variation Button / Label Swatch Preview
4. Product QuickView Preview
5. Out Of Stock Variation Preview
6. Tooltip Preview
7. Tooltip Setting
8. Attribute Variation Shape
9. Attribute Variation Display Behavior
10. Variation Swatches Size and Font Setting
11. Global Variation Image Swatches Attribute List Preview
12. Global Variation Color Swatches Attribute List Preview

== Changelog ==

= 2.2.0 - 16-02-2025 =

* Fixed: Broken Settings html table.
* Added: Lazy Loading on swatches images to improve performance.

= 2.1.3 - 11-12-2024 =

* Fixed: JS Setting update warning trigger issue.
* Added: WP 6.7+ compatibility.
* Added: WC 9.4+ compatibility.

= 2.1.2 - 29-08-2024 =

* Added: WooCommerce 9.2+ Compatibility.
* Added: Admin Menu "Clear swatches transient".

= 2.1.1 - 02-07-2024 =

* Fixed: Attribute meta save issue.
* Update: WooCommerce 9.0+ Compatibility.

= 2.1.0 - 06-06-2024 =

* Fixed: PHPCS Issues.
* Updated: Add 3rd param to `get_swatch_data()` method.
* New: `wpml-config.xml` file added to support WPML.
* Update: WooCommerce 8.9+ Compatibility.
* Update: Modify theme support feature. to `woo_variation_swatches`. Example: `add_theme_support( 'woo_variation_swatches', array( 'enable_stylesheet' => 'no', 'enable_tooltip' => 'no' ) );` for theme developer default setting control.

= 2.0.31 - 23-04-2024 =

* Update: WordPress 6.5+ Compatibility.
* Update: WooCommerce 8.8+ Compatibility.

= 2.0.30 - 15-01-2023 =

* Update: WooCommerce 8.4+ Compatibility.

= 2.0.29 - 21-11-2023 =

* Update: WordPress 6.4+ Compatibility.
* Update: WooCommerce 8.3+ Compatibility.

= 2.0.28 - 25-10-2023 =

* Fix: Hide disabled variation attributes

= 2.0.27 - 18-10-2023 =

* Fix: Block variation product add to cart
* Fix: Select2 hover css issue fixed.
* Update: WooCommerce 8.2 Compatibility.
* Add: Filter added: `woo_variation_swatches_remove_attribute_item` and `woo_variation_swatches_get_swatch_data`

= 2.0.26 - 05-09-2023 =

* Fix: All import plugin issue.

= 2.0.25 - 31-08-2023 =

* Add: WordPress 6.3 compatibility
* Add: WooCommerce 8.0 compatibility
* Update: Color / Image data can update by API Request

= 2.0.24 - 05-07-2023 =

* Fix: Bundle product radio attribute issue.

= 2.0.23 - 18-06-2023 =

* Update: WooCommerce 7.8 Compatibility
* Fix: API Issue

= 2.0.22 - 11-06-2023 =

* Fix: Tooltip width issue

= 2.0.21 - 02-06-2023 =

* Update: WooCommerce 7.7 Compatibility
* Update: Responsive tooltip
* Add: Additional rest api response
* Add: Filters to modify attribute template response

= 2.0.20 - 13-04-2023 =

* Fix: Product children check issue
* Update: WP 6.2 Compatibility

= 2.0.19 - 16-03-2023 =

* Add: High-Performance order storage (COT) feature compatibility.
* Fix: Variation cache cleaning issue.
* Update: WC 7.5 Compatibility

= 2.0.18 - 18-01-2023 =

* Update: caching function
* Update: WC 7.3 Compatibility

= 2.0.17 - 06-01-2023 =

* Add: show product variation image if image type attribute have no image selected
* Fix: clearing transient after attribute update

= 2.0.16 - 13-12-2022 =

* Fix: litespeed-cache plugin trigger error to use WP_Object_Cache::flush_group method

= 2.0.15 - 12-12-2022 =

* Fix: Object cache issue

= 2.0.14 - 06-12-2022 =

* Fix: Variation delete error

= 2.0.13 - 17-11-2022 =

* Update: Performance Improvement

= 2.0.12 - 31-10-2022 =

* Update: Update Migration Script
* Update: Modify `woo_variation_swatches_variable_item_custom_attributes` filter

= 2.0.11 - 28-09-2022 =

* Update: WooCommerce Support

= 2.0.10 - 15-09-2022 =

* Update: WooCommerce Support
* Update: JS Scripts
* Fix: WPML Option issue

= 2.0.9 - 28-08-2022 =

* Update: Translation String
* Update: Settings Script and RTL

= 2.0.8 - 17-08-2022 =

* Fix: Caching header issue

= 2.0.7 - 11-08-2022 =

* Update: Woocommerce 6.8 support
* Add: Caching header for ajax response

= 2.0.6 - 31-07-2022 =

* Fix: dependency script updated for product page settings save issue
* Update: `is_admin()` check removed for some theme support

= 2.0.5 - 20-07-2022 =

* Add: `woo_variation_swatches_html` filter to modify swatches output
* Add: `woo_variation_swatches_nav_widget_html` filter to modify widget output
* Update: `[wvs_show_archive_variation]` shortcode updated, now `product_id` can be added like: `[wvs_show_archive_variation product_id="ID"]`.
* Update: `data-dependency` attribute to `data-gwp_dependency` for possible conflict.
* Update: translation string.

= 2.0.4 - 08-07-2022 =

* Fix: Composite Product Selecting Issue
* Add: Option to change tick and cross icon color
* Add: Option to enable/disable preloader

= 2.0.3 - 26-06-2022 =

* Fix: Out of Stock info

= 2.0.2 - 23-06-2022 =

* Add: Image type fallback if no attribute image added.
* Fix: Archive add to cart issue
* Add: Ajax Quick View / Load More support

= 2.0.1 - 16-06-2022 =

* Add: Hex to RGBA color conversion on migration
* Fix: Disable attribute hide css issue.
* Fix: variation selected css issue.

= 2.0.0 - 13-06-2022 =

* Update: plugin structure change and add more features.

= 1.1.19 - 07-09-2021 =

* Fix: show_option_none value

= 1.1.18 - 06-09-2021 =

* Update: WooCommerce and WordPress Compatibility
* Fix: Bundle Product selected issue

= 1.1.17 - 08-05-2021 =

* Update: Update PHP functions

= 1.1.16 - 03-05-2021 =

* Fix: Backend CSS

= 1.1.15 - 24-04-2021 =

* Fix: WPML currency switcher issue.
* Add: Function added to get Dual color values

= 1.1.14 - 11-04-2021 =

* Fix: WPML product term meta issue.
* Update: Backend CSS

= 1.1.13 - 10-03-2021 =

* Add: WooCommerce and WordPress Compatibility
* Update: Remove `_.contains` and replace with `_.includes`

= 1.1.12 - 02-03-2021 =

* Update: Add UnderscoreJS on script dependency

= 1.1.11 - 01-03-2021 =

* Fix: WooCommerce Composite Products selection issue
* Update: Swatch support for Global Ajax complete event
* Update: Swatches Markup Modified
* New: Disable WoodMart theme default swatches

= 1.1.10 - 16-02-2021 =

* Fix: Error when woocommerce not available

= 1.1.9 - 12-02-2021 =

* Fix: no_cache header modification issue

= 1.1.8 - 08-02-2021 =

* Fix: RTL label css
* Update: Load scripts globally

= 1.1.7 - 01-02-2021 =

* Support: WooCommerce Composite Product

= 1.1.6 - 27-01-2021 =

* Update: Extended note on Performance Section

= 1.1.5 - 13-01-2021 =

* Update: Remove `wp_ajax_wvs_get_available_variations` hook

= 1.1.4 - 12-01-2021 =

* Fix: `WC_Cache_Helper::invalidate_cache_group()` method issue.

= 1.1.3 - 10-01-2021 =

* Update: Use `WC_Cache_Helper` for template caching
* Update: Support Latest WP and WC

= 1.1.2 - 26-11-2020 =

* Fix: Customizer JS Issue.

= 1.1.1 - 16-11-2020 =

* Update: WooCommerce 4.7 compatibility

= 1.1.0 - 27-10-2020 =

* Add: Selected attribute name beside label
* Update: Support WooCommerce 4.6 version
* Update: Accessibility

= 1.0.86 =

* Update: Support WooCommerce 4.4 version
* Update: Support WordPress 5.5 version
* Fix: Alpha color channel issue

= 1.0.85 =

* Update: Support WooCommerce 4.3 version

= 1.0.84 =

* Add: `wvs-no-css` will add on when stylesheet disabled. `wvs-css` will add on when stylesheet enabled, `wvs-no-tooltip` will add on when tooltip disabled, `wvs-tooltip` will add on when tooltip enabled.

= 1.0.83 =

* Update: Remove CSS Selector from body tag.

= 1.0.82 =

* Add: Filter `wvs_product_global_attribute_image_id` to modify attribute image.
* Add: Variation attribute image preview based on availability.

= 1.0.81 =

* Fix: User select issue on firefox mobile.

= 1.0.80 =

* Add: WordPress 5.4 compatibility
* Fix: Default image for quote attribute value.

= 1.0.79 =

* Fix: Rollback some features to 1.0.76 to fix a known issue.

= 1.0.78 =

* Fix: Attribute behavior: Hide issue

= 1.0.77 =

* Add: WooCommerce 4.0 compatibility
* Fix: Facebook for woocommerce issue.
* Divi theme css issue fix.

= 1.0.76 =

* Fix: settings option php notice.

= 1.0.75 =

* Fix: mobile double tab clear issue.

= 1.0.74 =

* Fix: tooltip issue.

= 1.0.73 =

* Fix: QuickView issue

= 1.0.72 =

* Add: Namespace on themes events
* Fix: WooCommerce Product bundle Issue.

= 1.0.71 =

* Update: Tooltip transform
* Fix: Field Dependency Js Load

= 1.0.70 =

* Fix: Divi builder load issue.
* Fix: Default selected tooltip Issue.
* Update: Field Dependency css.

= 1.0.69 =

* Update: Disable Defer JS Load by Default and on mobile device.

= 1.0.68 =

* Fix: WooCommerce transients clear fix

= 1.0.67 =

* Fix: Swatch Image Size CSS Issue

= 1.0.66 =

* Fix: Simple product Issue

= 1.0.65 =

* Add: Speed Improvement

= 1.0.64 =

* Fix: Mobile Scroll Issue

= 1.0.63 =

* Add: Select attribute set type button by default.
* Fix: Transients issue.

= 1.0.62 =

* Add: WC 3.7 update

= 1.0.61 =

* Fix: CSS source map issue.

= 1.0.60 =

* Fix: Clear selection js error fix.

= 1.0.59 =

* Add: Dokan Multivendor Support added

= 1.0.58 =

* Fix: Inactive WooCommerce notice
* Fix: Known Issues

= 1.0.57 =

* Fix: Some known issues
* Add: WordPress 5.2 Compatibility

= 1.0.56 =

* Fix: Language load
* Add: FacetWP Load More Option
* Add: WooCommerce 3.6 update

= 1.0.55 =

* Add: Oxygen Theme Support CSS

= 1.0.54 =

* Fix: Load Assets without CDN.
* Remove: Product Page popup attribute

= 1.0.53 =

* Fix: IE 11, CSS flexbox issue

= 1.0.52 =

* Add: Yith Product filter support

= 1.0.51 =

* Add: Flatsome theme Infinite Scroll Support
* Fix: Massive Dynamic theme CSS Issue

= 1.0.50 =

* Fix: Archive more click issue.
* Add: Adiva Theme support.

= 1.0.49 =

* Fix: Gecko theme support
* Add: Set default Shape style to Squared Shape

= 1.0.48 =

* Fix: Some speed issue.

= 1.0.47 =

* Add: Filter `disable_wvs_body_class` to disable or enable body class.

= 1.0.46 =

* Add: Filter `disable_wvs_enqueue_scripts` to disable or enable enqueuing scripts.
* Add: Filter `disable_wvs_inline_style` to disable or enable inline style.

= 1.0.45 =

* Fix: WC 3.5 Issue

= 1.0.44 =

* Add: IE-11 Support
* Add: Exporter-Importer

= 1.0.43 =

* Fix: Uncode theme Issue

= 1.0.42 =

* Fix: JS Reload variation.

= 1.0.41 =

* Fix: uShop by themetim CSS Issue

= 1.0.40 =

* Improve: Live Feed

= 1.0.39 and ******** =

* Fix: Elementor Pro CSS Issue

= 1.0.38 =

* Remove: Pro Class from Settings

= 1.0.37 =

* Add: RTL Support
* Fix: The Gem Theme CSS Issue

= 1.0.36 =

* Add: Yith Infinite Scroll support
* Fix: Yoast SEO Focused keyword issue.

= 1.0.35 =

* Add: AURUM theme support
* Fix: WooCommerce Product Bundle conflict.

= 1.0.34 =

* Fix: Known JS Issue

= 1.0.33 =

* Fix: CSS Issue

= 1.0.32 =

* Add: Out of stock product crossed also if "Hide out of stock items from the catalog" unchecked.

= 1.0.31 =

* Add: **Kalium** theme css fix

= 1.0.30 =

* Add: body class uniqueness
* Add: Centralise feed
* Fix: Elementor Pro JS Issue

= 1.0.29 =

* Add: IE-11 fallback
* Add: Increase `variation_threshold` label
* Fix: Divi woo_layout_injector script override

= 1.0.28 =

* Add: Admin Notice CSS

= 1.0.27 =

* Add: SelectBox class for more control
* Add: **StockHolm** Theme Compatibility

= 1.0.26 =

* Improve: Tooltip
* Add: **Enfold** Theme Compatibility

= 1.0.25 =

* Add: `gwp_affiliate_id` Filter for Affiliate link ID in more place.

= 1.0.24 =

* Improve: Frontend Visual Appearance
* Fix: Some known issues

= 1.0.23 =

* Improve: Frontend Visual Appearance
* Add: **Flatsome** Theme shortcode override

= 1.0.22 =

* Add: **OceanWP** Theme Compatibility
* Add: **Sober** Theme Compatibility
* Add: **Shophistic Lite** Theme Compatibility
* Add: **Old Haven** Theme Compatibility

= 1.0.21 =

* Fix: Known JS Issue
* Fix: Tooltip disable item disable issue
* Add: `gwp_affiliate_id` Filter to add affiliate referral link

= 1.0.20 =

* New: Font Size settings
* Improve: Frontend Visual Appearance

= 1.0.19 =

* New: Size settings
* Improve: Visual Appearance

= 1.0.18 =

* Update: CSS
* New: Attribute behaviour

= 1.0.17 =

* Fix: Visual CSS Issue

= 1.0.16 =

* Update: Improve JS Functionality
* Update: Multisite Compatibility

= 1.0.15 =

* New: Attribute deselect option
* Fix: Custom attribute select from product
* Update: translation

= 1.0.14 =

* New: Support Jetpack's Infinite Scroll event, `post-load` event.
* New: `wvs_product_attribute_image_size` filter to change attribute image size
* New: Increased tooltip z-index

= 1.0.13 =

* Fix: Avada theme select issue

= 1.0.12 =

* Fix: Disable publish button issue

= 1.0.11 =

* New: Attribute create from product
* New: Variation Preview color, image
* New: Settings option also added on customizer
* Fix: Any variation issue caused by Out-of-Stock variation

= 1.0.10 =

* New: Extendable hooks added to extend
* New: ajax variation threshold option added to control ajax variation.
* Fix: Out Of Stock Product Issue.

= 1.0.9 =

* Fix: Merged Pull request from `spoyntersmith`
* Fix: Tooltip hardware acceleration issue fix for theme animation
* Fix: use jquery `sibling` instead of `prev`

= 1.0.8 =

* Update: Improve variation javascript to support ajax variation
* Update: Renamed tooltip attribute to resolve conflict
* Update: Renamed variation javascript class name

= 1.0.7 - 2018-01-21 =

* Update: Improving frontend CSS
* Update: Disable Bootstrap tooltip conflict

= 1.0.6 - 2018-01-19 =

* Update: translation

= 1.0.5 - 2018-01-19 =

* Fix: backend js issue

= 1.0.4 - 2018-01-17 =

* Fix: Number Select Issue

= 1.0.3 - 2018-01-15 =

* New: Css class on body based on settings
* Update: tooltip and frontend css changed
* New: `add_theme_support( 'woo-variation-swatches', array( 'tooltip' => FALSE, 'stylesheet' => FALSE ) );` for theme developer default setting control.
* New: `wvs_clear_transient` to clear saved transient.

= 1.0.2 - 2018-01-08 =

* New: attribute tooltip
* New: Default stylesheet enable/disable option
* New: Display style added to show Rounded / Squared shaped style

= 1.0.1 - 2017-12-23 =

* Fix: text type select list

= 1.0.0 - 2017-12-20 =

* Initial release

== Upgrade Notice ==

= 2.0 =
If you are using PRO version of this plugin please disable it first. This version has a lot of update.
