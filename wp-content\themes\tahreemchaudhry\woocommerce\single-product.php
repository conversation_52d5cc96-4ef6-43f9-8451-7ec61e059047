<?php

/**
 * The Template for displaying all single products
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/single-product.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see         https://woocommerce.com/document/template-structure/
 * @package     WooCommerce\Templates
 * @version     1.6.4
 */

if (! defined('ABSPATH')) {
	exit; // Exit if accessed directly
}

get_header('shop'); ?>

<div class="container my-9">
	<div class="row">
		<div class="col-12">
			<?php
			/**
			 * woocommerce_before_main_content hook.
			 *
			 * @hooked woocommerce_output_content_wrapper - 10 (outputs opening divs for the content)
			 * @hooked woocommerce_breadcrumb - 20
			 */
			do_action('woocommerce_before_main_content');
			?>

			<?php while (have_posts()) : ?>
				<?php the_post(); ?>

				<?php wc_get_template_part('content', 'single-product'); ?>

			<?php endwhile; // end of the loop. 
			?>

			<?php
			/**
			 * woocommerce_after_main_content hook.
			 *
			 * @hooked woocommerce_output_content_wrapper_end - 10 (outputs closing divs for the content)
			 */
			do_action('woocommerce_after_main_content');
			?>

			<?php
			/**
			 * woocommerce_sidebar hook.
			 *
			 * @hooked woocommerce_get_sidebar - 10
			 */
			//do_action('woocommerce_sidebar');
			?>
		</div>
	</div>
</div>


<script>
document.addEventListener("DOMContentLoaded", function () {
  const qtyInput = document.querySelector('input.qty');
  const checkbox = document.getElementById('giftWrapCheckbox');
  const labelSpan = document.getElementById('giftWrapLabel');
  const giftWrapPrice = <?php echo json_encode(get_field('gift_wrap_price', get_the_ID())); ?>;
  const baseLabel = <?php echo json_encode(get_field('gift_wrap_label', get_the_ID())); ?>;

  function formatPrice(price) {
    return price % 1 === 0 ? price : price.toFixed(2);
  }

  function updateGiftWrapLabel() {
    const qty = parseInt(qtyInput.value) || 1;
    const total = giftWrapPrice * qty;
    const formatted = `(+Rs ${formatPrice(total)})`;
    labelSpan.textContent = checkbox.checked ? `${baseLabel} ${formatted}` : baseLabel;
  }

  if (qtyInput && checkbox && labelSpan) {
    qtyInput.addEventListener('input', updateGiftWrapLabel);
    checkbox.addEventListener('change', updateGiftWrapLabel);
    updateGiftWrapLabel();
  }
});
</script>

<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.css" />
<script src="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.js"></script>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fancyapps/ui@4.0/dist/fancybox.css" />
<script src="https://cdn.jsdelivr.net/npm/@fancyapps/ui@4.0/dist/fancybox.umd.js"></script>
<script>
document.addEventListener("DOMContentLoaded", function () {
  // Swiper init (unchanged)
  window.productSliderThumbs = new Swiper('.singleProductThumbSlider', {
    slidesPerView: 4,
    spaceBetween: 10,
    freeMode: true,
    watchSlidesProgress: true,
    // breakpoints: {
    //     0: { slidesPerView: 3 },
    //     576: { slidesPerView: 4 },
    //     768: { slidesPerView: 5 },
    //     992: { slidesPerView: 6 },
    // }
  });

  window.productSliderMain = new Swiper('.productSingleMainSlider', {
    slidesPerView: 1,
    spaceBetween: 10,
    effect: 'fade',
    fadeEffect: {
      crossFade: true, // Optional for smoother fade
    },
    autoHeight: true,
    loop: false,
    navigation: {
        nextEl: '.swiper-button-next',
        prevEl: '.swiper-button-prev',
    },
    thumbs: { swiper: window.productSliderThumbs }
    //thumbs: { swiper: thumbs }
  });

  // Zoom on hover
  document.querySelectorAll('.singleProductZoomContainer').forEach(container => {
    const img = container.querySelector('.singleProductZoomImg');

    container.addEventListener("mousemove", function (e) {
      const rect = container.getBoundingClientRect();
      const x = ((e.clientX - rect.left) / rect.width) * 100;
      const y = ((e.clientY - rect.top) / rect.height) * 100;

      img.style.transformOrigin = `${x}% ${y}%`;
      img.style.transform = "scale(2)";
    });

    container.addEventListener("mouseleave", function () {
      img.style.transform = "scale(1)";
      img.style.transformOrigin = "center center";
    });
  });

  Fancybox.bind('[data-fancybox="product-gallery"]', {
  Thumbs: {
    autoStart: false,
  },
  Toolbar: {
    display: [
      "zoom",
      "slideshow",
      "fullscreen",
      "thumbs",
      "close"
    ]
  },
  on: {
    done: (fancybox, slide) => {
      // Wait a tiny bit for DOM to update
      setTimeout(() => {
        // Try iframe
        const iframe = document.querySelector(".fancybox__iframe");

        if (iframe) {
          Object.assign(iframe.style, {
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
            height: "80vh",
            width: "100%",
          });
        }

        // Try HTML5 video
        const video = document.querySelector(".fancybox__content video");

        if (video) {
          Object.assign(video.style, {
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
            height: "80vh",
            width: "100%",
          });
        }
      }, 100); // Slight delay to ensure element exists
    }
  }
});


});
</script>

<?php
get_footer('shop');

/* Omit closing PHP tag at the end of PHP files to avoid "headers already sent" issues. */
