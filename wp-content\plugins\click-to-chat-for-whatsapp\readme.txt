=== Click to Chat - HoliThemes ===
Requires at least: 4.7
Tested up to: 6.8.2
Requires PHP: 5.6
Contributors: HoliThemes
Stable tag: 4.26
Tags: whatsapp, whatsapp business, click to chat, whatsapp chat, WooCommerce WhatsApp
License: GPLv2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html

WhatsApp Chat🔥. Let's make your Web page visitors contact you through 'WhatsApp', 'WhatsApp Business'. Add matching Widget✅

== Description ==

WhatsApp Chat. Let's make your Web page visitors contact you through "WhatsApp" or "WhatsApp Business" with a single click (WhatsApp Chat, Group).

[Demo](https://holithemes.com/plugins/click-to-chat/list-of-styles/)  |  [Documentation](https://holithemes.com/plugins/click-to-chat/)  |  [Support](https://holithemes.com/plugins/click-to-chat/support/)  |  [PRO](https://holithemes.com/plugins/click-to-chat/pricing/)

== WhatsApp Chat ==

Add 'WhatsApp' or 'WhatsApp Business' Number and let your website visitors contact you with a single click.

**📱 Mobile:**  Open the WhatsApp Mobile App for a seamless connection.

**💻 Desktop:** Direct visitors to the WhatsApp Desktop App or Web WhatsApp page (web.whatsapp.com)

New in 4.26: Custom URL: WhatsApp Channel URL
* Now we can add a WhatsApp Channel URL in the plugin settings, allowing visitors to join your WhatsApp Channel directly.

https://youtu.be/O_BF9rhazvI

== 💎 Styles ==

Select a style that complements the design of your website.

* 8 pre-defined customizable styles/designs
* Add your own Image/GIF
* Custom Element/Design (convert any element to a WhatsApp Chat element)
* Shortcodes (Add WhatsApp button/icon with inline the content)
* Different Styles, Positions for Mobile, Desktop
* Choose a style and customize it to match the website's design

[list of Styles](https://holithemes.com/plugins/click-to-chat/list-of-styles/)  |  🎨[Customize Styles](https://holithemes.com/plugins/click-to-chat/customize-styles/)


= 💡 Add Own Image =

Instead of selecting a pre-defined style, add any Image/Animated-image/GIF.

== 🌈 Custom Element == 

Convert any Element as a WhatsApp Chat Element by adding  

* Class/ID name: 'ctc_chat' (or)
* Href/link: '#ctc_chat'

The custom design element will navigate to WhatsApp based on plugin settings (WhatsApp Number, pre-filled message, Analytics, .... ). 
(e.g. menu item, button, image, link - just add ctc_chat as a class name)

[Custom Element](https://holithemes.com/plugins/click-to-chat/custom-element/)

== 🎉 Greetings Dialog ==

Add customizable greeting dialogs for boosting user attention and increasing interaction. Seamlessly integrate these greetings into your website for better engagement.

* [Greetings-1](https://holithemes.com/plugins/click-to-chat/greetings-1/) - Customizable Design: Personalize the design to match your branding with full control over fonts, colors, images, and more.
* [Greetings-2](https://holithemes.com/plugins/click-to-chat/greetings-2/) - Content Specific: Deliver focused messages that resonate effectively with your users.

= 📝 Form Filling =

Get the necessary information from the website visitors before initiating the chat.

* Get an email notification when the user fills out the form.
* Call a webhook with the form data to integrate with other applications. Using integrations tools add data in Google Sheet, CRM and many more applications.
* Form data can be prefilled in the WhatsApp chat window.

PRO: [Greetings-Form](https://holithemes.com/plugins/click-to-chat/greetings-form/)

= 👥 Multi-Agent =

Add multiple WhatsApp chat accounts within a single Greetings dialog.

* We can set different time ranges for each agent. (24x7 or multiple time ranges for each day of the week).
* Offline Agents
    * Chat when offline (display agent with next available time).
    * Disable chat (display agent with next available time).
    * Hide offline agents

PRO: [Multi-Agent](https://holithemes.com/plugins/click-to-chat/multi-agent)

= ⌛ Greetings Actions =

* **Click Action**: Displays greeting dialog when a user clicks on any element with the class name: ‘ctc_greetings’.
* **ViewPort Action**: Displays Greetings When an element is in/reached viewport(25% margin) with the Class name: 'ctc_greetings_now' [PRO]
* **Time, Scroll Actions**: Display Greetings based on time, scroll [PRO]

These actions enhance user interaction by triggering greetings at the right moments, improving engagement and support efficiency.

[Actions](https://holithemes.com/plugins/click-to-chat/greetings-actions/)

== 🔴 Notification Badge ==

Get user attention by displaying a notification badge on the WhatsApp Chat element.  

Customize the notification badge with the notification count, text color, background color, border color, and time delay to display the notification badge.

[Notification Badge](https://holithemes.com/plugins/click-to-chat/notification-badge/)

== ✳️ Position to Place == 

* Add WhatsApp at any position of the screen  
    (not limited to fixed positions).
* Different positions for Mobile and Desktop.  
     
== ⏩ Pre-filled Message ==

Text that appears in the WhatsApp chat window when the user clicks on the WhatsApp. 

Users can easily start the conversation.

Variables to change values dynamically  

* **{site}** -> Website Title
* **{title}** -> Page Title
* **{url}** -> Web page URL
* **[url]** -> Web page full URL including query parameters

With these variables, we can understand from which page the user started WhatsApp chat.
 
[Pre-filled Message](https://holithemes.com/plugins/click-to-chat/pre-filled-message/)

== 🛍️ WooCommerce ==

== WooCommerce product pages ==

For WooCommerce, single product pages can overwrite the main setings to add a product specific message using dynamic variables.

* Pre-filled Message
* Call to Action
* Greetings Template, Content [PRO]

Additional variables, specific to WooCommerce single product page to change values dynamically   

* **{product}** -> Product Name
* **{price}** -> Product Price (current price)
* **{regular_price}** -> Regular product price (without any sale)
* **{sku}** -> Stock keeping unit

For Shop, Cart, Checkout, and Account pages we can overwrite at page level settings

== Add WhatsApp - Single Product Pages == 

Add WhatsApp button or icon at WooCommerce single product pages. 

* Before Main Content
* Before Product
* Before Product Summary
* Product Summary
* Before Add to Cart Form
* Before Cart Button
* After Cart Button
* After Add to Cart Form
* After Product
* After product summary

We can add dynamic variables for the Call to Action.
E.g. Buy {product}
{product} will be replaced with the product name for all product pages.

[Add WhatsApp Icon/Button in WooCommerce Product pages](https://holithemes.com/plugins/click-to-chat/add-whatsapp-in-woocommerce-single-product-pages/)

= Add WhatsApp - Shop Page =

Add WhatsApp to WooCommerce Products list (shop page, related products list)

[Shop page](https://holithemes.com/plugins/click-to-chat/whatsapp-chat-in-woocommerce-shop-page/)

== 📒 Page Level Settings ==

At the page level, we can overwrite the settings for each post. We can add a different Whatsapp Number, Prefilled Message, Call to Action for each post
(while editing the post, in the right sidebar 'Click to Chat' meta box)

* WhatsApp Number
* Call to Action
* Pre-filled Message
* Display Settings

PRO:

* Change Styles
* Time Delay
* Scroll Delay
* Greetings Template
* Greetings Header, Main, Bottom Content

[Page-level settings](https://holithemes.com/plugins/click-to-chat/change-values-at-page-level/)

== 📈 Analytics ==

Creates an Event when the user clicks on the WhatsApp Icon/button.

[Google Analytics](https://holithemes.com/plugins/click-to-chat/google-analytics/)

[Meta Pixel](https://holithemes.com/plugins/click-to-chat/facebook-pixel/)

[Google Ads Conversations](https://holithemes.com/plugins/click-to-chat/google-ads-conversion/)

== Webhooks ==

Connect other applications using Integrate, Automation tools like Zapier, IFTTT, Pipedream, etc.

[Webhooks](https://holithemes.com/plugins/click-to-chat/webhooks)

== ⭐ PRO ==

* Multi-Agent: Displays multiple WhatsApp chat accounts
    * Set multiple time ranges for each agent's availability
    * Hide offline agents or display agent when offline with next available time
* Random Numbers: Navigates to a random WhatsApp number from the predefined list
* Form: Get the necessary information from the website visitors, before initiating the chat
    * Get an email notification when the user fills out the form.
    * Use webhooks to send form data to external apps in real-time for seamless integration.
    * Prefill WhatsApp messages with user-provided form data to streamline conversations
* Business hours: Offline/Online Settings
    * Set business hours for a specific time range within a day, specific days in a week.
    * Hide the widget during offline hours or automatically change the WhatsApp numbers and call to action.
* Display based on website visitor's country
* Webhooks - Dynamic variables
    * {url} - Current page URL.
    * {time} - Time user interacted with the WhatsApp Button/Icon.
    * {number} - WhatsApp Number associated with the icon/button.
    * Get values from url parameters by adding name with in single square brackets E.g. [gclid], [utm_source]
    * Get values from cookies by adding the name with in double square brackets. E.g. [[cookie_name]]
* Get additional values at Google Analytics, Meta Pixel
    * Get values from url parameters. E.g. [gclid], [utm_source]
    * Get values from cookies. E.g. [[cookie_name]]
* Greetings Actions: (for all greeting dialogs: Greetings-1, Greetings-2, Form, Multi-agent)
    * Display greetings dynamically based on user actions such as 
        * Time: Time spent on the page, 
        * Scroll: Page scroll percentage, 
        * Click: specific button clicks 
        * ViewPort: when an element becomes visible in the viewport.
* Position to place
    * Fixed: Fixed position on the screen (default position)
    * Absolute: Fixed position to the body content. (Moves when the user scrolls the page)
* Time Delay & Scroll Delay: Display WhatsApp widget after a specified time delay or once the user scrolls a certain percentage of the page.
* Display based on Website visitor's login status
* Page-level settings: Fine-tune WhatsApp button behavior for individual pages. Change styles, time delays, scroll delay, Greetings Template, and Greetings Content
* WooCommerce: Customize WhatsApp widget behavior specifically for WooCommerce pages
    * Overwrite greetings template, Content for Single product pages.
    * Overwrite settings for Shop, Checkout, and Account pages at page-level settings


🔆 [PRO](https://holithemes.com/plugins/click-to-chat/pricing/)

== 🎯 Localization ==

Click to Chat is Compatible with translation plugins. [WPML](https://wpml.org/), Polylang.

It is easy to set up different values for each language   

* WhatsApp Number
* Call to Action
* Pre-filled Message
* Greetings Content
* Group ID
* Share Text

Setup for [WPML](https://holithemes.com/plugins/click-to-chat/translate-click-to-chat-settings-using-wpml-plugin/), [Polylang](https://holithemes.com/plugins/click-to-chat/translate-click-to-chat-settings-using-polylang-plugin/)

== 🚀 Performance ==

* Rich in features, but the site’s front end is very lightweight.

We highly concentrate on speed and performance.

== 👓 Display Settings == 

Customize the visibility of the widget by showing or hiding styles based on specific settings, such as:

* Post type
* Post Id
* Category name
* Device Type(Mobile, Desktop)
* WooCommerce single product pages

PRO:

* Time delay
* Scroll delay
* Selected time range in a day
* Selected Days in a week
* Website visitor login status
* Website visitor country

== ✅ Opt-in ==

Make the website users opt-in / accept consent before initiating the chat.

[Opt-in](https://holithemes.com/plugins/click-to-chat/opt-in/)

== ⛳ Shortcodes ==

Use shortcodes to instead a WhatsApp icon or button with inline the Post content, widget area.

change the default setting values using shortcode attributes - WhatsApp Number, Style, Pre-filled message, Call to Action.

= Chat Shortcodes =

[ht-ctc-chat]

To change the WhatsApp number use the 'number' attribute

[ht-ctc-chat number=915123456789]

[Shortcodes for Chat](https://holithemes.com/plugins/click-to-chat/shortcodes-chat/)

= Group =

Enable the Group chat feature and add WhatsApp Group id in the plugin settings.

Make it easy for your customers to join Whatsapp Group.

[Group](https://holithemes.com/plugins/click-to-chat/group-chat-feature/)

= Share =

Let users share your website with their WhatsApp contacts and get more leads.

[Share](https://holithemes.com/plugins/click-to-chat/share-feature/)

= 🌏 Help Translate The Plugin =

Help by [Translating the plugin](https://translate.wordpress.org/projects/wp-plugins/click-to-chat-for-whatsapp/) to be available in more languages  


== screenshots ==

1. 8 Styles, 1 Add your own image or GIF. Add at any position to the screen (Desktop, Mobile: can set different styles, positions)
1. Select a style and customize it to match your website design.
1. Chat Settings - Enter WhatsApp Number, prefilled message, call to action, desktop: web view / desktop app view
1. Style, position to place (at any position of the screen) - Desktop, Mobile
1. Add WhatsApp Menu item (Custom Element)
1. Change Values at the page level: 'WhatsApp Number', 'Call to action', 'Group ID'
1. Style - 5 - Image with content box
1. Style - 8 - Materialize button
1. Style - 99 - Add your own Image / GIF(Animated Image)
1. Customize Styles
1. Enable Group, Share Settings
1. Group settings page
1. Share settings page
1. Display settings
1. Webhooks
1. prefilled, call to action for WooCommerce single product pages
1. Animations
1. Analytics - Google Analytics, Meta Pixel, Google Ads Conversations


== Installation ==

= From Dashboard ( WordPress admin ) =
* plugins -> Add New
* search for 'click to chat'
* click on Install Now and then Activate.

= using FTP or similar =
* Unzip "Click-to-Chat" file and 
* Upload "Click-to-Chat" folder to the "/wp-content/plugins/" directory.
* Activate the plugin through the "Plugins" menu in WordPress.

== Frequently Asked Questions ==

= WhatsApp Number = 

Enter the WhatsApp number with country code 

E.g.
country code +1
number: 6123456789
** WhatsApp number: 16123456789 **

(WhatsApp Number)[https://holithemes.com/plugins/click-to-chat/whatsapp-number/]


= Pre-filled Message =

Text that appears in the WhatsApp chat window when the user clicks on the WhatsApp.

Add variables to change values dynamically  
`
{url} -> Web page URL
{title} -> Page Title
{site} -> Website Title
`

E.g.

    Hi {site}!!
    I like to know more information about this {title}, {url}.

Variables for WooCommerce single product pages  


`
{product} -> Product Name
{price} -> Product Price (current price might be the sale price/regular price)
{regular_price} -> Regular product price (with out any sale)
{sku} ->Stock keeping unit
`

E.g.

    Hi {site}!!
    Name: 
    I like to buy this {product}, {url}


With this prefilled text, we can know from which page the user started the conversation, the purpose of the contact, and their details

[Pre-filled Message](https://holithemes.com/plugins/click-to-chat/pre-filled-message/)
[Pre-filled Message for WooCommerce](https://holithemes.com/plugins/click-to-chat/woocommerce-single-product-pages/)

= Select Styles =

Select Styles/widget that matches the design
* customize the style to match the website design.
* Add your own Image/GIF
* Custom Element/Design (convert any element to a WhatsApp Chat element)
* Select differnt Styles for mobile, desktop to match the website design.

= Position to place the styles =

Add Styles at any position of the screen (not just fixed to selected positions). 

*Position to place* settings

E.g.  

> bottom-right corner:
bottom: 10px
right: 10px

> Center of the Screen (bottom-center)
bottom: 10px 
right: 50%

> Center of the Screen (left-center)
bottom: 50%
right: 10px

Plugin supports all CSS units as the suffix. (px, %, rem,...)

[position to place](https://holithemes.com/plugins/click-to-chat/position-to-place/)

= GDPR = 

"Click to Chat" don't collect any of the user data and don't use cookies.

User consent before starting the chat.

[Opt-in](https://holithemes.com/plugins/click-to-chat/opt-in/)

= Display only on selected countries =

[Display based on country](https://holithemes.com/plugins/click-to-chat/display-based-on-country/)

= Show/Hide on Selected pages/devices = 

[Show only on selected pages](https://holithemes.com/plugins/click-to-chat/show-only-on-selected-pages/)

[Hide only on selected pages](https://holithemes.com/plugins/click-to-chat/hide-only-on-selected-pages/)

[Show/Hide on Mobile/Desktop](https://holithemes.com/plugins/click-to-chat/show-hide-on-mobile-desktop/)

= Localization: Change number, content for each language / page =

We can change values for each language if using WPML, Polylang for translation.

[Multilingual](https://holithemes.com/plugins/click-to-chat/multilingual/)

From WPML / Polylang Settings -> 'String Translations' and filter strings to 'Click to chat for WhatsApp' and change value based on language.

* User Input (Plugin Settings values): - 'Click to chat for WhatsApp'  
* Admin strings (Plugin Settings) - text domain: 'click-to-chat-for-whatsapp'

[Page-level settings](https://holithemes.com/plugins/click-to-chat/change-values-at-page-level/)

= WhatsApp Group Invite/chat = 

[Enable Group Feature](https://holithemes.com/plugins/click-to-chat/enable-group-feature/) Add [WhatsApp group id](https://holithemes.com/plugins/click-to-chat/find-whatsapp-group-id/) at plugin settings page. and let user join in WhatsApp Group.

= WhatsApp Share =

[Enable Share feature](https://holithemes.com/plugins/click-to-chat/enable-share-feature/) and let users share your website easily.

Add variables to change values dynamically  
    {url} -> Web page URL
    {title} -> Page Title
    {site} -> Website Title

Share Text - E.g.

> Checkout this Awesome page {title}, {url}

= AMP Compatibility =

Click to Chat is Compatible with (AMP)[https://wordpress.org/plugins/amp/] plugin. AMP won't load plugins JavaScript files so limited functionality available.

= 🔧 Basic Troubleshooting = 

* Clear cache
    * from Cache plugins
    * Server side cache (some hosting providers adds cache functionality by default)
    * CDN cache (Cache invalidation)
* Check Display settings
* (page level settings)[https://holithemes.com/plugins/click-to-chat/change-values-at-page-level/]

✍️ For any queries, please contact us.

= ❤️ Support / Contact =

For any issues with the plugin / suggestions:

* WordPress [new topic](https://wordpress.org/support/plugin/click-to-chat-for-whatsapp/#new-topic-0) 

= ⭐️ Give Support =

If you like the plugin, support the developers by giving [5 star rating](https://wordpress.org/support/plugin/click-to-chat-for-whatsapp/reviews/#new-post)  

Thank you so much in advance from "Click to Chat" Team

== Upgrade Notice ==

= using FTP or similar =
* Delete Click-to-Chat folder - your setting will not lost.
* unzip Click-to-Chat file and 
* Upload "Click-to-Chat" folder to the "/wp-content/plugins/" directory.
* Activate the plugin through the "Plugins" menu in WordPress.

= From WordPress Dashboard =
* From wp-admin -> plugins
* Click Update now when a new version is available
* Or enable Auto-updates to update automatically

== Changelog ==

= 4.26 =
* Custom URL feature is now available in this main plugin. We can add WhatsApp Chanel URL in the plugin settings to open WhatsApp Channel. 
* Enhancement: Page level settings design.

= 4.25 =
* Fix: Greeting dialog modal not displaying correctly due to z-index issues when certain background elements were present.

= 4.24 =
* Fixed: Page-level settings now work correctly on the WooCommerce Shop page.
* Improved: Better visual styling for the base widget.

= 4.23 =
🚨 Important Update
* ✨ New Feature: Greetings Dialog can now appear as a modal for better visibility and UX.
* 🛠️ Fix: Resolved an issue where the Greetings Dialog was not displaying correctly when the base widget was positioned at the top.
* 🛠️ Fix: On iPhone Chrome, the Share feature now opens WhatsApp correctly without opening a new tab.
* 🔧 Improvement: When a WhatsApp number is not set, the admin notice is now displayed using a more reliable and secure method — instead of using JS .html().

= 4.22 =
* The Custom Image widget now automatically uses the image file name as the alt attribute
* Settings to disable page-level settings.

= 4.21 =
* Fix: Resolved issue where phone numbers with +1888 prefix were not being saved in settings.

= 4.20 =
* New: Added {{price}} variable for WooCommerce product pages to display price with currency symbol, thousand separator, and decimal separator.
* Fix: Resolved conflict with some themes caused by animation class name.

= 4.19 =
* Fix: PHP Error. Thanks, Malae, for reporting it!

= 4.18 =
* Fix: Resolved an issue where the Style-5 profile image was displaying above the greetings dialog on mobile devices.
* Fix: Corrected the border alignment in Style-5.
* Enhancement: Admin Demo for better usability

= 4.17 =
* Fix: Display Widget as an Add to Cart like button is not working on the WooCommerce single product page, Archive list.

= 4.16 =
* Added 'Alt' attribute value for greetings header image
* Fix: Style-7, Style-7 Extend hover effects not working.

= 4.15 = 
* Fix: Page-level settings not working properly.
* Fix: Alignment issues at admin setting pages

= 4.14 =
* Enhancement: Show or hide the admin demo.

= 4.13 =
* Fix: Alignment issues at admin setting pages

= 4.12.1 =
* Fix: Hover effects for Style-7 Extend
* Fix: Dual lines issue for Style-8 with large call to action text

= 4.12 =
* Fix: Custom CSS Code block not allowing quotes at front end

= 4.11 =
* New: Custom CSS Code block.

= 4.10 =
* Fix: Page level display settings for WooCommerce single product page

= 4.9 =
* Fix: Display based on device not working as expected on iPad Pro

[Changelog](https://holithemes.com/plugins/click-to-chat/changelog/)