# Advanced Custom Fields Translations are a combination of translate.wordpress.org contributions,
# combined with user contributed strings for the PRO version.
# Translations from translate.wordpress.org take priority over translations in this file.
# translate.wordpress.org contributions are synced at the time of each release.
#
# If you would like to contribute translations, please visit
# https://translate.wordpress.org/projects/wp-plugins/advanced-custom-fields/stable/
#
# For additional ACF PRO strings, please submit a pull request over on the ACF GitHub repo at
# http://github.com/advancedcustomfields/acf using the .pot (and any existing .po) files in /lang/pro/
#
# This file is distributed under the same license as Advanced Custom Fields.
msgid ""
msgstr ""
"PO-Revision-Date: 2025-07-21T18:29:30+00:00\n"
"Report-Msgid-Bugs-To: http://support.advancedcustomfields.com\n"
"Language: fr_FR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: gettext\n"
"Project-Id-Version: Advanced Custom Fields\n"

#: includes/fields/class-acf-field-select.php:471
msgid ""
"Save created options back to the \"Choices\" setting in the field definition."
msgstr ""

#: includes/fields/class-acf-field-select.php:470
msgid "Save Options"
msgstr ""

#: includes/fields/class-acf-field-select.php:448
msgid ""
"Allow content editors to create new options by typing in the Select input. "
"Multiple options can be created from a comma separated string."
msgstr ""

#: includes/fields/class-acf-field-select.php:447
msgid "Create Options"
msgstr ""

#: includes/admin/views/global/navigation.php:179
#: includes/admin/views/global/navigation.php:183
msgid "Edit ACF Field Groups"
msgstr ""

#: includes/admin/views/global/navigation.php:100
msgid "Get 4 months free on any WP Engine plan"
msgstr ""

#: src/Site_Health/Site_Health.php:522
msgid "Number of Field Groups with Blocks and Other Locations"
msgstr ""

#: src/Site_Health/Site_Health.php:517
msgid "Number of Field Groups with Multiple Block Locations"
msgstr ""

#: src/Site_Health/Site_Health.php:512
msgid "Number of Field Groups with a Single Block Location"
msgstr ""

#: src/Site_Health/Site_Health.php:481
msgid "All Location Rules"
msgstr ""

#: includes/validation.php:144
msgid "Learn more"
msgstr "En savoir plus"

#: includes/validation.php:133
msgid ""
"ACF was unable to perform validation because the provided nonce failed "
"verification."
msgstr ""
"ACF n’a pas pu effectuer la validation car le jeton de sécurité fourni a "
"échoué à la vérification."

#: includes/validation.php:131
msgid ""
"ACF was unable to perform validation because no nonce was received by the "
"server."
msgstr ""
"ACF n’a pas pu effectuer la validation car aucun jeton de sécurité n’a été "
"reçu par le serveur."

#. translators: This text is prepended by a link to ACF's website, and appended
#. by a link to WP Engine's website.
#: includes/admin/admin.php:324
msgid "are developed and maintained by"
msgstr "sont développés et maintenus par"

#: src/Site_Health/Site_Health.php:295
msgid "Update Source"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:850
#: includes/admin/views/acf-taxonomy/advanced-settings.php:810
msgid "By default only admin users can edit this setting."
msgstr "Par défaut, seuls les administrateurs peuvent modifier ce réglage."

#: includes/admin/views/acf-post-type/advanced-settings.php:848
#: includes/admin/views/acf-taxonomy/advanced-settings.php:808
msgid "By default only super admin users can edit this setting."
msgstr "Par défaut, seuls les super-admins peuvent modifier ce réglage."

#: includes/admin/views/acf-field-group/field.php:322
msgid "Close and Add Field"
msgstr "Fermer et Ajouter un Champ"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:804
msgid ""
"A PHP function name to be called to handle the content of a meta box on your "
"taxonomy. For security, this callback will be executed in a special context "
"without access to any superglobals like $_POST or $_GET."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:842
msgid ""
"A PHP function name to be called when setting up the meta boxes for the edit "
"screen. For security, this callback will be executed in a special context "
"without access to any superglobals like $_POST or $_GET."
msgstr ""

#: src/Site_Health/Site_Health.php:296
msgid "wordpress.org"
msgstr ""

#: includes/fields/class-acf-field.php:359
msgid "Allow Access to Value in Editor UI"
msgstr "Autoriser l’accès à la valeur dans l’interface de l’éditeur"

#: includes/fields/class-acf-field.php:341
msgid "Learn more."
msgstr "En savoir plus."

#. translators: %s A "Learn More" link to documentation explaining the setting
#. further.
#: includes/fields/class-acf-field.php:340
msgid ""
"Allow content editors to access and display the field value in the editor UI "
"using Block Bindings or the ACF Shortcode. %s"
msgstr ""
"Autoriser aux éditeurs et éditrices de contenu d’accéder à la valeur du "
"champ et de l’afficher dans l’interface de l’éditeur en utilisant les blocs "
"bindings ou le code court ACF. %s"

#: src/Blocks/Bindings.php:67
msgid ""
"The requested ACF field type does not support output in Block Bindings or "
"the ACF shortcode."
msgstr ""
"Le type de champ ACF demandé ne prend pas en charge la sortie via les Block "
"Bindings ou le code court ACF."

#: includes/api/api-template.php:1085 src/Blocks/Bindings.php:75
msgid ""
"The requested ACF field is not allowed to be output in bindings or the ACF "
"Shortcode."
msgstr ""
"Le champ ACF demandé n’est pas autorisé à être affiché via les bindings ou "
"le code court ACF."

#: includes/api/api-template.php:1077
msgid ""
"The requested ACF field type does not support output in bindings or the ACF "
"Shortcode."
msgstr ""
"Le type de champ ACF demandé ne prend pas en charge l’affichage via les "
"bindings ou le code court ACF."

#: includes/api/api-template.php:1054
msgid "[The ACF shortcode cannot display fields from non-public posts]"
msgstr ""
"[Le code court ACF ne peut pas afficher les champs des publications non "
"publiques]"

#: includes/api/api-template.php:1011
msgid "[The ACF shortcode is disabled on this site]"
msgstr "[Le code court ACF est désactivé sur ce site]"

#: includes/fields/class-acf-field-icon_picker.php:476
msgid "Businessman Icon"
msgstr "Icône d’homme d’affaires"

#: includes/fields/class-acf-field-icon_picker.php:468
msgid "Forums Icon"
msgstr "Icône de forums"

#: includes/fields/class-acf-field-icon_picker.php:747
msgid "YouTube Icon"
msgstr "Icône YouTube"

#: includes/fields/class-acf-field-icon_picker.php:746
msgid "Yes (alt) Icon"
msgstr "Icône Oui (alt)"

#: includes/fields/class-acf-field-icon_picker.php:744
msgid "Xing Icon"
msgstr "Icône Xing"

#: includes/fields/class-acf-field-icon_picker.php:743
msgid "WordPress (alt) Icon"
msgstr "Icône WordPress (alt)"

#: includes/fields/class-acf-field-icon_picker.php:741
msgid "WhatsApp Icon"
msgstr "Icône WhatsApp"

#: includes/fields/class-acf-field-icon_picker.php:740
msgid "Write Blog Icon"
msgstr "Icône Rédaction de blog"

#: includes/fields/class-acf-field-icon_picker.php:739
msgid "Widgets Menus Icon"
msgstr "Icône de widgets de menus"

#: includes/fields/class-acf-field-icon_picker.php:738
msgid "View Site Icon"
msgstr "Voir l’icône du site"

#: includes/fields/class-acf-field-icon_picker.php:737
msgid "Learn More Icon"
msgstr "Icône En savoir plus"

#: includes/fields/class-acf-field-icon_picker.php:735
msgid "Add Page Icon"
msgstr "Icône Ajouter une page"

#: includes/fields/class-acf-field-icon_picker.php:732
msgid "Video (alt3) Icon"
msgstr "Icône Vidéo (alt3)"

#: includes/fields/class-acf-field-icon_picker.php:731
msgid "Video (alt2) Icon"
msgstr "Icône vidéo (alt2)"

#: includes/fields/class-acf-field-icon_picker.php:730
msgid "Video (alt) Icon"
msgstr "Icône vidéo (alt)"

#: includes/fields/class-acf-field-icon_picker.php:727
msgid "Update (alt) Icon"
msgstr "Icône de mise à jour (alt)"

#: includes/fields/class-acf-field-icon_picker.php:724
msgid "Universal Access (alt) Icon"
msgstr "Icône d’accès universel (alt)"

#: includes/fields/class-acf-field-icon_picker.php:721
msgid "Twitter (alt) Icon"
msgstr "Icône Twitter (alt)"

#: includes/fields/class-acf-field-icon_picker.php:719
msgid "Twitch Icon"
msgstr "Icône Twitch"

#: includes/fields/class-acf-field-icon_picker.php:716
msgid "Tide Icon"
msgstr "Icône de marée"

#: includes/fields/class-acf-field-icon_picker.php:715
msgid "Tickets (alt) Icon"
msgstr "Icône de billets (alt)"

#: includes/fields/class-acf-field-icon_picker.php:711
msgid "Text Page Icon"
msgstr "Icône de page de texte"

#: includes/fields/class-acf-field-icon_picker.php:705
msgid "Table Row Delete Icon"
msgstr "Icône de suppression de rangée de tableau"

#: includes/fields/class-acf-field-icon_picker.php:704
msgid "Table Row Before Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:703
msgid "Table Row After Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:702
msgid "Table Col Delete Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:701
msgid "Table Col Before Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:700
msgid "Table Col After Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:699
msgid "Superhero (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:698
msgid "Superhero Icon"
msgstr "Icône de super-héros"

#: includes/fields/class-acf-field-icon_picker.php:692
msgid "Spotify Icon"
msgstr "Icône Spotify"

#: includes/fields/class-acf-field-icon_picker.php:686
msgid "Shortcode Icon"
msgstr "Icône de code court"

#: includes/fields/class-acf-field-icon_picker.php:685
msgid "Shield (alt) Icon"
msgstr "Icône de bouclier (alt)"

#: includes/fields/class-acf-field-icon_picker.php:683
msgid "Share (alt2) Icon"
msgstr "Icône de partage (alt2)"

#: includes/fields/class-acf-field-icon_picker.php:682
msgid "Share (alt) Icon"
msgstr "Icône de partage (alt)"

#: includes/fields/class-acf-field-icon_picker.php:677
msgid "Saved Icon"
msgstr "Icône enregistrée"

#: includes/fields/class-acf-field-icon_picker.php:676
msgid "RSS Icon"
msgstr "Icône RSS"

#: includes/fields/class-acf-field-icon_picker.php:675
msgid "REST API Icon"
msgstr "Icône d’API REST"

#: includes/fields/class-acf-field-icon_picker.php:674
msgid "Remove Icon"
msgstr "Retirer l’icône"

#: includes/fields/class-acf-field-icon_picker.php:672
msgid "Reddit Icon"
msgstr "Icône Reddit"

#: includes/fields/class-acf-field-icon_picker.php:669
msgid "Privacy Icon"
msgstr "Icône de confidentialité"

#: includes/fields/class-acf-field-icon_picker.php:668
msgid "Printer Icon"
msgstr "Icône d’imprimante"

#: includes/fields/class-acf-field-icon_picker.php:664
msgid "Podio Icon"
msgstr "Icône Podio"

#: includes/fields/class-acf-field-icon_picker.php:663
msgid "Plus (alt2) Icon"
msgstr "Icône Plus (alt2)"

#: includes/fields/class-acf-field-icon_picker.php:662
msgid "Plus (alt) Icon"
msgstr "Icône Plus (alt)"

#: includes/fields/class-acf-field-icon_picker.php:660
msgid "Plugins Checked Icon"
msgstr "Icône d’extensions cochée"

#: includes/fields/class-acf-field-icon_picker.php:657
msgid "Pinterest Icon"
msgstr "Icône Pinterest"

#: includes/fields/class-acf-field-icon_picker.php:655
msgid "Pets Icon"
msgstr "Icône d’animaux"

#: includes/fields/class-acf-field-icon_picker.php:653
msgid "PDF Icon"
msgstr "Icône de PDF"

#: includes/fields/class-acf-field-icon_picker.php:651
msgid "Palm Tree Icon"
msgstr "Icône de palmier"

#: includes/fields/class-acf-field-icon_picker.php:650
msgid "Open Folder Icon"
msgstr "Icône d’ouverture du dossier"

#: includes/fields/class-acf-field-icon_picker.php:649
msgid "No (alt) Icon"
msgstr "Pas d’icône (alt)"

#: includes/fields/class-acf-field-icon_picker.php:644
msgid "Money (alt) Icon"
msgstr "Icône d’argent (alt)"

#: includes/fields/class-acf-field-icon_picker.php:639
msgid "Menu (alt3) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:638
msgid "Menu (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:637
msgid "Menu (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:632
msgid "Spreadsheet Icon"
msgstr "Icône de feuille de calcul"

#: includes/fields/class-acf-field-icon_picker.php:631
msgid "Interactive Icon"
msgstr "Icône interactive"

#: includes/fields/class-acf-field-icon_picker.php:630
msgid "Document Icon"
msgstr "Icône de document"

#: includes/fields/class-acf-field-icon_picker.php:629
msgid "Default Icon"
msgstr "Icône par défaut"

#: includes/fields/class-acf-field-icon_picker.php:623
msgid "Location (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:620
msgid "LinkedIn Icon"
msgstr "Icône LinkedIn"

#: includes/fields/class-acf-field-icon_picker.php:615
msgid "Instagram Icon"
msgstr "Icône Instagram"

#: includes/fields/class-acf-field-icon_picker.php:614
msgid "Insert Before Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:613
msgid "Insert After Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:612
msgid "Insert Icon"
msgstr "Insérer un icône"

#: includes/fields/class-acf-field-icon_picker.php:611
msgid "Info Outline Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:608
msgid "Images (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:607
msgid "Images (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:606
msgid "Rotate Right Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:605
msgid "Rotate Left Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:604
msgid "Rotate Icon"
msgstr "Icône de rotation"

#: includes/fields/class-acf-field-icon_picker.php:603
msgid "Flip Vertical Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:602
msgid "Flip Horizontal Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:600
msgid "Crop Icon"
msgstr "Icône de recadrage"

#: includes/fields/class-acf-field-icon_picker.php:599
msgid "ID (alt) Icon"
msgstr "Icône d’ID (alt)"

#: includes/fields/class-acf-field-icon_picker.php:597
msgid "HTML Icon"
msgstr "Icône HTML"

#: includes/fields/class-acf-field-icon_picker.php:596
msgid "Hourglass Icon"
msgstr "Icône de sablier"

#: includes/fields/class-acf-field-icon_picker.php:593
msgid "Heading Icon"
msgstr "Icône de titre"

#: includes/fields/class-acf-field-icon_picker.php:589
msgid "Google Icon"
msgstr "Icône Google"

#: includes/fields/class-acf-field-icon_picker.php:588
msgid "Games Icon"
msgstr "Icône de jeux"

#: includes/fields/class-acf-field-icon_picker.php:587
msgid "Fullscreen Exit (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:586
msgid "Fullscreen (alt) Icon"
msgstr "Icône plein écran (alt)"

#: includes/fields/class-acf-field-icon_picker.php:583
msgid "Status Icon"
msgstr "Icône d’état"

#: includes/fields/class-acf-field-icon_picker.php:581
msgid "Image Icon"
msgstr "Icône d’image"

#: includes/fields/class-acf-field-icon_picker.php:580
msgid "Gallery Icon"
msgstr "Icône de galerie"

#: includes/fields/class-acf-field-icon_picker.php:579
msgid "Chat Icon"
msgstr "Icône de chat"

#: includes/fields/class-acf-field-icon_picker.php:578
#: includes/fields/class-acf-field-icon_picker.php:627
msgid "Audio Icon"
msgstr "Icône audio"

#: includes/fields/class-acf-field-icon_picker.php:577
msgid "Aside Icon"
msgstr "Icône Aparté"

#: includes/fields/class-acf-field-icon_picker.php:576
msgid "Food Icon"
msgstr "Icône d’alimentation"

#: includes/fields/class-acf-field-icon_picker.php:569
msgid "Exit Icon"
msgstr "Icône de sortie"

#: includes/fields/class-acf-field-icon_picker.php:568
msgid "Excerpt View Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:567
msgid "Embed Video Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:566
msgid "Embed Post Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:565
msgid "Embed Photo Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:564
msgid "Embed Generic Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:563
msgid "Embed Audio Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:562
msgid "Email (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:559
msgid "Ellipsis Icon"
msgstr "Icône de points de suspension"

#: includes/fields/class-acf-field-icon_picker.php:555
msgid "Unordered List Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:550
msgid "RTL Icon"
msgstr "Icône RTL"

#: includes/fields/class-acf-field-icon_picker.php:543
msgid "Ordered List RTL Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:542
msgid "Ordered List Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:541
msgid "LTR Icon"
msgstr "Icône LTR"

#: includes/fields/class-acf-field-icon_picker.php:533
msgid "Custom Character Icon"
msgstr "Icône de caractère personnalisé"

#: includes/fields/class-acf-field-icon_picker.php:525
msgid "Edit Page Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:524
msgid "Edit Large Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:522
msgid "Drumstick Icon"
msgstr "Icône de pilon"

#: includes/fields/class-acf-field-icon_picker.php:518
msgid "Database View Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:517
msgid "Database Remove Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:516
msgid "Database Import Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:515
msgid "Database Export Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:514
msgid "Database Add Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:513
msgid "Database Icon"
msgstr "Icône de base de données"

#: includes/fields/class-acf-field-icon_picker.php:511
msgid "Cover Image Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:510
msgid "Volume On Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:509
msgid "Volume Off Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:508
msgid "Skip Forward Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:507
msgid "Skip Back Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:506
msgid "Repeat Icon"
msgstr "Icône de répétition"

#: includes/fields/class-acf-field-icon_picker.php:505
msgid "Play Icon"
msgstr "Icône de lecture"

#: includes/fields/class-acf-field-icon_picker.php:504
msgid "Pause Icon"
msgstr "Icône de pause"

#: includes/fields/class-acf-field-icon_picker.php:503
msgid "Forward Icon"
msgstr "Icône de transfert"

#: includes/fields/class-acf-field-icon_picker.php:502
msgid "Back Icon"
msgstr "Icône de retour"

#: includes/fields/class-acf-field-icon_picker.php:501
msgid "Columns Icon"
msgstr "Icône de colonnes"

#: includes/fields/class-acf-field-icon_picker.php:500
msgid "Color Picker Icon"
msgstr "Icône de sélecteur de couleurs"

#: includes/fields/class-acf-field-icon_picker.php:499
msgid "Coffee Icon"
msgstr "Icône de café"

#: includes/fields/class-acf-field-icon_picker.php:498
msgid "Code Standards Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:497
msgid "Cloud Upload Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:496
msgid "Cloud Saved Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:485
msgid "Car Icon"
msgstr "Icône de voiture"

#: includes/fields/class-acf-field-icon_picker.php:484
msgid "Camera (alt) Icon"
msgstr "Icône d’appareil photo (alt)"

#: includes/fields/class-acf-field-icon_picker.php:480
msgid "Calculator Icon"
msgstr "Icône de calculatrice"

#: includes/fields/class-acf-field-icon_picker.php:479
msgid "Button Icon"
msgstr "Icône de bouton"

#: includes/fields/class-acf-field-icon_picker.php:477
msgid "Businessperson Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:474
msgid "Tracking Icon"
msgstr "Icône de suivi"

#: includes/fields/class-acf-field-icon_picker.php:473
msgid "Topics Icon"
msgstr "Icône de sujets"

#: includes/fields/class-acf-field-icon_picker.php:472
msgid "Replies Icon"
msgstr "Icône de réponses"

#: includes/fields/class-acf-field-icon_picker.php:471
msgid "PM Icon"
msgstr "Icône Pm"

#: includes/fields/class-acf-field-icon_picker.php:469
msgid "Friends Icon"
msgstr "Icône d’amis"

#: includes/fields/class-acf-field-icon_picker.php:467
msgid "Community Icon"
msgstr "Icône de communauté"

#: includes/fields/class-acf-field-icon_picker.php:466
msgid "BuddyPress Icon"
msgstr "Icône BuddyPress"

#: includes/fields/class-acf-field-icon_picker.php:465
msgid "bbPress Icon"
msgstr "Icône bbPress"

#: includes/fields/class-acf-field-icon_picker.php:464
msgid "Activity Icon"
msgstr "Icône d’activité"

#: includes/fields/class-acf-field-icon_picker.php:463
msgid "Book (alt) Icon"
msgstr "Icône de livre (alt)"

#: includes/fields/class-acf-field-icon_picker.php:461
msgid "Block Default Icon"
msgstr "Icône de bloc par défaut"

#: includes/fields/class-acf-field-icon_picker.php:460
msgid "Bell Icon"
msgstr "Icône de cloche"

#: includes/fields/class-acf-field-icon_picker.php:459
msgid "Beer Icon"
msgstr "Icône de bière"

#: includes/fields/class-acf-field-icon_picker.php:458
msgid "Bank Icon"
msgstr "Icône de banque"

#: includes/fields/class-acf-field-icon_picker.php:454
msgid "Arrow Up (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:453
msgid "Arrow Up (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:451
msgid "Arrow Right (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:450
msgid "Arrow Right (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:448
msgid "Arrow Left (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:447
msgid "Arrow Left (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:445
msgid "Arrow Down (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:444
msgid "Arrow Down (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:440
msgid "Amazon Icon"
msgstr "Icône Amazon"

#: includes/fields/class-acf-field-icon_picker.php:439
msgid "Align Wide Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:437
msgid "Align Pull Right Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:436
msgid "Align Pull Left Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:433
msgid "Align Full Width Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:430
msgid "Airplane Icon"
msgstr "Icône d’avion"

#: includes/fields/class-acf-field-icon_picker.php:427
msgid "Site (alt3) Icon"
msgstr "Icône de site (alt3)"

#: includes/fields/class-acf-field-icon_picker.php:426
msgid "Site (alt2) Icon"
msgstr "Icône de site (alt2)"

#: includes/fields/class-acf-field-icon_picker.php:425
msgid "Site (alt) Icon"
msgstr "Icône de site (alt)"

#: includes/admin/views/options-page-preview.php:26
msgid "Upgrade to ACF PRO to create options pages in just a few clicks"
msgstr ""

#: includes/ajax/class-acf-ajax-query-users.php:24
msgid "Invalid request args."
msgstr "Arguments de requête invalides."

#: includes/ajax/class-acf-ajax-check-screen.php:37
#: includes/ajax/class-acf-ajax-local-json-diff.php:37
#: includes/ajax/class-acf-ajax-query-users.php:33
#: includes/ajax/class-acf-ajax-upgrade.php:24
#: includes/ajax/class-acf-ajax-user-setting.php:38
msgid "Sorry, you do not have permission to do that."
msgstr "Désolé, vous n’avez pas la permission de faire cette action"

#: src/Site_Health/Site_Health.php:720
msgid "Blocks Using Post Meta"
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:25
#: includes/admin/views/acf-field-group/pro-features.php:27
#: includes/admin/views/global/header.php:27
msgid "ACF PRO logo"
msgstr "Logo ACF PRO"

#: includes/admin/views/acf-field-group/field.php:37
msgid "ACF PRO Logo"
msgstr "Logo ACF PRO"

#. translators: %s - field/param name
#: includes/fields/class-acf-field-icon_picker.php:813
msgid "%s requires a valid attachment ID when type is set to media_library."
msgstr ""
"%s nécessite un ID de pièce jointe valide lorsque le type est défini sur "
"media_library."

#. translators: %s - field name
#: includes/fields/class-acf-field-icon_picker.php:797
msgid "%s is a required property of acf."
msgstr "%s est une propriété nécessaire d’ACF."

#: includes/fields/class-acf-field-icon_picker.php:773
msgid "The value of icon to save."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:767
msgid "The type of icon to save."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:745
msgid "Yes Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:742
msgid "WordPress Icon"
msgstr "Icône WordPress"

#: includes/fields/class-acf-field-icon_picker.php:734
msgid "Warning Icon"
msgstr "Icône d’avertissement"

#: includes/fields/class-acf-field-icon_picker.php:733
msgid "Visibility Icon"
msgstr "Icône de visibilité"

#: includes/fields/class-acf-field-icon_picker.php:729
msgid "Vault Icon"
msgstr "Icône de coffre-fort"

#: includes/fields/class-acf-field-icon_picker.php:728
msgid "Upload Icon"
msgstr "Icône de téléversement"

#: includes/fields/class-acf-field-icon_picker.php:726
msgid "Update Icon"
msgstr "Mettre à jour l’icône"

#: includes/fields/class-acf-field-icon_picker.php:725
msgid "Unlock Icon"
msgstr "Icône de déverrouillage"

#: includes/fields/class-acf-field-icon_picker.php:723
msgid "Universal Access Icon"
msgstr "Icône d’accès universel"

#: includes/fields/class-acf-field-icon_picker.php:722
msgid "Undo Icon"
msgstr "Icône d’annulation"

#: includes/fields/class-acf-field-icon_picker.php:720
msgid "Twitter Icon"
msgstr "Icône Twitter"

#: includes/fields/class-acf-field-icon_picker.php:718
msgid "Trash Icon"
msgstr "Icône de corbeille"

#: includes/fields/class-acf-field-icon_picker.php:717
msgid "Translation Icon"
msgstr "Icône de traduction"

#: includes/fields/class-acf-field-icon_picker.php:714
msgid "Tickets Icon"
msgstr "Icône de billets"

#: includes/fields/class-acf-field-icon_picker.php:713
msgid "Thumbs Up Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:712
msgid "Thumbs Down Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:633
#: includes/fields/class-acf-field-icon_picker.php:710
msgid "Text Icon"
msgstr "Icône de texte"

#: includes/fields/class-acf-field-icon_picker.php:709
msgid "Testimonial Icon"
msgstr "Icône de témoignage"

#: includes/fields/class-acf-field-icon_picker.php:708
msgid "Tagcloud Icon"
msgstr "Icône Tagcloud"

#: includes/fields/class-acf-field-icon_picker.php:707
msgid "Tag Icon"
msgstr "Icône de balise"

#: includes/fields/class-acf-field-icon_picker.php:706
msgid "Tablet Icon"
msgstr "Icône de tablette"

#: includes/fields/class-acf-field-icon_picker.php:697
msgid "Store Icon"
msgstr "Icône de boutique"

#: includes/fields/class-acf-field-icon_picker.php:696
msgid "Sticky Icon"
msgstr "Icône d’épinglage"

#: includes/fields/class-acf-field-icon_picker.php:695
msgid "Star Half Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:694
msgid "Star Filled Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:693
msgid "Star Empty Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:691
msgid "Sos Icon"
msgstr "Icône Sos"

#: includes/fields/class-acf-field-icon_picker.php:690
msgid "Sort Icon"
msgstr "Icône de tri"

#: includes/fields/class-acf-field-icon_picker.php:689
msgid "Smiley Icon"
msgstr "Icône Smiley"

#: includes/fields/class-acf-field-icon_picker.php:688
msgid "Smartphone Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:687
msgid "Slides Icon"
msgstr "Icône de diapositives"

#: includes/fields/class-acf-field-icon_picker.php:684
msgid "Shield Icon"
msgstr "Icône de bouclier"

#: includes/fields/class-acf-field-icon_picker.php:681
msgid "Share Icon"
msgstr "Icône de partage"

#: includes/fields/class-acf-field-icon_picker.php:680
msgid "Search Icon"
msgstr "Icône de recherche"

#: includes/fields/class-acf-field-icon_picker.php:679
msgid "Screen Options Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:678
msgid "Schedule Icon"
msgstr "Icône de calendrier"

#: includes/fields/class-acf-field-icon_picker.php:673
msgid "Redo Icon"
msgstr "Icône de rétablissement"

#: includes/fields/class-acf-field-icon_picker.php:671
msgid "Randomize Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:670
msgid "Products Icon"
msgstr "Icône de produits"

#: includes/fields/class-acf-field-icon_picker.php:667
msgid "Pressthis Icon"
msgstr "Icône Pressthis"

#: includes/fields/class-acf-field-icon_picker.php:666
msgid "Post Status Icon"
msgstr "Icône d’état de publication"

#: includes/fields/class-acf-field-icon_picker.php:665
msgid "Portfolio Icon"
msgstr "Icône de portfolio"

#: includes/fields/class-acf-field-icon_picker.php:661
msgid "Plus Icon"
msgstr "Icône Plus"

#: includes/fields/class-acf-field-icon_picker.php:659
msgid "Playlist Video Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:658
msgid "Playlist Audio Icon"
msgstr "Icône de liste de lecture audio"

#: includes/fields/class-acf-field-icon_picker.php:656
msgid "Phone Icon"
msgstr "Icône de téléphone"

#: includes/fields/class-acf-field-icon_picker.php:654
msgid "Performance Icon"
msgstr "Icône de performance"

#: includes/fields/class-acf-field-icon_picker.php:652
msgid "Paperclip Icon"
msgstr "Icône de trombone"

#: includes/fields/class-acf-field-icon_picker.php:648
msgid "No Icon"
msgstr "Pas d'icône"

#: includes/fields/class-acf-field-icon_picker.php:647
msgid "Networking Icon"
msgstr "Icône de réseau"

#: includes/fields/class-acf-field-icon_picker.php:646
msgid "Nametag Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:645
msgid "Move Icon"
msgstr "Icône de déplacement"

#: includes/fields/class-acf-field-icon_picker.php:643
msgid "Money Icon"
msgstr "Icône d’argent"

#: includes/fields/class-acf-field-icon_picker.php:642
msgid "Minus Icon"
msgstr "Icône Moins"

#: includes/fields/class-acf-field-icon_picker.php:641
msgid "Migrate Icon"
msgstr "Icône de migration"

#: includes/fields/class-acf-field-icon_picker.php:640
msgid "Microphone Icon"
msgstr "Icône de micro"

#: includes/fields/class-acf-field-icon_picker.php:635
msgid "Megaphone Icon"
msgstr "Icône de mégaphone"

#: includes/fields/class-acf-field-icon_picker.php:625
msgid "Marker Icon"
msgstr "Icône de marqueur"

#: includes/fields/class-acf-field-icon_picker.php:624
msgid "Lock Icon"
msgstr "Icône de verrouillage"

#: includes/fields/class-acf-field-icon_picker.php:622
msgid "Location Icon"
msgstr "Icône d’emplacement"

#: includes/fields/class-acf-field-icon_picker.php:621
msgid "List View Icon"
msgstr "Icône Vue en liste"

#: includes/fields/class-acf-field-icon_picker.php:619
msgid "Lightbulb Icon"
msgstr "Icône d’ampoule"

#: includes/fields/class-acf-field-icon_picker.php:618
msgid "Left Right Icon"
msgstr "Icône Gauche/droite"

#: includes/fields/class-acf-field-icon_picker.php:617
msgid "Layout Icon"
msgstr "Icône de mise en page"

#: includes/fields/class-acf-field-icon_picker.php:616
msgid "Laptop Icon"
msgstr "Icône de portable"

#: includes/fields/class-acf-field-icon_picker.php:610
msgid "Info Icon"
msgstr "Icône d’information"

#: includes/fields/class-acf-field-icon_picker.php:609
msgid "Index Card Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:598
msgid "ID Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:595
msgid "Hidden Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:594
msgid "Heart Icon"
msgstr "Icône de coeur"

#: includes/fields/class-acf-field-icon_picker.php:592
msgid "Hammer Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:470
#: includes/fields/class-acf-field-icon_picker.php:591
msgid "Groups Icon"
msgstr "Icône de groupes"

#: includes/fields/class-acf-field-icon_picker.php:590
msgid "Grid View Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:585
msgid "Forms Icon"
msgstr "Icône de formulaires"

#: includes/fields/class-acf-field-icon_picker.php:575
msgid "Flag Icon"
msgstr "Icône de drapeau"

#: includes/fields/class-acf-field-icon_picker.php:574
#: includes/fields/class-acf-field-icon_picker.php:601
msgid "Filter Icon"
msgstr "Icône de filtre"

#: includes/fields/class-acf-field-icon_picker.php:573
msgid "Feedback Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:572
msgid "Facebook (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:571
msgid "Facebook Icon"
msgstr "Icône Facebook"

#: includes/fields/class-acf-field-icon_picker.php:570
msgid "External Icon"
msgstr "Icône externe"

#: includes/fields/class-acf-field-icon_picker.php:561
msgid "Email (alt) Icon"
msgstr "Icône d’e-mail (alt)"

#: includes/fields/class-acf-field-icon_picker.php:560
msgid "Email Icon"
msgstr "Icône d’e-mail"

#: includes/fields/class-acf-field-icon_picker.php:558
#: includes/fields/class-acf-field-icon_picker.php:584
#: includes/fields/class-acf-field-icon_picker.php:634
msgid "Video Icon"
msgstr "Icône de vidéo"

#: includes/fields/class-acf-field-icon_picker.php:557
msgid "Unlink Icon"
msgstr "Icône de dissociation"

#: includes/fields/class-acf-field-icon_picker.php:556
msgid "Underline Icon"
msgstr "Icône de soulignement"

#: includes/fields/class-acf-field-icon_picker.php:554
msgid "Text Color Icon"
msgstr "Icône de couleur de texte"

#: includes/fields/class-acf-field-icon_picker.php:553
msgid "Table Icon"
msgstr "Icône de tableau"

#: includes/fields/class-acf-field-icon_picker.php:552
msgid "Strikethrough Icon"
msgstr "Icône barrée"

#: includes/fields/class-acf-field-icon_picker.php:551
msgid "Spellcheck Icon"
msgstr "Icône de vérification orthographique"

#: includes/fields/class-acf-field-icon_picker.php:549
msgid "Remove Formatting Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:548
#: includes/fields/class-acf-field-icon_picker.php:582
msgid "Quote Icon"
msgstr "Icône de citation"

#: includes/fields/class-acf-field-icon_picker.php:547
msgid "Paste Word Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:546
msgid "Paste Text Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:545
msgid "Paragraph Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:544
msgid "Outdent Icon"
msgstr "Icône de retrait"

#: includes/fields/class-acf-field-icon_picker.php:540
msgid "Kitchen Sink Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:539
msgid "Justify Icon"
msgstr "Justifier l’icône"

#: includes/fields/class-acf-field-icon_picker.php:538
msgid "Italic Icon"
msgstr "Icône italique"

#: includes/fields/class-acf-field-icon_picker.php:537
msgid "Insert More Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:536
msgid "Indent Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:535
msgid "Help Icon"
msgstr "Icône d’aide"

#: includes/fields/class-acf-field-icon_picker.php:534
msgid "Expand Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:532
msgid "Contract Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:531
#: includes/fields/class-acf-field-icon_picker.php:628
msgid "Code Icon"
msgstr "Icône de code"

#: includes/fields/class-acf-field-icon_picker.php:530
msgid "Break Icon"
msgstr "Icône de rupture"

#: includes/fields/class-acf-field-icon_picker.php:529
msgid "Bold Icon"
msgstr "Icône en gras"

#: includes/fields/class-acf-field-icon_picker.php:523
msgid "Edit Icon"
msgstr "Modifier l’icône"

#: includes/fields/class-acf-field-icon_picker.php:521
msgid "Download Icon"
msgstr "Icône de téléchargement"

#: includes/fields/class-acf-field-icon_picker.php:520
msgid "Dismiss Icon"
msgstr "Ignorer l’icône"

#: includes/fields/class-acf-field-icon_picker.php:519
msgid "Desktop Icon"
msgstr "Icône d’ordinateur"

#: includes/fields/class-acf-field-icon_picker.php:512
msgid "Dashboard Icon"
msgstr "Icône de tableau de bord"

#: includes/fields/class-acf-field-icon_picker.php:495
msgid "Cloud Icon"
msgstr "Icône de nuage"

#: includes/fields/class-acf-field-icon_picker.php:494
msgid "Clock Icon"
msgstr "Icône d’horloge"

#: includes/fields/class-acf-field-icon_picker.php:493
msgid "Clipboard Icon"
msgstr "Icône de presse-papiers"

#: includes/fields/class-acf-field-icon_picker.php:492
msgid "Chart Pie Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:491
msgid "Chart Line Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:490
msgid "Chart Bar Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:489
msgid "Chart Area Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:488
msgid "Category Icon"
msgstr "Icône de catégorie"

#: includes/fields/class-acf-field-icon_picker.php:487
msgid "Cart Icon"
msgstr "Icône de panier"

#: includes/fields/class-acf-field-icon_picker.php:486
msgid "Carrot Icon"
msgstr "Icône de carotte"

#: includes/fields/class-acf-field-icon_picker.php:483
msgid "Camera Icon"
msgstr "Icône d’appareil photo"

#: includes/fields/class-acf-field-icon_picker.php:482
msgid "Calendar (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:481
msgid "Calendar Icon"
msgstr "Icône de calendrier"

#: includes/fields/class-acf-field-icon_picker.php:478
msgid "Businesswoman Icon"
msgstr "Icône de femme d’affaires"

#: includes/fields/class-acf-field-icon_picker.php:475
msgid "Building Icon"
msgstr "Icône de bâtiment"

#: includes/fields/class-acf-field-icon_picker.php:462
msgid "Book Icon"
msgstr "Icône de livre"

#: includes/fields/class-acf-field-icon_picker.php:457
msgid "Backup Icon"
msgstr "Icône de sauvegarde"

#: includes/fields/class-acf-field-icon_picker.php:456
msgid "Awards Icon"
msgstr "Icône de prix"

#: includes/fields/class-acf-field-icon_picker.php:455
msgid "Art Icon"
msgstr "Icône d’art"

#: includes/fields/class-acf-field-icon_picker.php:452
msgid "Arrow Up Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:449
msgid "Arrow Right Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:446
msgid "Arrow Left Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:443
msgid "Arrow Down Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:442
#: includes/fields/class-acf-field-icon_picker.php:626
msgid "Archive Icon"
msgstr "Icône d’archive"

#: includes/fields/class-acf-field-icon_picker.php:441
msgid "Analytics Icon"
msgstr "Icône de statistiques"

#: includes/fields/class-acf-field-icon_picker.php:438
#: includes/fields/class-acf-field-icon_picker.php:528
msgid "Align Right Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:435
msgid "Align None Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:434
#: includes/fields/class-acf-field-icon_picker.php:527
msgid "Align Left Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:432
#: includes/fields/class-acf-field-icon_picker.php:526
msgid "Align Center Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:431
msgid "Album Icon"
msgstr "Icône d’album"

#: includes/fields/class-acf-field-icon_picker.php:429
msgid "Users Icon"
msgstr "Icône d’utilisateurs/utilisatrices"

#: includes/fields/class-acf-field-icon_picker.php:428
msgid "Tools Icon"
msgstr "Icône d’outils"

#: includes/fields/class-acf-field-icon_picker.php:424
msgid "Site Icon"
msgstr "Icône de site"

#: includes/fields/class-acf-field-icon_picker.php:423
msgid "Settings Icon"
msgstr "Icône de réglages"

#: includes/fields/class-acf-field-icon_picker.php:422
msgid "Post Icon"
msgstr "Icône de publication"

#: includes/fields/class-acf-field-icon_picker.php:421
msgid "Plugins Icon"
msgstr "Icône d’extensions"

#: includes/fields/class-acf-field-icon_picker.php:420
msgid "Page Icon"
msgstr "Icône de page"

#: includes/fields/class-acf-field-icon_picker.php:419
msgid "Network Icon"
msgstr "Icône de réseau"

#: includes/fields/class-acf-field-icon_picker.php:418
msgid "Multisite Icon"
msgstr "Icône multisite"

#: includes/fields/class-acf-field-icon_picker.php:417
msgid "Media Icon"
msgstr "Icône de média"

#: includes/fields/class-acf-field-icon_picker.php:416
msgid "Links Icon"
msgstr "Icône de liens"

#: includes/fields/class-acf-field-icon_picker.php:415
msgid "Home Icon"
msgstr "Icon d’accueil"

#: includes/fields/class-acf-field-icon_picker.php:413
msgid "Customizer Icon"
msgstr "Icône de personnalisation"

#: includes/fields/class-acf-field-icon_picker.php:412
#: includes/fields/class-acf-field-icon_picker.php:736
msgid "Comments Icon"
msgstr "Icône de commentaires"

#: includes/fields/class-acf-field-icon_picker.php:411
msgid "Collapse Icon"
msgstr "Icône de réduction"

#: includes/fields/class-acf-field-icon_picker.php:410
msgid "Appearance Icon"
msgstr "Icône d’apparence"

#: includes/fields/class-acf-field-icon_picker.php:414
msgid "Generic Icon"
msgstr "Icône générique"

#: includes/fields/class-acf-field-icon_picker.php:346
msgid "Icon picker requires a value."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:341
msgid "Icon picker requires an icon type."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:310
msgid ""
"The available icons matching your search query have been updated in the icon "
"picker below."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:309
msgid "No results found for that search term"
msgstr "Aucun résultat trouvé pour ce terme de recherche"

#: includes/fields/class-acf-field-icon_picker.php:291
msgid "Array"
msgstr "Tableau"

#: includes/fields/class-acf-field-icon_picker.php:290
msgid "String"
msgstr "Chaîne"

#. translators: %s - link to documentation
#: includes/fields/class-acf-field-icon_picker.php:278
msgid "Specify the return format for the icon. %s"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:263
msgid "Select where content editors can choose the icon from."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:224
msgid "The URL to the icon you'd like to use, or svg as Data URI"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:207
msgid "Browse Media Library"
msgstr "Parcourir la médiathèque"

#: includes/fields/class-acf-field-icon_picker.php:198
msgid "The currently selected image preview"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:189
msgid "Click to change the icon in the Media Library"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:84
msgid "Search icons..."
msgstr "Rechercher des icônes…"

#: includes/fields/class-acf-field-icon_picker.php:53
msgid "Media Library"
msgstr "Médiathèque"

#: includes/fields/class-acf-field-icon_picker.php:49
msgid "Dashicons"
msgstr "Dashicons"

#: includes/fields/class-acf-field-icon_picker.php:26
msgid ""
"An interactive UI for selecting an icon. Select from Dashicons, the media "
"library, or a standalone URL input."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:23
msgid "Icon Picker"
msgstr "Sélecteur d’icône"

#: src/Site_Health/Site_Health.php:781
msgid "JSON Load Paths"
msgstr ""

#: src/Site_Health/Site_Health.php:775
msgid "JSON Save Paths"
msgstr ""

#: src/Site_Health/Site_Health.php:766
msgid "Registered ACF Forms"
msgstr ""

#: src/Site_Health/Site_Health.php:760
msgid "Shortcode Enabled"
msgstr "Code court activé"

#: src/Site_Health/Site_Health.php:752
msgid "Field Settings Tabs Enabled"
msgstr ""

#: src/Site_Health/Site_Health.php:744
msgid "Field Type Modal Enabled"
msgstr ""

#: src/Site_Health/Site_Health.php:736
msgid "Admin UI Enabled"
msgstr ""

#: src/Site_Health/Site_Health.php:727
msgid "Block Preloading Enabled"
msgstr ""

#: src/Site_Health/Site_Health.php:715
msgid "Blocks Per ACF Block Version"
msgstr ""

#: src/Site_Health/Site_Health.php:710
msgid "Blocks Per API Version"
msgstr ""

#: src/Site_Health/Site_Health.php:683
msgid "Registered ACF Blocks"
msgstr ""

#: src/Site_Health/Site_Health.php:677
msgid "Light"
msgstr "Clair"

#: src/Site_Health/Site_Health.php:677
msgid "Standard"
msgstr "Normal"

#: src/Site_Health/Site_Health.php:676
msgid "REST API Format"
msgstr "Format de l’API REST"

#: src/Site_Health/Site_Health.php:668
msgid "Registered Options Pages (PHP)"
msgstr ""

#: src/Site_Health/Site_Health.php:654
msgid "Registered Options Pages (JSON)"
msgstr ""

#: src/Site_Health/Site_Health.php:649
msgid "Registered Options Pages (UI)"
msgstr ""

#: src/Site_Health/Site_Health.php:619
msgid "Options Pages UI Enabled"
msgstr ""

#: src/Site_Health/Site_Health.php:611
msgid "Registered Taxonomies (JSON)"
msgstr ""

#: src/Site_Health/Site_Health.php:599
msgid "Registered Taxonomies (UI)"
msgstr ""

#: src/Site_Health/Site_Health.php:587
msgid "Registered Post Types (JSON)"
msgstr ""

#: src/Site_Health/Site_Health.php:575
msgid "Registered Post Types (UI)"
msgstr ""

#: src/Site_Health/Site_Health.php:562
msgid "Post Types and Taxonomies Enabled"
msgstr ""

#: src/Site_Health/Site_Health.php:555
msgid "Number of Third Party Fields by Field Type"
msgstr ""

#: src/Site_Health/Site_Health.php:550
msgid "Number of Fields by Field Type"
msgstr ""

#: src/Site_Health/Site_Health.php:449
msgid "Field Groups Enabled for GraphQL"
msgstr ""

#: src/Site_Health/Site_Health.php:436
msgid "Field Groups Enabled for REST API"
msgstr ""

#: src/Site_Health/Site_Health.php:424
msgid "Registered Field Groups (JSON)"
msgstr ""

#: src/Site_Health/Site_Health.php:412
msgid "Registered Field Groups (PHP)"
msgstr ""

#: src/Site_Health/Site_Health.php:400
msgid "Registered Field Groups (UI)"
msgstr ""

#: src/Site_Health/Site_Health.php:388
msgid "Active Plugins"
msgstr "Extensions actives"

#: src/Site_Health/Site_Health.php:362
msgid "Parent Theme"
msgstr "Thème parent"

#: src/Site_Health/Site_Health.php:351
msgid "Active Theme"
msgstr "Thème actif"

#: src/Site_Health/Site_Health.php:342
msgid "Is Multisite"
msgstr "Est un multisite"

#: src/Site_Health/Site_Health.php:337
msgid "MySQL Version"
msgstr "Version MySQL"

#: src/Site_Health/Site_Health.php:332
msgid "WordPress Version"
msgstr "Version de WordPress"

#: src/Site_Health/Site_Health.php:325
msgid "Subscription Expiry Date"
msgstr ""

#: src/Site_Health/Site_Health.php:317
msgid "License Status"
msgstr "État de la licence"

#: src/Site_Health/Site_Health.php:312
msgid "License Type"
msgstr "Type de licence"

#: src/Site_Health/Site_Health.php:307
msgid "Licensed URL"
msgstr "URL sous licence"

#: src/Site_Health/Site_Health.php:301
msgid "License Activated"
msgstr "Licence activée"

#: src/Site_Health/Site_Health.php:290
msgid "Free"
msgstr "Gratuit"

#: src/Site_Health/Site_Health.php:289
msgid "Plugin Type"
msgstr "Type d’extension"

#: src/Site_Health/Site_Health.php:284
msgid "Plugin Version"
msgstr "Version de l’extension"

#: src/Site_Health/Site_Health.php:255
msgid ""
"This section contains debug information about your ACF configuration which "
"can be useful to provide to support."
msgstr ""

#: includes/assets.php:373
msgid "An ACF Block on this page requires attention before you can save."
msgstr ""

#. translators: %s - The clear log button opening HTML tag. %s - The closing
#. HTML tag.
#: includes/admin/views/escaped-html-notice.php:63
msgid ""
"This data is logged as we detect values that have been changed during "
"output. %1$sClear log and dismiss%2$s after escaping the values in your "
"code. The notice will reappear if we detect changed values again."
msgstr ""

#: includes/admin/views/escaped-html-notice.php:25
msgid "Dismiss permanently"
msgstr ""

#: includes/admin/views/acf-field-group/field.php:220
msgid "Instructions for content editors. Shown when submitting data."
msgstr ""

#: includes/admin/post-types/admin-field-group.php:143
msgid "Has no term selected"
msgstr "N’a aucun terme sélectionné"

#: includes/admin/post-types/admin-field-group.php:142
msgid "Has any term selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:141
msgid "Terms do not contain"
msgstr "Les termes ne contiennent pas"

#: includes/admin/post-types/admin-field-group.php:140
msgid "Terms contain"
msgstr "Les termes contiennent"

#: includes/admin/post-types/admin-field-group.php:139
msgid "Term is not equal to"
msgstr "Le terme n’est pas égal à"

#: includes/admin/post-types/admin-field-group.php:138
msgid "Term is equal to"
msgstr "Le terme est égal à"

#: includes/admin/post-types/admin-field-group.php:137
msgid "Has no user selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:136
msgid "Has any user selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:135
msgid "Users do not contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:134
msgid "Users contain"
msgstr "Les utilisateurs contiennent"

#: includes/admin/post-types/admin-field-group.php:133
msgid "User is not equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:132
msgid "User is equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:131
msgid "Has no page selected"
msgstr "N’a pas de page sélectionnée"

#: includes/admin/post-types/admin-field-group.php:130
msgid "Has any page selected"
msgstr "Comme une page sélectionnée"

#: includes/admin/post-types/admin-field-group.php:129
msgid "Pages do not contain"
msgstr "Les pages ne contiennent pas"

#: includes/admin/post-types/admin-field-group.php:128
msgid "Pages contain"
msgstr "Les pages contiennent"

#: includes/admin/post-types/admin-field-group.php:127
msgid "Page is not equal to"
msgstr "La page n’est pas égale à"

#: includes/admin/post-types/admin-field-group.php:126
msgid "Page is equal to"
msgstr "La page est égale à"

#: includes/admin/post-types/admin-field-group.php:125
msgid "Has no relationship selected"
msgstr "N’a aucune relation sélectionnée"

#: includes/admin/post-types/admin-field-group.php:124
msgid "Has any relationship selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:123
msgid "Has no post selected"
msgstr "N’a aucune publication sélectionnée"

#: includes/admin/post-types/admin-field-group.php:122
msgid "Has any post selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:121
msgid "Posts do not contain"
msgstr "Les publications ne contiennent pas"

#: includes/admin/post-types/admin-field-group.php:120
msgid "Posts contain"
msgstr "Les publications contiennent"

#: includes/admin/post-types/admin-field-group.php:119
msgid "Post is not equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:118
msgid "Post is equal to"
msgstr "La publication est égale à"

#: includes/admin/post-types/admin-field-group.php:117
msgid "Relationships do not contain"
msgstr "Les relations ne contiennent pas"

#: includes/admin/post-types/admin-field-group.php:116
msgid "Relationships contain"
msgstr "Les relations contiennent"

#: includes/admin/post-types/admin-field-group.php:115
msgid "Relationship is not equal to"
msgstr "La relation n’est pas égale à"

#: includes/admin/post-types/admin-field-group.php:114
msgid "Relationship is equal to"
msgstr "La relation est égale à"

#: src/Blocks/Bindings.php:38
msgctxt "The core ACF block binding source name for fields on the current page"
msgid "ACF Fields"
msgstr "Champs ACF"

#: includes/admin/views/browse-fields-modal.php:14
msgid "ACF PRO Feature"
msgstr "Fonctionnalité ACF PRO"

#: includes/admin/views/browse-fields-modal.php:10
msgid "Renew PRO to Unlock"
msgstr "Renouvelez la version PRO pour déverrouiller"

#: includes/admin/views/browse-fields-modal.php:8
msgid "Renew PRO License"
msgstr "Renouveler la licence PRO"

#: includes/admin/views/acf-field-group/field.php:41
msgid "PRO fields cannot be edited without an active license."
msgstr "Les champs PRO ne peuvent pas être modifiés sans une licence active."

#: includes/admin/admin-internal-post-type-list.php:232
msgid ""
"Please activate your ACF PRO license to edit field groups assigned to an ACF "
"Block."
msgstr ""
"Veuillez activer votre licence ACF PRO pour modifier les groupes de champs "
"assignés à un bloc ACF."

#: includes/admin/admin-internal-post-type-list.php:231
msgid "Please activate your ACF PRO license to edit this options page."
msgstr ""
"Veuillez activer votre licence ACF PRO pour modifier cette page d’options."

#: includes/api/api-template.php:385 includes/api/api-template.php:439
msgid ""
"Returning escaped HTML values is only possible when format_value is also "
"true. The field values have not been returned for security."
msgstr ""
"Le renvoi des valeurs HTML n’est possible que lorsque format_value est "
"également vrai. Les valeurs des champs n’ont pas été renvoyées pour des "
"raisons de sécurité."

#: includes/api/api-template.php:46 includes/api/api-template.php:251
#: includes/api/api-template.php:947
msgid ""
"Returning an escaped HTML value is only possible when format_value is also "
"true. The field value has not been returned for security."
msgstr ""
"Le renvoi d’une valeur HTML n’est possible que lorsque format_value est "
"également vrai. La valeur du champ n’a pas été renvoyée pour des raisons de "
"sécurité."

#. translators: %1$s - name of the ACF plugin. %2$s - Link to documentation.
#: includes/admin/views/escaped-html-notice.php:32
msgid ""
"%1$s ACF now automatically escapes unsafe HTML when rendered by "
"<code>the_field</code> or the ACF shortcode. We've detected the output of "
"some of your fields has been modified by this change, but this may not be a "
"breaking change. %2$s."
msgstr ""
"%1$s ACF renvoi désormais automatiquement au HTML non sécurisé lorsqu’il est "
"rendu par <code>the_field</code> ou le code court ACF. Nous avons détecté "
"que le résultat de certains de vos champs a été modifié par ce changement, "
"mais il ne s’agit peut-être pas d’un changement radical. %2$s."

#: includes/admin/views/escaped-html-notice.php:27
msgid "Please contact your site administrator or developer for more details."
msgstr ""
"Veuillez contacter l’admin ou le développeur/développeuse de votre site pour "
"plus de détails."

#: includes/admin/views/escaped-html-notice.php:5
msgid "Learn&nbsp;more"
msgstr "Lire&nbsp;la&nbsp;suite"

#: includes/admin/admin.php:63
msgid "Hide&nbsp;details"
msgstr "Masquer les détails"

#: includes/admin/admin.php:62 includes/admin/views/escaped-html-notice.php:24
msgid "Show&nbsp;details"
msgstr "Afficher les détails"

#. translators: %1$s - The selector used  %2$s The field name  3%$s The parent
#. function name
#: includes/admin/views/escaped-html-notice.php:49
msgid "%1$s (%2$s) - rendered via %3$s"
msgstr "%1$s (%2$s) - rendu via %3$s"

#: includes/admin/views/global/navigation.php:229
msgid "Renew ACF PRO License"
msgstr "Renouveler la licence ACF PRO"

#: includes/admin/views/acf-field-group/pro-features.php:17
msgid "Renew License"
msgstr "Renouveler la licence"

#: includes/admin/views/acf-field-group/pro-features.php:14
msgid "Manage License"
msgstr "Gérer la licence"

#: includes/admin/views/acf-field-group/options.php:102
msgid "'High' position not supported in the Block Editor"
msgstr "La position « Haute » n’est pas prise en charge par l’éditeur de bloc"

#: includes/admin/views/options-page-preview.php:30
msgid "Upgrade to ACF PRO"
msgstr "Mettre à niveau vers ACF PRO"

#. translators: %s URL to ACF options pages documentation
#: includes/admin/views/options-page-preview.php:7
msgid ""
"ACF <a href=\"%s\" target=\"_blank\">options pages</a> are custom admin "
"pages for managing global settings via fields. You can create multiple pages "
"and sub-pages."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Les pages d’options</a> ACF sont des pages "
"d’administration personnalisées pour gérer des réglages globaux via des "
"champs. Vous pouvez créer plusieurs pages et sous-pages."

#: includes/admin/views/global/header.php:35
msgid "Add Options Page"
msgstr "Ajouter une page d’options"

#: includes/admin/views/acf-post-type/advanced-settings.php:708
msgid "In the editor used as the placeholder of the title."
msgstr "Dans l’éditeur, utilisé comme texte indicatif du titre."

#: includes/admin/views/acf-post-type/advanced-settings.php:707
msgid "Title Placeholder"
msgstr "Texte indicatif du titre"

#: includes/admin/views/global/navigation.php:97
msgid "4 Months Free"
msgstr "4 mois gratuits"

#. translators: %s - A singular label for a post type or taxonomy.
#: includes/admin/views/global/form-top.php:59
msgid "(Duplicated from %s)"
msgstr " (Dupliqué depuis %s)"

#: includes/admin/tools/class-acf-admin-tool-export.php:289
msgid "Select Options Pages"
msgstr "Sélectionner des pages d’options"

#: includes/admin/post-types/admin-taxonomy.php:107
msgid "Duplicate taxonomy"
msgstr "Dupliquer une taxonomie"

#: includes/admin/post-types/admin-post-type.php:106
#: includes/admin/post-types/admin-taxonomy.php:106
msgid "Create taxonomy"
msgstr "Créer une taxonomie"

#: includes/admin/post-types/admin-post-type.php:105
msgid "Duplicate post type"
msgstr "Dupliquer un type de publication"

#: includes/admin/post-types/admin-post-type.php:104
#: includes/admin/post-types/admin-taxonomy.php:108
msgid "Create post type"
msgstr "Créer un type de publication"

#: includes/admin/post-types/admin-post-type.php:103
#: includes/admin/post-types/admin-taxonomy.php:105
msgid "Link field groups"
msgstr "Lier des groupes de champs"

#: includes/admin/post-types/admin-post-type.php:102
#: includes/admin/post-types/admin-taxonomy.php:104
msgid "Add fields"
msgstr "Ajouter des champs"

#: includes/admin/post-types/admin-field-group.php:147
msgid "This Field"
msgstr "Ce champ"

#: includes/admin/admin.php:361
msgid "ACF PRO"
msgstr "ACF Pro"

#: includes/admin/admin.php:359
msgid "Feedback"
msgstr "Retour"

#: includes/admin/admin.php:357
msgid "Support"
msgstr "Support"

#. translators: This text is prepended by a link to ACF's website, and appended
#. by a link to WP Engine's website.
#: includes/admin/admin.php:332
msgid "is developed and maintained by"
msgstr "est développé et maintenu par"

#. translators: %s - either "post type" or "taxonomy"
#: includes/admin/admin-internal-post-type.php:313
msgid "Add this %s to the location rules of the selected field groups."
msgstr ""
"Ajoutez ce(tte) %s aux règles de localisation des groupes de champs "
"sélectionnés."

#. translators: %s the URL to ACF's bidirectional relationship documentation
#: includes/acf-bidirectional-functions.php:272
msgid ""
"Enabling the bidirectional setting allows you to update a value in the "
"target fields for each value selected for this field, adding or removing the "
"Post ID, Taxonomy ID or User ID of the item being updated. For more "
"information, please read the <a href=\"%s\" target=\"_blank\">documentation</"
"a>."
msgstr ""
"L’activation du paramètre bidirectionnel vous permet de mettre à jour une "
"valeur dans les champs cibles pour chaque valeur sélectionnée pour ce champ, "
"en ajoutant ou en supprimant l’ID de publication, l’ID de taxonomie ou l’ID "
"d’utilisateur de l’élément en cours de mise à jour. Pour plus "
"d’informations, veuillez lire la <a href=\"%s\" "
"target=\"_blank\">documentation</a>."

#: includes/acf-bidirectional-functions.php:248
msgid ""
"Select field(s) to store the reference back to the item being updated. You "
"may select this field. Target fields must be compatible with where this "
"field is being displayed. For example, if this field is displayed on a "
"Taxonomy, your target field should be of type Taxonomy"
msgstr ""
"Sélectionnee le(s) champ(s) pour stocker la référence à l’élément en cours "
"de mise à jour. Vous pouvez sélectionner ce champ. Les champs cibles doivent "
"être compatibles avec l’endroit où ce champ est affiché. Par exemple, si ce "
"champ est affiché sur une taxonomie, votre champ cible doit être de type "
"Taxonomie"

#: includes/acf-bidirectional-functions.php:247
msgid "Target Field"
msgstr "Champ cible"

#: includes/acf-bidirectional-functions.php:221
msgid "Update a field on the selected values, referencing back to this ID"
msgstr ""
"Mettre à jour un champ sur les valeurs sélectionnées, en faisant référence à "
"cet ID"

#: includes/acf-bidirectional-functions.php:220
msgid "Bidirectional"
msgstr "Bidirectionnel"

#. translators: %s A field type name, such as "Relationship"
#: includes/acf-bidirectional-functions.php:193
msgid "%s Field"
msgstr "Champ %s"

#: includes/fields/class-acf-field-page_link.php:498
#: includes/fields/class-acf-field-post_object.php:411
#: includes/fields/class-acf-field-select.php:378
#: includes/fields/class-acf-field-user.php:111
msgid "Select Multiple"
msgstr "Sélection multiple"

#: includes/admin/views/global/navigation.php:241
msgid "WP Engine logo"
msgstr "Logo WP Engine"

#: includes/admin/views/acf-taxonomy/basic-settings.php:58
msgid "Lower case letters, underscores and dashes only, Max 32 characters."
msgstr ""
"Lettres minuscules, tirets hauts et tirets bas uniquement. Maximum 32 "
"caractères."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1156
msgid "The capability name for assigning terms of this taxonomy."
msgstr "Le nom de la permission pour assigner les termes de cette taxonomie."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1155
msgid "Assign Terms Capability"
msgstr "Permission d’assigner les termes"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1139
msgid "The capability name for deleting terms of this taxonomy."
msgstr "Le nom de la permission pour supprimer les termes de cette taxonomie."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1138
msgid "Delete Terms Capability"
msgstr "Permission de supprimer les termes"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1122
msgid "The capability name for editing terms of this taxonomy."
msgstr "Le nom de la permission pour modifier les termes de cette taxonomie."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1121
msgid "Edit Terms Capability"
msgstr "Permission de modifier les termes"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1105
msgid "The capability name for managing terms of this taxonomy."
msgstr "Le nom de la permission pour gérer les termes de cette taxonomie."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1104
msgid "Manage Terms Capability"
msgstr "Permission de gérer les termes"

#: includes/admin/views/acf-post-type/advanced-settings.php:929
msgid ""
"Sets whether posts should be excluded from search results and taxonomy "
"archive pages."
msgstr ""
"Définit si les publications doivent être exclues des résultats de recherche "
"et des pages d’archive de taxonomie."

#: includes/admin/views/acf-field-group/pro-features.php:78
msgid "More Tools from WP Engine"
msgstr "Autres outils de WP Engine"

#. translators: %s - WP Engine logo
#: includes/admin/views/acf-field-group/pro-features.php:73
msgid "Built for those that build with WordPress, by the team at %s"
msgstr "Conçu pour ceux qui construisent avec WordPress, par l’équipe %s"

#: includes/admin/views/acf-field-group/pro-features.php:6
msgid "View Pricing & Upgrade"
msgstr "Voir les tarifs & mettre à niveau"

#: includes/admin/views/acf-field-group/pro-features.php:3
#: includes/admin/views/options-page-preview.php:29
#: includes/fields/class-acf-field-icon_picker.php:273
msgid "Learn More"
msgstr "En Savoir Plus"

#: includes/admin/views/acf-field-group/pro-features.php:28
msgid ""
"Speed up your workflow and develop better websites with features like ACF "
"Blocks and Options Pages, and sophisticated field types like Repeater, "
"Flexible Content, Clone, and Gallery."
msgstr ""
"Accélérez votre productivité et développez de meilleurs sites avec des "
"fonctionnalités comme les blocs ACF et les pages d’options, ainsi que des "
"champs avancés comme répéteur, contenu flexible, clones et galerie."

#: includes/admin/views/acf-field-group/pro-features.php:2
msgid "Unlock Advanced Features and Build Even More with ACF PRO"
msgstr ""
"Débloquer les fonctionnalités avancées et aller encore plus loin avec ACF PRO"

#. translators: %s - singular label of post type/taxonomy, i.e. "Movie"/"Genre"
#: includes/admin/views/global/form-top.php:19
msgid "%s fields"
msgstr "%s champs"

#: includes/admin/post-types/admin-taxonomies.php:267
msgid "No terms"
msgstr "Aucun terme"

#: includes/admin/post-types/admin-taxonomies.php:240
msgid "No post types"
msgstr "Aucun type de publication"

#: includes/admin/post-types/admin-post-types.php:264
msgid "No posts"
msgstr "Aucun article"

#: includes/admin/post-types/admin-post-types.php:238
msgid "No taxonomies"
msgstr "Aucune taxonomie"

#: includes/admin/post-types/admin-post-types.php:183
#: includes/admin/post-types/admin-taxonomies.php:182
msgid "No field groups"
msgstr "Aucun groupe de champs"

#: includes/admin/post-types/admin-field-groups.php:255
msgid "No fields"
msgstr "Aucun champ"

#: includes/admin/post-types/admin-field-groups.php:128
#: includes/admin/post-types/admin-post-types.php:147
#: includes/admin/post-types/admin-taxonomies.php:146
msgid "No description"
msgstr "Aucune description"

#: includes/fields/class-acf-field-page_link.php:465
#: includes/fields/class-acf-field-post_object.php:374
#: includes/fields/class-acf-field-relationship.php:573
msgid "Any post status"
msgstr "Tout état de publication"

#: includes/post-types/class-acf-taxonomy.php:288
msgid ""
"This taxonomy key is already in use by another taxonomy registered outside "
"of ACF and cannot be used."
msgstr ""
"Cette clé de taxonomie est déjà utilisée par une autre taxonomie enregistrée "
"en dehors d’ACF et ne peut pas être utilisée."

#: includes/post-types/class-acf-taxonomy.php:284
msgid ""
"This taxonomy key is already in use by another taxonomy in ACF and cannot be "
"used."
msgstr ""
"Cette clé de taxonomie est déjà utilisée par une autre taxonomie dans ACF et "
"ne peut pas être utilisée."

#: includes/post-types/class-acf-taxonomy.php:256
msgid ""
"The taxonomy key must only contain lower case alphanumeric characters, "
"underscores or dashes."
msgstr ""
"La clé de taxonomie doit uniquement contenir des caractères alphanumériques, "
"des tirets bas ou des tirets."

#: includes/post-types/class-acf-taxonomy.php:251
msgid "The taxonomy key must be under 32 characters."
msgstr "La clé de taxonomie doit comporter moins de 32 caractères."

#: includes/post-types/class-acf-taxonomy.php:99
msgid "No Taxonomies found in Trash"
msgstr "Aucune taxonomie trouvée dans la corbeille"

#: includes/post-types/class-acf-taxonomy.php:98
msgid "No Taxonomies found"
msgstr "Aucune taxonomie trouvée"

#: includes/post-types/class-acf-taxonomy.php:97
msgid "Search Taxonomies"
msgstr "Rechercher des taxonomies"

#: includes/post-types/class-acf-taxonomy.php:96
msgid "View Taxonomy"
msgstr "Afficher la taxonomie"

#: includes/post-types/class-acf-taxonomy.php:95
msgid "New Taxonomy"
msgstr "Nouvelle taxonomie"

#: includes/post-types/class-acf-taxonomy.php:94
msgid "Edit Taxonomy"
msgstr "Modifier la taxonomie"

#: includes/post-types/class-acf-taxonomy.php:93
msgid "Add New Taxonomy"
msgstr "Ajouter une nouvelle taxonomie"

#: includes/post-types/class-acf-post-type.php:100
msgid "No Post Types found in Trash"
msgstr "Aucun type de publication trouvé dans la corbeille"

#: includes/post-types/class-acf-post-type.php:99
msgid "No Post Types found"
msgstr "Aucun type de publication trouvé"

#: includes/post-types/class-acf-post-type.php:98
msgid "Search Post Types"
msgstr "Rechercher des types de publication"

#: includes/post-types/class-acf-post-type.php:97
msgid "View Post Type"
msgstr "Voir le type de publication"

#: includes/post-types/class-acf-post-type.php:96
msgid "New Post Type"
msgstr "Nouveau type de publication"

#: includes/post-types/class-acf-post-type.php:95
msgid "Edit Post Type"
msgstr "Modifier le type de publication"

#: includes/post-types/class-acf-post-type.php:94
msgid "Add New Post Type"
msgstr "Ajouter un nouveau type de publication personnalisé"

#: includes/post-types/class-acf-post-type.php:366
msgid ""
"This post type key is already in use by another post type registered outside "
"of ACF and cannot be used."
msgstr ""
"Cette clé de type de publication est déjà utilisée par un autre type de "
"publication enregistré en dehors d’ACF et ne peut pas être utilisée."

#: includes/post-types/class-acf-post-type.php:361
msgid ""
"This post type key is already in use by another post type in ACF and cannot "
"be used."
msgstr ""
"Cette clé de type de publication est déjà utilisée par un autre type de "
"publication dans ACF et ne peut pas être utilisée."

#. translators: %s a link to WordPress.org's Reserved Terms page
#: includes/post-types/class-acf-post-type.php:339
#: includes/post-types/class-acf-taxonomy.php:262
msgid ""
"This field must not be a WordPress <a href=\"%s\" target=\"_blank\">reserved "
"term</a>."
msgstr ""
"Ce champ ne doit pas être un <a href=\"%s\" target=\"_blank\">terme réservé</"
"a> WordPress."

#: includes/post-types/class-acf-post-type.php:333
msgid ""
"The post type key must only contain lower case alphanumeric characters, "
"underscores or dashes."
msgstr ""
"La clé du type de publication doit contenir uniquement des caractères "
"alphanumériques, des tirets bas ou des tirets."

#: includes/post-types/class-acf-post-type.php:328
msgid "The post type key must be under 20 characters."
msgstr "La clé du type de publication doit comporter moins de 20 caractères."

#: includes/fields/class-acf-field-wysiwyg.php:24
msgid "We do not recommend using this field in ACF Blocks."
msgstr "Nous vous déconseillons d’utiliser ce champ dans les blocs ACF."

#: includes/fields/class-acf-field-wysiwyg.php:24
msgid ""
"Displays the WordPress WYSIWYG editor as seen in Posts and Pages allowing "
"for a rich text-editing experience that also allows for multimedia content."
msgstr ""
"Affiche l’éditeur WordPress WYSIWYG tel qu’il apparaît dans les articles et "
"pages, permettant une expérience d’édition de texte riche qui autorise "
"également du contenu multimédia."

#: includes/fields/class-acf-field-wysiwyg.php:22
msgid "WYSIWYG Editor"
msgstr "Éditeur WYSIWYG"

#: includes/fields/class-acf-field-user.php:17
msgid ""
"Allows the selection of one or more users which can be used to create "
"relationships between data objects."
msgstr ""
"Autorise la sélection d’un ou plusieurs comptes pouvant être utilisés pour "
"créer des relations entre les objets de données."

#: includes/fields/class-acf-field-url.php:20
msgid "A text input specifically designed for storing web addresses."
msgstr "Une saisie de texte spécialement conçue pour stocker des adresses web."

#: includes/fields/class-acf-field-icon_picker.php:56
#: includes/fields/class-acf-field-url.php:19
msgid "URL"
msgstr "URL"

#: includes/fields/class-acf-field-true_false.php:24
msgid ""
"A toggle that allows you to pick a value of 1 or 0 (on or off, true or "
"false, etc). Can be presented as a stylized switch or checkbox."
msgstr ""
"Une permutation qui vous permet de choisir une valeur de 1 ou 0 (actif ou "
"inactif, vrai ou faux, etc.). Peut être présenté sous la forme de "
"commutateur stylisé ou de case à cocher."

#: includes/fields/class-acf-field-time_picker.php:24
msgid ""
"An interactive UI for picking a time. The time format can be customized "
"using the field settings."
msgstr ""
"Une interface utilisateur interactive pour choisir une heure. Le format de "
"retour de date peut être personnalisé à l’aide des réglages du champ."

#: includes/fields/class-acf-field-textarea.php:23
msgid "A basic textarea input for storing paragraphs of text."
msgstr ""
"Une entrée de zone de texte de base pour stocker des paragraphes de texte."

#: includes/fields/class-acf-field-text.php:23
msgid "A basic text input, useful for storing single string values."
msgstr ""
"Une entrée de texte de base, utile pour stocker des valeurs de chaîne unique."

#: includes/fields/class-acf-field-taxonomy.php:22
msgid ""
"Allows the selection of one or more taxonomy terms based on the criteria and "
"options specified in the fields settings."
msgstr ""
"Permet de choisir un ou plusieurs termes de taxonomie en fonction des "
"critères et options spécifiés dans les réglages des champs."

#: includes/fields/class-acf-field-tab.php:25
msgid ""
"Allows you to group fields into tabbed sections in the edit screen. Useful "
"for keeping fields organized and structured."
msgstr ""
"Permet de regrouper les champs en sections à onglets dans l’écran d’édition. "
"Utile pour garder les champs organisés et structurés."

#: includes/fields/class-acf-field-select.php:18
msgid "A dropdown list with a selection of choices that you specify."
msgstr "Une liste déroulante avec une sélection de choix que vous spécifiez."

#: includes/fields/class-acf-field-relationship.php:19
msgid ""
"A dual-column interface to select one or more posts, pages, or custom post "
"type items to create a relationship with the item that you're currently "
"editing. Includes options to search and filter."
msgstr ""
"Une interface à deux colonnes pour choisir une ou plusieurs publications, "
"pages ou éléments de type publication personnalisés afin de créer une "
"relation avec l’élément que vous êtes en train de modifier. Inclut des "
"options de recherche et de filtrage."

#: includes/fields/class-acf-field-range.php:23
msgid ""
"An input for selecting a numerical value within a specified range using a "
"range slider element."
msgstr ""
"Entrée permettant de choisir une valeur numérique dans une plage spécifiée à "
"l’aide d’un élément de curseur de plage."

#: includes/fields/class-acf-field-radio.php:24
msgid ""
"A group of radio button inputs that allows the user to make a single "
"selection from values that you specify."
msgstr ""
"Groupe d’entrées de boutons radio qui permet à l’utilisateur d’effectuer une "
"sélection unique parmi les valeurs que vous spécifiez."

#: includes/fields/class-acf-field-post_object.php:17
msgid ""
"An interactive and customizable UI for picking one or many posts, pages or "
"post type items with the option to search. "
msgstr ""
"Une interface utilisateur interactive et personnalisable pour choisir un ou "
"plusieurs articles, pages ou éléments de type publication avec la "
"possibilité de rechercher. "

#: includes/fields/class-acf-field-password.php:23
msgid "An input for providing a password using a masked field."
msgstr "Une entrée pour fournir un mot de passe à l’aide d’un champ masqué."

#: includes/fields/class-acf-field-page_link.php:457
#: includes/fields/class-acf-field-post_object.php:366
#: includes/fields/class-acf-field-relationship.php:565
msgid "Filter by Post Status"
msgstr "Filtrer par état de publication"

#: includes/fields/class-acf-field-page_link.php:24
msgid ""
"An interactive dropdown to select one or more posts, pages, custom post type "
"items or archive URLs, with the option to search."
msgstr ""
"Une liste déroulante interactive pour choisir un ou plusieurs articles, "
"pages, éléments de type de publication personnalisés ou URL d’archive, avec "
"la possibilité de rechercher."

#: includes/fields/class-acf-field-oembed.php:24
msgid ""
"An interactive component for embedding videos, images, tweets, audio and "
"other content by making use of the native WordPress oEmbed functionality."
msgstr ""
"Un composant interactif pour intégrer des vidéos, des images, des tweets, de "
"l’audio et d’autres contenus en utilisant la fonctionnalité native WordPress "
"oEmbed."

#: includes/fields/class-acf-field-number.php:23
msgid "An input limited to numerical values."
msgstr "Une entrée limitée à des valeurs numériques."

#: includes/fields/class-acf-field-message.php:25
msgid ""
"Used to display a message to editors alongside other fields. Useful for "
"providing additional context or instructions around your fields."
msgstr ""
"Utilisé pour afficher un message aux éditeurs à côté d’autres champs. Utile "
"pour fournir un contexte ou des instructions supplémentaires concernant vos "
"champs."

#: includes/fields/class-acf-field-link.php:24
msgid ""
"Allows you to specify a link and its properties such as title and target "
"using the WordPress native link picker."
msgstr ""
"Permet de spécifier un lien et ses propriétés, telles que le titre et la "
"cible en utilisant le sélecteur de liens de WordPress."

#: includes/fields/class-acf-field-image.php:24
msgid "Uses the native WordPress media picker to upload, or choose images."
msgstr ""
"Utilise le sélecteur de média natif de WordPress pour téléverser ou choisir "
"des images."

#: includes/fields/class-acf-field-group.php:24
msgid ""
"Provides a way to structure fields into groups to better organize the data "
"and the edit screen."
msgstr ""
"Permet de structurer les champs en groupes afin de mieux organiser les "
"données et l’écran d‘édition."

#: includes/fields/class-acf-field-google-map.php:24
msgid ""
"An interactive UI for selecting a location using Google Maps. Requires a "
"Google Maps API key and additional configuration to display correctly."
msgstr ""
"Une interface utilisateur interactive pour sélectionner un emplacement à "
"l’aide de Google Maps. Nécessite une clé de l’API Google Maps et une "
"configuration supplémentaire pour s’afficher correctement."

#: includes/fields/class-acf-field-file.php:24
msgid "Uses the native WordPress media picker to upload, or choose files."
msgstr ""
"Utilise le sélecteur de médias WordPress natif pour téléverser ou choisir "
"des fichiers."

#: includes/fields/class-acf-field-email.php:23
msgid "A text input specifically designed for storing email addresses."
msgstr ""
"Une saisie de texte spécialement conçue pour stocker des adresses e-mail."

#: includes/fields/class-acf-field-date_time_picker.php:24
msgid ""
"An interactive UI for picking a date and time. The date return format can be "
"customized using the field settings."
msgstr ""
"Une interface utilisateur interactive pour choisir une date et un horaire. "
"Le format de la date retour peut être personnalisé à l’aide des réglages du "
"champ."

#: includes/fields/class-acf-field-date_picker.php:24
msgid ""
"An interactive UI for picking a date. The date return format can be "
"customized using the field settings."
msgstr ""
"Une interface utilisateur interactive pour choisir une date. Le format de la "
"date retour peut être personnalisé en utilisant les champs de réglages."

#: includes/fields/class-acf-field-color_picker.php:24
msgid "An interactive UI for selecting a color, or specifying a Hex value."
msgstr ""
"Une interface utilisateur interactive pour sélectionner une couleur ou "
"spécifier la valeur hexa."

#: includes/fields/class-acf-field-checkbox.php:24
msgid ""
"A group of checkbox inputs that allow the user to select one, or multiple "
"values that you specify."
msgstr ""
"Un groupe de case à cocher autorisant l’utilisateur/utilisatrice à "
"sélectionner une ou plusieurs valeurs que vous spécifiez."

#: includes/fields/class-acf-field-button-group.php:25
msgid ""
"A group of buttons with values that you specify, users can choose one option "
"from the values provided."
msgstr ""
"Un groupe de boutons avec des valeurs que vous spécifiez, les utilisateurs/"
"utilisatrices peuvent choisir une option parmi les valeurs fournies."

#: includes/fields/class-acf-field-accordion.php:26
msgid ""
"Allows you to group and organize custom fields into collapsable panels that "
"are shown while editing content. Useful for keeping large datasets tidy."
msgstr ""
"Autorise à regrouper et organiser les champs personnalisés dans des volets "
"dépliants qui s’affichent lors de la modification du contenu. Utile pour "
"garder de grands ensembles de données ordonnés."

#: includes/fields.php:449
msgid ""
"This provides a solution for repeating content such as slides, team members, "
"and call-to-action tiles, by acting as a parent to a set of subfields which "
"can be repeated again and again."
msgstr ""
"Cela propose une solution pour dupliquer des contenus tels que des "
"diapositive, des membres de l’équipe et des boutons d’appel à l‘action, en "
"agissant comme un parent pour un ensemble de sous-champs qui peuvent être "
"répétés à l’infini."

#: includes/fields.php:439
msgid ""
"This provides an interactive interface for managing a collection of "
"attachments. Most settings are similar to the Image field type. Additional "
"settings allow you to specify where new attachments are added in the gallery "
"and the minimum/maximum number of attachments allowed."
msgstr ""
"Cela propose une interface interactive permettant de gérer une collection de "
"fichiers joints. La plupart des réglages sont similaires à ceux du champ "
"Image. Des réglages supplémentaires vous autorise à spécifier l’endroit où "
"les nouveaux fichiers joints sont ajoutés dans la galerie et le nombre "
"minimum/maximum de fichiers joints autorisées."

#: includes/fields.php:429
msgid ""
"This provides a simple, structured, layout-based editor. The Flexible "
"Content field allows you to define, create and manage content with total "
"control by using layouts and subfields to design the available blocks."
msgstr ""
"Cela fournit un éditeur simple, structuré et basé sur la mise en page. Le "
"champ Contenu flexible vous permet de définir, créer et gérer du contenu "
"avec un contrôle total en utilisant des mises en page et des sous-champs "
"pour concevoir les blocs disponibles."

#: includes/fields.php:419
msgid ""
"This allows you to select and display existing fields. It does not duplicate "
"any fields in the database, but loads and displays the selected fields at "
"run-time. The Clone field can either replace itself with the selected fields "
"or display the selected fields as a group of subfields."
msgstr ""
"Cela vous permet de choisir et d’afficher les champs existants. Il ne "
"duplique aucun champ de la base de données, mais charge et affiche les "
"champs sélectionnés au moment de l’exécution. Le champ Clone peut soit se "
"remplacer par les champs sélectionnés, soit afficher les champs sélectionnés "
"sous la forme d’un groupe de sous-champs."

#: includes/fields.php:416
msgctxt "noun"
msgid "Clone"
msgstr "Cloner"

#: includes/admin/views/global/navigation.php:86 includes/fields.php:331
#: src/Site_Health/Site_Health.php:290
msgid "PRO"
msgstr "Pro"

#: includes/fields.php:329 includes/fields.php:386
msgid "Advanced"
msgstr "Avancé"

#: includes/ajax/class-acf-ajax-local-json-diff.php:90
msgid "JSON (newer)"
msgstr "JSON (plus récent)"

#: includes/ajax/class-acf-ajax-local-json-diff.php:86
msgid "Original"
msgstr "Original"

#: includes/ajax/class-acf-ajax-local-json-diff.php:60
msgid "Invalid post ID."
msgstr "ID de publication invalide."

#: includes/ajax/class-acf-ajax-local-json-diff.php:52
msgid "Invalid post type selected for review."
msgstr "Type de publication sélectionné pour révision invalide."

#: includes/admin/views/global/navigation.php:192
msgid "More"
msgstr "Plus"

#: includes/admin/views/browse-fields-modal.php:96
msgid "Tutorial"
msgstr "Tutoriel"

#: includes/admin/views/browse-fields-modal.php:73
msgid "Select Field"
msgstr "Sélectionner le champ"

#. translators: %s: A link to the popular fields used in ACF
#: includes/admin/views/browse-fields-modal.php:60
msgid "Try a different search term or browse %s"
msgstr "Essayez un autre terme de recherche ou parcourez %s"

#: includes/admin/views/browse-fields-modal.php:57
msgid "Popular fields"
msgstr "Champs populaires"

#. translators: %s: The invalid search term
#: includes/admin/views/browse-fields-modal.php:50
#: includes/fields/class-acf-field-icon_picker.php:97
msgid "No search results for '%s'"
msgstr "Aucun résultat de recherche pour « %s »"

#: includes/admin/views/browse-fields-modal.php:23
msgid "Search fields..."
msgstr "Rechercher des champs…"

#: includes/admin/views/browse-fields-modal.php:21
msgid "Select Field Type"
msgstr "Sélectionner le type de champ"

#: includes/admin/views/browse-fields-modal.php:4
msgid "Popular"
msgstr "Populaire"

#: includes/admin/views/acf-taxonomy/list-empty.php:15
msgid "Add Taxonomy"
msgstr "Ajouter une taxonomie"

#: includes/admin/views/acf-taxonomy/list-empty.php:14
msgid "Create custom taxonomies to classify post type content"
msgstr ""
"Créer des taxonomies personnalisées pour classer le contenu du type de "
"publication"

#: includes/admin/views/acf-taxonomy/list-empty.php:13
msgid "Add Your First Taxonomy"
msgstr "Ajouter votre première taxonomie"

#: includes/admin/views/acf-taxonomy/basic-settings.php:122
msgid "Hierarchical taxonomies can have descendants (like categories)."
msgstr ""
"Les taxonomies hiérarchiques peuvent avoir des enfants (comme les "
"catégories)."

#: includes/admin/views/acf-taxonomy/basic-settings.php:107
msgid "Makes a taxonomy visible on the frontend and in the admin dashboard."
msgstr ""
"Rend une taxonomie visible sur l’interface publique et dans le tableau de "
"bord d’administration."

#: includes/admin/views/acf-taxonomy/basic-settings.php:91
msgid "One or many post types that can be classified with this taxonomy."
msgstr ""
"Un ou plusieurs types de publication peuvant être classés avec cette "
"taxonomie."

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:60
msgid "genre"
msgstr "genre"

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:42
msgid "Genre"
msgstr "Genre"

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:25
msgid "Genres"
msgstr "Genres"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1231
msgid ""
"Optional custom controller to use instead of `WP_REST_Terms_Controller `."
msgstr ""
"Contrôleur personnalisé facultatif à utiliser à la place de "
"« WP_REST_Terms_Controller »."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1175
msgid "Expose this post type in the REST API."
msgstr "Exposez ce type de publication dans l’API REST."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1075
msgid "Customize the query variable name"
msgstr "Personnaliser le nom de la variable de requête"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1048
msgid ""
"Terms can be accessed using the non-pretty permalink, e.g., {query_var}"
"={term_slug}."
msgstr ""
"Les termes sont accessibles en utilisant le permalien non joli, par exemple, "
"{query_var}={term_slug}."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1001
msgid "Parent-child terms in URLs for hierarchical taxonomies."
msgstr "Termes parent-enfant dans les URL pour les taxonomies hiérarchiques."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:961
msgid "Customize the slug used in the URL"
msgstr "Personnaliser le slug utilisé dans l’URL"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:944
msgid "Permalinks for this taxonomy are disabled."
msgstr "Les permaliens sont désactivés pour cette taxonomie."

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-taxonomy/advanced-settings.php:941
msgid ""
"Rewrite the URL using the taxonomy key as the slug. Your permalink structure "
"will be"
msgstr ""
"Réécrire l’URL en utilisant la clé de taxonomie comme slug. Votre structure "
"de permalien sera"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:933
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1050
#: includes/admin/views/acf-taxonomy/basic-settings.php:57
msgid "Taxonomy Key"
msgstr "Clé de taxonomie"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:931
msgid "Select the type of permalink to use for this taxonomy."
msgstr "Sélectionnez le type de permalien à utiliser pour cette taxonomie."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:916
msgid "Display a column for the taxonomy on post type listing screens."
msgstr ""
"Affichez une colonne pour la taxonomie sur les écrans de liste de type de "
"publication."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:915
msgid "Show Admin Column"
msgstr "Afficher la colonne « Admin »"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:902
msgid "Show the taxonomy in the quick/bulk edit panel."
msgstr "Afficher la taxonomie dans le panneau de modification rapide/groupée."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:901
msgid "Quick Edit"
msgstr "Modification rapide"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:888
msgid "List the taxonomy in the Tag Cloud Widget controls."
msgstr ""
"Lister la taxonomie dans les contrôles du widget « Nuage d’étiquettes »."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:887
msgid "Tag Cloud"
msgstr "Nuage d’étiquettes"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:842
msgid ""
"A PHP function name to be called for sanitizing taxonomy data saved from a "
"meta box."
msgstr ""
"Un nom de fonction PHP à appeler pour nettoyer les données de taxonomie "
"enregistrées à partir d’une boîte méta."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:841
msgid "Meta Box Sanitization Callback"
msgstr "Rappel de désinfection de la méta-boîte"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:821
msgid "Register Meta Box Callback"
msgstr "Enregistrer le rappel de la boîte méta"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:752
msgid "No Meta Box"
msgstr "Aucune boîte méta"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:751
msgid "Custom Meta Box"
msgstr "Boîte méta personnalisée"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:768
msgid ""
"Controls the meta box on the content editor screen. By default, the "
"Categories meta box is shown for hierarchical taxonomies, and the Tags meta "
"box is shown for non-hierarchical taxonomies."
msgstr ""
"Contrôle la boîte méta sur l’écran de l’éditeur de contenu. Par défaut, la "
"boîte méta Catégories est affichée pour les taxonomies hiérarchiques et la "
"boîte de métadonnées Étiquettes est affichée pour les taxonomies non "
"hiérarchiques."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:767
msgid "Meta Box"
msgstr "Boîte méta"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:746
#: includes/admin/views/acf-taxonomy/advanced-settings.php:773
msgid "Categories Meta Box"
msgstr "Boîte méta des catégories"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:745
#: includes/admin/views/acf-taxonomy/advanced-settings.php:772
msgid "Tags Meta Box"
msgstr "Boîte méta des étiquettes"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:704
msgid "A link to a tag"
msgstr "Un lien vers une étiquette"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:703
msgid "Describes a navigation link block variation used in the block editor."
msgstr ""
"Décrit une variante de bloc de lien de navigation utilisée dans l’éditeur de "
"blocs."

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:698
msgid "A link to a %s"
msgstr "Un lien vers un %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:683
msgid "Tag Link"
msgstr "Lien de l’étiquette"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:682
msgid ""
"Assigns a title for navigation link block variation used in the block editor."
msgstr ""
"Assigner un titre à la variante de bloc de lien de navigation utilisée dans "
"l’éditeur de blocs."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:663
msgid "← Go to tags"
msgstr "← Aller aux étiquettes"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:662
msgid ""
"Assigns the text used to link back to the main index after updating a term."
msgstr ""
"Assigner le texte utilisé pour renvoyer à l’index principal après la mise à "
"jour d’un terme."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:661
msgid "Back To Items"
msgstr "Retour aux éléments"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:657
msgid "← Go to %s"
msgstr "← Aller à « %s »"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:642
msgid "Tags list"
msgstr "Liste des étiquettes"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:641
msgid "Assigns text to the table hidden heading."
msgstr "Assigne du texte au titre masqué du tableau."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:622
msgid "Tags list navigation"
msgstr "Navigation de la liste des étiquettes"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:621
msgid "Assigns text to the table pagination hidden heading."
msgstr "Affecte du texte au titre masqué de pagination de tableau."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:597
msgid "Filter by category"
msgstr "Filtrer par catégorie"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:596
msgid "Assigns text to the filter button in the posts lists table."
msgstr ""
"Affecte du texte au bouton de filtre dans le tableau des listes de "
"publications."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:595
msgid "Filter By Item"
msgstr "Filtrer par élément"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:591
msgid "Filter by %s"
msgstr "Filtrer par %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:575
#: includes/admin/views/acf-taxonomy/advanced-settings.php:576
msgid ""
"The description is not prominent by default; however, some themes may show "
"it."
msgstr ""
"La description n’est pas très utilisée par défaut, cependant de plus en plus "
"de thèmes l’affichent."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:574
msgid "Describes the Description field on the Edit Tags screen."
msgstr ""
"Décrit le champ « Description » sur l’écran « Modifier les étiquettes »."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:573
msgid "Description Field Description"
msgstr "Description du champ « Description »"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:554
#: includes/admin/views/acf-taxonomy/advanced-settings.php:555
msgid ""
"Assign a parent term to create a hierarchy. The term Jazz, for example, "
"would be the parent of Bebop and Big Band"
msgstr ""
"Affectez un terme parent pour créer une hiérarchie. Le terme Jazz, par "
"exemple, serait le parent du Bebop et du Big Band."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:553
msgid "Describes the Parent field on the Edit Tags screen."
msgstr "Décrit le champ parent sur l’écran « Modifier les étiquettes »."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:552
msgid "Parent Field Description"
msgstr "Description du champ « Parent »"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:538
#: includes/admin/views/acf-taxonomy/advanced-settings.php:539
msgid ""
"The \"slug\" is the URL-friendly version of the name. It is usually all "
"lower case and contains only letters, numbers, and hyphens."
msgstr ""
"Le « slug » est la version URL conviviale du nom. Il est généralement tout "
"en minuscules et contient uniquement des lettres, des chiffres et des traits "
"d’union."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:537
msgid "Describes the Slug field on the Edit Tags screen."
msgstr "Décrit le slug du champ sur l’écran « Modifier les étiquettes »."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:536
msgid "Slug Field Description"
msgstr "Description du champ « Slug »"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:522
#: includes/admin/views/acf-taxonomy/advanced-settings.php:523
msgid "The name is how it appears on your site"
msgstr "Le nom est la façon dont il apparaît sur votre site"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:521
msgid "Describes the Name field on the Edit Tags screen."
msgstr "Décrit le champ « Nom » sur l’écran « Modifier les étiquettes »."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:520
msgid "Name Field Description"
msgstr "Description du champ « Nom »"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:507
msgid "No tags"
msgstr "Aucune étiquette"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:506
msgid ""
"Assigns the text displayed in the posts and media list tables when no tags "
"or categories are available."
msgstr ""
"Attribue le texte affiché dans les tableaux de liste des publications et des "
"médias lorsqu’aucune balise ou catégorie n’est disponible."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:505
msgid "No Terms"
msgstr "Aucun terme"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:501
msgid "No %s"
msgstr "Aucun %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:486
msgid "No tags found"
msgstr "Aucune étiquette trouvée"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:485
msgid ""
"Assigns the text displayed when clicking the 'choose from most used' text in "
"the taxonomy meta box when no tags are available, and assigns the text used "
"in the terms list table when there are no items for a taxonomy."
msgstr ""
"Assigne le texte affiché lorsque vous cliquez sur le texte « choisir parmi "
"les plus utilisés » dans la boîte de méta de taxonomie lorsqu’aucune "
"étiquette n’est disponible, et affecte le texte utilisé dans le tableau de "
"liste des termes lorsqu’il n’y a pas d’élément pour une taxonomie."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:484
msgid "Not Found"
msgstr "Non trouvé"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:463
msgid "Assigns text to the Title field of the Most Used tab."
msgstr ""
"Affecte du texte au champ Titre de l’onglet Utilisation la plus utilisée."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:462
#: includes/admin/views/acf-taxonomy/advanced-settings.php:464
#: includes/admin/views/acf-taxonomy/advanced-settings.php:465
msgid "Most Used"
msgstr "Les plus utilisés"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:444
msgid "Choose from the most used tags"
msgstr "Choisir parmi les étiquettes les plus utilisées"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:443
msgid ""
"Assigns the 'choose from most used' text used in the meta box when "
"JavaScript is disabled. Only used on non-hierarchical taxonomies."
msgstr ""
"Attribue le texte « choisir parmi les plus utilisés » utilisé dans la méta-"
"zone lorsque JavaScript est désactivé. Utilisé uniquement sur les taxonomies "
"non hiérarchiques."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:442
msgid "Choose From Most Used"
msgstr "Choisir parmi les plus utilisés"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:438
msgid "Choose from the most used %s"
msgstr "Choisir parmi les %s les plus utilisés"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:418
msgid "Add or remove tags"
msgstr "Ajouter ou retirer des étiquettes"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:417
msgid ""
"Assigns the add or remove items text used in the meta box when JavaScript is "
"disabled. Only used on non-hierarchical taxonomies"
msgstr ""
"Assigne le texte d’ajout ou de suppression d’éléments utilisé dans la boîte "
"méta lorsque JavaScript est désactivé. Utilisé uniquement sur les taxonomies "
"non hiérarchiques"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:416
msgid "Add Or Remove Items"
msgstr "Ajouter ou supprimer des éléments"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:412
msgid "Add or remove %s"
msgstr "Ajouter ou retirer %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:392
msgid "Separate tags with commas"
msgstr "Séparer les étiquettes par des virgules"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:391
msgid ""
"Assigns the separate item with commas text used in the taxonomy meta box. "
"Only used on non-hierarchical taxonomies."
msgstr ""
"Affecte l’élément distinct avec des virgules utilisées dans la méta-zone de "
"taxonomie. Utilisé uniquement sur les taxonomies non hiérarchiques."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:390
msgid "Separate Items With Commas"
msgstr "Séparer les éléments par des virgules"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:386
msgid "Separate %s with commas"
msgstr "Séparer les %s avec une virgule"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:366
msgid "Popular Tags"
msgstr "Étiquettes populaires"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:365
msgid "Assigns popular items text. Only used for non-hierarchical taxonomies."
msgstr ""
"Affecte le texte des éléments populaires. Utilisé uniquement pour les "
"taxonomies non hiérarchiques."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:364
msgid "Popular Items"
msgstr "Éléments populaires"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:361
msgid "Popular %s"
msgstr "%s populaire"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:347
msgid "Search Tags"
msgstr "Rechercher des étiquettes"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:346
msgid "Assigns search items text."
msgstr "Assigne le texte des éléments de recherche."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:323
msgid "Parent Category:"
msgstr "Catégorie parente :"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:322
msgid "Assigns parent item text, but with a colon (:) added to the end."
msgstr ""
"Assigne le texte de l’élément parent, mais avec deux points (:) ajouté à la "
"fin."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:321
msgid "Parent Item With Colon"
msgstr "Élément parent avec deux-points"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:298
msgid "Parent Category"
msgstr "Catégorie parente"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:297
msgid "Assigns parent item text. Only used on hierarchical taxonomies."
msgstr ""
"Assigne le texte de l’élément parent. Utilisé uniquement sur les taxonomies "
"hiérarchiques."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:296
msgid "Parent Item"
msgstr "Élément parent"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:293
msgid "Parent %s"
msgstr "%s parent"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:278
msgid "New Tag Name"
msgstr "Nom de la nouvelle étiquette"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:277
msgid "Assigns the new item name text."
msgstr "Assigne le texte du nom du nouvel élément."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:276
msgid "New Item Name"
msgstr "Nom du nouvel élément"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:273
msgid "New %s Name"
msgstr "Nom du nouveau %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:258
msgid "Add New Tag"
msgstr "Ajouter une nouvelle étiquette"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:257
msgid "Assigns the add new item text."
msgstr "Assigne le texte « Ajouter un nouvel élément »."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:238
msgid "Update Tag"
msgstr "Mettre à jour l’étiquette"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:237
msgid "Assigns the update item text."
msgstr "Assigne le texte de l’élément « Mettre à jour »."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:236
msgid "Update Item"
msgstr "Mettre à jour l’élément"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:233
msgid "Update %s"
msgstr "Mettre à jour %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:218
msgid "View Tag"
msgstr "Voir l’étiquette"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:217
msgid "In the admin bar to view term during editing."
msgstr ""
"Dans la barre d’administration pour voir le terme lors de la modification."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:198
msgid "Edit Tag"
msgstr "Modifier l’étiquette"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:197
msgid "At the top of the editor screen when editing a term."
msgstr "En haut de l’écran de l’éditeur lors de la modification d’un terme."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:178
msgid "All Tags"
msgstr "Toutes les étiquettes"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:177
msgid "Assigns the all items text."
msgstr "Assigne le texte « Tous les éléments »."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:158
msgid "Assigns the menu name text."
msgstr "Assigne le texte du nom du menu."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:157
msgid "Menu Label"
msgstr "Libellé du menu"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:131
msgid "Active taxonomies are enabled and registered with WordPress."
msgstr "Les taxonomies actives sont activées et enregistrées avec WordPress."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:115
msgid "A descriptive summary of the taxonomy."
msgstr "Un résumé descriptif de la taxonomie."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:95
msgid "A descriptive summary of the term."
msgstr "Un résumé descriptif du terme."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:94
msgid "Term Description"
msgstr "Description du terme"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:76
msgid "Single word, no spaces. Underscores and dashes allowed."
msgstr "Un seul mot, aucun espace. Tirets bas et tirets autorisés."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:75
msgid "Term Slug"
msgstr "Slug du terme"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:56
msgid "The name of the default term."
msgstr "Nom du terme par défaut."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:55
msgid "Term Name"
msgstr "Nom du terme"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:41
msgid ""
"Create a term for the taxonomy that cannot be deleted. It will not be "
"selected for posts by default."
msgstr ""
"Créez un terme pour la taxonomie qui ne peut pas être supprimé. Il ne sera "
"pas sélectionné pour les publications par défaut."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:40
msgid "Default Term"
msgstr "Terme par défaut"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:28
msgid ""
"Whether terms in this taxonomy should be sorted in the order they are "
"provided to `wp_set_object_terms()`."
msgstr ""
"Si les termes de cette taxonomie doivent être triés dans l’ordre dans lequel "
"ils sont fournis à « wp_set_object_terms() »."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:27
msgid "Sort Terms"
msgstr "Trier les termes"

#: includes/admin/views/acf-post-type/list-empty.php:14
msgid "Add Post Type"
msgstr "Ajouter un type de publication"

#: includes/admin/views/acf-post-type/list-empty.php:13
msgid ""
"Expand the functionality of WordPress beyond standard posts and pages with "
"custom post types."
msgstr ""
"Étendez les fonctionnalités de WordPress au-delà des publications standard "
"et des pages avec des types de publication personnalisés."

#: includes/admin/views/acf-post-type/list-empty.php:12
msgid "Add Your First Post Type"
msgstr "Ajouter votre premier type de publication"

#: includes/admin/views/acf-post-type/basic-settings.php:136
#: includes/admin/views/acf-taxonomy/basic-settings.php:135
msgid "I know what I'm doing, show me all the options."
msgstr "Je sais ce que je fais, affichez-moi toutes les options."

#: includes/admin/views/acf-post-type/basic-settings.php:135
#: includes/admin/views/acf-taxonomy/basic-settings.php:134
msgid "Advanced Configuration"
msgstr "Configuration avancée"

#: includes/admin/views/acf-post-type/basic-settings.php:123
msgid "Hierarchical post types can have descendants (like pages)."
msgstr ""
"Les types de publication hiérarchiques peuvent avoir des descendants (comme "
"les pages)."

#: includes/admin/views/acf-post-type/basic-settings.php:122
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1000
#: includes/admin/views/acf-taxonomy/basic-settings.php:121
msgid "Hierarchical"
msgstr "Hiérachique"

#: includes/admin/views/acf-post-type/basic-settings.php:107
msgid "Visible on the frontend and in the admin dashboard."
msgstr ""
"Visible sur l’interface publique et dans le tableau de bord de "
"l’administration."

#: includes/admin/views/acf-post-type/basic-settings.php:106
#: includes/admin/views/acf-taxonomy/basic-settings.php:106
msgid "Public"
msgstr "Public"

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:59
msgid "movie"
msgstr "film"

#: includes/admin/views/acf-post-type/basic-settings.php:57
msgid "Lower case letters, underscores and dashes only, Max 20 characters."
msgstr ""
"Lettres minuscules, tiret bas et tirets uniquement, maximum 20 caractères."

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:41
msgid "Movie"
msgstr "Film"

#: includes/admin/views/acf-post-type/basic-settings.php:39
#: includes/admin/views/acf-taxonomy/basic-settings.php:40
msgid "Singular Label"
msgstr "Libellé au singulier"

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:24
msgid "Movies"
msgstr "Films"

#: includes/admin/views/acf-post-type/basic-settings.php:22
#: includes/admin/views/acf-taxonomy/basic-settings.php:23
msgid "Plural Label"
msgstr "Libellé au pluriel"

#: includes/admin/views/acf-post-type/advanced-settings.php:1313
msgid ""
"Optional custom controller to use instead of `WP_REST_Posts_Controller`."
msgstr ""
"Contrôleur personnalisé facultatif à utiliser à la place de "
"« WP_REST_Posts_Controller »."

#: includes/admin/views/acf-post-type/advanced-settings.php:1312
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1230
msgid "Controller Class"
msgstr "Classe de contrôleur"

#: includes/admin/views/acf-post-type/advanced-settings.php:1294
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1211
msgid "The namespace part of the REST API URL."
msgstr "Partie de l’espace de noms de l’URL DE L’API REST."

#: includes/admin/views/acf-post-type/advanced-settings.php:1293
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1210
msgid "Namespace Route"
msgstr "Route de l’espace de noms"

#: includes/admin/views/acf-post-type/advanced-settings.php:1275
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1192
msgid "The base URL for the post type REST API URLs."
msgstr "URL de base pour les URL de l’API REST du type de publication."

#: includes/admin/views/acf-post-type/advanced-settings.php:1274
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1191
msgid "Base URL"
msgstr "URL de base"

#: includes/admin/views/acf-post-type/advanced-settings.php:1260
msgid ""
"Exposes this post type in the REST API. Required to use the block editor."
msgstr ""
"Expose ce type de publication dans l’API REST. Nécessaire pour utiliser "
"l’éditeur de bloc."

#: includes/admin/views/acf-post-type/advanced-settings.php:1259
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1174
msgid "Show In REST API"
msgstr "Afficher dans l’API Rest"

#: includes/admin/views/acf-post-type/advanced-settings.php:1238
msgid "Customize the query variable name."
msgstr "Personnaliser le nom de la variable de requête."

#: includes/admin/views/acf-post-type/advanced-settings.php:1237
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1074
msgid "Query Variable"
msgstr "Variable de requête"

#: includes/admin/views/acf-post-type/advanced-settings.php:1215
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1052
msgid "No Query Variable Support"
msgstr "Aucune prise en charge des variables de requête"

#: includes/admin/views/acf-post-type/advanced-settings.php:1214
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1051
msgid "Custom Query Variable"
msgstr "Variable de requête personnalisée"

#: includes/admin/views/acf-post-type/advanced-settings.php:1211
msgid ""
"Items can be accessed using the non-pretty permalink, eg. {post_type}"
"={post_slug}."
msgstr ""
"Les articles sont accessibles en utilisant le permalien non-jolie, par "
"exemple. {post_type}={post_slug}."

#: includes/admin/views/acf-post-type/advanced-settings.php:1210
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1047
msgid "Query Variable Support"
msgstr "Prise en charge des variables de requête"

#: includes/admin/views/acf-post-type/advanced-settings.php:1185
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1023
msgid "URLs for an item and items can be accessed with a query string."
msgstr ""
"Les URL d’un élément et d’éléments sont accessibles à l’aide d’une chaîne de "
"requête."

#: includes/admin/views/acf-post-type/advanced-settings.php:1184
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1022
msgid "Publicly Queryable"
msgstr "Publiquement interrogeable"

#: includes/admin/views/acf-post-type/advanced-settings.php:1163
msgid "Custom slug for the Archive URL."
msgstr "Slug personnalisé pour l’URL de l’archive."

#: includes/admin/views/acf-post-type/advanced-settings.php:1162
msgid "Archive Slug"
msgstr "Slug de l’archive"

#: includes/admin/views/acf-post-type/advanced-settings.php:1149
msgid ""
"Has an item archive that can be customized with an archive template file in "
"your theme."
msgstr ""
"Possède une archive d’élément qui peut être personnalisée avec un fichier de "
"modèle d’archive dans votre thème."

#: includes/admin/views/acf-post-type/advanced-settings.php:1148
msgid "Archive"
msgstr "Archive"

#: includes/admin/views/acf-post-type/advanced-settings.php:1128
msgid "Pagination support for the items URLs such as the archives."
msgstr ""
"Prise en charge de la pagination pour les URL des éléments tels que les "
"archives."

#: includes/admin/views/acf-post-type/advanced-settings.php:1127
msgid "Pagination"
msgstr "Pagination"

#: includes/admin/views/acf-post-type/advanced-settings.php:1110
msgid "RSS feed URL for the post type items."
msgstr "URL de flux RSS pour les éléments du type de publication."

#: includes/admin/views/acf-post-type/advanced-settings.php:1109
msgid "Feed URL"
msgstr "URL du flux"

#: includes/admin/views/acf-post-type/advanced-settings.php:1091
#: includes/admin/views/acf-taxonomy/advanced-settings.php:981
msgid ""
"Alters the permalink structure to add the `WP_Rewrite::$front` prefix to "
"URLs."
msgstr ""
"Modifie la structure du permalien pour ajouter le préfixe « WP_Rewrite::"
"$front » aux URL."

#: includes/admin/views/acf-post-type/advanced-settings.php:1090
#: includes/admin/views/acf-taxonomy/advanced-settings.php:980
msgid "Front URL Prefix"
msgstr "Préfixe d’URL avant"

#: includes/admin/views/acf-post-type/advanced-settings.php:1071
msgid "Customize the slug used in the URL."
msgstr "Personnalisez le slug utilisé dans l’URL."

#: includes/admin/views/acf-post-type/advanced-settings.php:1070
#: includes/admin/views/acf-taxonomy/advanced-settings.php:960
msgid "URL Slug"
msgstr "Slug de l’URL"

#: includes/admin/views/acf-post-type/advanced-settings.php:1054
msgid "Permalinks for this post type are disabled."
msgstr "Les permaliens sont désactivés pour ce type de publication."

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-post-type/advanced-settings.php:1053
#: includes/admin/views/acf-taxonomy/advanced-settings.php:943
msgid ""
"Rewrite the URL using a custom slug defined in the input below. Your "
"permalink structure will be"
msgstr ""
"Réécrivez l’URL à l’aide d’un identifiant d’URL personnalisé défini dans "
"l’entrée ci-dessous. Votre structure de permalien sera"

#: includes/admin/views/acf-post-type/advanced-settings.php:1045
#: includes/admin/views/acf-taxonomy/advanced-settings.php:935
msgid "No Permalink (prevent URL rewriting)"
msgstr "Aucun permalien (empêcher la réécriture d’URL)"

#: includes/admin/views/acf-post-type/advanced-settings.php:1044
#: includes/admin/views/acf-taxonomy/advanced-settings.php:934
msgid "Custom Permalink"
msgstr "Permalien personnalisé"

#: includes/admin/views/acf-post-type/advanced-settings.php:1043
#: includes/admin/views/acf-post-type/advanced-settings.php:1213
#: includes/admin/views/acf-post-type/basic-settings.php:56
msgid "Post Type Key"
msgstr "Clé du type de publication"

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-post-type/advanced-settings.php:1041
#: includes/admin/views/acf-post-type/advanced-settings.php:1051
msgid ""
"Rewrite the URL using the post type key as the slug. Your permalink "
"structure will be"
msgstr ""
"Réécrire l’URL en utilisant la clé du type de publication comme slug. Votre "
"structure de permalien sera"

#: includes/admin/views/acf-post-type/advanced-settings.php:1039
#: includes/admin/views/acf-taxonomy/advanced-settings.php:930
msgid "Permalink Rewrite"
msgstr "Réécriture du permalien"

#: includes/admin/views/acf-post-type/advanced-settings.php:1025
msgid "Delete items by a user when that user is deleted."
msgstr "Supprimer les éléments d’un compte lorsque ce dernier est supprimé."

#: includes/admin/views/acf-post-type/advanced-settings.php:1024
msgid "Delete With User"
msgstr "Supprimer avec le compte"

#: includes/admin/views/acf-post-type/advanced-settings.php:1010
msgid "Allow the post type to be exported from 'Tools' > 'Export'."
msgstr ""
"Autoriser l’exportation du type de publication depuis « Outils » > "
"« Exporter »."

#: includes/admin/views/acf-post-type/advanced-settings.php:1009
msgid "Can Export"
msgstr "Exportable"

#: includes/admin/views/acf-post-type/advanced-settings.php:978
msgid "Optionally provide a plural to be used in capabilities."
msgstr ""
"Fournissez éventuellement un pluriel à utiliser dans les fonctionnalités."

#: includes/admin/views/acf-post-type/advanced-settings.php:977
msgid "Plural Capability Name"
msgstr "Nom de la capacité au pluriel"

#: includes/admin/views/acf-post-type/advanced-settings.php:959
msgid "Choose another post type to base the capabilities for this post type."
msgstr ""
"Choisissez un autre type de publication pour baser les fonctionnalités de ce "
"type de publication."

#: includes/admin/views/acf-post-type/advanced-settings.php:958
msgid "Singular Capability Name"
msgstr "Nom de capacité singulier"

#: includes/admin/views/acf-post-type/advanced-settings.php:944
msgid ""
"By default the capabilities of the post type will inherit the 'Post' "
"capability names, eg. edit_post, delete_posts. Enable to use post type "
"specific capabilities, eg. edit_{singular}, delete_{plural}."
msgstr ""
"Par défaut, les capacités du type de publication hériteront des noms de "
"capacité « Post », par exemple. edit_post, delete_posts. Permet d’utiliser "
"des fonctionnalités spécifiques au type de publication, par exemple. "
"edit_{singular}, delete_{plural}."

#: includes/admin/views/acf-post-type/advanced-settings.php:943
msgid "Rename Capabilities"
msgstr "Renommer les permissions"

#: includes/admin/views/acf-post-type/advanced-settings.php:928
msgid "Exclude From Search"
msgstr "Exclure de la recherche"

#: includes/admin/views/acf-post-type/advanced-settings.php:915
#: includes/admin/views/acf-taxonomy/advanced-settings.php:874
msgid ""
"Allow items to be added to menus in the 'Appearance' > 'Menus' screen. Must "
"be turned on in 'Screen options'."
msgstr ""
"Autorisez l’ajout d’éléments aux menus dans l’écran « Apparence » > "
"« Menus ». Doit être activé dans « Options de l’écran »."

#: includes/admin/views/acf-post-type/advanced-settings.php:914
#: includes/admin/views/acf-taxonomy/advanced-settings.php:873
msgid "Appearance Menus Support"
msgstr "Prise en charge des menus d’apparence"

#: includes/admin/views/acf-post-type/advanced-settings.php:896
msgid "Appears as an item in the 'New' menu in the admin bar."
msgstr ""
"Apparaît en tant qu’élément dans le menu « Nouveau » de la barre "
"d’administration."

#: includes/admin/views/acf-post-type/advanced-settings.php:895
msgid "Show In Admin Bar"
msgstr "Afficher dans la barre d’administration"

#: includes/admin/views/acf-post-type/advanced-settings.php:861
msgid "Custom Meta Box Callback"
msgstr "Rappel de boîte méta personnalisée"

#: includes/admin/views/acf-post-type/advanced-settings.php:822
#: includes/fields/class-acf-field-icon_picker.php:636
msgid "Menu Icon"
msgstr "Icône de menu"

#: includes/admin/views/acf-post-type/advanced-settings.php:778
msgid "The position in the sidebar menu in the admin dashboard."
msgstr ""
"Position dans le menu de la colonne latérale du tableau de bord "
"d’administration."

#: includes/admin/views/acf-post-type/advanced-settings.php:777
msgid "Menu Position"
msgstr "Position du menu"

#: includes/admin/views/acf-post-type/advanced-settings.php:759
msgid ""
"By default the post type will get a new top level item in the admin menu. If "
"an existing top level item is supplied here, the post type will be added as "
"a submenu item under it."
msgstr ""
"Par défaut, le type de publication obtiendra un nouvel élément de niveau "
"supérieur dans le menu d’administration. Si un élément de niveau supérieur "
"existant est fourni ici, le type de publication sera ajouté en tant "
"qu’élément de sous-menu sous celui-ci."

#: includes/admin/views/acf-post-type/advanced-settings.php:758
msgid "Admin Menu Parent"
msgstr "Parent du menu d’administration"

#: includes/admin/views/acf-post-type/advanced-settings.php:739
#: includes/admin/views/acf-taxonomy/advanced-settings.php:734
msgid "Admin editor navigation in the sidebar menu."
msgstr ""
"Navigation de l’éditeur d’administration dans le menu de la colonne latérale."

#: includes/admin/views/acf-post-type/advanced-settings.php:738
#: includes/admin/views/acf-taxonomy/advanced-settings.php:733
msgid "Show In Admin Menu"
msgstr "Afficher dans le menu d’administration"

#: includes/admin/views/acf-post-type/advanced-settings.php:725
#: includes/admin/views/acf-taxonomy/advanced-settings.php:719
msgid "Items can be edited and managed in the admin dashboard."
msgstr ""
"Les éléments peuvent être modifiés et gérés dans le tableau de bord "
"d’administration."

#: includes/admin/views/acf-post-type/advanced-settings.php:724
#: includes/admin/views/acf-taxonomy/advanced-settings.php:718
msgid "Show In UI"
msgstr "Afficher dans l’interface utilisateur"

#: includes/admin/views/acf-post-type/advanced-settings.php:694
msgid "A link to a post."
msgstr "Un lien vers une publication."

#: includes/admin/views/acf-post-type/advanced-settings.php:693
msgid "Description for a navigation link block variation."
msgstr "Description d’une variation de bloc de lien de navigation."

#: includes/admin/views/acf-post-type/advanced-settings.php:692
#: includes/admin/views/acf-taxonomy/advanced-settings.php:702
msgid "Item Link Description"
msgstr "Description du lien de l’élément"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:688
msgid "A link to a %s."
msgstr "Un lien vers un %s."

#: includes/admin/views/acf-post-type/advanced-settings.php:673
msgid "Post Link"
msgstr "Lien de publication"

#: includes/admin/views/acf-post-type/advanced-settings.php:672
msgid "Title for a navigation link block variation."
msgstr "Titre d’une variante de bloc de lien de navigation."

#: includes/admin/views/acf-post-type/advanced-settings.php:671
#: includes/admin/views/acf-taxonomy/advanced-settings.php:681
msgid "Item Link"
msgstr "Lien de l’élément"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:668
#: includes/admin/views/acf-taxonomy/advanced-settings.php:678
msgid "%s Link"
msgstr "Lien %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:653
msgid "Post updated."
msgstr "Publication mise à jour."

#: includes/admin/views/acf-post-type/advanced-settings.php:652
msgid "In the editor notice after an item is updated."
msgstr "Dans l’éditeur, notification après la mise à jour d’un élément."

#: includes/admin/views/acf-post-type/advanced-settings.php:651
msgid "Item Updated"
msgstr "Élément mis à jour"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:648
msgid "%s updated."
msgstr "%s mis à jour."

#: includes/admin/views/acf-post-type/advanced-settings.php:633
msgid "Post scheduled."
msgstr "Publication planifiée."

#: includes/admin/views/acf-post-type/advanced-settings.php:632
msgid "In the editor notice after scheduling an item."
msgstr "Dans l’éditeur, notification après la planification d’un élément."

#: includes/admin/views/acf-post-type/advanced-settings.php:631
msgid "Item Scheduled"
msgstr "Élément planifié"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:628
msgid "%s scheduled."
msgstr "%s planifié."

#: includes/admin/views/acf-post-type/advanced-settings.php:613
msgid "Post reverted to draft."
msgstr "Publication reconvertie en brouillon."

#: includes/admin/views/acf-post-type/advanced-settings.php:612
msgid "In the editor notice after reverting an item to draft."
msgstr ""
"Dans l’éditeur, notification après avoir rétabli un élément en brouillon."

#: includes/admin/views/acf-post-type/advanced-settings.php:611
msgid "Item Reverted To Draft"
msgstr "Élément repassé en brouillon"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:608
msgid "%s reverted to draft."
msgstr "%s repassé en brouillon."

#: includes/admin/views/acf-post-type/advanced-settings.php:593
msgid "Post published privately."
msgstr "Publication publiée en privé."

#: includes/admin/views/acf-post-type/advanced-settings.php:592
msgid "In the editor notice after publishing a private item."
msgstr "Dans l’éditeur, notification après avoir rétabli un élément en privé."

#: includes/admin/views/acf-post-type/advanced-settings.php:591
msgid "Item Published Privately"
msgstr "Élément publié en privé"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:588
msgid "%s published privately."
msgstr "%s publié en privé."

#: includes/admin/views/acf-post-type/advanced-settings.php:573
msgid "Post published."
msgstr "Publication mise en ligne."

#: includes/admin/views/acf-post-type/advanced-settings.php:572
msgid "In the editor notice after publishing an item."
msgstr "Dans la notification de l’éditeur après la publication d’un élément."

#: includes/admin/views/acf-post-type/advanced-settings.php:571
msgid "Item Published"
msgstr "Élément publié"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:568
msgid "%s published."
msgstr "%s publié."

#: includes/admin/views/acf-post-type/advanced-settings.php:553
msgid "Posts list"
msgstr "Liste des publications"

#: includes/admin/views/acf-post-type/advanced-settings.php:552
msgid "Used by screen readers for the items list on the post type list screen."
msgstr ""
"Utilisé par les lecteurs d’écran pour la liste des éléments sur l’écran de "
"la liste des types de publication."

#: includes/admin/views/acf-post-type/advanced-settings.php:551
#: includes/admin/views/acf-taxonomy/advanced-settings.php:640
msgid "Items List"
msgstr "Liste des éléments"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:548
#: includes/admin/views/acf-taxonomy/advanced-settings.php:637
msgid "%s list"
msgstr "Liste %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:533
msgid "Posts list navigation"
msgstr "Navigation dans la liste des publications"

#: includes/admin/views/acf-post-type/advanced-settings.php:532
msgid ""
"Used by screen readers for the filter list pagination on the post type list "
"screen."
msgstr ""
"Utilisé par les lecteurs d’écran pour la pagination de la liste de filtres "
"sur l’écran de liste des types de publication."

#: includes/admin/views/acf-post-type/advanced-settings.php:531
#: includes/admin/views/acf-taxonomy/advanced-settings.php:620
msgid "Items List Navigation"
msgstr "Liste des éléments de navigation"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:528
#: includes/admin/views/acf-taxonomy/advanced-settings.php:617
msgid "%s list navigation"
msgstr "Navigation dans la liste %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:512
msgid "Filter posts by date"
msgstr "Filtrer les publications par date"

#: includes/admin/views/acf-post-type/advanced-settings.php:511
msgid ""
"Used by screen readers for the filter by date heading on the post type list "
"screen."
msgstr ""
"Utilisé par les lecteurs d’écran pour l’en-tête de filtre par date sur "
"l’écran de liste des types de publication."

#: includes/admin/views/acf-post-type/advanced-settings.php:510
msgid "Filter Items By Date"
msgstr "Filtrer les éléments par date"

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:506
msgid "Filter %s by date"
msgstr "Filtrer %s par date"

#: includes/admin/views/acf-post-type/advanced-settings.php:491
msgid "Filter posts list"
msgstr "Filtrer la liste des publications"

#: includes/admin/views/acf-post-type/advanced-settings.php:490
msgid ""
"Used by screen readers for the filter links heading on the post type list "
"screen."
msgstr ""
"Utilisé par les lecteurs d’écran pour l’en-tête des liens de filtre sur "
"l’écran de liste des types de publication."

#: includes/admin/views/acf-post-type/advanced-settings.php:489
msgid "Filter Items List"
msgstr "Filtrer la liste des éléments"

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:485
msgid "Filter %s list"
msgstr "Filtrer la liste %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:469
msgid "In the media modal showing all media uploaded to this item."
msgstr ""
"Dans le mode modal multimédia affichant tous les médias téléchargés sur cet "
"élément."

#: includes/admin/views/acf-post-type/advanced-settings.php:468
msgid "Uploaded To This Item"
msgstr "Téléversé dans l’élément"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:464
msgid "Uploaded to this %s"
msgstr "Téléversé sur ce %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:449
msgid "Insert into post"
msgstr "Insérer dans la publication"

#: includes/admin/views/acf-post-type/advanced-settings.php:448
msgid "As the button label when adding media to content."
msgstr "En tant que libellé du bouton lors de l’ajout de média au contenu."

#: includes/admin/views/acf-post-type/advanced-settings.php:447
msgid "Insert Into Media Button"
msgstr "Bouton « Insérer dans le média »"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:443
msgid "Insert into %s"
msgstr "Insérer dans %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:428
msgid "Use as featured image"
msgstr "Utiliser comme image mise en avant"

#: includes/admin/views/acf-post-type/advanced-settings.php:427
msgid ""
"As the button label for selecting to use an image as the featured image."
msgstr ""
"Comme étiquette de bouton pour choisir d’utiliser une image comme image en "
"vedette."

#: includes/admin/views/acf-post-type/advanced-settings.php:426
msgid "Use Featured Image"
msgstr "Utiliser l’image mise en avant"

#: includes/admin/views/acf-post-type/advanced-settings.php:413
msgid "Remove featured image"
msgstr "Retirer l’image mise en avant"

#: includes/admin/views/acf-post-type/advanced-settings.php:412
msgid "As the button label when removing the featured image."
msgstr ""
"Comme étiquette de bouton lors de la suppression de l’image en vedette."

#: includes/admin/views/acf-post-type/advanced-settings.php:411
msgid "Remove Featured Image"
msgstr "Retirer l’image mise en avant"

#: includes/admin/views/acf-post-type/advanced-settings.php:398
msgid "Set featured image"
msgstr "Définir l’image mise en avant"

#: includes/admin/views/acf-post-type/advanced-settings.php:397
msgid "As the button label when setting the featured image."
msgstr "Comme étiquette de bouton lors de la définition de l’image en vedette."

#: includes/admin/views/acf-post-type/advanced-settings.php:396
msgid "Set Featured Image"
msgstr "Définir l’image mise en avant"

#: includes/admin/views/acf-post-type/advanced-settings.php:383
msgid "Featured image"
msgstr "Image mise en avant"

#: includes/admin/views/acf-post-type/advanced-settings.php:382
msgid "In the editor used for the title of the featured image meta box."
msgstr ""
"Dans l’éditeur utilisé pour le titre de la méta-boîte d’image en vedette."

#: includes/admin/views/acf-post-type/advanced-settings.php:381
msgid "Featured Image Meta Box"
msgstr "Boîte méta de l’image mise en avant"

#: includes/admin/views/acf-post-type/advanced-settings.php:368
msgid "Post Attributes"
msgstr "Attributs de la publication"

#: includes/admin/views/acf-post-type/advanced-settings.php:367
msgid "In the editor used for the title of the post attributes meta box."
msgstr ""
"Dans l’éditeur utilisé pour le titre de la boîte méta des attributs de "
"publication."

#: includes/admin/views/acf-post-type/advanced-settings.php:366
msgid "Attributes Meta Box"
msgstr "Boîte méta Attributs"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:363
msgid "%s Attributes"
msgstr "Attributs des %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:348
msgid "Post Archives"
msgstr "Archives de publication"

#: includes/admin/views/acf-post-type/advanced-settings.php:347
msgid ""
"Adds 'Post Type Archive' items with this label to the list of posts shown "
"when adding items to an existing menu in a CPT with archives enabled. Only "
"appears when editing menus in 'Live Preview' mode and a custom archive slug "
"has been provided."
msgstr ""
"Ajoute les éléments « Archive de types de publication » avec ce libellé à la "
"liste des publications affichées lors de l’ajout d’éléments à un menu "
"existant dans un CPT avec les archives activées. N’apparaît que lors de "
"l’édition des menus en mode « Aperçu en direct » et qu’un identifiant d”URL "
"d’archive personnalisé a été fourni."

#: includes/admin/views/acf-post-type/advanced-settings.php:346
msgid "Archives Nav Menu"
msgstr "Menu de navigation des archives"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:343
msgid "%s Archives"
msgstr "Archives des %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:328
msgid "No posts found in Trash"
msgstr "Aucune publication trouvée dans la corbeille"

#: includes/admin/views/acf-post-type/advanced-settings.php:327
msgid ""
"At the top of the post type list screen when there are no posts in the trash."
msgstr ""
"En haut de l’écran de liste des types de publication lorsqu’il n’y a pas de "
"publications dans la corbeille."

#: includes/admin/views/acf-post-type/advanced-settings.php:326
msgid "No Items Found in Trash"
msgstr "Aucun élément trouvé dans la corbeille"

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:322
msgid "No %s found in Trash"
msgstr "Aucun %s trouvé dans la corbeille"

#: includes/admin/views/acf-post-type/advanced-settings.php:307
msgid "No posts found"
msgstr "Aucune publication trouvée"

#: includes/admin/views/acf-post-type/advanced-settings.php:306
msgid ""
"At the top of the post type list screen when there are no posts to display."
msgstr ""
"En haut de l’écran de liste des types de publication lorsqu’il n’y a pas de "
"publications à afficher."

#: includes/admin/views/acf-post-type/advanced-settings.php:305
msgid "No Items Found"
msgstr "Aucun élément trouvé"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:301
#: includes/admin/views/acf-taxonomy/advanced-settings.php:480
msgid "No %s found"
msgstr "Aucun %s trouvé"

#: includes/admin/views/acf-post-type/advanced-settings.php:286
msgid "Search Posts"
msgstr "Rechercher des publications"

#: includes/admin/views/acf-post-type/advanced-settings.php:285
msgid "At the top of the items screen when searching for an item."
msgstr "En haut de l’écran des éléments lors de la recherche d’un élément."

#: includes/admin/views/acf-post-type/advanced-settings.php:284
#: includes/admin/views/acf-taxonomy/advanced-settings.php:345
msgid "Search Items"
msgstr "Rechercher des éléments"

#. translators: %s Singular form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:281
#: includes/admin/views/acf-taxonomy/advanced-settings.php:342
msgid "Search %s"
msgstr "Rechercher %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:266
msgid "Parent Page:"
msgstr "Page parente :"

#: includes/admin/views/acf-post-type/advanced-settings.php:265
msgid "For hierarchical types in the post type list screen."
msgstr ""
"Pour les types hiérarchiques dans l’écran de liste des types de publication."

#: includes/admin/views/acf-post-type/advanced-settings.php:264
msgid "Parent Item Prefix"
msgstr "Préfixe de l’élément parent"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:261
#: includes/admin/views/acf-taxonomy/advanced-settings.php:318
msgid "Parent %s:"
msgstr "%s parent :"

#: includes/admin/views/acf-post-type/advanced-settings.php:246
msgid "New Post"
msgstr "Nouvelle publication"

#: includes/admin/views/acf-post-type/advanced-settings.php:244
msgid "New Item"
msgstr "Nouvel élément"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:241
msgid "New %s"
msgstr "Nouveau %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:206
#: includes/admin/views/acf-post-type/advanced-settings.php:226
msgid "Add New Post"
msgstr "Ajouter une nouvelle publication"

#: includes/admin/views/acf-post-type/advanced-settings.php:205
msgid "At the top of the editor screen when adding a new item."
msgstr "En haut de l’écran de l’éditeur lors de l’ajout d’un nouvel élément."

#: includes/admin/views/acf-post-type/advanced-settings.php:204
#: includes/admin/views/acf-taxonomy/advanced-settings.php:256
msgid "Add New Item"
msgstr "Ajouter un nouvel élément"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:201
#: includes/admin/views/acf-post-type/advanced-settings.php:221
#: includes/admin/views/acf-taxonomy/advanced-settings.php:253
msgid "Add New %s"
msgstr "Ajouter %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:186
msgid "View Posts"
msgstr "Voir les publications"

#: includes/admin/views/acf-post-type/advanced-settings.php:185
msgid ""
"Appears in the admin bar in the 'All Posts' view, provided the post type "
"supports archives and the home page is not an archive of that post type."
msgstr ""
"Apparaît dans la barre d’administration dans la vue « Tous les messages », à "
"condition que le type de publication prenne en charge les archives et que la "
"page d’accueil ne soit pas une archive de ce type de publication."

#: includes/admin/views/acf-post-type/advanced-settings.php:184
msgid "View Items"
msgstr "Voir les éléments"

#: includes/admin/views/acf-post-type/advanced-settings.php:166
msgid "View Post"
msgstr "Voir la publication"

#: includes/admin/views/acf-post-type/advanced-settings.php:165
msgid "In the admin bar to view item when editing it."
msgstr ""
"Dans la barre d’administration pour afficher l’élément lors de sa "
"modification."

#: includes/admin/views/acf-post-type/advanced-settings.php:164
#: includes/admin/views/acf-taxonomy/advanced-settings.php:216
msgid "View Item"
msgstr "Voir l’élément"

#. translators: %s Singular form of post type name
#. translators: %s Plural form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:161
#: includes/admin/views/acf-post-type/advanced-settings.php:181
#: includes/admin/views/acf-taxonomy/advanced-settings.php:213
msgid "View %s"
msgstr "Voir %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:146
msgid "Edit Post"
msgstr "Modifier la publication"

#: includes/admin/views/acf-post-type/advanced-settings.php:145
msgid "At the top of the editor screen when editing an item."
msgstr "En haut de l’écran de l’éditeur lors de la modification d’un élément."

#: includes/admin/views/acf-post-type/advanced-settings.php:144
#: includes/admin/views/acf-taxonomy/advanced-settings.php:196
msgid "Edit Item"
msgstr "Modifier l’élément"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:141
#: includes/admin/views/acf-taxonomy/advanced-settings.php:193
msgid "Edit %s"
msgstr "Modifier %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:126
msgid "All Posts"
msgstr "Toutes les publications"

#: includes/admin/views/acf-post-type/advanced-settings.php:125
#: includes/admin/views/acf-post-type/advanced-settings.php:225
#: includes/admin/views/acf-post-type/advanced-settings.php:245
msgid "In the post type submenu in the admin dashboard."
msgstr ""
"Dans le sous-menu de type de publication du tableau de bord d’administration."

#: includes/admin/views/acf-post-type/advanced-settings.php:124
#: includes/admin/views/acf-taxonomy/advanced-settings.php:176
msgid "All Items"
msgstr "Tous les éléments"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:121
#: includes/admin/views/acf-taxonomy/advanced-settings.php:173
msgid "All %s"
msgstr "Tous les %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:105
msgid "Admin menu name for the post type."
msgstr "Nom du menu d’administration pour le type de publication."

#: includes/admin/views/acf-post-type/advanced-settings.php:104
msgid "Menu Name"
msgstr "Nom du menu"

#: includes/admin/views/acf-post-type/advanced-settings.php:90
#: includes/admin/views/acf-taxonomy/advanced-settings.php:142
msgid "Regenerate all labels using the Singular and Plural labels"
msgstr ""
"Régénérer tous les libellés avec des libellés « Singulier » et « Pluriel »"

#: includes/admin/views/acf-post-type/advanced-settings.php:88
#: includes/admin/views/acf-taxonomy/advanced-settings.php:140
msgid "Regenerate"
msgstr "Régénérer"

#: includes/admin/views/acf-post-type/advanced-settings.php:79
msgid "Active post types are enabled and registered with WordPress."
msgstr ""
"Les types de publication actifs sont activés et enregistrés avec WordPress."

#: includes/admin/views/acf-post-type/advanced-settings.php:63
msgid "A descriptive summary of the post type."
msgstr "Un résumé descriptif du type de publication."

#: includes/admin/views/acf-post-type/advanced-settings.php:48
msgid "Add Custom"
msgstr "Ajouter une personalisation"

#: includes/admin/views/acf-post-type/advanced-settings.php:42
msgid "Enable various features in the content editor."
msgstr "Activez diverses fonctionnalités dans l’éditeur de contenu."

#: includes/admin/views/acf-post-type/advanced-settings.php:31
msgid "Post Formats"
msgstr "Formats des publications"

#: includes/admin/views/acf-post-type/advanced-settings.php:25
msgid "Editor"
msgstr "Éditeur"

#: includes/admin/views/acf-post-type/advanced-settings.php:24
msgid "Trackbacks"
msgstr "Rétroliens"

#: includes/admin/views/acf-post-type/basic-settings.php:87
msgid "Select existing taxonomies to classify items of the post type."
msgstr ""
"Sélectionnez les taxonomies existantes pour classer les éléments du type de "
"publication."

#: includes/admin/views/acf-field-group/field.php:158
msgid "Browse Fields"
msgstr "Parcourir les champs"

#: includes/admin/tools/class-acf-admin-tool-import.php:287
msgid "Nothing to import"
msgstr "Rien à importer"

#: includes/admin/tools/class-acf-admin-tool-import.php:282
msgid ". The Custom Post Type UI plugin can be deactivated."
msgstr ". L’extension Custom Post Type UI peut être désactivée."

#. translators: %d - number of items imported from CPTUI
#: includes/admin/tools/class-acf-admin-tool-import.php:273
msgid "Imported %d item from Custom Post Type UI -"
msgid_plural "Imported %d items from Custom Post Type UI -"
msgstr[0] ""
"Élément %d importé à partir de l’interface utilisateur de type de "
"publication personnalisée -"
msgstr[1] ""
"Éléments %d importés à partir de l’interface utilisateur de type de "
"publication personnalisée -"

#: includes/admin/tools/class-acf-admin-tool-import.php:257
msgid "Failed to import taxonomies."
msgstr "Impossible d’importer des taxonomies."

#: includes/admin/tools/class-acf-admin-tool-import.php:239
msgid "Failed to import post types."
msgstr "Impossible d’importer les types de publication."

#: includes/admin/tools/class-acf-admin-tool-import.php:228
msgid "Nothing from Custom Post Type UI plugin selected for import."
msgstr ""
"Rien depuis l’extension sélectionné Custom Post Type UI pour l’importation."

#: includes/admin/tools/class-acf-admin-tool-import.php:204
msgid "Imported 1 item"
msgid_plural "Imported %s items"
msgstr[0] "Importé 1 élément"
msgstr[1] "Importé %s éléments"

#: includes/admin/tools/class-acf-admin-tool-import.php:119
msgid ""
"Importing a Post Type or Taxonomy with the same key as one that already "
"exists will overwrite the settings for the existing Post Type or Taxonomy "
"with those of the import."
msgstr ""
"L’importation d’un type de publication ou d’une taxonomie avec la même clé "
"qu’une clé qui existe déjà remplacera les paramètres du type de publication "
"ou de la taxonomie existants par ceux de l’importation."

#: includes/admin/tools/class-acf-admin-tool-import.php:108
#: includes/admin/tools/class-acf-admin-tool-import.php:124
msgid "Import from Custom Post Type UI"
msgstr "Importer à partir de Custom Post Type UI"

#: includes/admin/tools/class-acf-admin-tool-export.php:354
msgid ""
"The following code can be used to register a local version of the selected "
"items. Storing field groups, post types, or taxonomies locally can provide "
"many benefits such as faster load times, version control & dynamic fields/"
"settings. Simply copy and paste the following code to your theme's "
"functions.php file or include it within an external file, then deactivate or "
"delete the items from the ACF admin."
msgstr ""
"Le code suivant peut être utilisé pour enregistrer une version locale des "
"éléments sélectionnés. Le stockage local de groupes de champs, de types de "
"publications ou de taxonomies peut offrir de nombreux avantages, tels que "
"des temps de chargement plus rapides, un contrôle de version et des champs/"
"paramètres dynamiques. Copiez et collez simplement le code suivant dans le "
"fichier des fonctions de votre thème.php ou incluez-le dans un fichier "
"externe, puis désactivez ou supprimez les éléments de l’administrateur ACF."

#: includes/admin/tools/class-acf-admin-tool-export.php:353
msgid "Export - Generate PHP"
msgstr "Exportation - Générer PHP"

#: includes/admin/tools/class-acf-admin-tool-export.php:330
msgid "Export"
msgstr "Exporter"

#: includes/admin/tools/class-acf-admin-tool-export.php:264
msgid "Select Taxonomies"
msgstr "Sélectionner les taxonomies"

#: includes/admin/tools/class-acf-admin-tool-export.php:239
msgid "Select Post Types"
msgstr "Sélectionner les types de publication"

#: includes/admin/tools/class-acf-admin-tool-export.php:155
msgid "Exported 1 item."
msgid_plural "Exported %s items."
msgstr[0] "Exporté 1 élément."
msgstr[1] "Exporté %s éléments."

#: includes/admin/post-types/admin-taxonomy.php:127
msgid "Category"
msgstr "Catégorie"

#: includes/admin/post-types/admin-taxonomy.php:125
msgid "Tag"
msgstr "Étiquette"

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:82
msgid "%s taxonomy created"
msgstr "%s taxonomie créée"

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:76
msgid "%s taxonomy updated"
msgstr "Taxonomie %s mise à jour"

#: includes/admin/post-types/admin-taxonomy.php:56
msgid "Taxonomy draft updated."
msgstr "Brouillon de taxonomie mis à jour."

#: includes/admin/post-types/admin-taxonomy.php:55
msgid "Taxonomy scheduled for."
msgstr "Taxonomie planifiée pour."

#: includes/admin/post-types/admin-taxonomy.php:54
msgid "Taxonomy submitted."
msgstr "Taxonomie envoyée."

#: includes/admin/post-types/admin-taxonomy.php:53
msgid "Taxonomy saved."
msgstr "Taxonomie enregistrée."

#: includes/admin/post-types/admin-taxonomy.php:49
msgid "Taxonomy deleted."
msgstr "Taxonomie supprimée."

#: includes/admin/post-types/admin-taxonomy.php:48
msgid "Taxonomy updated."
msgstr "Taxonomie mise à jour."

#: includes/admin/post-types/admin-taxonomies.php:351
#: includes/admin/post-types/admin-taxonomy.php:153
msgid ""
"This taxonomy could not be registered because its key is in use by another "
"taxonomy registered by another plugin or theme."
msgstr ""
"Cette taxonomie n’a pas pu être enregistrée car sa clé est utilisée par une "
"autre taxonomie enregistrée par une autre extension ou un thème."

#. translators: %s number of taxonomies synchronized
#: includes/admin/post-types/admin-taxonomies.php:333
msgid "Taxonomy synchronized."
msgid_plural "%s taxonomies synchronized."
msgstr[0] "Taxonomie synchronisée."
msgstr[1] "%s taxonomies synchronisées."

#. translators: %s number of taxonomies duplicated
#: includes/admin/post-types/admin-taxonomies.php:326
msgid "Taxonomy duplicated."
msgid_plural "%s taxonomies duplicated."
msgstr[0] "Taxonomie dupliquée."
msgstr[1] "%s taxonomies dupliquées."

#. translators: %s number of taxonomies deactivated
#: includes/admin/post-types/admin-taxonomies.php:319
msgid "Taxonomy deactivated."
msgid_plural "%s taxonomies deactivated."
msgstr[0] "Taxonomie désactivée."
msgstr[1] "%s taxonomies désactivées."

#. translators: %s number of taxonomies activated
#: includes/admin/post-types/admin-taxonomies.php:312
msgid "Taxonomy activated."
msgid_plural "%s taxonomies activated."
msgstr[0] "Taxonomie activée."
msgstr[1] "%s taxonomies activées."

#: includes/admin/post-types/admin-taxonomies.php:113
msgid "Terms"
msgstr "Termes"

#. translators: %s number of post types synchronized
#: includes/admin/post-types/admin-post-types.php:327
msgid "Post type synchronized."
msgid_plural "%s post types synchronized."
msgstr[0] "Type de publication synchronisé."
msgstr[1] "%s types de publication synchronisés."

#. translators: %s number of post types duplicated
#: includes/admin/post-types/admin-post-types.php:320
msgid "Post type duplicated."
msgid_plural "%s post types duplicated."
msgstr[0] "Type de publication dupliqué."
msgstr[1] "%s types de publication dupliqués."

#. translators: %s number of post types deactivated
#: includes/admin/post-types/admin-post-types.php:313
msgid "Post type deactivated."
msgid_plural "%s post types deactivated."
msgstr[0] "Type de publication désactivé."
msgstr[1] "%s types de publication désactivés."

#. translators: %s number of post types activated
#: includes/admin/post-types/admin-post-types.php:306
msgid "Post type activated."
msgid_plural "%s post types activated."
msgstr[0] "Type de publication activé."
msgstr[1] "%s types de publication activés."

#: includes/admin/post-types/admin-post-types.php:87
#: includes/admin/post-types/admin-taxonomies.php:111
#: includes/admin/tools/class-acf-admin-tool-import.php:79
#: includes/admin/views/acf-taxonomy/basic-settings.php:82
#: includes/post-types/class-acf-post-type.php:91
msgid "Post Types"
msgstr "Types de publication"

#: includes/admin/post-types/admin-post-type.php:158
#: includes/admin/post-types/admin-taxonomy.php:160
msgid "Advanced Settings"
msgstr "Réglages avancés"

#: includes/admin/post-types/admin-post-type.php:157
#: includes/admin/post-types/admin-taxonomy.php:159
msgid "Basic Settings"
msgstr "Réglages de base"

#: includes/admin/post-types/admin-post-type.php:151
#: includes/admin/post-types/admin-post-types.php:345
msgid ""
"This post type could not be registered because its key is in use by another "
"post type registered by another plugin or theme."
msgstr ""
"Ce type de publication n’a pas pu être enregistré car sa clé est utilisée "
"par un autre type de publication enregistré par une autre extensions ou un "
"thème."

#: includes/admin/post-types/admin-post-type.php:126
msgid "Pages"
msgstr "Pages"

#: includes/admin/admin-internal-post-type.php:347
msgid "Link Existing Field Groups"
msgstr "Lier des groupes de champs existants"

#. translators: %s post type name
#: includes/admin/post-types/admin-post-type.php:80
msgid "%s post type created"
msgstr "%s type de publication créé"

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:78
msgid "Add fields to %s"
msgstr "Ajouter des champs à %s"

#. translators: %s post type name
#: includes/admin/post-types/admin-post-type.php:76
msgid "%s post type updated"
msgstr "Type de publication %s mis à jour"

#: includes/admin/post-types/admin-post-type.php:56
msgid "Post type draft updated."
msgstr "Le brouillon du type de publication a été mis à jour."

#: includes/admin/post-types/admin-post-type.php:55
msgid "Post type scheduled for."
msgstr "Type de publication planifié pour."

#: includes/admin/post-types/admin-post-type.php:54
msgid "Post type submitted."
msgstr "Type de publication envoyé."

#: includes/admin/post-types/admin-post-type.php:53
msgid "Post type saved."
msgstr "Type de publication enregistré."

#: includes/admin/post-types/admin-post-type.php:50
msgid "Post type updated."
msgstr "Type de publication mis à jour."

#: includes/admin/post-types/admin-post-type.php:49
msgid "Post type deleted."
msgstr "Type de publication supprimé."

#: includes/admin/post-types/admin-field-group.php:146
msgid "Type to search..."
msgstr "Type à rechercher…"

#: includes/admin/post-types/admin-field-group.php:101
msgid "PRO Only"
msgstr "Uniquement sur PRO"

#: includes/admin/post-types/admin-field-group.php:93
msgid "Field groups linked successfully."
msgstr "Les groupes de champs ont bien été liés."

#. translators: %s - URL to ACF tools page.
#: includes/admin/admin.php:199
msgid ""
"Import Post Types and Taxonomies registered with Custom Post Type UI and "
"manage them with ACF. <a href=\"%s\">Get Started</a>."
msgstr ""
"Importez les types de publication et les taxonomies enregistrés avec "
"l’interface utilisateur de type de publication personnalisée et gérez-les "
"avec ACF. <a href=\"%s\">Lancez-vous</a>."

#: includes/admin/admin.php:46 includes/admin/admin.php:361
#: src/Site_Health/Site_Health.php:254
msgid "ACF"
msgstr "ACF"

#: includes/admin/admin-internal-post-type.php:314
msgid "taxonomy"
msgstr "taxonomie"

#: includes/admin/admin-internal-post-type.php:314
msgid "post type"
msgstr "type de publication"

#: includes/admin/admin-internal-post-type.php:338
msgid "Done"
msgstr "Terminé"

#: includes/admin/admin-internal-post-type.php:324
msgid "Field Group(s)"
msgstr "Groupe(s) de champs"

#: includes/admin/admin-internal-post-type.php:323
msgid "Select one or many field groups..."
msgstr "Sélectionner un ou plusieurs groupes de champs…"

#: includes/admin/admin-internal-post-type.php:322
msgid "Please select the field groups to link."
msgstr "Veuillez choisir les groupes de champs à lier."

#: includes/admin/admin-internal-post-type.php:280
msgid "Field group linked successfully."
msgid_plural "Field groups linked successfully."
msgstr[0] "Groupe de champs bien lié."
msgstr[1] "Groupes de champs bien liés."

#: includes/admin/admin-internal-post-type-list.php:277
#: includes/admin/post-types/admin-post-types.php:346
#: includes/admin/post-types/admin-taxonomies.php:352
msgctxt "post status"
msgid "Registration Failed"
msgstr "Echec de l’enregistrement"

#: includes/admin/admin-internal-post-type-list.php:276
msgid ""
"This item could not be registered because its key is in use by another item "
"registered by another plugin or theme."
msgstr ""
"Cet élément n’a pas pu être enregistré car sa clé est utilisée par un autre "
"élément enregistré par une autre extension ou un thème."

#: includes/acf-internal-post-type-functions.php:509
#: includes/acf-internal-post-type-functions.php:538
msgid "REST API"
msgstr "API REST"

#: includes/acf-internal-post-type-functions.php:508
#: includes/acf-internal-post-type-functions.php:537
#: includes/acf-internal-post-type-functions.php:564
msgid "Permissions"
msgstr "Droits"

#: includes/acf-internal-post-type-functions.php:507
#: includes/acf-internal-post-type-functions.php:536
msgid "URLs"
msgstr "URL"

#: includes/acf-internal-post-type-functions.php:506
#: includes/acf-internal-post-type-functions.php:535
#: includes/acf-internal-post-type-functions.php:562
msgid "Visibility"
msgstr "Visibilité"

#: includes/acf-internal-post-type-functions.php:505
#: includes/acf-internal-post-type-functions.php:534
#: includes/acf-internal-post-type-functions.php:563
msgid "Labels"
msgstr "Libellés"

#: includes/admin/post-types/admin-field-group.php:279
msgid "Field Settings Tabs"
msgstr "Onglets des réglages du champ"

#. Author URI of the plugin
#: acf.php
msgid ""
"https://wpengine.com/?"
"utm_source=wordpress.org&utm_medium=referral&utm_campaign=plugin_directory&utm_content=advanced_custom_fields"
msgstr ""
"https://wpengine.com/?"
"utm_source=wordpress.org&utm_medium=referral&utm_campaign=plugin_directory&utm_content=advanced_custom_fields"

#: includes/api/api-template.php:1027
msgid "[ACF shortcode value disabled for preview]"
msgstr "[Valeur du code court ACF désactivée pour l’aperçu]"

#: includes/admin/admin-internal-post-type.php:290
#: includes/admin/post-types/admin-field-group.php:572
msgid "Close Modal"
msgstr "Fermer la modale"

#: includes/admin/post-types/admin-field-group.php:92
msgid "Field moved to other group"
msgstr "Champ déplacé vers un autre groupe"

#: includes/admin/post-types/admin-field-group.php:91
msgid "Close modal"
msgstr "Fermer la modale"

#: includes/fields/class-acf-field-tab.php:122
msgid "Start a new group of tabs at this tab."
msgstr "Commencez un nouveau groupe d’onglets dans cet onglet."

#: includes/fields/class-acf-field-tab.php:121
msgid "New Tab Group"
msgstr "Nouveau groupe d’onglets"

#: includes/fields/class-acf-field-select.php:421
#: includes/fields/class-acf-field-true_false.php:188
msgid "Use a stylized checkbox using select2"
msgstr "Utiliser une case à cocher stylisée avec select2"

#: includes/fields/class-acf-field-radio.php:250
msgid "Save Other Choice"
msgstr "Enregistrer un autre choix"

#: includes/fields/class-acf-field-radio.php:239
msgid "Allow Other Choice"
msgstr "Autoriser un autre choix"

#: includes/fields/class-acf-field-checkbox.php:420
msgid "Add Toggle All"
msgstr "Ajouter « Tout basculer »"

#: includes/fields/class-acf-field-checkbox.php:379
msgid "Save Custom Values"
msgstr "Enregistrer les valeurs personnalisées"

#: includes/fields/class-acf-field-checkbox.php:368
msgid "Allow Custom Values"
msgstr "Autoriser les valeurs personnalisées"

#: includes/fields/class-acf-field-checkbox.php:134
msgid "Checkbox custom values cannot be empty. Uncheck any empty values."
msgstr ""
"Les valeurs personnalisées des cases à cocher ne peuvent pas être vides. "
"Décochez toutes les valeurs vides."

#: includes/admin/views/global/navigation.php:256
msgid "Updates"
msgstr "Mises à jour"

#: includes/admin/views/global/navigation.php:180
#: includes/admin/views/global/navigation.php:184
msgid "Advanced Custom Fields logo"
msgstr "Logo Advanced Custom Fields"

#: includes/admin/views/global/form-top.php:92
msgid "Save Changes"
msgstr "Enregistrer les modifications"

#: includes/admin/views/global/form-top.php:79
msgid "Field Group Title"
msgstr "Titre du groupe de champs"

#: includes/admin/views/acf-post-type/advanced-settings.php:709
#: includes/admin/views/global/form-top.php:3
msgid "Add title"
msgstr "Ajouter un titre"

#. translators: %s url to getting started guide
#: includes/admin/views/acf-field-group/list-empty.php:30
#: includes/admin/views/acf-post-type/list-empty.php:20
#: includes/admin/views/acf-taxonomy/list-empty.php:21
#: includes/admin/views/options-page-preview.php:13
msgid ""
"New to ACF? Take a look at our <a href=\"%s\" target=\"_blank\">getting "
"started guide</a>."
msgstr ""
"Nouveau sur ACF ? Jetez un œil à notre <a href=\"%s\" "
"target=\"_blank\">guide des premiers pas</a>."

#: includes/admin/views/acf-field-group/list-empty.php:24
msgid "Add Field Group"
msgstr "Ajouter un groupe de champs"

#. translators: %s url to creating a field group page
#: includes/admin/views/acf-field-group/list-empty.php:18
msgid ""
"ACF uses <a href=\"%s\" target=\"_blank\">field groups</a> to group custom "
"fields together, and then attach those fields to edit screens."
msgstr ""
"ACF utilise <a href=\"%s\" target=\"_blank\">des groupes de champs</a> pour "
"grouper des champs personnalisés et les associer aux écrans de modification."

#: includes/admin/views/acf-field-group/list-empty.php:12
msgid "Add Your First Field Group"
msgstr "Ajouter votre premier groupe de champs"

#: includes/admin/admin-options-pages-preview.php:28
#: includes/admin/views/acf-field-group/pro-features.php:58
#: includes/admin/views/global/navigation.php:86
#: includes/admin/views/global/navigation.php:258
msgid "Options Pages"
msgstr "Pages d’options"

#: includes/admin/views/acf-field-group/pro-features.php:54
msgid "ACF Blocks"
msgstr "Blocs ACF"

#: includes/admin/views/acf-field-group/pro-features.php:62
msgid "Gallery Field"
msgstr "Champ galerie"

#: includes/admin/views/acf-field-group/pro-features.php:42
msgid "Flexible Content Field"
msgstr "Champ de contenu flexible"

#: includes/admin/views/acf-field-group/pro-features.php:46
msgid "Repeater Field"
msgstr "Champ répéteur"

#: includes/admin/views/global/navigation.php:218
msgid "Unlock Extra Features with ACF PRO"
msgstr "Débloquer des fonctionnalités supplémentaires avec ACF PRO"

#: includes/admin/views/acf-field-group/options.php:267
msgid "Delete Field Group"
msgstr "Supprimer le groupe de champ"

#. translators: 1: Post creation date 2: Post creation time
#: includes/admin/views/acf-field-group/options.php:261
msgid "Created on %1$s at %2$s"
msgstr "Créé le %1$s à %2$s"

#: includes/acf-field-group-functions.php:497
msgid "Group Settings"
msgstr "Réglages du groupe"

#: includes/acf-field-group-functions.php:495
msgid "Location Rules"
msgstr "Règles de localisation"

#. translators: %s url to field types list
#: includes/admin/views/acf-field-group/fields.php:73
msgid ""
"Choose from over 30 field types. <a href=\"%s\" target=\"_blank\">Learn "
"more</a>."
msgstr ""
"Choisissez parmi plus de 30 types de champs. <a href=\"%s\" "
"target=\"_blank\">En savoir plus</a>."

#: includes/admin/views/acf-field-group/fields.php:65
msgid ""
"Get started creating new custom fields for your posts, pages, custom post "
"types and other WordPress content."
msgstr ""
"Commencez à créer de nouveaux champs personnalisés pour vos articles, pages, "
"types de publication personnalisés et autres contenus WordPress."

#: includes/admin/views/acf-field-group/fields.php:64
msgid "Add Your First Field"
msgstr "Ajouter votre premier champ"

#. translators: A symbol (or text, if not available in your locale) meaning
#. "Order Number", in terms of positional placement.
#: includes/admin/views/acf-field-group/fields.php:43
msgid "#"
msgstr "N°"

#: includes/admin/views/acf-field-group/fields.php:33
#: includes/admin/views/acf-field-group/fields.php:67
#: includes/admin/views/acf-field-group/fields.php:101
#: includes/admin/views/global/form-top.php:88
msgid "Add Field"
msgstr "Ajouter un champ"

#: includes/acf-field-group-functions.php:496 includes/fields.php:384
msgid "Presentation"
msgstr "Présentation"

#: includes/fields.php:383
msgid "Validation"
msgstr "Validation"

#: includes/acf-internal-post-type-functions.php:504
#: includes/acf-internal-post-type-functions.php:533 includes/fields.php:382
msgid "General"
msgstr "Général"

#: includes/admin/tools/class-acf-admin-tool-import.php:67
msgid "Import JSON"
msgstr "Importer un JSON"

#: includes/admin/tools/class-acf-admin-tool-export.php:338
msgid "Export As JSON"
msgstr "Exporter en tant que JSON"

#. translators: %s number of field groups deactivated
#: includes/admin/post-types/admin-field-groups.php:360
msgid "Field group deactivated."
msgid_plural "%s field groups deactivated."
msgstr[0] "Groupe de champs désactivé."
msgstr[1] "%s groupes de champs désactivés."

#. translators: %s number of field groups activated
#: includes/admin/post-types/admin-field-groups.php:353
msgid "Field group activated."
msgid_plural "%s field groups activated."
msgstr[0] "Groupe de champs activé."
msgstr[1] "%s groupes de champs activés."

#: includes/admin/admin-internal-post-type-list.php:470
#: includes/admin/admin-internal-post-type-list.php:496
msgid "Deactivate"
msgstr "Désactiver"

#: includes/admin/admin-internal-post-type-list.php:470
msgid "Deactivate this item"
msgstr "Désactiver cet élément"

#: includes/admin/admin-internal-post-type-list.php:466
#: includes/admin/admin-internal-post-type-list.php:495
msgid "Activate"
msgstr "Activer"

#: includes/admin/admin-internal-post-type-list.php:466
msgid "Activate this item"
msgstr "Activer cet élément"

#: includes/admin/post-types/admin-field-group.php:88
msgid "Move field group to trash?"
msgstr "Déplacer le groupe de champs vers la corbeille ?"

#: acf.php:520 includes/admin/admin-internal-post-type-list.php:264
#: includes/admin/post-types/admin-field-group.php:304
#: includes/admin/post-types/admin-post-type.php:282
#: includes/admin/post-types/admin-taxonomy.php:284
msgctxt "post status"
msgid "Inactive"
msgstr "Inactif"

#. Author of the plugin
#: acf.php includes/admin/views/global/navigation.php:240
msgid "WP Engine"
msgstr "WP Engine"

#: acf.php:578
msgid ""
"Advanced Custom Fields and Advanced Custom Fields PRO should not be active "
"at the same time. We've automatically deactivated Advanced Custom Fields PRO."
msgstr ""
"Advanced Custom Fields et Advanced Custom Fields Pro ne doivent pas être "
"actives en même temps. Nous avons automatiquement désactivé Advanced Custom "
"Fields Pro."

#: acf.php:576
msgid ""
"Advanced Custom Fields and Advanced Custom Fields PRO should not be active "
"at the same time. We've automatically deactivated Advanced Custom Fields."
msgstr ""
"Advanced Custom Fields et Advanced Custom Fields Pro ne doivent pas être "
"actives en même temps. Nous avons automatiquement désactivé Advanced Custom "
"Fields."

#: includes/fields/class-acf-field-user.php:578
msgid "%1$s must have a user with the %2$s role."
msgid_plural "%1$s must have a user with one of the following roles: %2$s"
msgstr[0] "%1$s doit avoir un compte avec le rôle %2$s."
msgstr[1] "%1$s doit avoir un compte avec l’un des rôles suivants : %2$s"

#: includes/fields/class-acf-field-user.php:569
msgid "%1$s must have a valid user ID."
msgstr "%1$s doit avoir un ID de compte valide."

#: includes/fields/class-acf-field-user.php:408
msgid "Invalid request."
msgstr "Demande invalide."

#: includes/fields/class-acf-field-select.php:689
msgid "%1$s is not one of %2$s"
msgstr "%1$s n’est pas l’un des %2$s"

#: includes/fields/class-acf-field-post_object.php:660
msgid "%1$s must have term %2$s."
msgid_plural "%1$s must have one of the following terms: %2$s"
msgstr[0] "%1$s doit contenir le terme %2$s."
msgstr[1] "%1$s doit contenir l’un des termes suivants : %2$s"

#: includes/fields/class-acf-field-post_object.php:644
msgid "%1$s must be of post type %2$s."
msgid_plural "%1$s must be of one of the following post types: %2$s"
msgstr[0] "%1$s doit être une publication de type %2$s."
msgstr[1] ""
"%1$s doit appartenir à l’un des types de publication suivants : %2$s"

#: includes/fields/class-acf-field-post_object.php:635
msgid "%1$s must have a valid post ID."
msgstr "%1$s doit avoir un ID de publication valide."

#: includes/fields/class-acf-field-file.php:447
msgid "%s requires a valid attachment ID."
msgstr "%s nécessite un ID de fichier jointe valide."

#: includes/admin/views/acf-field-group/options.php:233
msgid "Show in REST API"
msgstr "Afficher dans l’API REST"

#: includes/fields/class-acf-field-color_picker.php:156
msgid "Enable Transparency"
msgstr "Activer la transparence"

#: includes/fields/class-acf-field-color_picker.php:175
msgid "RGBA Array"
msgstr "Tableau RGBA"

#: includes/fields/class-acf-field-color_picker.php:92
msgid "RGBA String"
msgstr "Chaine RGBA"

#: includes/fields/class-acf-field-color_picker.php:91
#: includes/fields/class-acf-field-color_picker.php:174
msgid "Hex String"
msgstr "Chaine hexadécimale"

#: includes/admin/views/browse-fields-modal.php:12
msgid "Upgrade to PRO"
msgstr "Passer à la version Pro"

#: includes/admin/post-types/admin-field-group.php:304
#: includes/admin/post-types/admin-post-type.php:282
#: includes/admin/post-types/admin-taxonomy.php:284
msgctxt "post status"
msgid "Active"
msgstr "Actif"

#: includes/fields/class-acf-field-email.php:166
msgid "'%s' is not a valid email address"
msgstr "« %s » n’est pas une adresse e-mail valide"

#: includes/fields/class-acf-field-color_picker.php:70
msgid "Color value"
msgstr "Valeur de la couleur"

#: includes/fields/class-acf-field-color_picker.php:68
msgid "Select default color"
msgstr "Sélectionner une couleur par défaut"

#: includes/fields/class-acf-field-color_picker.php:66
msgid "Clear color"
msgstr "Effacer la couleur"

#: includes/acf-wp-functions.php:90
msgid "Blocks"
msgstr "Blocs"

#: includes/acf-wp-functions.php:86
msgid "Options"
msgstr "Options"

#: includes/acf-wp-functions.php:82
msgid "Users"
msgstr "Comptes"

#: includes/acf-wp-functions.php:78
msgid "Menu items"
msgstr "Éléments de menu"

#: includes/acf-wp-functions.php:70
msgid "Widgets"
msgstr "Widgets"

#: includes/acf-wp-functions.php:62
msgid "Attachments"
msgstr "Fichiers joints"

#: includes/acf-wp-functions.php:57
#: includes/admin/post-types/admin-post-types.php:112
#: includes/admin/post-types/admin-taxonomies.php:86
#: includes/admin/tools/class-acf-admin-tool-import.php:90
#: includes/admin/views/acf-post-type/basic-settings.php:86
#: includes/post-types/class-acf-taxonomy.php:90
#: includes/post-types/class-acf-taxonomy.php:91
msgid "Taxonomies"
msgstr "Taxonomies"

#: includes/acf-wp-functions.php:44
#: includes/admin/post-types/admin-post-type.php:124
#: includes/admin/post-types/admin-post-types.php:114
#: includes/admin/views/acf-post-type/advanced-settings.php:106
msgid "Posts"
msgstr "Publications"

#: includes/ajax/class-acf-ajax-local-json-diff.php:81
msgid "Last updated: %s"
msgstr "Dernière mise à jour : %s"

#: includes/ajax/class-acf-ajax-local-json-diff.php:75
msgid "Sorry, this post is unavailable for diff comparison."
msgstr ""
"Désolé, cette publication n’est pas disponible pour la comparaison de "
"différence."

#: includes/ajax/class-acf-ajax-local-json-diff.php:47
msgid "Invalid field group parameter(s)."
msgstr "Paramètres de groupe de champ invalides."

#: includes/admin/admin-internal-post-type-list.php:429
msgid "Awaiting save"
msgstr "En attente d’enregistrement"

#: includes/admin/admin-internal-post-type-list.php:426
msgid "Saved"
msgstr "Enregistré"

#: includes/admin/admin-internal-post-type-list.php:422
#: includes/admin/tools/class-acf-admin-tool-import.php:46
msgid "Import"
msgstr "Importer"

#: includes/admin/admin-internal-post-type-list.php:418
msgid "Review changes"
msgstr "Évaluer les modifications"

#: includes/admin/admin-internal-post-type-list.php:394
msgid "Located in: %s"
msgstr "Situés dans : %s"

#: includes/admin/admin-internal-post-type-list.php:391
msgid "Located in plugin: %s"
msgstr "Situés dans l’extension : %s"

#: includes/admin/admin-internal-post-type-list.php:388
msgid "Located in theme: %s"
msgstr "Situés dans le thème : %s"

#: includes/admin/post-types/admin-field-groups.php:235
msgid "Various"
msgstr "Divers"

#: includes/admin/admin-internal-post-type-list.php:230
#: includes/admin/admin-internal-post-type-list.php:503
msgid "Sync changes"
msgstr "Synchroniser les modifications"

#: includes/admin/admin-internal-post-type-list.php:229
msgid "Loading diff"
msgstr "Chargement du différentiel"

#: includes/admin/admin-internal-post-type-list.php:228
msgid "Review local JSON changes"
msgstr "Vérifier les modifications JSON locales"

#: includes/admin/admin.php:174
msgid "Visit website"
msgstr "Visiter le site"

#: includes/admin/admin.php:173
msgid "View details"
msgstr "Voir les détails"

#: includes/admin/admin.php:172
msgid "Version %s"
msgstr "Version %s"

#: includes/admin/admin.php:171
msgid "Information"
msgstr "Informations"

#: includes/admin/admin.php:162
msgid ""
"<a href=\"%s\" target=\"_blank\">Help Desk</a>. The support professionals on "
"our Help Desk will assist with your more in depth, technical challenges."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Service d’assistance</a>. Les "
"professionnels du support de notre service d’assistance vous aideront à "
"résoudre vos problèmes techniques les plus complexes."

#: includes/admin/admin.php:158
msgid ""
"<a href=\"%s\" target=\"_blank\">Discussions</a>. We have an active and "
"friendly community on our Community Forums who may be able to help you "
"figure out the 'how-tos' of the ACF world."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Discussions</a>. Nous avons une communauté "
"active et amicale sur nos forums communautaires qui peut vous aider à "
"comprendre le fonctionnement de l’écosystème ACF."

#: includes/admin/admin.php:154
msgid ""
"<a href=\"%s\" target=\"_blank\">Documentation</a>. Our extensive "
"documentation contains references and guides for most situations you may "
"encounter."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Documentation</a>. Notre documentation "
"complète contient des références et des guides pour la plupart des "
"situations que vous pouvez rencontrer."

#: includes/admin/admin.php:151
msgid ""
"We are fanatical about support, and want you to get the best out of your "
"website with ACF. If you run into any difficulties, there are several places "
"you can find help:"
msgstr ""
"Nous sommes fanatiques du support et souhaitons que vous tiriez le meilleur "
"parti de votre site avec ACF. Si vous rencontrez des difficultés, il existe "
"plusieurs endroits où vous pouvez trouver de l’aide :"

#: includes/admin/admin.php:148 includes/admin/admin.php:150
msgid "Help & Support"
msgstr "Aide & support"

#: includes/admin/admin.php:139
msgid ""
"Please use the Help & Support tab to get in touch should you find yourself "
"requiring assistance."
msgstr ""
"Veuillez utiliser l’onglet « Aide & support » pour nous contacter si vous "
"avez besoin d’assistance."

#: includes/admin/admin.php:136
msgid ""
"Before creating your first Field Group, we recommend first reading our <a "
"href=\"%s\" target=\"_blank\">Getting started</a> guide to familiarize "
"yourself with the plugin's philosophy and best practises."
msgstr ""
"Avant de créer votre premier groupe de champ, nous vous recommandons de lire "
"notre guide <a href=\"%s\" target=\"_blank\">Pour commencer</a> afin de vous "
"familiariser avec la philosophie et les meilleures pratiques de l’extension."

#: includes/admin/admin.php:134
msgid ""
"The Advanced Custom Fields plugin provides a visual form builder to "
"customize WordPress edit screens with extra fields, and an intuitive API to "
"display custom field values in any theme template file."
msgstr ""
"L’extension Advanced Custom Fields fournit un constructeur visuel de "
"formulaires pour modifier les écrans de WordPress avec des champs "
"supplémentaires, ainsi qu’une API intuitive pour afficher les valeurs des "
"champs personnalisés dans n’importe quel fichier de modèle de thème."

#: includes/admin/admin.php:131 includes/admin/admin.php:133
msgid "Overview"
msgstr "Aperçu"

#. translators: %s the name of the location type
#: includes/locations.php:38
msgid "Location type \"%s\" is already registered."
msgstr "Le type d’emplacement « %s » est déjà inscrit."

#. translators: %s class name for a location that could not be found
#: includes/locations.php:26
msgid "Class \"%s\" does not exist."
msgstr "La classe « %s » n’existe pas."

#: includes/ajax/class-acf-ajax-query-users.php:43
#: includes/ajax/class-acf-ajax.php:157
msgid "Invalid nonce."
msgstr "Nonce invalide."

#: includes/fields/class-acf-field-user.php:400
msgid "Error loading field."
msgstr "Erreur lors du chargement du champ."

#: includes/forms/form-user.php:328
msgid "<strong>Error</strong>: %s"
msgstr "<strong>Erreur</strong> : %s"

#: includes/locations/class-acf-location-widget.php:22
msgid "Widget"
msgstr "Widget"

#: includes/locations/class-acf-location-user-role.php:24
msgid "User Role"
msgstr "Rôle du compte"

#: includes/locations/class-acf-location-comment.php:22
msgid "Comment"
msgstr "Commenter"

#: includes/locations/class-acf-location-post-format.php:22
msgid "Post Format"
msgstr "Format de publication"

#: includes/locations/class-acf-location-nav-menu-item.php:22
msgid "Menu Item"
msgstr "Élément de menu"

#: includes/locations/class-acf-location-post-status.php:22
msgid "Post Status"
msgstr "État de la publication"

#: includes/acf-wp-functions.php:74
#: includes/locations/class-acf-location-nav-menu.php:89
msgid "Menus"
msgstr "Menus"

#: includes/locations/class-acf-location-nav-menu.php:80
msgid "Menu Locations"
msgstr "Emplacements de menus"

#: includes/locations/class-acf-location-nav-menu.php:22
msgid "Menu"
msgstr "Menu"

#: includes/locations/class-acf-location-post-taxonomy.php:22
msgid "Post Taxonomy"
msgstr "Taxonomie de la publication"

#: includes/locations/class-acf-location-page-type.php:114
msgid "Child Page (has parent)"
msgstr "Page enfant (a un parent)"

#: includes/locations/class-acf-location-page-type.php:113
msgid "Parent Page (has children)"
msgstr "Page parent (a des enfants)"

#: includes/locations/class-acf-location-page-type.php:112
msgid "Top Level Page (no parent)"
msgstr "Page de plus haut niveau (aucun parent)"

#: includes/locations/class-acf-location-page-type.php:111
msgid "Posts Page"
msgstr "Page des publications"

#: includes/locations/class-acf-location-page-type.php:110
msgid "Front Page"
msgstr "Page d’accueil"

#: includes/locations/class-acf-location-page-type.php:22
msgid "Page Type"
msgstr "Type de page"

#: includes/locations/class-acf-location-current-user.php:73
msgid "Viewing back end"
msgstr "Affichage de l’interface d’administration"

#: includes/locations/class-acf-location-current-user.php:72
msgid "Viewing front end"
msgstr "Vue de l’interface publique"

#: includes/locations/class-acf-location-current-user.php:71
msgid "Logged in"
msgstr "Connecté"

#: includes/locations/class-acf-location-current-user.php:22
msgid "Current User"
msgstr "Compte actuel"

#: includes/locations/class-acf-location-page-template.php:22
msgid "Page Template"
msgstr "Modèle de page"

#: includes/locations/class-acf-location-user-form.php:74
msgid "Register"
msgstr "S’inscrire"

#: includes/locations/class-acf-location-user-form.php:73
msgid "Add / Edit"
msgstr "Ajouter/Modifier"

#: includes/locations/class-acf-location-user-form.php:22
msgid "User Form"
msgstr "Formulaire de profil"

#: includes/locations/class-acf-location-page-parent.php:22
msgid "Page Parent"
msgstr "Page parente"

#: includes/locations/class-acf-location-current-user-role.php:77
msgid "Super Admin"
msgstr "Super admin"

#: includes/locations/class-acf-location-current-user-role.php:22
msgid "Current User Role"
msgstr "Rôle du compte actuel"

#: includes/locations/class-acf-location-page-template.php:73
#: includes/locations/class-acf-location-post-template.php:85
msgid "Default Template"
msgstr "Modèle par défaut"

#: includes/locations/class-acf-location-post-template.php:22
msgid "Post Template"
msgstr "Modèle de publication"

#: includes/locations/class-acf-location-post-category.php:22
msgid "Post Category"
msgstr "Catégorie de publication"

#: includes/locations/class-acf-location-attachment.php:84
msgid "All %s formats"
msgstr "Tous les formats %s"

#: includes/locations/class-acf-location-attachment.php:22
msgid "Attachment"
msgstr "Fichier joint"

#: includes/validation.php:323
msgid "%s value is required"
msgstr "La valeur %s est obligatoire"

#: includes/admin/views/acf-field-group/conditional-logic.php:64
msgid "Show this field if"
msgstr "Afficher ce champ si"

#: includes/admin/views/acf-field-group/conditional-logic.php:25
#: includes/admin/views/acf-field-group/field.php:122 includes/fields.php:385
msgid "Conditional Logic"
msgstr "Logique conditionnelle"

#: includes/admin/views/acf-field-group/conditional-logic.php:169
#: includes/admin/views/acf-field-group/location-rule.php:84
msgid "and"
msgstr "et"

#: includes/admin/post-types/admin-field-groups.php:97
#: includes/admin/post-types/admin-post-types.php:118
#: includes/admin/post-types/admin-taxonomies.php:117
msgid "Local JSON"
msgstr "JSON Local"

#: includes/admin/views/acf-field-group/pro-features.php:50
msgid "Clone Field"
msgstr "Champ clone"

#. translators: %s a list of plugin
#: includes/admin/views/upgrade/notice.php:32
msgid ""
"Please also check all premium add-ons (%s) are updated to the latest version."
msgstr ""
"Veuillez également vérifier que tous les modules PRO (%s) soient mis à jour "
"à la dernière version."

#: includes/admin/views/upgrade/notice.php:29
msgid ""
"This version contains improvements to your database and requires an upgrade."
msgstr ""
"Cette version contient des améliorations de la base de données et nécessite "
"une mise à niveau."

#. translators: %1 plugin name, %2 version number
#: includes/admin/views/upgrade/notice.php:28
msgid "Thank you for updating to %1$s v%2$s!"
msgstr "Merci d‘avoir mis à jour %1$s v%2$s !"

#: includes/admin/views/upgrade/notice.php:26
msgid "Database Upgrade Required"
msgstr "Mise à niveau de la base de données requise"

#: includes/admin/post-types/admin-field-group.php:159
#: includes/admin/views/upgrade/notice.php:17
msgid "Options Page"
msgstr "Page d’options"

#: includes/admin/views/upgrade/notice.php:14 includes/fields.php:436
msgid "Gallery"
msgstr "Galerie"

#: includes/admin/views/upgrade/notice.php:11 includes/fields.php:426
msgid "Flexible Content"
msgstr "Contenu flexible"

#: includes/admin/views/upgrade/notice.php:8 includes/fields.php:446
msgid "Repeater"
msgstr "Répéteur"

#: includes/admin/views/tools/tools.php:16
msgid "Back to all tools"
msgstr "Retour aux outils"

#: includes/admin/views/acf-field-group/options.php:195
msgid ""
"If multiple field groups appear on an edit screen, the first field group's "
"options will be used (the one with the lowest order number)"
msgstr ""
"Si plusieurs groupes de champs apparaissent sur un écran de modification, "
"les options du premier groupe de champs seront utilisées (celle avec le "
"numéro de commande le plus bas)."

#: includes/admin/views/acf-field-group/options.php:195
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr ""
"<b>Sélectionner</b> les éléments à <b>masquer</b> sur l’écran de "
"modification."

#: includes/admin/views/acf-field-group/options.php:194
msgid "Hide on screen"
msgstr "Masquer de l’écran"

#: includes/admin/views/acf-field-group/options.php:186
msgid "Send Trackbacks"
msgstr "Envoyer des rétroliens"

#: includes/admin/post-types/admin-taxonomy.php:126
#: includes/admin/views/acf-field-group/options.php:185
#: includes/admin/views/acf-taxonomy/advanced-settings.php:159
msgid "Tags"
msgstr "Étiquettes"

#: includes/admin/post-types/admin-taxonomy.php:128
#: includes/admin/views/acf-field-group/options.php:184
msgid "Categories"
msgstr "Catégories"

#: includes/admin/views/acf-field-group/options.php:182
#: includes/admin/views/acf-post-type/advanced-settings.php:28
msgid "Page Attributes"
msgstr "Attributs de page"

#: includes/admin/views/acf-field-group/options.php:181
msgid "Format"
msgstr "Format"

#: includes/admin/views/acf-field-group/options.php:180
#: includes/admin/views/acf-post-type/advanced-settings.php:22
msgid "Author"
msgstr "Auteur/autrice"

#: includes/admin/views/acf-field-group/options.php:179
msgid "Slug"
msgstr "Slug"

#: includes/admin/views/acf-field-group/options.php:178
#: includes/admin/views/acf-post-type/advanced-settings.php:27
msgid "Revisions"
msgstr "Révisions"

#: includes/acf-wp-functions.php:66
#: includes/admin/views/acf-field-group/options.php:177
#: includes/admin/views/acf-post-type/advanced-settings.php:23
msgid "Comments"
msgstr "Commentaires"

#: includes/admin/views/acf-field-group/options.php:176
msgid "Discussion"
msgstr "Commentaires"

#: includes/admin/views/acf-field-group/options.php:174
#: includes/admin/views/acf-post-type/advanced-settings.php:26
msgid "Excerpt"
msgstr "Extrait"

#: includes/admin/views/acf-field-group/options.php:173
msgid "Content Editor"
msgstr "Éditeur de contenu"

#: includes/admin/views/acf-field-group/options.php:172
msgid "Permalink"
msgstr "Permalien"

#: includes/admin/views/acf-field-group/options.php:250
msgid "Shown in field group list"
msgstr "Affiché dans la liste des groupes de champs"

#: includes/admin/views/acf-field-group/options.php:157
msgid "Field groups with a lower order will appear first"
msgstr "Les groupes de champs avec un ordre inférieur apparaitront en premier"

#: includes/admin/views/acf-field-group/options.php:156
msgid "Order No."
msgstr "N° d’ordre."

#: includes/admin/views/acf-field-group/options.php:147
msgid "Below fields"
msgstr "Sous les champs"

#: includes/admin/views/acf-field-group/options.php:146
msgid "Below labels"
msgstr "Sous les libellés"

#: includes/admin/views/acf-field-group/options.php:139
msgid "Instruction Placement"
msgstr "Emplacement des instructions"

#: includes/admin/views/acf-field-group/options.php:122
msgid "Label Placement"
msgstr "Emplacement des libellés"

#: includes/admin/views/acf-field-group/options.php:110
msgid "Side"
msgstr "Sur le côté"

#: includes/admin/views/acf-field-group/options.php:109
msgid "Normal (after content)"
msgstr "Normal (après le contenu)"

#: includes/admin/views/acf-field-group/options.php:108
msgid "High (after title)"
msgstr "Haute (après le titre)"

#: includes/admin/views/acf-field-group/options.php:101
msgid "Position"
msgstr "Emplacement"

#: includes/admin/views/acf-field-group/options.php:92
msgid "Seamless (no metabox)"
msgstr "Sans contour (pas de boîte meta)"

#: includes/admin/views/acf-field-group/options.php:91
msgid "Standard (WP metabox)"
msgstr "Standard (boîte méta WP)"

#: includes/admin/views/acf-field-group/options.php:84
msgid "Style"
msgstr "Style"

#: includes/admin/views/acf-field-group/fields.php:55
msgid "Type"
msgstr "Type"

#: includes/admin/post-types/admin-field-groups.php:91
#: includes/admin/post-types/admin-post-types.php:111
#: includes/admin/post-types/admin-taxonomies.php:110
#: includes/admin/views/acf-field-group/fields.php:54
msgid "Key"
msgstr "Clé"

#. translators: Hidden accessibility text for the positional order number of
#. the field.
#: includes/admin/views/acf-field-group/fields.php:48
msgid "Order"
msgstr "Ordre"

#: includes/admin/views/acf-field-group/field.php:321
msgid "Close Field"
msgstr "Fermer le champ"

#: includes/admin/views/acf-field-group/field.php:252
msgid "id"
msgstr "ID"

#: includes/admin/views/acf-field-group/field.php:236
msgid "class"
msgstr "classe"

#: includes/admin/views/acf-field-group/field.php:278
msgid "width"
msgstr "largeur"

#: includes/admin/views/acf-field-group/field.php:272
msgid "Wrapper Attributes"
msgstr "Attributs du conteneur"

#: includes/fields/class-acf-field.php:312
msgid "Required"
msgstr "Obligatoire"

#: includes/admin/views/acf-field-group/field.php:219
msgid "Instructions"
msgstr "Instructions"

#: includes/admin/views/acf-field-group/field.php:142
msgid "Field Type"
msgstr "Type de champ"

#: includes/admin/views/acf-field-group/field.php:183
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr ""
"Un seul mot. Aucun espace. Les tirets bas et les tirets sont autorisés."

#: includes/admin/views/acf-field-group/field.php:182
msgid "Field Name"
msgstr "Nom du champ"

#: includes/admin/views/acf-field-group/field.php:170
msgid "This is the name which will appear on the EDIT page"
msgstr "Ceci est le nom qui apparaîtra sur la page de modification"

#: includes/admin/views/acf-field-group/field.php:169
#: includes/admin/views/browse-fields-modal.php:69
msgid "Field Label"
msgstr "Libellé du champ"

#: includes/admin/views/acf-field-group/field.php:94
msgid "Delete"
msgstr "Supprimer"

#: includes/admin/views/acf-field-group/field.php:94
msgid "Delete field"
msgstr "Supprimer le champ"

#: includes/admin/views/acf-field-group/field.php:92
msgid "Move"
msgstr "Déplacer"

#: includes/admin/views/acf-field-group/field.php:92
msgid "Move field to another group"
msgstr "Deplacer le champ vers un autre groupe"

#: includes/admin/views/acf-field-group/field.php:90
msgid "Duplicate field"
msgstr "Dupliquer le champ"

#: includes/admin/views/acf-field-group/field.php:86
#: includes/admin/views/acf-field-group/field.php:89
msgid "Edit field"
msgstr "Modifier le champ"

#: includes/admin/views/acf-field-group/field.php:82
msgid "Drag to reorder"
msgstr "Faites glisser pour réorganiser"

#: includes/admin/post-types/admin-field-group.php:99
#: includes/admin/views/acf-field-group/location-group.php:3
msgid "Show this field group if"
msgstr "Afficher ce groupe de champs si"

#: includes/admin/views/upgrade/upgrade.php:93
#: includes/ajax/class-acf-ajax-upgrade.php:34
msgid "No updates available."
msgstr "Aucune mise à jour disponible."

#. translators: %s the url to the field group page.
#: includes/admin/views/upgrade/upgrade.php:32
msgid "Database upgrade complete. <a href=\"%s\">See what's new</a>"
msgstr ""
"Mise à niveau de base de données complète. <a href=\"%s\">Voir les "
"nouveautés</a>"

#: includes/admin/views/upgrade/upgrade.php:27
msgid "Reading upgrade tasks..."
msgstr "Lecture des tâches de mise à niveau…"

#: includes/admin/views/upgrade/network.php:165
#: includes/admin/views/upgrade/upgrade.php:64
msgid "Upgrade failed."
msgstr "Mise à niveau échouée."

#: includes/admin/views/upgrade/network.php:162
msgid "Upgrade complete."
msgstr "Mise à niveau terminée."

#. translators: %s the version being upgraded to.
#. translators: %s the new ACF version
#: includes/admin/views/upgrade/network.php:148
#: includes/admin/views/upgrade/upgrade.php:29
msgid "Upgrading data to version %s"
msgstr "Mise à niveau des données vers la version %s"

#: includes/admin/views/upgrade/network.php:120
#: includes/admin/views/upgrade/notice.php:46
msgid ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"
msgstr ""
"Il est recommandé de sauvegarder votre base de données avant de procéder. "
"Confirmez-vous le lancement de la mise à jour maintenant ?"

#: includes/admin/views/upgrade/network.php:116
msgid "Please select at least one site to upgrade."
msgstr "Veuillez choisir au moins un site à mettre à niveau."

#. translators: %s admin dashboard url page
#: includes/admin/views/upgrade/network.php:96
msgid ""
"Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr ""
"Mise à niveau de la base de données effectuée. <a href=\"%s\">Retourner au "
"tableau de bord du réseau</a>"

#: includes/admin/views/upgrade/network.php:79
msgid "Site is up to date"
msgstr "Le site est à jour"

#. translators: %1 current db version, %2 available db version
#: includes/admin/views/upgrade/network.php:77
msgid "Site requires database upgrade from %1$s to %2$s"
msgstr ""
"Le site nécessite une mise à niveau de la base de données à partir de %1$s "
"vers %2$s"

#: includes/admin/views/upgrade/network.php:34
#: includes/admin/views/upgrade/network.php:45
msgid "Site"
msgstr "Site"

#. translators: %s The button label name, translated seperately
#: includes/admin/views/upgrade/network.php:24
#: includes/admin/views/upgrade/network.php:25
#: includes/admin/views/upgrade/network.php:94
msgid "Upgrade Sites"
msgstr "Mettre à niveau les sites"

#. translators: %s The button label name, translated seperately
#: includes/admin/views/upgrade/network.php:24
msgid ""
"The following sites require a DB upgrade. Check the ones you want to update "
"and then click %s."
msgstr ""
"Les sites suivants nécessitent une mise à niveau de la base de données. "
"Sélectionner ceux que vous voulez mettre à jour et cliquer sur %s."

#: includes/admin/views/acf-field-group/conditional-logic.php:184
#: includes/admin/views/acf-field-group/locations.php:37
msgid "Add rule group"
msgstr "Ajouter un groupe de règles"

#: includes/admin/views/acf-field-group/locations.php:10
msgid ""
"Create a set of rules to determine which edit screens will use these "
"advanced custom fields"
msgstr ""
"Créer un ensemble de règles pour déterminer quels écrans de modification "
"utiliseront ces champs personnalisés avancés"

#: includes/admin/views/acf-field-group/locations.php:9
msgid "Rules"
msgstr "Règles"

#: includes/admin/tools/class-acf-admin-tool-export.php:449
msgid "Copied"
msgstr "Copié"

#: includes/admin/tools/class-acf-admin-tool-export.php:425
msgid "Copy to clipboard"
msgstr "Copier dans le presse-papier"

#: includes/admin/tools/class-acf-admin-tool-export.php:331
msgid ""
"Select the items you would like to export and then select your export "
"method. Export As JSON to export to a .json file which you can then import "
"to another ACF installation. Generate PHP to export to PHP code which you "
"can place in your theme."
msgstr ""
"Sélectionnez les éléments que vous souhaitez exporter, puis choisissez la "
"méthode d’exportation. « Exporter comme JSON » pour exporter vers un "
"fichier .json que vous pouvez ensuite importer dans une autre installation "
"ACF. « Générer le PHP » pour exporter un code PHP que vous pouvez placer "
"dans votre thème."

#: includes/admin/tools/class-acf-admin-tool-export.php:215
msgid "Select Field Groups"
msgstr "Sélectionner les groupes de champs"

#: includes/admin/tools/class-acf-admin-tool-export.php:88
#: includes/admin/tools/class-acf-admin-tool-export.php:121
msgid "No field groups selected"
msgstr "Aucun groupe de champs sélectionné"

#: includes/admin/tools/class-acf-admin-tool-export.php:38
#: includes/admin/tools/class-acf-admin-tool-export.php:339
#: includes/admin/tools/class-acf-admin-tool-export.php:363
msgid "Generate PHP"
msgstr "Générer le PHP"

#: includes/admin/tools/class-acf-admin-tool-export.php:34
msgid "Export Field Groups"
msgstr "Exporter les groupes de champs"

#: includes/admin/tools/class-acf-admin-tool-import.php:172
msgid "Import file empty"
msgstr "Fichier d’importation vide"

#: includes/admin/tools/class-acf-admin-tool-import.php:163
msgid "Incorrect file type"
msgstr "Type de fichier incorrect"

#: includes/admin/tools/class-acf-admin-tool-import.php:158
msgid "Error uploading file. Please try again"
msgstr "Erreur de téléversement du fichier. Veuillez réessayer."

#: includes/admin/tools/class-acf-admin-tool-import.php:47
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the items in that file."
msgstr ""
"Sélectionnez le fichier JSON Advanced Custom Fields que vous souhaitez "
"importer. Quand vous cliquez sur le bouton « Importer » ci-dessous, ACF "
"importera les éléments dans ce fichier."

#: includes/admin/tools/class-acf-admin-tool-import.php:26
msgid "Import Field Groups"
msgstr "Importer les groupes de champs"

#: includes/admin/admin-internal-post-type-list.php:417
msgid "Sync"
msgstr "Synchroniser"

#. translators: %s: field group title
#: includes/admin/admin-internal-post-type-list.php:960
msgid "Select %s"
msgstr "Sélectionner %s"

#: includes/admin/admin-internal-post-type-list.php:460
#: includes/admin/admin-internal-post-type-list.php:492
#: includes/admin/views/acf-field-group/field.php:90
msgid "Duplicate"
msgstr "Dupliquer"

#: includes/admin/admin-internal-post-type-list.php:460
msgid "Duplicate this item"
msgstr "Dupliquer cet élément"

#: includes/admin/views/acf-post-type/advanced-settings.php:41
msgid "Supports"
msgstr "Prend en charge"

#: includes/admin/admin.php:355
#: includes/admin/views/browse-fields-modal.php:102
msgid "Documentation"
msgstr "Documentation"

#: includes/admin/post-types/admin-field-groups.php:90
#: includes/admin/post-types/admin-post-types.php:110
#: includes/admin/post-types/admin-taxonomies.php:109
#: includes/admin/views/acf-field-group/options.php:249
#: includes/admin/views/acf-post-type/advanced-settings.php:62
#: includes/admin/views/acf-taxonomy/advanced-settings.php:114
#: includes/admin/views/upgrade/network.php:36
#: includes/admin/views/upgrade/network.php:47
msgid "Description"
msgstr "Description"

#: includes/admin/admin-internal-post-type-list.php:414
#: includes/admin/admin-internal-post-type-list.php:832
msgid "Sync available"
msgstr "Synchronisation disponible"

#. translators: %s number of field groups synchronized
#: includes/admin/post-types/admin-field-groups.php:374
msgid "Field group synchronized."
msgid_plural "%s field groups synchronized."
msgstr[0] "Groupe de champs synchronisé."
msgstr[1] "%s groupes de champs synchronisés."

#. translators: %s number of field groups duplicated
#: includes/admin/post-types/admin-field-groups.php:367
msgid "Field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] "Groupe de champs dupliqué."
msgstr[1] "%s groupes de champs dupliqués."

#: includes/admin/admin-internal-post-type-list.php:155
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] "Actif <span class=\"count\">(%s)</span>"
msgstr[1] "Actifs <span class=\"count\">(%s)</span>"

#: includes/admin/admin-upgrade.php:251
msgid "Review sites & upgrade"
msgstr "Examiner les sites et mettre à niveau"

#: includes/admin/admin-upgrade.php:59 includes/admin/admin-upgrade.php:90
#: includes/admin/admin-upgrade.php:91 includes/admin/admin-upgrade.php:227
#: includes/admin/views/upgrade/network.php:21
#: includes/admin/views/upgrade/upgrade.php:23
msgid "Upgrade Database"
msgstr "Mettre à niveau la base de données"

#: includes/admin/views/acf-field-group/options.php:175
#: includes/admin/views/acf-post-type/advanced-settings.php:30
msgid "Custom Fields"
msgstr "Champs personnalisés"

#: includes/admin/post-types/admin-field-group.php:609
msgid "Move Field"
msgstr "Déplacer le champ"

#: includes/admin/post-types/admin-field-group.php:602
#: includes/admin/post-types/admin-field-group.php:606
msgid "Please select the destination for this field"
msgstr "Veuillez sélectionner la destination pour ce champ"

#. translators: Confirmation message once a field has been moved to a different
#. field group.
#: includes/admin/post-types/admin-field-group.php:568
msgid "The %1$s field can now be found in the %2$s field group"
msgstr ""
"Le champ %1$s peut maintenant être trouvé dans le groupe de champs %2$s"

#: includes/admin/post-types/admin-field-group.php:565
msgid "Move Complete."
msgstr "Déplacement effectué."

#: includes/admin/views/acf-field-group/field.php:52
#: includes/admin/views/acf-field-group/options.php:217
#: includes/admin/views/acf-post-type/advanced-settings.php:78
#: includes/admin/views/acf-taxonomy/advanced-settings.php:130
msgid "Active"
msgstr "Actif"

#: includes/admin/post-types/admin-field-group.php:276
msgid "Field Keys"
msgstr "Clés des champs"

#: includes/admin/post-types/admin-field-group.php:180
msgid "Settings"
msgstr "Réglages"

#: includes/admin/post-types/admin-field-groups.php:92
msgid "Location"
msgstr "Emplacement"

#: includes/admin/post-types/admin-field-group.php:100
msgid "Null"
msgstr "Null"

#: includes/admin/post-types/admin-field-group.php:97
#: includes/class-acf-internal-post-type.php:728
#: includes/post-types/class-acf-field-group.php:345
msgid "copy"
msgstr "copier"

#: includes/admin/post-types/admin-field-group.php:96
msgid "(this field)"
msgstr "(ce champ)"

#: includes/admin/post-types/admin-field-group.php:94
msgid "Checked"
msgstr "Coché"

#: includes/admin/post-types/admin-field-group.php:90
msgid "Move Custom Field"
msgstr "Déplacer le champ personnalisé"

#: includes/admin/post-types/admin-field-group.php:89
msgid "No toggle fields available"
msgstr "Aucun champ de sélection disponible"

#: includes/admin/post-types/admin-field-group.php:87
msgid "Field group title is required"
msgstr "Le titre du groupe de champ est requis"

#: includes/admin/post-types/admin-field-group.php:86
msgid "This field cannot be moved until its changes have been saved"
msgstr ""
"Ce champ ne peut pas être déplacé tant que ses modifications n’ont pas été "
"enregistrées"

#: includes/admin/post-types/admin-field-group.php:85
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr ""
"La chaine « field_ » ne peut pas être utilisée au début du nom d’un champ"

#: includes/admin/post-types/admin-field-group.php:69
msgid "Field group draft updated."
msgstr "Brouillon du groupe de champs mis à jour."

#: includes/admin/post-types/admin-field-group.php:68
msgid "Field group scheduled for."
msgstr "Groupe de champs programmé."

#: includes/admin/post-types/admin-field-group.php:67
msgid "Field group submitted."
msgstr "Groupe de champs envoyé."

#: includes/admin/post-types/admin-field-group.php:66
msgid "Field group saved."
msgstr "Groupe de champs sauvegardé."

#: includes/admin/post-types/admin-field-group.php:65
msgid "Field group published."
msgstr "Groupe de champs publié."

#: includes/admin/post-types/admin-field-group.php:62
msgid "Field group deleted."
msgstr "Groupe de champs supprimé."

#: includes/admin/post-types/admin-field-group.php:60
#: includes/admin/post-types/admin-field-group.php:61
#: includes/admin/post-types/admin-field-group.php:63
msgid "Field group updated."
msgstr "Groupe de champs mis à jour."

#: includes/admin/admin-tools.php:107
#: includes/admin/views/global/navigation.php:254
#: includes/admin/views/tools/tools.php:14
msgid "Tools"
msgstr "Outils"

#: includes/locations/abstract-acf-location.php:105
msgid "is not equal to"
msgstr "n’est pas égal à"

#: includes/locations/abstract-acf-location.php:104
msgid "is equal to"
msgstr "est égal à"

#: includes/locations.php:104
msgid "Forms"
msgstr "Formulaires"

#: includes/admin/post-types/admin-post-type.php:125 includes/locations.php:102
#: includes/locations/class-acf-location-page.php:22
msgid "Page"
msgstr "Page"

#: includes/admin/post-types/admin-post-type.php:123 includes/locations.php:101
#: includes/locations/class-acf-location-post.php:22
msgid "Post"
msgstr "Publication"

#: includes/fields.php:328
msgid "Relational"
msgstr "Relationnel"

#: includes/fields.php:327
msgid "Choice"
msgstr "Choix"

#: includes/fields.php:325
msgid "Basic"
msgstr "Basique"

#: includes/fields.php:276
msgid "Unknown"
msgstr "Inconnu"

#: includes/fields.php:276
msgid "Field type does not exist"
msgstr "Le type de champ n’existe pas"

#: includes/forms/form-front.php:220
msgid "Spam Detected"
msgstr "Indésirable détecté"

#: includes/forms/form-front.php:103
msgid "Post updated"
msgstr "Publication mise à jour"

#: includes/forms/form-front.php:102
msgid "Update"
msgstr "Mise à jour"

#: includes/forms/form-front.php:63
msgid "Validate Email"
msgstr "Valider l’e-mail"

#: includes/fields.php:326 includes/forms/form-front.php:55
msgid "Content"
msgstr "Contenu"

#: includes/admin/views/acf-post-type/advanced-settings.php:21
#: includes/forms/form-front.php:46
msgid "Title"
msgstr "Titre"

#: includes/assets.php:376 includes/forms/form-comment.php:140
msgid "Edit field group"
msgstr "Modifier le groupe de champs"

#: includes/admin/post-types/admin-field-group.php:113
msgid "Selection is less than"
msgstr "La sélection est inférieure à"

#: includes/admin/post-types/admin-field-group.php:112
msgid "Selection is greater than"
msgstr "La sélection est supérieure à"

#: includes/admin/post-types/admin-field-group.php:111
msgid "Value is less than"
msgstr "La valeur est plus petite que"

#: includes/admin/post-types/admin-field-group.php:110
msgid "Value is greater than"
msgstr "La valeur est plus grande que"

#: includes/admin/post-types/admin-field-group.php:109
msgid "Value contains"
msgstr "La valeur contient"

#: includes/admin/post-types/admin-field-group.php:108
msgid "Value matches pattern"
msgstr "La valeur correspond au modèle"

#: includes/admin/post-types/admin-field-group.php:107
msgid "Value is not equal to"
msgstr "La valeur n’est pas égale à"

#: includes/admin/post-types/admin-field-group.php:106
msgid "Value is equal to"
msgstr "La valeur est égale à"

#: includes/admin/post-types/admin-field-group.php:105
msgid "Has no value"
msgstr "A aucune valeur"

#: includes/admin/post-types/admin-field-group.php:104
msgid "Has any value"
msgstr "A n’importe quelle valeur"

#: includes/admin/admin-internal-post-type.php:337
#: includes/admin/views/browse-fields-modal.php:72 includes/assets.php:354
msgid "Cancel"
msgstr "Annuler"

#: includes/assets.php:350
msgid "Are you sure?"
msgstr "Confirmez-vous ?"

#: includes/assets.php:370
msgid "%d fields require attention"
msgstr "%d champs nécessitent votre attention"

#: includes/assets.php:369
msgid "1 field requires attention"
msgstr "Un champ nécessite votre attention"

#: includes/assets.php:368 includes/validation.php:257
#: includes/validation.php:265
msgid "Validation failed"
msgstr "Échec de la validation"

#: includes/assets.php:367
msgid "Validation successful"
msgstr "Validation réussie"

#: includes/media.php:54
msgid "Restricted"
msgstr "Limité"

#: includes/media.php:53
msgid "Collapse Details"
msgstr "Replier les détails"

#: includes/media.php:52
msgid "Expand Details"
msgstr "Déplier les détails"

#: includes/admin/views/acf-post-type/advanced-settings.php:470
#: includes/media.php:51
msgid "Uploaded to this post"
msgstr "Téléversé sur cette publication"

#: includes/media.php:50
msgctxt "verb"
msgid "Update"
msgstr "Mettre à jour"

#: includes/media.php:49
msgctxt "verb"
msgid "Edit"
msgstr "Modifier"

#: includes/assets.php:364
msgid "The changes you made will be lost if you navigate away from this page"
msgstr ""
"Les modifications que vous avez effectuées seront perdues si vous quittez "
"cette page"

#: includes/api/api-helpers.php:3000
msgid "File type must be %s."
msgstr "Le type de fichier doit être %s."

#: includes/admin/post-types/admin-field-group.php:98
#: includes/admin/views/acf-field-group/conditional-logic.php:64
#: includes/admin/views/acf-field-group/conditional-logic.php:182
#: includes/admin/views/acf-field-group/location-group.php:3
#: includes/admin/views/acf-field-group/locations.php:35
#: includes/api/api-helpers.php:2997
msgid "or"
msgstr "ou"

#: includes/api/api-helpers.php:2973
msgid "File size must not exceed %s."
msgstr "La taille du fichier ne doit pas excéder %s."

#: includes/api/api-helpers.php:2969
msgid "File size must be at least %s."
msgstr "La taille du fichier doit être d’au moins %s."

#: includes/api/api-helpers.php:2956
msgid "Image height must not exceed %dpx."
msgstr "La hauteur de l’image ne doit pas excéder %d px."

#: includes/api/api-helpers.php:2952
msgid "Image height must be at least %dpx."
msgstr "La hauteur de l’image doit être au minimum de %d px."

#: includes/api/api-helpers.php:2940
msgid "Image width must not exceed %dpx."
msgstr "La largeur de l’image ne doit pas excéder %d px."

#: includes/api/api-helpers.php:2936
msgid "Image width must be at least %dpx."
msgstr "La largeur de l’image doit être au minimum de %d px."

#: includes/api/api-helpers.php:1425 includes/api/api-term.php:140
msgid "(no title)"
msgstr "(aucun titre)"

#: includes/api/api-helpers.php:781
msgid "Full Size"
msgstr "Taille originale"

#: includes/api/api-helpers.php:746
msgid "Large"
msgstr "Grand"

#: includes/api/api-helpers.php:745
msgid "Medium"
msgstr "Moyen"

#: includes/api/api-helpers.php:744
msgid "Thumbnail"
msgstr "Miniature"

#: includes/acf-field-functions.php:854
#: includes/admin/post-types/admin-field-group.php:95
msgid "(no label)"
msgstr "(aucun libellé)"

#: includes/fields/class-acf-field-textarea.php:135
msgid "Sets the textarea height"
msgstr "Définir la hauteur de la zone de texte"

#: includes/fields/class-acf-field-textarea.php:134
msgid "Rows"
msgstr "Lignes"

#: includes/fields/class-acf-field-textarea.php:22
msgid "Text Area"
msgstr "Zone de texte"

#: includes/fields/class-acf-field-checkbox.php:421
msgid "Prepend an extra checkbox to toggle all choices"
msgstr "Ajouter une case à cocher pour basculer tous les choix"

#: includes/fields/class-acf-field-checkbox.php:383
msgid "Save 'custom' values to the field's choices"
msgstr "Enregistrez les valeurs « personnalisées » dans les choix du champ"

#: includes/fields/class-acf-field-checkbox.php:372
msgid "Allow 'custom' values to be added"
msgstr "Autoriser l’ajout de valeurs personnalisées"

#: includes/fields/class-acf-field-checkbox.php:35
msgid "Add new choice"
msgstr "Ajouter un nouveau choix"

#: includes/fields/class-acf-field-checkbox.php:157
msgid "Toggle All"
msgstr "Tout (dé)sélectionner"

#: includes/fields/class-acf-field-page_link.php:487
msgid "Allow Archives URLs"
msgstr "Afficher les URL des archives"

#: includes/fields/class-acf-field-page_link.php:196
msgid "Archives"
msgstr "Archives"

#: includes/fields/class-acf-field-page_link.php:22
msgid "Page Link"
msgstr "Lien vers la page"

#: includes/fields/class-acf-field-taxonomy.php:884
#: includes/locations/class-acf-location-user-form.php:72
msgid "Add"
msgstr "Ajouter"

#: includes/admin/views/acf-field-group/fields.php:53
#: includes/fields/class-acf-field-taxonomy.php:854
msgid "Name"
msgstr "Nom"

#: includes/fields/class-acf-field-taxonomy.php:839
msgid "%s added"
msgstr "%s ajouté"

#: includes/fields/class-acf-field-taxonomy.php:803
msgid "%s already exists"
msgstr "%s existe déjà"

#: includes/fields/class-acf-field-taxonomy.php:791
msgid "User unable to add new %s"
msgstr "Compte incapable d’ajouter un nouveau %s"

#: includes/fields/class-acf-field-taxonomy.php:678
msgid "Term ID"
msgstr "ID de terme"

#: includes/fields/class-acf-field-taxonomy.php:677
msgid "Term Object"
msgstr "Objet de terme"

#: includes/fields/class-acf-field-taxonomy.php:662
msgid "Load value from posts terms"
msgstr "Charger une valeur depuis les termes de publications"

#: includes/fields/class-acf-field-taxonomy.php:661
msgid "Load Terms"
msgstr "Charger les termes"

#: includes/fields/class-acf-field-taxonomy.php:651
msgid "Connect selected terms to the post"
msgstr "Lier les termes sélectionnés à la publication"

#: includes/fields/class-acf-field-taxonomy.php:650
msgid "Save Terms"
msgstr "Enregistrer les termes"

#: includes/fields/class-acf-field-taxonomy.php:640
msgid "Allow new terms to be created whilst editing"
msgstr "Autoriser la création de nouveaux termes pendant la modification"

#: includes/fields/class-acf-field-taxonomy.php:639
msgid "Create Terms"
msgstr "Créer des termes"

#: includes/fields/class-acf-field-taxonomy.php:698
msgid "Radio Buttons"
msgstr "Boutons radio"

#: includes/fields/class-acf-field-taxonomy.php:697
msgid "Single Value"
msgstr "Valeur unique"

#: includes/fields/class-acf-field-taxonomy.php:695
msgid "Multi Select"
msgstr "Liste déroulante à multiple choix"

#: includes/fields/class-acf-field-checkbox.php:22
#: includes/fields/class-acf-field-taxonomy.php:694
msgid "Checkbox"
msgstr "Case à cocher"

#: includes/fields/class-acf-field-taxonomy.php:693
msgid "Multiple Values"
msgstr "Valeurs multiples"

#: includes/fields/class-acf-field-taxonomy.php:688
msgid "Select the appearance of this field"
msgstr "Sélectionner l’apparence de ce champ"

#: includes/fields/class-acf-field-taxonomy.php:687
msgid "Appearance"
msgstr "Apparence"

#: includes/fields/class-acf-field-taxonomy.php:629
msgid "Select the taxonomy to be displayed"
msgstr "Sélectionner la taxonomie à afficher"

#: includes/fields/class-acf-field-taxonomy.php:593
msgctxt "No Terms"
msgid "No %s"
msgstr "N° %s"

#: includes/fields/class-acf-field-number.php:240
msgid "Value must be equal to or lower than %d"
msgstr "La valeur doit être inférieure ou égale à %d"

#: includes/fields/class-acf-field-number.php:235
msgid "Value must be equal to or higher than %d"
msgstr "La valeur doit être être supérieure ou égale à %d"

#: includes/fields/class-acf-field-number.php:223
msgid "Value must be a number"
msgstr "La valeur doit être un nombre"

#: includes/fields/class-acf-field-number.php:22
msgid "Number"
msgstr "Nombre"

#: includes/fields/class-acf-field-radio.php:254
msgid "Save 'other' values to the field's choices"
msgstr "Sauvegarder les valeurs « Autres » dans les choix des champs"

#: includes/fields/class-acf-field-radio.php:243
msgid "Add 'other' choice to allow for custom values"
msgstr "Ajouter le choix « Autre » pour autoriser les valeurs personnalisées"

#: includes/admin/views/global/navigation.php:202
msgid "Other"
msgstr "Autre"

#: includes/fields/class-acf-field-radio.php:22
msgid "Radio Button"
msgstr "Bouton radio"

#: includes/fields/class-acf-field-accordion.php:106
msgid ""
"Define an endpoint for the previous accordion to stop. This accordion will "
"not be visible."
msgstr ""
"Définir un point de terminaison pour l’accordéon précédent. Cet accordéon ne "
"sera pas visible."

#: includes/fields/class-acf-field-accordion.php:95
msgid "Allow this accordion to open without closing others."
msgstr "Autoriser l’ouverture de cet accordéon sans fermer les autres."

#: includes/fields/class-acf-field-accordion.php:94
msgid "Multi-Expand"
msgstr "Ouverture multiple"

#: includes/fields/class-acf-field-accordion.php:84
msgid "Display this accordion as open on page load."
msgstr "Ouvrir l’accordéon au chargement de la page."

#: includes/fields/class-acf-field-accordion.php:83
msgid "Open"
msgstr "Ouvrir"

#: includes/fields/class-acf-field-accordion.php:24
msgid "Accordion"
msgstr "Accordéon"

#: includes/fields/class-acf-field-file.php:253
#: includes/fields/class-acf-field-file.php:265
msgid "Restrict which files can be uploaded"
msgstr "Restreindre quels fichiers peuvent être téléversés"

#: includes/fields/class-acf-field-file.php:207
msgid "File ID"
msgstr "ID du fichier"

#: includes/fields/class-acf-field-file.php:206
msgid "File URL"
msgstr "URL du fichier"

#: includes/fields/class-acf-field-file.php:205
msgid "File Array"
msgstr "Tableau de fichiers"

#: includes/fields/class-acf-field-file.php:176
msgid "Add File"
msgstr "Ajouter un fichier"

#: includes/admin/tools/class-acf-admin-tool-import.php:151
#: includes/fields/class-acf-field-file.php:176
msgid "No file selected"
msgstr "Aucun fichier sélectionné"

#: includes/fields/class-acf-field-file.php:140
msgid "File name"
msgstr "Nom du fichier"

#: includes/fields/class-acf-field-file.php:57
msgid "Update File"
msgstr "Mettre à jour le fichier"

#: includes/fields/class-acf-field-file.php:56
msgid "Edit File"
msgstr "Modifier le fichier"

#: includes/admin/tools/class-acf-admin-tool-import.php:55
#: includes/fields/class-acf-field-file.php:55
msgid "Select File"
msgstr "Sélectionner un fichier"

#: includes/fields/class-acf-field-file.php:22
msgid "File"
msgstr "Fichier"

#: includes/fields/class-acf-field-password.php:22
msgid "Password"
msgstr "Mot de Passe"

#: includes/fields/class-acf-field-select.php:363
msgid "Specify the value returned"
msgstr "Spécifier la valeur retournée"

#: includes/fields/class-acf-field-select.php:431
msgid "Use AJAX to lazy load choices?"
msgstr "Utiliser AJAX pour un chargement différé des choix ?"

#: includes/fields/class-acf-field-checkbox.php:333
#: includes/fields/class-acf-field-select.php:352
msgid "Enter each default value on a new line"
msgstr "Saisir chaque valeur par défaut sur une nouvelle ligne"

#: includes/fields/class-acf-field-select.php:217 includes/media.php:48
msgctxt "verb"
msgid "Select"
msgstr "Sélectionner"

#: includes/fields/class-acf-field-select.php:95
msgctxt "Select2 JS load_fail"
msgid "Loading failed"
msgstr "Le chargement a échoué"

#: includes/fields/class-acf-field-select.php:94
msgctxt "Select2 JS searching"
msgid "Searching&hellip;"
msgstr "Recherche en cours..."

#: includes/fields/class-acf-field-select.php:93
msgctxt "Select2 JS load_more"
msgid "Loading more results&hellip;"
msgstr "Chargement de résultats supplémentaires…"

#. translators: %d - maximum number of items that can be selected in the select
#. field
#: includes/fields/class-acf-field-select.php:92
msgctxt "Select2 JS selection_too_long_n"
msgid "You can only select %d items"
msgstr "Vous ne pouvez choisir que %d éléments"

#: includes/fields/class-acf-field-select.php:90
msgctxt "Select2 JS selection_too_long_1"
msgid "You can only select 1 item"
msgstr "Vous ne pouvez choisir qu’un seul élément"

#. translators: %d - number of characters that should be removed from select
#. field
#: includes/fields/class-acf-field-select.php:89
msgctxt "Select2 JS input_too_long_n"
msgid "Please delete %d characters"
msgstr "Veuillez supprimer %d caractères"

#: includes/fields/class-acf-field-select.php:87
msgctxt "Select2 JS input_too_long_1"
msgid "Please delete 1 character"
msgstr "Veuillez supprimer 1 caractère"

#. translators: %d - number of characters to enter into select field input
#: includes/fields/class-acf-field-select.php:86
msgctxt "Select2 JS input_too_short_n"
msgid "Please enter %d or more characters"
msgstr "Veuillez saisir %d caractères ou plus"

#: includes/fields/class-acf-field-select.php:84
msgctxt "Select2 JS input_too_short_1"
msgid "Please enter 1 or more characters"
msgstr "Veuillez saisir au minimum 1 caractère"

#: includes/fields/class-acf-field-select.php:83
msgctxt "Select2 JS matches_0"
msgid "No matches found"
msgstr "Aucun résultat"

#. translators: %d - number of results available in select field
#: includes/fields/class-acf-field-select.php:82
msgctxt "Select2 JS matches_n"
msgid "%d results are available, use up and down arrow keys to navigate."
msgstr ""
"%d résultats disponibles, utilisez les flèches haut et bas pour naviguer "
"parmi ceux-ci."

#: includes/fields/class-acf-field-select.php:80
msgctxt "Select2 JS matches_1"
msgid "One result is available, press enter to select it."
msgstr ""
"Un résultat est disponible, appuyez sur « Entrée » pour le sélectionner."

#: includes/fields/class-acf-field-select.php:16
#: includes/fields/class-acf-field-taxonomy.php:699
msgctxt "noun"
msgid "Select"
msgstr "Liste déroulante"

#: includes/fields/class-acf-field-user.php:102
msgid "User ID"
msgstr "ID du compte"

#: includes/fields/class-acf-field-user.php:101
msgid "User Object"
msgstr "Objet du compte"

#: includes/fields/class-acf-field-user.php:100
msgid "User Array"
msgstr "Tableau de comptes"

#: includes/fields/class-acf-field-user.php:88
msgid "All user roles"
msgstr "Tous les rôles de comptes"

#: includes/fields/class-acf-field-user.php:80
msgid "Filter by Role"
msgstr "Filtrer par rôle"

#: includes/fields/class-acf-field-user.php:15 includes/locations.php:103
msgid "User"
msgstr "Compte"

#: includes/fields/class-acf-field-separator.php:22
msgid "Separator"
msgstr "Séparateur"

#: includes/fields/class-acf-field-color_picker.php:69
msgid "Select Color"
msgstr "Sélectionner une couleur"

#: includes/admin/post-types/admin-post-type.php:127
#: includes/admin/post-types/admin-taxonomy.php:129
#: includes/fields/class-acf-field-color_picker.php:67
msgid "Default"
msgstr "Par défaut"

#: includes/admin/views/acf-post-type/advanced-settings.php:89
#: includes/admin/views/acf-taxonomy/advanced-settings.php:141
#: includes/fields/class-acf-field-color_picker.php:65
msgid "Clear"
msgstr "Effacer"

#: includes/fields/class-acf-field-color_picker.php:22
msgid "Color Picker"
msgstr "Sélecteur de couleur"

#: includes/fields/class-acf-field-date_time_picker.php:82
msgctxt "Date Time Picker JS pmTextShort"
msgid "P"
msgstr "P"

#: includes/fields/class-acf-field-date_time_picker.php:81
msgctxt "Date Time Picker JS pmText"
msgid "PM"
msgstr "PM"

#: includes/fields/class-acf-field-date_time_picker.php:78
msgctxt "Date Time Picker JS amTextShort"
msgid "A"
msgstr "A"

#: includes/fields/class-acf-field-date_time_picker.php:77
msgctxt "Date Time Picker JS amText"
msgid "AM"
msgstr "AM"

#: includes/fields/class-acf-field-date_time_picker.php:75
msgctxt "Date Time Picker JS selectText"
msgid "Select"
msgstr "Sélectionner"

#: includes/fields/class-acf-field-date_time_picker.php:74
msgctxt "Date Time Picker JS closeText"
msgid "Done"
msgstr "Terminé"

#: includes/fields/class-acf-field-date_time_picker.php:73
msgctxt "Date Time Picker JS currentText"
msgid "Now"
msgstr "Maintenant"

#: includes/fields/class-acf-field-date_time_picker.php:72
msgctxt "Date Time Picker JS timezoneText"
msgid "Time Zone"
msgstr "Fuseau horaire"

#: includes/fields/class-acf-field-date_time_picker.php:71
msgctxt "Date Time Picker JS microsecText"
msgid "Microsecond"
msgstr "Microseconde"

#: includes/fields/class-acf-field-date_time_picker.php:70
msgctxt "Date Time Picker JS millisecText"
msgid "Millisecond"
msgstr "Milliseconde"

#: includes/fields/class-acf-field-date_time_picker.php:69
msgctxt "Date Time Picker JS secondText"
msgid "Second"
msgstr "Seconde"

#: includes/fields/class-acf-field-date_time_picker.php:68
msgctxt "Date Time Picker JS minuteText"
msgid "Minute"
msgstr "Minute"

#: includes/fields/class-acf-field-date_time_picker.php:67
msgctxt "Date Time Picker JS hourText"
msgid "Hour"
msgstr "Heure"

#: includes/fields/class-acf-field-date_time_picker.php:66
msgctxt "Date Time Picker JS timeText"
msgid "Time"
msgstr "Heure"

#: includes/fields/class-acf-field-date_time_picker.php:65
msgctxt "Date Time Picker JS timeOnlyTitle"
msgid "Choose Time"
msgstr "Choisir l’heure"

#: includes/fields/class-acf-field-date_time_picker.php:22
msgid "Date Time Picker"
msgstr "Sélecteur de date et heure"

#: includes/fields/class-acf-field-accordion.php:105
msgid "Endpoint"
msgstr "Point de terminaison"

#: includes/admin/views/acf-field-group/options.php:130
#: includes/fields/class-acf-field-tab.php:112
msgid "Left aligned"
msgstr "Aligné à gauche"

#: includes/admin/views/acf-field-group/options.php:129
#: includes/fields/class-acf-field-tab.php:111
msgid "Top aligned"
msgstr "Aligné en haut"

#: includes/fields/class-acf-field-tab.php:107
msgid "Placement"
msgstr "Positionnement"

#: includes/fields/class-acf-field-tab.php:23
msgid "Tab"
msgstr "Onglet"

#: includes/fields/class-acf-field-url.php:138
msgid "Value must be a valid URL"
msgstr "Le champ doit contenir une URL valide"

#: includes/fields/class-acf-field-link.php:153
msgid "Link URL"
msgstr "URL du lien"

#: includes/fields/class-acf-field-link.php:152
msgid "Link Array"
msgstr "Tableau de liens"

#: includes/fields/class-acf-field-link.php:124
msgid "Opens in a new window/tab"
msgstr "Ouvrir dans une nouvelle fenêtre/onglet"

#: includes/fields/class-acf-field-link.php:119
msgid "Select Link"
msgstr "Sélectionner un lien"

#: includes/fields/class-acf-field-link.php:22
msgid "Link"
msgstr "Lien"

#: includes/fields/class-acf-field-email.php:22
msgid "Email"
msgstr "E-mail"

#: includes/fields/class-acf-field-number.php:173
#: includes/fields/class-acf-field-range.php:206
msgid "Step Size"
msgstr "Taille de l’incrément"

#: includes/fields/class-acf-field-number.php:143
#: includes/fields/class-acf-field-range.php:184
msgid "Maximum Value"
msgstr "Valeur maximum"

#: includes/fields/class-acf-field-number.php:133
#: includes/fields/class-acf-field-range.php:173
msgid "Minimum Value"
msgstr "Valeur minimum"

#: includes/fields/class-acf-field-range.php:22
msgid "Range"
msgstr "Plage"

#: includes/fields/class-acf-field-button-group.php:165
#: includes/fields/class-acf-field-checkbox.php:350
#: includes/fields/class-acf-field-radio.php:210
#: includes/fields/class-acf-field-select.php:370
msgid "Both (Array)"
msgstr "Les deux (tableau)"

#: includes/admin/views/acf-field-group/fields.php:52
#: includes/fields/class-acf-field-button-group.php:164
#: includes/fields/class-acf-field-checkbox.php:349
#: includes/fields/class-acf-field-radio.php:209
#: includes/fields/class-acf-field-select.php:369
msgid "Label"
msgstr "Libellé"

#: includes/fields/class-acf-field-button-group.php:163
#: includes/fields/class-acf-field-checkbox.php:348
#: includes/fields/class-acf-field-radio.php:208
#: includes/fields/class-acf-field-select.php:368
msgid "Value"
msgstr "Valeur"

#: includes/fields/class-acf-field-button-group.php:211
#: includes/fields/class-acf-field-checkbox.php:411
#: includes/fields/class-acf-field-radio.php:282
msgid "Vertical"
msgstr "Vertical"

#: includes/fields/class-acf-field-button-group.php:210
#: includes/fields/class-acf-field-checkbox.php:412
#: includes/fields/class-acf-field-radio.php:283
msgid "Horizontal"
msgstr "Horizontal"

#: includes/fields/class-acf-field-button-group.php:138
#: includes/fields/class-acf-field-checkbox.php:323
#: includes/fields/class-acf-field-radio.php:183
#: includes/fields/class-acf-field-select.php:341
msgid "red : Red"
msgstr "rouge : Rouge"

#: includes/fields/class-acf-field-button-group.php:138
#: includes/fields/class-acf-field-checkbox.php:323
#: includes/fields/class-acf-field-radio.php:183
#: includes/fields/class-acf-field-select.php:341
msgid "For more control, you may specify both a value and label like this:"
msgstr ""
"Pour plus de contrôle, vous pouvez spécifier une valeur et un libellé de "
"cette manière :"

#: includes/fields/class-acf-field-button-group.php:138
#: includes/fields/class-acf-field-checkbox.php:323
#: includes/fields/class-acf-field-radio.php:183
#: includes/fields/class-acf-field-select.php:341
msgid "Enter each choice on a new line."
msgstr "Saisir chaque choix sur une nouvelle ligne."

#: includes/fields/class-acf-field-button-group.php:137
#: includes/fields/class-acf-field-checkbox.php:322
#: includes/fields/class-acf-field-radio.php:182
#: includes/fields/class-acf-field-select.php:340
msgid "Choices"
msgstr "Choix"

#: includes/fields/class-acf-field-button-group.php:23
msgid "Button Group"
msgstr "Groupe de boutons"

#: includes/fields/class-acf-field-button-group.php:183
#: includes/fields/class-acf-field-page_link.php:519
#: includes/fields/class-acf-field-post_object.php:432
#: includes/fields/class-acf-field-radio.php:228
#: includes/fields/class-acf-field-select.php:399
#: includes/fields/class-acf-field-taxonomy.php:708
#: includes/fields/class-acf-field-user.php:132
msgid "Allow Null"
msgstr "Autoriser une valeur vide"

#: includes/fields/class-acf-field-page_link.php:273
#: includes/fields/class-acf-field-post_object.php:254
#: includes/fields/class-acf-field-taxonomy.php:872
msgid "Parent"
msgstr "Parent"

#: includes/fields/class-acf-field-wysiwyg.php:367
msgid "TinyMCE will not be initialized until field is clicked"
msgstr "TinyMCE ne sera pas initialisé avant un clic dans le champ"

#: includes/fields/class-acf-field-wysiwyg.php:366
msgid "Delay Initialization"
msgstr "Retarder l’initialisation"

#: includes/fields/class-acf-field-wysiwyg.php:355
msgid "Show Media Upload Buttons"
msgstr "Afficher les boutons de téléversement de média"

#: includes/fields/class-acf-field-wysiwyg.php:339
msgid "Toolbar"
msgstr "Barre d’outils"

#: includes/fields/class-acf-field-wysiwyg.php:331
msgid "Text Only"
msgstr "Texte Uniquement"

#: includes/fields/class-acf-field-wysiwyg.php:330
msgid "Visual Only"
msgstr "Visuel uniquement"

#: includes/fields/class-acf-field-wysiwyg.php:329
msgid "Visual & Text"
msgstr "Visuel & texte"

#: includes/fields/class-acf-field-icon_picker.php:262
#: includes/fields/class-acf-field-wysiwyg.php:324
msgid "Tabs"
msgstr "Onglets"

#: includes/fields/class-acf-field-wysiwyg.php:268
msgid "Click to initialize TinyMCE"
msgstr "Cliquer pour initialiser TinyMCE"

#: includes/fields/class-acf-field-wysiwyg.php:262
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr "Texte"

#: includes/fields/class-acf-field-wysiwyg.php:261
msgid "Visual"
msgstr "Visuel"

#: includes/fields/class-acf-field-text.php:181
#: includes/fields/class-acf-field-textarea.php:217
msgid "Value must not exceed %d characters"
msgstr "La valeur ne doit pas excéder %d caractères"

#: includes/fields/class-acf-field-text.php:116
#: includes/fields/class-acf-field-textarea.php:114
msgid "Leave blank for no limit"
msgstr "Laisser vide pour ne fixer aucune limite"

#: includes/fields/class-acf-field-text.php:115
#: includes/fields/class-acf-field-textarea.php:113
msgid "Character Limit"
msgstr "Limite de caractères"

#: includes/fields/class-acf-field-email.php:144
#: includes/fields/class-acf-field-number.php:194
#: includes/fields/class-acf-field-password.php:95
#: includes/fields/class-acf-field-range.php:228
#: includes/fields/class-acf-field-text.php:156
msgid "Appears after the input"
msgstr "Apparait après le champ"

#: includes/fields/class-acf-field-email.php:143
#: includes/fields/class-acf-field-number.php:193
#: includes/fields/class-acf-field-password.php:94
#: includes/fields/class-acf-field-range.php:227
#: includes/fields/class-acf-field-text.php:155
msgid "Append"
msgstr "Ajouter après"

#: includes/fields/class-acf-field-email.php:134
#: includes/fields/class-acf-field-number.php:184
#: includes/fields/class-acf-field-password.php:85
#: includes/fields/class-acf-field-range.php:218
#: includes/fields/class-acf-field-text.php:146
msgid "Appears before the input"
msgstr "Apparait avant le champ"

#: includes/fields/class-acf-field-email.php:133
#: includes/fields/class-acf-field-number.php:183
#: includes/fields/class-acf-field-password.php:84
#: includes/fields/class-acf-field-range.php:217
#: includes/fields/class-acf-field-text.php:145
msgid "Prepend"
msgstr "Ajouter avant"

#: includes/fields/class-acf-field-email.php:124
#: includes/fields/class-acf-field-number.php:164
#: includes/fields/class-acf-field-password.php:75
#: includes/fields/class-acf-field-text.php:136
#: includes/fields/class-acf-field-textarea.php:146
#: includes/fields/class-acf-field-url.php:105
msgid "Appears within the input"
msgstr "Apparaît dans l’entrée"

#: includes/fields/class-acf-field-email.php:123
#: includes/fields/class-acf-field-number.php:163
#: includes/fields/class-acf-field-password.php:74
#: includes/fields/class-acf-field-text.php:135
#: includes/fields/class-acf-field-textarea.php:145
#: includes/fields/class-acf-field-url.php:104
msgid "Placeholder Text"
msgstr "Texte indicatif"

#: includes/fields/class-acf-field-button-group.php:148
#: includes/fields/class-acf-field-email.php:104
#: includes/fields/class-acf-field-number.php:114
#: includes/fields/class-acf-field-radio.php:193
#: includes/fields/class-acf-field-range.php:154
#: includes/fields/class-acf-field-text.php:96
#: includes/fields/class-acf-field-textarea.php:94
#: includes/fields/class-acf-field-url.php:85
#: includes/fields/class-acf-field-wysiwyg.php:292
msgid "Appears when creating a new post"
msgstr "Apparaît à la création d’une nouvelle publication"

#: includes/fields/class-acf-field-text.php:22
msgid "Text"
msgstr "Texte"

#: includes/fields/class-acf-field-relationship.php:753
msgid "%1$s requires at least %2$s selection"
msgid_plural "%1$s requires at least %2$s selections"
msgstr[0] "%1$s requiert au moins %2$s sélection"
msgstr[1] "%1$s requiert au moins %2$s sélections"

#: includes/fields/class-acf-field-post_object.php:402
#: includes/fields/class-acf-field-relationship.php:616
msgid "Post ID"
msgstr "ID de la publication"

#: includes/fields/class-acf-field-post_object.php:15
#: includes/fields/class-acf-field-post_object.php:401
#: includes/fields/class-acf-field-relationship.php:615
msgid "Post Object"
msgstr "Objet de la publication"

#: includes/fields/class-acf-field-relationship.php:648
msgid "Maximum Posts"
msgstr "Maximum de publications"

#: includes/fields/class-acf-field-relationship.php:638
msgid "Minimum Posts"
msgstr "Minimum de publications"

#: includes/admin/views/acf-field-group/options.php:183
#: includes/admin/views/acf-post-type/advanced-settings.php:29
#: includes/fields/class-acf-field-relationship.php:673
msgid "Featured Image"
msgstr "Image mise en avant"

#: includes/fields/class-acf-field-relationship.php:669
msgid "Selected elements will be displayed in each result"
msgstr "Les éléments sélectionnés seront affichés dans chaque résultat"

#: includes/fields/class-acf-field-relationship.php:668
msgid "Elements"
msgstr "Éléments"

#: includes/fields/class-acf-field-relationship.php:602
#: includes/fields/class-acf-field-taxonomy.php:20
#: includes/fields/class-acf-field-taxonomy.php:628
#: includes/locations/class-acf-location-taxonomy.php:22
msgid "Taxonomy"
msgstr "Taxonomie"

#: includes/fields/class-acf-field-relationship.php:601
#: includes/locations/class-acf-location-post-type.php:22
#: includes/post-types/class-acf-post-type.php:92
msgid "Post Type"
msgstr "Type de publication"

#: includes/fields/class-acf-field-relationship.php:595
msgid "Filters"
msgstr "Filtres"

#: includes/fields/class-acf-field-page_link.php:480
#: includes/fields/class-acf-field-post_object.php:389
#: includes/fields/class-acf-field-relationship.php:588
msgid "All taxonomies"
msgstr "Toutes les taxonomies"

#: includes/fields/class-acf-field-page_link.php:472
#: includes/fields/class-acf-field-post_object.php:381
#: includes/fields/class-acf-field-relationship.php:580
msgid "Filter by Taxonomy"
msgstr "Filtrer par taxonomie"

#: includes/fields/class-acf-field-page_link.php:450
#: includes/fields/class-acf-field-post_object.php:359
#: includes/fields/class-acf-field-relationship.php:558
msgid "All post types"
msgstr "Tous les types de publication"

#: includes/fields/class-acf-field-page_link.php:442
#: includes/fields/class-acf-field-post_object.php:351
#: includes/fields/class-acf-field-relationship.php:550
msgid "Filter by Post Type"
msgstr "Filtrer par type de publication"

#: includes/fields/class-acf-field-relationship.php:450
msgid "Search..."
msgstr "Rechercher…"

#: includes/fields/class-acf-field-relationship.php:380
msgid "Select taxonomy"
msgstr "Sélectionner la taxonomie"

#: includes/fields/class-acf-field-relationship.php:372
msgid "Select post type"
msgstr "Choisissez le type de publication"

#: includes/fields/class-acf-field-relationship.php:78
msgid "No matches found"
msgstr "Aucune correspondance trouvée"

#: includes/fields/class-acf-field-relationship.php:77
msgid "Loading"
msgstr "Chargement"

#: includes/fields/class-acf-field-relationship.php:76
msgid "Maximum values reached ( {max} values )"
msgstr "Valeurs maximum atteintes ({max} valeurs)"

#: includes/fields/class-acf-field-relationship.php:17
msgid "Relationship"
msgstr "Relation"

#: includes/fields/class-acf-field-file.php:277
#: includes/fields/class-acf-field-image.php:307
msgid "Comma separated list. Leave blank for all types"
msgstr ""
"Séparez les valeurs par une virgule. Laissez blanc pour tout autoriser."

#: includes/fields/class-acf-field-file.php:276
#: includes/fields/class-acf-field-image.php:306
msgid "Allowed File Types"
msgstr "Types de fichiers autorisés"

#: includes/fields/class-acf-field-file.php:264
#: includes/fields/class-acf-field-image.php:270
msgid "Maximum"
msgstr "Maximum"

#: includes/fields/class-acf-field-file.php:144
#: includes/fields/class-acf-field-file.php:256
#: includes/fields/class-acf-field-file.php:268
#: includes/fields/class-acf-field-image.php:261
#: includes/fields/class-acf-field-image.php:297
msgid "File size"
msgstr "Taille du fichier"

#: includes/fields/class-acf-field-image.php:235
#: includes/fields/class-acf-field-image.php:271
msgid "Restrict which images can be uploaded"
msgstr "Restreindre quelles images peuvent être téléversées"

#: includes/fields/class-acf-field-file.php:252
#: includes/fields/class-acf-field-image.php:234
msgid "Minimum"
msgstr "Minimum"

#: includes/fields/class-acf-field-file.php:222
#: includes/fields/class-acf-field-image.php:200
msgid "Uploaded to post"
msgstr "Téléversé dans la publication"

#: includes/fields/class-acf-field-file.php:221
#: includes/fields/class-acf-field-image.php:199
#: includes/locations/class-acf-location-attachment.php:73
#: includes/locations/class-acf-location-comment.php:61
#: includes/locations/class-acf-location-nav-menu.php:74
#: includes/locations/class-acf-location-taxonomy.php:63
#: includes/locations/class-acf-location-user-form.php:71
#: includes/locations/class-acf-location-user-role.php:78
#: includes/locations/class-acf-location-widget.php:65
msgid "All"
msgstr "Tous"

#: includes/fields/class-acf-field-file.php:216
#: includes/fields/class-acf-field-image.php:194
msgid "Limit the media library choice"
msgstr "Limiter le choix de la médiathèque"

#: includes/fields/class-acf-field-file.php:215
#: includes/fields/class-acf-field-image.php:193
msgid "Library"
msgstr "Bibliothèque"

#: includes/fields/class-acf-field-image.php:326
msgid "Preview Size"
msgstr "Taille de prévisualisation"

#: includes/fields/class-acf-field-image.php:185
msgid "Image ID"
msgstr "ID de l’image"

#: includes/fields/class-acf-field-image.php:184
msgid "Image URL"
msgstr "URL de l’image"

#: includes/fields/class-acf-field-image.php:183
msgid "Image Array"
msgstr "Tableau de l’image"

#: includes/fields/class-acf-field-button-group.php:158
#: includes/fields/class-acf-field-checkbox.php:343
#: includes/fields/class-acf-field-file.php:200
#: includes/fields/class-acf-field-link.php:147
#: includes/fields/class-acf-field-radio.php:203
msgid "Specify the returned value on front end"
msgstr "Spécifier la valeur renvoyée publiquement"

#: includes/fields/class-acf-field-button-group.php:157
#: includes/fields/class-acf-field-checkbox.php:342
#: includes/fields/class-acf-field-file.php:199
#: includes/fields/class-acf-field-link.php:146
#: includes/fields/class-acf-field-radio.php:202
#: includes/fields/class-acf-field-taxonomy.php:672
msgid "Return Value"
msgstr "Valeur de retour"

#: includes/fields/class-acf-field-image.php:155
msgid "Add Image"
msgstr "Ajouter image"

#: includes/fields/class-acf-field-image.php:155
msgid "No image selected"
msgstr "Aucune image sélectionnée"

#: includes/assets.php:353 includes/fields/class-acf-field-file.php:152
#: includes/fields/class-acf-field-image.php:135
#: includes/fields/class-acf-field-link.php:124
msgid "Remove"
msgstr "Retirer"

#: includes/admin/views/acf-field-group/field.php:89
#: includes/fields/class-acf-field-file.php:150
#: includes/fields/class-acf-field-image.php:133
#: includes/fields/class-acf-field-link.php:124
msgid "Edit"
msgstr "Modifier"

#: includes/fields/class-acf-field-image.php:63 includes/media.php:55
msgid "All images"
msgstr "Toutes les images"

#: includes/fields/class-acf-field-image.php:62
msgid "Update Image"
msgstr "Mettre à jour l’image"

#: includes/fields/class-acf-field-image.php:61
msgid "Edit Image"
msgstr "Modifier l’image"

#: includes/fields/class-acf-field-image.php:60
msgid "Select Image"
msgstr "Sélectionner une image"

#: includes/fields/class-acf-field-image.php:22
msgid "Image"
msgstr "Image"

#: includes/fields/class-acf-field-message.php:113
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr "Permet l’affichage du code HTML à l’écran au lieu de l’interpréter"

#: includes/fields/class-acf-field-message.php:112
msgid "Escape HTML"
msgstr "Autoriser le code HTML"

#: includes/fields/class-acf-field-message.php:104
#: includes/fields/class-acf-field-textarea.php:162
msgid "No Formatting"
msgstr "Aucun formatage"

#: includes/fields/class-acf-field-message.php:103
#: includes/fields/class-acf-field-textarea.php:161
msgid "Automatically add &lt;br&gt;"
msgstr "Ajouter automatiquement &lt;br&gt;"

#: includes/fields/class-acf-field-message.php:102
#: includes/fields/class-acf-field-textarea.php:160
msgid "Automatically add paragraphs"
msgstr "Ajouter automatiquement des paragraphes"

#: includes/fields/class-acf-field-message.php:98
#: includes/fields/class-acf-field-textarea.php:156
msgid "Controls how new lines are rendered"
msgstr "Contrôle comment les nouvelles lignes sont rendues"

#: includes/fields/class-acf-field-message.php:97
#: includes/fields/class-acf-field-textarea.php:155
msgid "New Lines"
msgstr "Nouvelles lignes"

#: includes/fields/class-acf-field-date_picker.php:221
#: includes/fields/class-acf-field-date_time_picker.php:208
msgid "Week Starts On"
msgstr "La semaine débute le"

#: includes/fields/class-acf-field-date_picker.php:190
msgid "The format used when saving a value"
msgstr "Le format utilisé lors de la sauvegarde d’une valeur"

#: includes/fields/class-acf-field-date_picker.php:189
msgid "Save Format"
msgstr "Enregistrer le format"

#: includes/fields/class-acf-field-date_picker.php:61
msgctxt "Date Picker JS weekHeader"
msgid "Wk"
msgstr "Wk"

#: includes/fields/class-acf-field-date_picker.php:60
msgctxt "Date Picker JS prevText"
msgid "Prev"
msgstr "Préc."

#: includes/fields/class-acf-field-date_picker.php:59
msgctxt "Date Picker JS nextText"
msgid "Next"
msgstr "Suivant"

#: includes/fields/class-acf-field-date_picker.php:58
msgctxt "Date Picker JS currentText"
msgid "Today"
msgstr "Aujourd’hui"

#: includes/fields/class-acf-field-date_picker.php:57
msgctxt "Date Picker JS closeText"
msgid "Done"
msgstr "Terminé"

#: includes/fields/class-acf-field-date_picker.php:22
msgid "Date Picker"
msgstr "Sélecteur de date"

#: includes/fields/class-acf-field-image.php:238
#: includes/fields/class-acf-field-image.php:274
#: includes/fields/class-acf-field-oembed.php:241
msgid "Width"
msgstr "Largeur"

#: includes/fields/class-acf-field-oembed.php:238
#: includes/fields/class-acf-field-oembed.php:250
msgid "Embed Size"
msgstr "Taille d’intégration"

#: includes/fields/class-acf-field-oembed.php:198
msgid "Enter URL"
msgstr "Saisissez l’URL"

#: includes/fields/class-acf-field-oembed.php:22
msgid "oEmbed"
msgstr "Contenu oEmbed"

#: includes/fields/class-acf-field-true_false.php:172
msgid "Text shown when inactive"
msgstr "Texte affiché lorsque inactif"

#: includes/fields/class-acf-field-true_false.php:171
msgid "Off Text"
msgstr "Texte « Inactif »"

#: includes/fields/class-acf-field-true_false.php:156
msgid "Text shown when active"
msgstr "Texte affiché lorsque actif"

#: includes/fields/class-acf-field-true_false.php:155
msgid "On Text"
msgstr "Texte « Actif »"

#: includes/fields/class-acf-field-select.php:420
#: includes/fields/class-acf-field-true_false.php:187
msgid "Stylized UI"
msgstr "Interface (UI) stylisée"

#: includes/fields/class-acf-field-button-group.php:147
#: includes/fields/class-acf-field-checkbox.php:332
#: includes/fields/class-acf-field-color_picker.php:144
#: includes/fields/class-acf-field-email.php:103
#: includes/fields/class-acf-field-number.php:113
#: includes/fields/class-acf-field-radio.php:192
#: includes/fields/class-acf-field-range.php:153
#: includes/fields/class-acf-field-select.php:351
#: includes/fields/class-acf-field-text.php:95
#: includes/fields/class-acf-field-textarea.php:93
#: includes/fields/class-acf-field-true_false.php:135
#: includes/fields/class-acf-field-url.php:84
#: includes/fields/class-acf-field-wysiwyg.php:291
msgid "Default Value"
msgstr "Valeur par défaut"

#: includes/fields/class-acf-field-true_false.php:126
msgid "Displays text alongside the checkbox"
msgstr "Affiche le texte à côté de la case à cocher"

#: includes/fields/class-acf-field-message.php:23
#: includes/fields/class-acf-field-message.php:87
#: includes/fields/class-acf-field-true_false.php:125
msgid "Message"
msgstr "Message"

#: includes/assets.php:352 includes/fields/class-acf-field-true_false.php:79
#: includes/fields/class-acf-field-true_false.php:175
#: src/Site_Health/Site_Health.php:281 src/Site_Health/Site_Health.php:343
msgid "No"
msgstr "Non"

#: includes/assets.php:351 includes/fields/class-acf-field-true_false.php:76
#: includes/fields/class-acf-field-true_false.php:159
#: src/Site_Health/Site_Health.php:280 src/Site_Health/Site_Health.php:343
msgid "Yes"
msgstr "Oui"

#: includes/fields/class-acf-field-true_false.php:22
msgid "True / False"
msgstr "Vrai/Faux"

#: includes/fields/class-acf-field-group.php:415
msgid "Row"
msgstr "Ligne"

#: includes/fields/class-acf-field-group.php:414
msgid "Table"
msgstr "Tableau"

#: includes/admin/post-types/admin-field-group.php:158
#: includes/fields/class-acf-field-group.php:413
msgid "Block"
msgstr "Bloc"

#: includes/fields/class-acf-field-group.php:408
msgid "Specify the style used to render the selected fields"
msgstr "Spécifier le style utilisé pour afficher les champs sélectionnés"

#: includes/fields.php:330 includes/fields/class-acf-field-button-group.php:204
#: includes/fields/class-acf-field-checkbox.php:405
#: includes/fields/class-acf-field-group.php:407
#: includes/fields/class-acf-field-radio.php:276
msgid "Layout"
msgstr "Mise en page"

#: includes/fields/class-acf-field-group.php:391
msgid "Sub Fields"
msgstr "Sous-champs"

#: includes/fields/class-acf-field-group.php:22
msgid "Group"
msgstr "Groupe"

#: includes/fields/class-acf-field-google-map.php:222
msgid "Customize the map height"
msgstr "Personnaliser la hauteur de la carte"

#: includes/fields/class-acf-field-google-map.php:221
#: includes/fields/class-acf-field-image.php:249
#: includes/fields/class-acf-field-image.php:285
#: includes/fields/class-acf-field-oembed.php:253
msgid "Height"
msgstr "Hauteur"

#: includes/fields/class-acf-field-google-map.php:210
msgid "Set the initial zoom level"
msgstr "Définir le niveau de zoom initial"

#: includes/fields/class-acf-field-google-map.php:209
msgid "Zoom"
msgstr "Zoom"

#: includes/fields/class-acf-field-google-map.php:183
#: includes/fields/class-acf-field-google-map.php:196
msgid "Center the initial map"
msgstr "Centrer la carte initiale"

#: includes/fields/class-acf-field-google-map.php:182
#: includes/fields/class-acf-field-google-map.php:195
msgid "Center"
msgstr "Centrer"

#: includes/fields/class-acf-field-google-map.php:154
msgid "Search for address..."
msgstr "Rechercher une adresse…"

#: includes/fields/class-acf-field-google-map.php:151
msgid "Find current location"
msgstr "Obtenir l’emplacement actuel"

#: includes/fields/class-acf-field-google-map.php:150
msgid "Clear location"
msgstr "Effacer la position"

#: includes/fields/class-acf-field-google-map.php:149
#: includes/fields/class-acf-field-relationship.php:600
msgid "Search"
msgstr "Rechercher"

#: includes/fields/class-acf-field-google-map.php:57
msgid "Sorry, this browser does not support geolocation"
msgstr "Désolé, ce navigateur ne prend pas en charge la géolocalisation"

#: includes/fields/class-acf-field-google-map.php:22
msgid "Google Map"
msgstr "Google Map"

#: includes/fields/class-acf-field-date_picker.php:201
#: includes/fields/class-acf-field-date_time_picker.php:189
#: includes/fields/class-acf-field-time_picker.php:122
msgid "The format returned via template functions"
msgstr "Le format retourné via les fonctions du modèle"

#: includes/fields/class-acf-field-color_picker.php:168
#: includes/fields/class-acf-field-date_picker.php:200
#: includes/fields/class-acf-field-date_time_picker.php:188
#: includes/fields/class-acf-field-icon_picker.php:285
#: includes/fields/class-acf-field-image.php:177
#: includes/fields/class-acf-field-post_object.php:396
#: includes/fields/class-acf-field-relationship.php:610
#: includes/fields/class-acf-field-select.php:362
#: includes/fields/class-acf-field-time_picker.php:121
#: includes/fields/class-acf-field-user.php:95
msgid "Return Format"
msgstr "Format de retour"

#: includes/fields/class-acf-field-date_picker.php:179
#: includes/fields/class-acf-field-date_picker.php:210
#: includes/fields/class-acf-field-date_time_picker.php:180
#: includes/fields/class-acf-field-date_time_picker.php:198
#: includes/fields/class-acf-field-time_picker.php:113
#: includes/fields/class-acf-field-time_picker.php:129
msgid "Custom:"
msgstr "Personnalisé :"

#: includes/fields/class-acf-field-date_picker.php:171
#: includes/fields/class-acf-field-date_time_picker.php:171
#: includes/fields/class-acf-field-time_picker.php:106
msgid "The format displayed when editing a post"
msgstr "Le format affiché lors de la modification d’une publication"

#: includes/fields/class-acf-field-date_picker.php:170
#: includes/fields/class-acf-field-date_time_picker.php:170
#: includes/fields/class-acf-field-time_picker.php:105
msgid "Display Format"
msgstr "Format d’affichage"

#: includes/fields/class-acf-field-time_picker.php:22
msgid "Time Picker"
msgstr "Sélecteur d’heure"

#. translators: counts for inactive field groups
#: acf.php:526
msgid "Inactive <span class=\"count\">(%s)</span>"
msgid_plural "Inactive <span class=\"count\">(%s)</span>"
msgstr[0] "<span class=\"count\">(%s)</span> inactif"
msgstr[1] "<span class=\"count\">(%s)</span> inactifs"

#: acf.php:487
msgid "No Fields found in Trash"
msgstr "Aucun champ trouvé dans la corbeille"

#: acf.php:486
msgid "No Fields found"
msgstr "Aucun champ trouvé"

#: acf.php:485
msgid "Search Fields"
msgstr "Rechercher des champs"

#: acf.php:484
msgid "View Field"
msgstr "Voir le champ"

#: acf.php:483 includes/admin/views/acf-field-group/fields.php:113
msgid "New Field"
msgstr "Nouveau champ"

#: acf.php:482
msgid "Edit Field"
msgstr "Modifier le champ"

#: acf.php:481
msgid "Add New Field"
msgstr "Ajouter un nouveau champ"

#: acf.php:479
msgid "Field"
msgstr "Champ"

#: acf.php:478 includes/admin/post-types/admin-field-group.php:179
#: includes/admin/post-types/admin-field-groups.php:93
#: includes/admin/views/acf-field-group/fields.php:32
msgid "Fields"
msgstr "Champs"

#: acf.php:453
msgid "No Field Groups found in Trash"
msgstr "Aucun groupe de champs trouvé dans la corbeille"

#: acf.php:452
msgid "No Field Groups found"
msgstr "Aucun groupe de champs trouvé"

#: acf.php:451
msgid "Search Field Groups"
msgstr "Rechercher des groupes de champs"

#: acf.php:450
msgid "View Field Group"
msgstr "Voir le groupe de champs"

#: acf.php:449
msgid "New Field Group"
msgstr "Nouveau groupe de champs"

#: acf.php:448
msgid "Edit Field Group"
msgstr "Modifier le groupe de champs"

#: acf.php:447
msgid "Add New Field Group"
msgstr "Ajouter un groupe de champs"

#: acf.php:446 acf.php:480
#: includes/admin/views/acf-post-type/advanced-settings.php:224
#: includes/post-types/class-acf-post-type.php:93
#: includes/post-types/class-acf-taxonomy.php:92
msgid "Add New"
msgstr "Ajouter"

#: acf.php:445
msgid "Field Group"
msgstr "Groupe de champs"

#: acf.php:444 includes/admin/post-types/admin-field-groups.php:55
#: includes/admin/post-types/admin-post-types.php:113
#: includes/admin/post-types/admin-taxonomies.php:112
msgid "Field Groups"
msgstr "Groupes de champs"

#. Description of the plugin
#: acf.php
msgid "Customize WordPress with powerful, professional and intuitive fields."
msgstr ""
"Personnalisez WordPress avec des champs intuitifs, puissants et "
"professionnels."

#. Plugin URI of the plugin
#: acf.php
msgid "https://www.advancedcustomfields.com"
msgstr "https://www.advancedcustomfields.com/"

#. Plugin Name of the plugin
#: acf.php acf.php:290
msgid "Advanced Custom Fields"
msgstr "Advanced Custom Fields"

# @ acf
#: pro/acf-pro.php:27
msgid "Advanced Custom Fields PRO"
msgstr "Advanced Custom Fields PRO"

#: pro/blocks.php:170
msgid "Block type name is required."
msgstr "Le nom du type de bloc est obligatoire."

#. translators: The name of the block type
#: pro/blocks.php:178
msgid "Block type \"%s\" is already registered."
msgstr "Le type de bloc \"%s\" est déjà déclaré."

#: pro/blocks.php:726
msgid "Switch to Edit"
msgstr "Passer en mode Édition"

#: pro/blocks.php:727
msgid "Switch to Preview"
msgstr "Passer en mode Aperçu"

#: pro/blocks.php:728
msgid "Change content alignment"
msgstr "Modifier l’alignement du contenu"

#. translators: %s: Block type title
#: pro/blocks.php:731
msgid "%s settings"
msgstr "Réglages de %s"

#: pro/blocks.php:936
msgid "This block contains no editable fields."
msgstr "Ce bloc ne contient aucun champ éditable."

#. translators: %s: an admin URL to the field group edit screen
#: pro/blocks.php:942
msgid ""
"Assign a <a href=\"%s\" target=\"_blank\">field group</a> to add fields to "
"this block."
msgstr ""
"Assignez un <a href=\"%s\" target=\"_blank\">groupe de champs</a> pour "
"ajouter des champs à ce bloc."

# @ acf
#: pro/options-page.php:78
msgid "Options Updated"
msgstr "Options mises à jour"

#: pro/updates.php:99
msgid ""
"To enable updates, please enter your license key on the <a "
"href=\"%1$s\">Updates</a> page. If you don't have a licence key, please see "
"<a href=\"%2$s\" target=\"_blank\">details & pricing</a>."
msgstr ""
"Pour activer les mises à jour, veuillez indiquer votre clé de licence sur la "
"page <a href=\"%1$s\">Mises à jour</a>. Si vous n’en possédez pas encore "
"une, jetez un oeil à nos <a href=\"%2$s\" target=\"_blank\">détails & "
"tarifs</a>."

#: pro/updates.php:159
msgid ""
"<b>ACF Activation Error</b>. Your defined license key has changed, but an "
"error occurred when deactivating your old licence"
msgstr ""
"<b>Erreur d’activation d’ACF</b>. Votre clé de licence a été modifiée, mais "
"une erreur est survenue lors de la désactivation de votre précédente licence"

#: pro/updates.php:154
msgid ""
"<b>ACF Activation Error</b>. Your defined license key has changed, but an "
"error occurred when connecting to activation server"
msgstr ""
"<b>Erreur d’activation d’ACF</b>. Votre clé de licence définie a été "
"modifiée, mais une erreur est survenue lors de la connexion au serveur "
"d’activation"

#: pro/updates.php:192
msgid "<b>ACF Activation Error</b>"
msgstr "<b>Erreur d’activation d’ACF</b>"

#: pro/updates.php:187
msgid ""
"<b>ACF Activation Error</b>. An error occurred when connecting to activation "
"server"
msgstr ""
"<b>Erreur d’activation d’ACF</b>. Une erreur est survenue lors de la "
"connexion au serveur d’activation"

#: pro/updates.php:279
msgid "Check Again"
msgstr "Vérifier à nouveau"

#: pro/updates.php:593
msgid "<b>ACF Activation Error</b>. Could not connect to activation server"
msgstr ""
"<b>Erreur d’activation d’ACF</b>. Impossible de se connecter au serveur "
"d’activation"

#: pro/admin/admin-options-page.php:195
msgid "Publish"
msgstr "Publier"

# @ default
#: pro/admin/admin-options-page.php:199
msgid ""
"No Custom Field Groups found for this options page. <a href=\"%s\">Create a "
"Custom Field Group</a>"
msgstr ""
"Aucun groupe de champs trouvé pour cette page options. <a href=\"%s\">Créer "
"un groupe de champs</a>"

#: pro/admin/admin-updates.php:52
msgid "<b>Error</b>. Could not connect to update server"
msgstr "<b>Erreur</b>. Impossible de joindre le serveur"

#: pro/admin/admin-updates.php:212
msgid ""
"<b>Error</b>. Could not authenticate update package. Please check again or "
"deactivate and reactivate your ACF PRO license."
msgstr ""
"<b>Erreur</b>. Impossible d’authentifier la mise à jour. Merci d’essayer à "
"nouveau et si le problème persiste, désactivez et réactivez votre licence "
"ACF PRO."

#: pro/admin/admin-updates.php:199
msgid ""
"<b>Error</b>. Your license for this site has expired or been deactivated. "
"Please reactivate your ACF PRO license."
msgstr ""
"<b>Erreur</b>. La licence pour ce site a expiré ou a été désactivée. "
"Veuillez réactiver votre licence ACF PRO."

#: pro/fields/class-acf-field-clone.php:27,
#: pro/fields/class-acf-field-repeater.php:31
msgid ""
"Allows you to select and display existing fields. It does not duplicate any "
"fields in the database, but loads and displays the selected fields at run-"
"time. The Clone field can either replace itself with the selected fields or "
"display the selected fields as a group of subfields."
msgstr ""
"Vous permet de sélectionner et afficher des champs existants. Le clone ne "
"duplique pas les champs dans la base de données, il récupère leurs valeurs "
"au chargement de la page. Le Clone sera remplacé par les champs qu’il "
"représente et les affiche comme un groupe de sous-champs."

#: pro/fields/class-acf-field-clone.php:819
msgid "Select one or more fields you wish to clone"
msgstr "Sélectionnez un ou plusieurs champs à cloner"

# @ acf
#: pro/fields/class-acf-field-clone.php:838
msgid "Display"
msgstr "Format d’affichage"

#: pro/fields/class-acf-field-clone.php:839
msgid "Specify the style used to render the clone field"
msgstr "Définit le style utilisé pour générer le champ dupliqué"

#: pro/fields/class-acf-field-clone.php:844
msgid "Group (displays selected fields in a group within this field)"
msgstr ""
"Groupe (affiche les champs sélectionnés dans un groupe à l’intérieur de ce "
"champ)"

#: pro/fields/class-acf-field-clone.php:845
msgid "Seamless (replaces this field with selected fields)"
msgstr "Remplace ce champ par les champs sélectionnés"

#: pro/fields/class-acf-field-clone.php:868
msgid "Labels will be displayed as %s"
msgstr "Les libellés seront affichés en tant que %s"

#: pro/fields/class-acf-field-clone.php:873
msgid "Prefix Field Labels"
msgstr "Préfixer les libellés de champs"

#: pro/fields/class-acf-field-clone.php:883
msgid "Values will be saved as %s"
msgstr "Les valeurs seront enregistrées en tant que %s"

#: pro/fields/class-acf-field-clone.php:888
msgid "Prefix Field Names"
msgstr "Préfixer les noms de champs"

#: pro/fields/class-acf-field-clone.php:1005
msgid "Unknown field"
msgstr "Champ inconnu"

#: pro/fields/class-acf-field-clone.php:1042
msgid "Unknown field group"
msgstr "Groupe de champ inconnu"

#: pro/fields/class-acf-field-clone.php:1046
msgid "All fields from %s field group"
msgstr "Tous les champs du groupe %s"

#: pro/fields/class-acf-field-flexible-content.php:27
msgid ""
"Allows you to define, create and manage content with total control by "
"creating layouts that contain subfields that content editors can choose from."
msgstr ""
"Vous permet de définir, créer et gérer des contenus avec un contrôle total : "
"les éditeurs peuvent créer des mises en page en sélectionnant des "
"dispositions basées sur des sous-champs."

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:36,
#: pro/fields/class-acf-field-repeater.php:103,
#: pro/fields/class-acf-field-repeater.php:297
msgid "Add Row"
msgstr "Ajouter un élément"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:76,
#: pro/fields/class-acf-field-flexible-content.php:943,
#: pro/fields/class-acf-field-flexible-content.php:1022
msgid "layout"
msgid_plural "layouts"
msgstr[0] "disposition"
msgstr[1] "dispositions"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:77
msgid "layouts"
msgstr "dispositions"

#: pro/fields/class-acf-field-flexible-content.php:81,
#: pro/fields/class-acf-field-flexible-content.php:942,
#: pro/fields/class-acf-field-flexible-content.php:1021
msgid "This field requires at least {min} {label} {identifier}"
msgstr "Ce champ requiert au moins {min} {label} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:82
msgid "This field has a limit of {max} {label} {identifier}"
msgstr "Ce champ a une limite de {max} {label} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:85
msgid "{available} {label} {identifier} available (max {max})"
msgstr "{available} {label} {identifier} disponible (max {max})"

#: pro/fields/class-acf-field-flexible-content.php:86
msgid "{required} {label} {identifier} required (min {min})"
msgstr "{required} {label} {identifier} required (min {min})"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:89
msgid "Flexible Content requires at least 1 layout"
msgstr "Le contenu flexible nécessite au moins une disposition"

#: pro/fields/class-acf-field-flexible-content.php:282
msgid "Click the \"%s\" button below to start creating your layout"
msgstr ""
"Cliquez sur le bouton \"%s\" ci-dessous pour créer votre première disposition"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:423
msgid "Add layout"
msgstr "Ajouter une disposition"

#: pro/fields/class-acf-field-flexible-content.php:424
msgid "Duplicate layout"
msgstr "Dupliquer la disposition"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:425
msgid "Remove layout"
msgstr "Retirer la disposition"

#: pro/fields/class-acf-field-flexible-content.php:426,
#: pro/fields/class-acf-repeater-table.php:382
msgid "Click to toggle"
msgstr "Cliquer pour afficher/cacher"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:562
msgid "Delete Layout"
msgstr "Supprimer la disposition"

#: pro/fields/class-acf-field-flexible-content.php:563
msgid "Duplicate Layout"
msgstr "Dupliquer la disposition"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:564
msgid "Add New Layout"
msgstr "Ajouter une disposition"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:564
msgid "Add Layout"
msgstr "Ajouter une disposition"

#: pro/fields/class-acf-field-flexible-content.php:647
msgid "Min"
msgstr "Min"

#: pro/fields/class-acf-field-flexible-content.php:662
msgid "Max"
msgstr "Max"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:705
msgid "Minimum Layouts"
msgstr "Nombre minimum de dispositions"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:716
msgid "Maximum Layouts"
msgstr "Nombre maximum de dispositions"

#: pro/fields/class-acf-field-flexible-content.php:727,
#: pro/fields/class-acf-field-repeater.php:293
msgid "Button Label"
msgstr "Intitulé du bouton"

#: pro/fields/class-acf-field-flexible-content.php:1710,
#: pro/fields/class-acf-field-repeater.php:918
msgid "%s must be of type array or null."
msgstr "la valeur de %s doit être un tableau ou null."

#: pro/fields/class-acf-field-flexible-content.php:1721
msgid "%1$s must contain at least %2$s %3$s layout."
msgid_plural "%1$s must contain at least %2$s %3$s layouts."
msgstr[0] "Le champ %1$s doit contenir au moins %2$s %3$s disposition."
msgstr[1] "Le champ %1$s doit contenir au moins %2$s %3$s dispositions."

#: pro/fields/class-acf-field-flexible-content.php:1737
msgid "%1$s must contain at most %2$s %3$s layout."
msgid_plural "%1$s must contain at most %2$s %3$s layouts."
msgstr[0] "Le champ %1$s doit contenir au maximum %2$s %3$s disposition."
msgstr[1] "Le champ %1$s doit contenir au maximum %2$s %3$s dispositions."

#: pro/fields/class-acf-field-gallery.php:27
msgid ""
"An interactive interface for managing a collection of attachments, such as "
"images."
msgstr ""
"Une interface interactive pour gérer une collection de fichiers joints, "
"telles que des images."

# @ acf
#: pro/fields/class-acf-field-gallery.php:77
msgid "Add Image to Gallery"
msgstr "Ajouter l’image à la galerie"

#: pro/fields/class-acf-field-gallery.php:78
msgid "Maximum selection reached"
msgstr "Nombre de sélections maximales atteint"

#: pro/fields/class-acf-field-gallery.php:324
msgid "Length"
msgstr "Longueur"

#: pro/fields/class-acf-field-gallery.php:368
msgid "Caption"
msgstr "Légende"

#: pro/fields/class-acf-field-gallery.php:380
msgid "Alt Text"
msgstr "Texte alternatif"

#: pro/fields/class-acf-field-gallery.php:504
msgid "Add to gallery"
msgstr "Ajouter à la galerie"

# @ acf
#: pro/fields/class-acf-field-gallery.php:508
msgid "Bulk actions"
msgstr "Actions de groupe"

#: pro/fields/class-acf-field-gallery.php:509
msgid "Sort by date uploaded"
msgstr "Ranger par date d’import"

#: pro/fields/class-acf-field-gallery.php:510
msgid "Sort by date modified"
msgstr "Ranger par date de modification"

# @ acf
#: pro/fields/class-acf-field-gallery.php:511
msgid "Sort by title"
msgstr "Ranger par titre"

#: pro/fields/class-acf-field-gallery.php:512
msgid "Reverse current order"
msgstr "Inverser l’ordre actuel"

# @ acf
#: pro/fields/class-acf-field-gallery.php:524
msgid "Close"
msgstr "Appliquer"

# @ acf
#: pro/fields/class-acf-field-gallery.php:615
msgid "Minimum Selection"
msgstr "Minimum d’images"

# @ acf
#: pro/fields/class-acf-field-gallery.php:625
msgid "Maximum Selection"
msgstr "Maximum d’images"

#: pro/fields/class-acf-field-gallery.php:707
msgid "Allowed file types"
msgstr "Types de fichiers autorisés"

#: pro/fields/class-acf-field-gallery.php:727
msgid "Insert"
msgstr "Insérer"

#: pro/fields/class-acf-field-gallery.php:728
msgid "Specify where new attachments are added"
msgstr "Définir comment les images sont insérées"

#: pro/fields/class-acf-field-gallery.php:732
msgid "Append to the end"
msgstr "Insérer à la fin"

#: pro/fields/class-acf-field-gallery.php:733
msgid "Prepend to the beginning"
msgstr "Insérer au début"

#: pro/fields/class-acf-field-repeater.php:66,
#: pro/fields/class-acf-field-repeater.php:463
msgid "Minimum rows not reached ({min} rows)"
msgstr "Nombre minimal d’éléments insuffisant ({min} éléments)"

#: pro/fields/class-acf-field-repeater.php:67
msgid "Maximum rows reached ({max} rows)"
msgstr "Nombre maximal d’éléments atteint ({max} éléments)"

#: pro/fields/class-acf-field-repeater.php:68
msgid "Error loading page"
msgstr "Erreur de chargement de la page"

#: pro/fields/class-acf-field-repeater.php:69
msgid "Order will be assigned upon save"
msgstr "L’ordre sera assigné après l’enregistrement"

#: pro/fields/class-acf-field-repeater.php:196
msgid "Useful for fields with a large number of rows."
msgstr "Utile pour les champs avec un grand nombre de lignes."

#: pro/fields/class-acf-field-repeater.php:207
msgid "Rows Per Page"
msgstr "Lignes par Page"

#: pro/fields/class-acf-field-repeater.php:208
msgid "Set the number of rows to be displayed on a page."
msgstr "Définir le nombre de lignes à afficher sur une page."

# @ acf
#: pro/fields/class-acf-field-repeater.php:240
msgid "Minimum Rows"
msgstr "Nombre minimum d’éléments"

# @ acf
#: pro/fields/class-acf-field-repeater.php:251
msgid "Maximum Rows"
msgstr "Nombre maximum d’éléments"

#: pro/fields/class-acf-field-repeater.php:281
msgid "Collapsed"
msgstr "Replié"

#: pro/fields/class-acf-field-repeater.php:282
msgid "Select a sub field to show when row is collapsed"
msgstr "Choisir un sous champ à montrer lorsque la ligne est refermée"

#: pro/fields/class-acf-field-repeater.php:1060
msgid "Invalid field key or name."
msgstr "Clé de champ invalide."

#: pro/fields/class-acf-field-repeater.php:1069
msgid "There was an error retrieving the field."
msgstr "Il y a une erreur lors de la récupération du champ."

#: pro/fields/class-acf-repeater-table.php:369
msgid "Click to reorder"
msgstr "Cliquer pour réorganiser"

# @ acf
#: pro/fields/class-acf-repeater-table.php:402
msgid "Add row"
msgstr "Ajouter un élément"

#: pro/fields/class-acf-repeater-table.php:403
msgid "Duplicate row"
msgstr "Dupliquer la ligne"

# @ acf
#: pro/fields/class-acf-repeater-table.php:404
msgid "Remove row"
msgstr "Retirer l’élément"

#: pro/fields/class-acf-repeater-table.php:448,
#: pro/fields/class-acf-repeater-table.php:465,
#: pro/fields/class-acf-repeater-table.php:466
msgid "Current Page"
msgstr "Page actuelle"

#: pro/fields/class-acf-repeater-table.php:456,
#: pro/fields/class-acf-repeater-table.php:457
msgid "First Page"
msgstr "Première page"

#: pro/fields/class-acf-repeater-table.php:460,
#: pro/fields/class-acf-repeater-table.php:461
msgid "Previous Page"
msgstr "Page précédente"

#. translators: 1: Current page, 2: Total pages.
#: pro/fields/class-acf-repeater-table.php:470
msgctxt "paging"
msgid "%1$s of %2$s"
msgstr "%1$s sur %2$s"

#: pro/fields/class-acf-repeater-table.php:477,
#: pro/fields/class-acf-repeater-table.php:478
msgid "Next Page"
msgstr "Page suivante"

#: pro/fields/class-acf-repeater-table.php:481,
#: pro/fields/class-acf-repeater-table.php:482
msgid "Last Page"
msgstr "Dernière page"

#: pro/locations/class-acf-location-block.php:71
msgid "No block types exist"
msgstr "Aucun type de blocs existant"

#: pro/locations/class-acf-location-options-page.php:70
msgid "No options pages exist"
msgstr "Aucune page d’option créée"

#: pro/admin/views/html-settings-updates.php:6
msgid "Deactivate License"
msgstr "Désactiver la licence"

# @ acf
#: pro/admin/views/html-settings-updates.php:6
msgid "Activate License"
msgstr "Activer votre licence"

# @ acf
#: pro/admin/views/html-settings-updates.php:16
msgid "License Information"
msgstr "Informations sur la licence"

#: pro/admin/views/html-settings-updates.php:34
msgid ""
"To unlock updates, please enter your license key below. If you don't have a "
"licence key, please see <a href=\"%s\" target=\"_blank\">details & pricing</"
"a>."
msgstr ""
"Pour débloquer les mises à jour, veuillez entrer votre clé de licence ci-"
"dessous. Si vous n’en possédez pas encore une, jetez un oeil à nos <a "
"href=\"%s\" target=\"_blank\">détails & tarifs</a>."

# @ acf
#: pro/admin/views/html-settings-updates.php:37
msgid "License Key"
msgstr "Clé de licence"

#: pro/admin/views/html-settings-updates.php:22
msgid "Your license key is defined in wp-config.php."
msgstr "Votre clé de licence est définie dans le fichier wp-config.php."

#: pro/admin/views/html-settings-updates.php:29
msgid "Retry Activation"
msgstr "Retenter l’activation"

# @ acf
#: pro/admin/views/html-settings-updates.php:61
msgid "Update Information"
msgstr "Informations de mise à jour"

#: pro/admin/views/html-settings-updates.php:68
msgid "Current Version"
msgstr "Version actuelle"

#: pro/admin/views/html-settings-updates.php:76
msgid "Latest Version"
msgstr "Dernière version"

# @ acf
#: pro/admin/views/html-settings-updates.php:84
msgid "Update Available"
msgstr "Mise à jour disponible"

# @ wp3i
#: pro/admin/views/html-settings-updates.php:98
msgid "Upgrade Notice"
msgstr "Améliorations"

#: pro/admin/views/html-settings-updates.php:126
msgid "Check For Updates"
msgstr "Vérifier les mises à jour"

#: pro/admin/views/html-settings-updates.php:121
msgid "Enter your license key to unlock updates"
msgstr "Indiquez votre clé de licence pour activer les mises à jour"

# @ acf
#: pro/admin/views/html-settings-updates.php:119
msgid "Update Plugin"
msgstr "Mettre à jour l’extension"

#: pro/admin/views/html-settings-updates.php:117
msgid "Please reactivate your license to unlock updates"
msgstr "Veuillez réactiver votre licence afin de débloquer les mises à jour"
