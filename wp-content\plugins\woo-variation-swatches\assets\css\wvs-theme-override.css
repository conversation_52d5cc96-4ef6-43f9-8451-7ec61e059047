/*!
 * Variation Swatches for WooCommerce v1.1.19 
 * 
 * Author: <PERSON><PERSON> ( <EMAIL> ) 
 * Date: 07/09/2021
 * Released under the GPLv3 license.
 */
.wvs-theme-sober .product form.cart .variations .variable,
.wvs-theme-child-sober .product form.cart .variations .variable {
  margin-bottom: 15px !important;
}

.wvs-theme-sober .product form.cart .variations .variable-options,
.wvs-theme-child-sober .product form.cart .variations .variable-options {
  border: 0 !important;
}

.wvs-theme-sober .product form.cart .variations .variable-options::after,
.wvs-theme-child-sober .product form.cart .variations .variable-options::after {
  display: none;
}

.wvs-theme-sober .product form.cart .variations .arrow,
.wvs-theme-child-sober .product form.cart .variations .arrow {
  display: none;
}

.wvs-theme-sober .product form.cart .variations .label,
.wvs-theme-child-sober .product form.cart .variations .label {
  display: block;
  text-align: right;
  margin-right: 10px;
}

.wvs-theme-sober .product form.cart .variations .value,
.wvs-theme-child-sober .product form.cart .variations .value {
  flex-grow: unset;
}

.wvs-theme-sober .product form.cart .variations select,
.wvs-theme-child-sober .product form.cart .variations select {
  border: 1px solid #E4E6EB !important;
  padding: .5em 1em !important;
  text-align: left !important;
  -moz-text-align-last: left !important;
       text-align-last: left !important;
  -webkit-appearance: menulist-button !important;
}

.wvs-theme-sober .product form.cart .radio-variable-item,
.wvs-theme-child-sober .product form.cart .radio-variable-item {
  margin: 5px;
  text-align: left;
}

.wvs-theme-sober .product form.cart .radio-variable-item:last-child,
.wvs-theme-child-sober .product form.cart .radio-variable-item:last-child {
  margin-right: 0;
}

.wvs-theme-sober .product form.cart .radio-variable-item label,
.wvs-theme-child-sober .product form.cart .radio-variable-item label {
  padding-left: 30px;
  margin: 0;
}

.wvs-theme-sober .product form.cart .radio-variable-item label::before,
.wvs-theme-child-sober .product form.cart .radio-variable-item label::before {
  top: 0;
  border-radius: 100%;
}

.wvs-theme-sober .product form.cart .radio-variable-item label::after,
.wvs-theme-child-sober .product form.cart .radio-variable-item label::after {
  top: 8px;
}

.wvs-theme-shophistic-lite.woocommerce #main .entry-summary .variations {
  display: block !important;
}

.wvs-theme-shophistic-lite .ql_custom_variations {
  display: none !important;
}

.wvs-theme-shophistic-lite .radio-variable-item input[type="radio"] {
  display: inline-block;
  margin: 0;
}

.wvs-theme-flatsome .variations .reset_variations {
  position: relative;
  bottom: 0;
  left: 0;
}

.wvs-theme-storefront.single-product div.product,
.wvs-theme-child-storefront.single-product div.product {
  overflow: visible !important;
}

.wvs-theme-stockholm .variations .reset_variations {
  position: relative;
  bottom: 0;
  left: 0;
  -webkit-transform: none;
          transform: none;
}

.wvs-theme-kalium .image-variable-item img,
.wvs-theme-kalium-child .image-variable-item img {
  width: 100% !important;
}

.wvs-theme-kalium .radio-variable-item input,
.wvs-theme-kalium-child .radio-variable-item input {
  width: 16px !important;
  height: 16px !important;
}

.wvs-theme-kalium .woo-variation-items-wrapper .select-option-ui,
.wvs-theme-kalium-child .woo-variation-items-wrapper .select-option-ui {
  display: none;
}

.wvs-theme-aurum .variable-items-wrapper .image-variable-item > img,
.wvs-theme-child-aurum .variable-items-wrapper .image-variable-item > img {
  width: 100%;
}

.wvs-theme-hestia .woo-variation-items-wrapper.value::before {
  display: none !important;
}

.wvs-theme-thegem .woo-variation-items-wrapper .combobox-wrapper,
.wvs-theme-child-thegem .woo-variation-items-wrapper .combobox-wrapper {
  display: none;
}

.wvs-theme-thegem .wvs-archive-variation-wrapper .variations,
.wvs-theme-child-thegem .wvs-archive-variation-wrapper .variations {
  padding: 0;
}

.wvs-theme-ushop .variations_form,
.wvs-theme-child-ushop .variations_form {
  overflow: visible !important;
}

.wvs-theme-ushop .single-product-summary .product_meta,
.wvs-theme-child-ushop .single-product-summary .product_meta {
  display: inline-table;
}

.wvs-theme-savoy .woo-variation-items-wrapper,
.wvs-theme-child-savoy .woo-variation-items-wrapper {
  position: relative !important;
}

.wvs-theme-savoy .woo-variation-items-wrapper .sod_select,
.wvs-theme-child-savoy .woo-variation-items-wrapper .sod_select {
  display: none !important;
}

.wvs-theme-savoy .nm-variation-row,
.wvs-theme-child-savoy .nm-variation-row {
  display: flex;
}

.wvs-theme-woodstock .woo-variation-items-wrapper .variation-select,
.wvs-theme-child-woodstock .woo-variation-items-wrapper .variation-select {
  display: none !important;
}

.wvs-theme-woodmart .woo-variation-items-wrapper > .swatches-select,
.wvs-theme-child-woodmart .woo-variation-items-wrapper > .swatches-select {
  display: none;
}

.wvs-theme-woodmart .wd-attr-selected,
.wvs-theme-child-woodmart .wd-attr-selected {
  display: none;
}

.wvs-theme-gecko .variations .value.woo-variation-items-wrapper,
.wvs-theme-gecko-child .variations .value.woo-variation-items-wrapper {
  border: 0 !important;
}

.wvs-theme-gecko .variations .value.woo-variation-items-wrapper::after,
.wvs-theme-gecko-child .variations .value.woo-variation-items-wrapper::after {
  display: none !important;
}

.woo-variation-gallery-theme-child-massive-dynamic .woo-variation-items-wrapper .clear-selection,
.woo-variation-gallery-theme-massive-dynamic .woo-variation-items-wrapper .clear-selection {
  border: 0;
  height: auto;
  width: auto;
}

.woo-variation-gallery-theme-child-massive-dynamic .woo-variation-items-wrapper .clear-selection .reset_variations,
.woo-variation-gallery-theme-massive-dynamic .woo-variation-items-wrapper .clear-selection .reset_variations {
  font-size: 8px !important;
  width: 20px;
  height: 20px;
  padding: 5px;
  box-sizing: border-box;
  border-radius: 100%;
  border: 1px solid #F04040;
  color: #F04040;
}

.wvs-theme-claue .woo-variation-items-wrapper,
.wvs-theme-child-claue .woo-variation-items-wrapper {
  border: none;
}

.wvs-theme-claue .woo-variation-items-wrapper::after,
.wvs-theme-child-claue .woo-variation-items-wrapper::after {
  display: none;
}

.wvs-theme-jupiter table.variations,
.wvs-theme-child-jupiter table.variations {
  overflow: initial;
}

.wvs-theme-oxygen .woo-variation-items-wrapper .select-wrapper,
.wvs-theme-child-oxygen .woo-variation-items-wrapper .select-wrapper {
  display: none;
}

.wvs-theme-oxygen .variable-items-wrapper.radio-variable-wrapper .radio-variable-item input,
.wvs-theme-child-oxygen .variable-items-wrapper.radio-variable-wrapper .radio-variable-item input {
  width: 18px;
}

.wvs-theme-simple-elegant .woo-variation-items-wrapper .wi-nice-select,
.wvs-theme-child-simple-elegant .woo-variation-items-wrapper .wi-nice-select {
  display: none;
}

.wvs-theme-twentytwenty table.variations,
.wvs-theme-child-twentytwenty table.variations {
  overflow: auto;
}

.wvs-theme-divi .et_pb_wc_add_to_cart form.variations_form.cart .variations td.value span:after,
.wvs-theme-child-divi .et_pb_wc_add_to_cart form.variations_form.cart .variations td.value span:after {
  display: none !important;
}

.wvs-theme-jevelin .sh-woo-layout table.variations td select.woo-variation-raw-select,
.wvs-theme-child-jevelin .sh-woo-layout table.variations td select.woo-variation-raw-select {
  display: none !important;
}

.wvs-theme-jevelin .sh-woo-layout table.variations,
.wvs-theme-child-jevelin .sh-woo-layout table.variations {
  max-width: 100%;
}

.wvs-theme-jevelin .radio-variable-item label,
.wvs-theme-child-jevelin .radio-variable-item label {
  line-height: 1 !important;
}

.wvs-theme-stockie .woo-variation-raw-select + .select-styled,
.wvs-theme-child-stockie .woo-variation-raw-select + .select-styled {
  display: none !important;
}