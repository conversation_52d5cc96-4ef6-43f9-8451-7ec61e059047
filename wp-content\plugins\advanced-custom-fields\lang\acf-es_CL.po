# Advanced Custom Fields Translations are a combination of translate.wordpress.org contributions,
# combined with user contributed strings for the PRO version.
# Translations from translate.wordpress.org take priority over translations in this file.
# translate.wordpress.org contributions are synced at the time of each release.
#
# If you would like to contribute translations, please visit
# https://translate.wordpress.org/projects/wp-plugins/advanced-custom-fields/stable/
#
# For additional ACF PRO strings, please submit a pull request over on the ACF GitHub repo at
# http://github.com/advancedcustomfields/acf using the .pot (and any existing .po) files in /lang/pro/
#
# This file is distributed under the same license as Advanced Custom Fields.
msgid ""
msgstr ""
"PO-Revision-Date: 2025-07-21T18:29:30+00:00\n"
"Report-Msgid-Bugs-To: http://support.advancedcustomfields.com\n"
"Language: es_CL\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: gettext\n"
"Project-Id-Version: Advanced Custom Fields\n"

#: includes/fields/class-acf-field-select.php:471
msgid ""
"Save created options back to the \"Choices\" setting in the field definition."
msgstr ""
"Guarda las opciones creadas en el ajuste “Opciones” en la definición del "
"campo."

#: includes/fields/class-acf-field-select.php:470
msgid "Save Options"
msgstr "Guardar opciones"

#: includes/fields/class-acf-field-select.php:448
msgid ""
"Allow content editors to create new options by typing in the Select input. "
"Multiple options can be created from a comma separated string."
msgstr ""
"Permite que los editores de contenido creen nuevas opciones escribiendo en "
"el campo de selección. Se pueden crear varias opciones a partir de una "
"cadena separada por comas."

#: includes/fields/class-acf-field-select.php:447
msgid "Create Options"
msgstr "Crear opciones"

#: includes/admin/views/global/navigation.php:179
#: includes/admin/views/global/navigation.php:183
msgid "Edit ACF Field Groups"
msgstr "Editar grupos de campos de ACF"

#: includes/admin/views/global/navigation.php:100
msgid "Get 4 months free on any WP Engine plan"
msgstr "Obtén 4 meses gratis en cualquier plan de WP Engine"

#: src/Site_Health/Site_Health.php:522
msgid "Number of Field Groups with Blocks and Other Locations"
msgstr "Número de grupos de campos con bloques y otras ubicaciones"

#: src/Site_Health/Site_Health.php:517
msgid "Number of Field Groups with Multiple Block Locations"
msgstr "Número de grupos de campos con múltiples ubicaciones de bloques"

#: src/Site_Health/Site_Health.php:512
msgid "Number of Field Groups with a Single Block Location"
msgstr "Número de grupos de campos con una sola ubicación de bloque"

#: src/Site_Health/Site_Health.php:481
msgid "All Location Rules"
msgstr "Todas las reglas de ubicación"

#: includes/validation.php:144
msgid "Learn more"
msgstr "Saber más"

#: includes/validation.php:133
msgid ""
"ACF was unable to perform validation because the provided nonce failed "
"verification."
msgstr ""
"ACE no fue capaz de realizar la validación porque falló la verificación del "
"nonce facilitado."

#: includes/validation.php:131
msgid ""
"ACF was unable to perform validation because no nonce was received by the "
"server."
msgstr ""
"CE no fue capaz de realizar la validación porque no se recibió ningún nonce "
"del servidor."

#. translators: This text is prepended by a link to ACF's website, and appended
#. by a link to WP Engine's website.
#: includes/admin/admin.php:324
msgid "are developed and maintained by"
msgstr "lo desarrollan y mantienen"

#: src/Site_Health/Site_Health.php:295
msgid "Update Source"
msgstr "Actualizar la fuente"

#: includes/admin/views/acf-post-type/advanced-settings.php:850
#: includes/admin/views/acf-taxonomy/advanced-settings.php:810
msgid "By default only admin users can edit this setting."
msgstr ""
"De forma predeterminada, solo los usuarios administradores pueden editar "
"esta configuración."

#: includes/admin/views/acf-post-type/advanced-settings.php:848
#: includes/admin/views/acf-taxonomy/advanced-settings.php:808
msgid "By default only super admin users can edit this setting."
msgstr ""
"De forma predeterminada, solo los usuarios superadministradores pueden "
"editar esta configuración."

#: includes/admin/views/acf-field-group/field.php:322
msgid "Close and Add Field"
msgstr "Cerrar y agregar campo"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:804
msgid ""
"A PHP function name to be called to handle the content of a meta box on your "
"taxonomy. For security, this callback will be executed in a special context "
"without access to any superglobals like $_POST or $_GET."
msgstr ""
"Una función PHP se llamará para manejar el contenido de un metabox en su "
"taxonomía. Por seguridad, esta devolución de llamada se ejecutará en un "
"contexto especial sin acceso a ninguna variable super global como $_POST o "
"$_GET."

#: includes/admin/views/acf-post-type/advanced-settings.php:842
msgid ""
"A PHP function name to be called when setting up the meta boxes for the edit "
"screen. For security, this callback will be executed in a special context "
"without access to any superglobals like $_POST or $_GET."
msgstr ""
"Una función PHP se llamará al configurar los meta boxes para la pantalla de "
"edición. Por seguridad, esta devolución de llamada se ejecutará en un "
"contexto especial sin acceso a ninguna variable super global como $_POST o "
"$_GET."

#: src/Site_Health/Site_Health.php:296
msgid "wordpress.org"
msgstr "wordpress.org"

#: includes/fields/class-acf-field.php:359
msgid "Allow Access to Value in Editor UI"
msgstr "Permitir el acceso al valor en la interfaz del editor"

#: includes/fields/class-acf-field.php:341
msgid "Learn more."
msgstr "Saber más."

#. translators: %s A "Learn More" link to documentation explaining the setting
#. further.
#: includes/fields/class-acf-field.php:340
msgid ""
"Allow content editors to access and display the field value in the editor UI "
"using Block Bindings or the ACF Shortcode. %s"
msgstr ""
"Permite que los editores de contenido accedan y muestren el valor del campo "
"en la interfaz de usuario del editor utilizando enlaces de bloque o el "
"shortcode de ACF. %s"

#: src/Blocks/Bindings.php:67
msgid ""
"The requested ACF field type does not support output in Block Bindings or "
"the ACF shortcode."
msgstr ""
"El tipo de campo ACF solicitado no admite la salida en bloques enlazados o "
"en el shortcode de ACF."

#: includes/api/api-template.php:1085 src/Blocks/Bindings.php:75
msgid ""
"The requested ACF field is not allowed to be output in bindings or the ACF "
"Shortcode."
msgstr ""
"El campo ACF solicitado no puede aparecer en los enlaces o en el shortcode "
"de ACF."

#: includes/api/api-template.php:1077
msgid ""
"The requested ACF field type does not support output in bindings or the ACF "
"Shortcode."
msgstr ""
"El tipo de campo ACF solicitado no admite la salida en enlaces o en el "
"shortcode de ACF."

#: includes/api/api-template.php:1054
msgid "[The ACF shortcode cannot display fields from non-public posts]"
msgstr "[El shortcode de ACF no puede mostrar campos de entradas no públicas]"

#: includes/api/api-template.php:1011
msgid "[The ACF shortcode is disabled on this site]"
msgstr "[El shortcode de ACF está desactivado en este sitio]"

#: includes/fields/class-acf-field-icon_picker.php:476
msgid "Businessman Icon"
msgstr "Ícono de hombre de negocios"

#: includes/fields/class-acf-field-icon_picker.php:468
msgid "Forums Icon"
msgstr "Ícono de foros"

#: includes/fields/class-acf-field-icon_picker.php:747
msgid "YouTube Icon"
msgstr "Ícono de YouTube"

#: includes/fields/class-acf-field-icon_picker.php:746
msgid "Yes (alt) Icon"
msgstr "Ícono de sí (alt)"

#: includes/fields/class-acf-field-icon_picker.php:744
msgid "Xing Icon"
msgstr "Ícono de Xing"

#: includes/fields/class-acf-field-icon_picker.php:743
msgid "WordPress (alt) Icon"
msgstr "Ícono de WordPress (alt)"

#: includes/fields/class-acf-field-icon_picker.php:741
msgid "WhatsApp Icon"
msgstr "Ícono de Whatsapp"

#: includes/fields/class-acf-field-icon_picker.php:740
msgid "Write Blog Icon"
msgstr "Ícono de escribir blog"

#: includes/fields/class-acf-field-icon_picker.php:739
msgid "Widgets Menus Icon"
msgstr "Ícono de widgets de menús"

#: includes/fields/class-acf-field-icon_picker.php:738
msgid "View Site Icon"
msgstr "Ícono de ver el sitio"

#: includes/fields/class-acf-field-icon_picker.php:737
msgid "Learn More Icon"
msgstr "Ícono de aprender más"

#: includes/fields/class-acf-field-icon_picker.php:735
msgid "Add Page Icon"
msgstr "Ícono de agregar página"

#: includes/fields/class-acf-field-icon_picker.php:732
msgid "Video (alt3) Icon"
msgstr "Ícono de video (alt3)"

#: includes/fields/class-acf-field-icon_picker.php:731
msgid "Video (alt2) Icon"
msgstr "Ícono de video (alt2)"

#: includes/fields/class-acf-field-icon_picker.php:730
msgid "Video (alt) Icon"
msgstr "Ícono de video (alt)"

#: includes/fields/class-acf-field-icon_picker.php:727
msgid "Update (alt) Icon"
msgstr "Ícono de actualizar (alt)"

#: includes/fields/class-acf-field-icon_picker.php:724
msgid "Universal Access (alt) Icon"
msgstr "Ícono de acceso universal (alt)"

#: includes/fields/class-acf-field-icon_picker.php:721
msgid "Twitter (alt) Icon"
msgstr "Ícono de Twitter (alt)"

#: includes/fields/class-acf-field-icon_picker.php:719
msgid "Twitch Icon"
msgstr "Ícono de Twitch"

#: includes/fields/class-acf-field-icon_picker.php:716
msgid "Tide Icon"
msgstr "Ícono de marea"

#: includes/fields/class-acf-field-icon_picker.php:715
msgid "Tickets (alt) Icon"
msgstr "Ícono de entradas (alt)"

#: includes/fields/class-acf-field-icon_picker.php:711
msgid "Text Page Icon"
msgstr "Ícono de página de texto"

#: includes/fields/class-acf-field-icon_picker.php:705
msgid "Table Row Delete Icon"
msgstr "Ícono de borrar fila de tabla"

#: includes/fields/class-acf-field-icon_picker.php:704
msgid "Table Row Before Icon"
msgstr "Ícono de fila antes de tabla"

#: includes/fields/class-acf-field-icon_picker.php:703
msgid "Table Row After Icon"
msgstr "Ícono de fila tras la tabla"

#: includes/fields/class-acf-field-icon_picker.php:702
msgid "Table Col Delete Icon"
msgstr "Ícono de borrar columna de tabla"

#: includes/fields/class-acf-field-icon_picker.php:701
msgid "Table Col Before Icon"
msgstr "Ícono de columna antes de la tabla"

#: includes/fields/class-acf-field-icon_picker.php:700
msgid "Table Col After Icon"
msgstr "Ícono de columna tras la tabla"

#: includes/fields/class-acf-field-icon_picker.php:699
msgid "Superhero (alt) Icon"
msgstr "Ícono de superhéroe (alt)"

#: includes/fields/class-acf-field-icon_picker.php:698
msgid "Superhero Icon"
msgstr "Ícono de superhéroe"

#: includes/fields/class-acf-field-icon_picker.php:692
msgid "Spotify Icon"
msgstr "Ícono de Spotify"

#: includes/fields/class-acf-field-icon_picker.php:686
msgid "Shortcode Icon"
msgstr "Ícono del shortcode"

#: includes/fields/class-acf-field-icon_picker.php:685
msgid "Shield (alt) Icon"
msgstr "Ícono de escudo (alt)"

#: includes/fields/class-acf-field-icon_picker.php:683
msgid "Share (alt2) Icon"
msgstr "Ícono de compartir (alt2)"

#: includes/fields/class-acf-field-icon_picker.php:682
msgid "Share (alt) Icon"
msgstr "Ícono de compartir (alt)"

#: includes/fields/class-acf-field-icon_picker.php:677
msgid "Saved Icon"
msgstr "Ícono de guardado"

#: includes/fields/class-acf-field-icon_picker.php:676
msgid "RSS Icon"
msgstr "Ícono de RSS"

#: includes/fields/class-acf-field-icon_picker.php:675
msgid "REST API Icon"
msgstr "Ícono de la API REST"

#: includes/fields/class-acf-field-icon_picker.php:674
msgid "Remove Icon"
msgstr "Ícono de quitar"

#: includes/fields/class-acf-field-icon_picker.php:672
msgid "Reddit Icon"
msgstr "Ícono de Reddit"

#: includes/fields/class-acf-field-icon_picker.php:669
msgid "Privacy Icon"
msgstr "Ícono de privacidad"

#: includes/fields/class-acf-field-icon_picker.php:668
msgid "Printer Icon"
msgstr "Ícono de la impresora"

#: includes/fields/class-acf-field-icon_picker.php:664
msgid "Podio Icon"
msgstr "Ícono del podio"

#: includes/fields/class-acf-field-icon_picker.php:663
msgid "Plus (alt2) Icon"
msgstr "Ícono de más (alt2)"

#: includes/fields/class-acf-field-icon_picker.php:662
msgid "Plus (alt) Icon"
msgstr "Ícono de más (alt)"

#: includes/fields/class-acf-field-icon_picker.php:660
msgid "Plugins Checked Icon"
msgstr "Ícono de plugins comprobados"

#: includes/fields/class-acf-field-icon_picker.php:657
msgid "Pinterest Icon"
msgstr "Ícono de Pinterest"

#: includes/fields/class-acf-field-icon_picker.php:655
msgid "Pets Icon"
msgstr "Ícono de mascotas"

#: includes/fields/class-acf-field-icon_picker.php:653
msgid "PDF Icon"
msgstr "Ícono de PDF"

#: includes/fields/class-acf-field-icon_picker.php:651
msgid "Palm Tree Icon"
msgstr "Ícono de la palmera"

#: includes/fields/class-acf-field-icon_picker.php:650
msgid "Open Folder Icon"
msgstr "Ícono de carpeta abierta"

#: includes/fields/class-acf-field-icon_picker.php:649
msgid "No (alt) Icon"
msgstr "Ícono del no (alt)"

#: includes/fields/class-acf-field-icon_picker.php:644
msgid "Money (alt) Icon"
msgstr "Ícono de dinero (alt)"

#: includes/fields/class-acf-field-icon_picker.php:639
msgid "Menu (alt3) Icon"
msgstr "Ícono de menú (alt3)"

#: includes/fields/class-acf-field-icon_picker.php:638
msgid "Menu (alt2) Icon"
msgstr "Ícono de menú (alt2)"

#: includes/fields/class-acf-field-icon_picker.php:637
msgid "Menu (alt) Icon"
msgstr "Ícono de menú (alt)"

#: includes/fields/class-acf-field-icon_picker.php:632
msgid "Spreadsheet Icon"
msgstr "Ícono de hoja de cálculo"

#: includes/fields/class-acf-field-icon_picker.php:631
msgid "Interactive Icon"
msgstr "Ícono interactivo"

#: includes/fields/class-acf-field-icon_picker.php:630
msgid "Document Icon"
msgstr "Ícono de documento"

#: includes/fields/class-acf-field-icon_picker.php:629
msgid "Default Icon"
msgstr "Ícono por defecto"

#: includes/fields/class-acf-field-icon_picker.php:623
msgid "Location (alt) Icon"
msgstr "Ícono de ubicación (alt)"

#: includes/fields/class-acf-field-icon_picker.php:620
msgid "LinkedIn Icon"
msgstr "Ícono de LinkedIn"

#: includes/fields/class-acf-field-icon_picker.php:615
msgid "Instagram Icon"
msgstr "Ícono de Instagram"

#: includes/fields/class-acf-field-icon_picker.php:614
msgid "Insert Before Icon"
msgstr "Ícono de insertar antes"

#: includes/fields/class-acf-field-icon_picker.php:613
msgid "Insert After Icon"
msgstr "Ícono de insertar después"

#: includes/fields/class-acf-field-icon_picker.php:612
msgid "Insert Icon"
msgstr "Ícono de insertar"

#: includes/fields/class-acf-field-icon_picker.php:611
msgid "Info Outline Icon"
msgstr "Ícono de esquema de información"

#: includes/fields/class-acf-field-icon_picker.php:608
msgid "Images (alt2) Icon"
msgstr "Ícono de imágenes (alt2)"

#: includes/fields/class-acf-field-icon_picker.php:607
msgid "Images (alt) Icon"
msgstr "Ícono de imágenes (alt)"

#: includes/fields/class-acf-field-icon_picker.php:606
msgid "Rotate Right Icon"
msgstr "Ícono de girar a la derecha"

#: includes/fields/class-acf-field-icon_picker.php:605
msgid "Rotate Left Icon"
msgstr "Ícono de girar a la izquierda"

#: includes/fields/class-acf-field-icon_picker.php:604
msgid "Rotate Icon"
msgstr "Ícono de girar"

#: includes/fields/class-acf-field-icon_picker.php:603
msgid "Flip Vertical Icon"
msgstr "Ícono de voltear verticalmente"

#: includes/fields/class-acf-field-icon_picker.php:602
msgid "Flip Horizontal Icon"
msgstr "Ícono de voltear horizontalmente"

#: includes/fields/class-acf-field-icon_picker.php:600
msgid "Crop Icon"
msgstr "Ícono de recortar"

#: includes/fields/class-acf-field-icon_picker.php:599
msgid "ID (alt) Icon"
msgstr "Ícono de ID (alt)"

#: includes/fields/class-acf-field-icon_picker.php:597
msgid "HTML Icon"
msgstr "Ícono de HTML"

#: includes/fields/class-acf-field-icon_picker.php:596
msgid "Hourglass Icon"
msgstr "Ícono de reloj de arena"

#: includes/fields/class-acf-field-icon_picker.php:593
msgid "Heading Icon"
msgstr "Ícono de encabezado"

#: includes/fields/class-acf-field-icon_picker.php:589
msgid "Google Icon"
msgstr "Ícono de Google"

#: includes/fields/class-acf-field-icon_picker.php:588
msgid "Games Icon"
msgstr "Ícono de juegos"

#: includes/fields/class-acf-field-icon_picker.php:587
msgid "Fullscreen Exit (alt) Icon"
msgstr "Ícono de salir a pantalla completa (alt)"

#: includes/fields/class-acf-field-icon_picker.php:586
msgid "Fullscreen (alt) Icon"
msgstr "Ícono de pantalla completa (alt)"

#: includes/fields/class-acf-field-icon_picker.php:583
msgid "Status Icon"
msgstr "Ícono de estado"

#: includes/fields/class-acf-field-icon_picker.php:581
msgid "Image Icon"
msgstr "Ícono de imagen"

#: includes/fields/class-acf-field-icon_picker.php:580
msgid "Gallery Icon"
msgstr "Ícono de galería"

#: includes/fields/class-acf-field-icon_picker.php:579
msgid "Chat Icon"
msgstr "Ícono del chat"

#: includes/fields/class-acf-field-icon_picker.php:578
#: includes/fields/class-acf-field-icon_picker.php:627
msgid "Audio Icon"
msgstr "Ícono de audio"

#: includes/fields/class-acf-field-icon_picker.php:577
msgid "Aside Icon"
msgstr "Ícono de minientrada"

#: includes/fields/class-acf-field-icon_picker.php:576
msgid "Food Icon"
msgstr "Ícono de comida"

#: includes/fields/class-acf-field-icon_picker.php:569
msgid "Exit Icon"
msgstr "Ícono de salida"

#: includes/fields/class-acf-field-icon_picker.php:568
msgid "Excerpt View Icon"
msgstr "Ícono de ver extracto"

#: includes/fields/class-acf-field-icon_picker.php:567
msgid "Embed Video Icon"
msgstr "Ícono de incrustar video"

#: includes/fields/class-acf-field-icon_picker.php:566
msgid "Embed Post Icon"
msgstr "Ícono de incrustar entrada"

#: includes/fields/class-acf-field-icon_picker.php:565
msgid "Embed Photo Icon"
msgstr "Ícono de incrustar foto"

#: includes/fields/class-acf-field-icon_picker.php:564
msgid "Embed Generic Icon"
msgstr "Ícono de incrustar genérico"

#: includes/fields/class-acf-field-icon_picker.php:563
msgid "Embed Audio Icon"
msgstr "Ícono de incrustar  audio"

#: includes/fields/class-acf-field-icon_picker.php:562
msgid "Email (alt2) Icon"
msgstr "Ícono de correo electrónico (alt2)"

#: includes/fields/class-acf-field-icon_picker.php:559
msgid "Ellipsis Icon"
msgstr "Ícono de puntos suspensivos"

#: includes/fields/class-acf-field-icon_picker.php:555
msgid "Unordered List Icon"
msgstr "Ícono de lista desordenada"

#: includes/fields/class-acf-field-icon_picker.php:550
msgid "RTL Icon"
msgstr "Ícono RTL"

#: includes/fields/class-acf-field-icon_picker.php:543
msgid "Ordered List RTL Icon"
msgstr "Ícono de lista ordenada RTL"

#: includes/fields/class-acf-field-icon_picker.php:542
msgid "Ordered List Icon"
msgstr "Ícono de lista ordenada"

#: includes/fields/class-acf-field-icon_picker.php:541
msgid "LTR Icon"
msgstr "Ícono LTR"

#: includes/fields/class-acf-field-icon_picker.php:533
msgid "Custom Character Icon"
msgstr "Ícono de personaje personalizado"

#: includes/fields/class-acf-field-icon_picker.php:525
msgid "Edit Page Icon"
msgstr "Ícono de editar página"

#: includes/fields/class-acf-field-icon_picker.php:524
msgid "Edit Large Icon"
msgstr "Ícono de edición grande"

#: includes/fields/class-acf-field-icon_picker.php:522
msgid "Drumstick Icon"
msgstr "Ícono de la baqueta"

#: includes/fields/class-acf-field-icon_picker.php:518
msgid "Database View Icon"
msgstr "Ícono de vista de la base de datos"

#: includes/fields/class-acf-field-icon_picker.php:517
msgid "Database Remove Icon"
msgstr "Ícono de eliminar base de datos"

#: includes/fields/class-acf-field-icon_picker.php:516
msgid "Database Import Icon"
msgstr "Ícono de importar base de datos"

#: includes/fields/class-acf-field-icon_picker.php:515
msgid "Database Export Icon"
msgstr "Ícono de exportar de base de datos"

#: includes/fields/class-acf-field-icon_picker.php:514
msgid "Database Add Icon"
msgstr "Ícono de agregar base de datos"

#: includes/fields/class-acf-field-icon_picker.php:513
msgid "Database Icon"
msgstr "Ícono de base de datos"

#: includes/fields/class-acf-field-icon_picker.php:511
msgid "Cover Image Icon"
msgstr "Ícono de imagen de portada"

#: includes/fields/class-acf-field-icon_picker.php:510
msgid "Volume On Icon"
msgstr "Ícono de volumen activado"

#: includes/fields/class-acf-field-icon_picker.php:509
msgid "Volume Off Icon"
msgstr "Ícono de volumen apagado"

#: includes/fields/class-acf-field-icon_picker.php:508
msgid "Skip Forward Icon"
msgstr "Ícono de saltar adelante"

#: includes/fields/class-acf-field-icon_picker.php:507
msgid "Skip Back Icon"
msgstr "Ícono de saltar atrás"

#: includes/fields/class-acf-field-icon_picker.php:506
msgid "Repeat Icon"
msgstr "Ícono de repetición"

#: includes/fields/class-acf-field-icon_picker.php:505
msgid "Play Icon"
msgstr "Ícono de reproducción"

#: includes/fields/class-acf-field-icon_picker.php:504
msgid "Pause Icon"
msgstr "Ícono de pausa"

#: includes/fields/class-acf-field-icon_picker.php:503
msgid "Forward Icon"
msgstr "Ícono de adelante"

#: includes/fields/class-acf-field-icon_picker.php:502
msgid "Back Icon"
msgstr "Ícono de atrás"

#: includes/fields/class-acf-field-icon_picker.php:501
msgid "Columns Icon"
msgstr "Ícono de columnas"

#: includes/fields/class-acf-field-icon_picker.php:500
msgid "Color Picker Icon"
msgstr "Ícono del selector de color"

#: includes/fields/class-acf-field-icon_picker.php:499
msgid "Coffee Icon"
msgstr "Ícono del café"

#: includes/fields/class-acf-field-icon_picker.php:498
msgid "Code Standards Icon"
msgstr "Ícono de normas del código"

#: includes/fields/class-acf-field-icon_picker.php:497
msgid "Cloud Upload Icon"
msgstr "Ícono de subir a la nube"

#: includes/fields/class-acf-field-icon_picker.php:496
msgid "Cloud Saved Icon"
msgstr "Ícono de nube guardada"

#: includes/fields/class-acf-field-icon_picker.php:485
msgid "Car Icon"
msgstr "Ícono del coche"

#: includes/fields/class-acf-field-icon_picker.php:484
msgid "Camera (alt) Icon"
msgstr "Ícono de cámara (alt)"

#: includes/fields/class-acf-field-icon_picker.php:480
msgid "Calculator Icon"
msgstr "Ícono de calculadora"

#: includes/fields/class-acf-field-icon_picker.php:479
msgid "Button Icon"
msgstr "Ícono de botón"

#: includes/fields/class-acf-field-icon_picker.php:477
msgid "Businessperson Icon"
msgstr "Ícono de empresario"

#: includes/fields/class-acf-field-icon_picker.php:474
msgid "Tracking Icon"
msgstr "Ícono de seguimiento"

#: includes/fields/class-acf-field-icon_picker.php:473
msgid "Topics Icon"
msgstr "Ícono de debate"

#: includes/fields/class-acf-field-icon_picker.php:472
msgid "Replies Icon"
msgstr "Ícono de respuestas"

#: includes/fields/class-acf-field-icon_picker.php:471
msgid "PM Icon"
msgstr "Ícono de PM"

#: includes/fields/class-acf-field-icon_picker.php:469
msgid "Friends Icon"
msgstr "Ícono de amistad"

#: includes/fields/class-acf-field-icon_picker.php:467
msgid "Community Icon"
msgstr "Ícono de la comunidad"

#: includes/fields/class-acf-field-icon_picker.php:466
msgid "BuddyPress Icon"
msgstr "Ícono de BuddyPress"

#: includes/fields/class-acf-field-icon_picker.php:465
msgid "bbPress Icon"
msgstr "Ícono de bbPress"

#: includes/fields/class-acf-field-icon_picker.php:464
msgid "Activity Icon"
msgstr "Ícono de actividad"

#: includes/fields/class-acf-field-icon_picker.php:463
msgid "Book (alt) Icon"
msgstr "Ícono del libro (alt)"

#: includes/fields/class-acf-field-icon_picker.php:461
msgid "Block Default Icon"
msgstr "Ícono de bloque por defecto"

#: includes/fields/class-acf-field-icon_picker.php:460
msgid "Bell Icon"
msgstr "Ícono de la campana"

#: includes/fields/class-acf-field-icon_picker.php:459
msgid "Beer Icon"
msgstr "Ícono de la cerveza"

#: includes/fields/class-acf-field-icon_picker.php:458
msgid "Bank Icon"
msgstr "Ícono del banco"

#: includes/fields/class-acf-field-icon_picker.php:454
msgid "Arrow Up (alt2) Icon"
msgstr "Ícono de flecha arriba (alt2)"

#: includes/fields/class-acf-field-icon_picker.php:453
msgid "Arrow Up (alt) Icon"
msgstr "Ícono de flecha arriba (alt)"

#: includes/fields/class-acf-field-icon_picker.php:451
msgid "Arrow Right (alt2) Icon"
msgstr "Ícono de flecha derecha (alt2)"

#: includes/fields/class-acf-field-icon_picker.php:450
msgid "Arrow Right (alt) Icon"
msgstr "Ícono de flecha derecha (alt)"

#: includes/fields/class-acf-field-icon_picker.php:448
msgid "Arrow Left (alt2) Icon"
msgstr "Ícono de flecha izquierda (alt2)"

#: includes/fields/class-acf-field-icon_picker.php:447
msgid "Arrow Left (alt) Icon"
msgstr "Ícono de flecha izquierda (alt)"

#: includes/fields/class-acf-field-icon_picker.php:445
msgid "Arrow Down (alt2) Icon"
msgstr "Ícono de flecha abajo (alt2)"

#: includes/fields/class-acf-field-icon_picker.php:444
msgid "Arrow Down (alt) Icon"
msgstr "Ícono de flecha abajo (alt)"

#: includes/fields/class-acf-field-icon_picker.php:440
msgid "Amazon Icon"
msgstr "Ícono de Amazon"

#: includes/fields/class-acf-field-icon_picker.php:439
msgid "Align Wide Icon"
msgstr "Ícono de alinear ancho"

#: includes/fields/class-acf-field-icon_picker.php:437
msgid "Align Pull Right Icon"
msgstr "Ícono de alinear tirar a la derecha"

#: includes/fields/class-acf-field-icon_picker.php:436
msgid "Align Pull Left Icon"
msgstr "Ícono de alinear tirar a la izquierda"

#: includes/fields/class-acf-field-icon_picker.php:433
msgid "Align Full Width Icon"
msgstr "Ícono de alinear al ancho completo"

#: includes/fields/class-acf-field-icon_picker.php:430
msgid "Airplane Icon"
msgstr "Ícono del avión"

#: includes/fields/class-acf-field-icon_picker.php:427
msgid "Site (alt3) Icon"
msgstr "Ícono de sitio (alt3)"

#: includes/fields/class-acf-field-icon_picker.php:426
msgid "Site (alt2) Icon"
msgstr "Ícono de sitio (alt2)"

#: includes/fields/class-acf-field-icon_picker.php:425
msgid "Site (alt) Icon"
msgstr "Ícono de sitio (alt)"

#: includes/admin/views/options-page-preview.php:26
msgid "Upgrade to ACF PRO to create options pages in just a few clicks"
msgstr "Mejora a ACF PRO para crear páginas de opciones en unos pocos clics"

#: includes/ajax/class-acf-ajax-query-users.php:24
msgid "Invalid request args."
msgstr "Argumentos de solicitud no válidos."

#: includes/ajax/class-acf-ajax-check-screen.php:37
#: includes/ajax/class-acf-ajax-local-json-diff.php:37
#: includes/ajax/class-acf-ajax-query-users.php:33
#: includes/ajax/class-acf-ajax-upgrade.php:24
#: includes/ajax/class-acf-ajax-user-setting.php:38
msgid "Sorry, you do not have permission to do that."
msgstr "Lo siento, no tienes permisos para hacer eso."

#: src/Site_Health/Site_Health.php:720
msgid "Blocks Using Post Meta"
msgstr "Bloques con Post Meta"

#: includes/admin/views/acf-field-group/pro-features.php:25
#: includes/admin/views/acf-field-group/pro-features.php:27
#: includes/admin/views/global/header.php:27
msgid "ACF PRO logo"
msgstr "Logotipo de ACF PRO"

#: includes/admin/views/acf-field-group/field.php:37
msgid "ACF PRO Logo"
msgstr "Logotipo de ACF PRO"

#. translators: %s - field/param name
#: includes/fields/class-acf-field-icon_picker.php:813
msgid "%s requires a valid attachment ID when type is set to media_library."
msgstr ""
"%s requiere un ID de archivo adjunto válido cuando el tipo se establece en "
"media_library."

#. translators: %s - field name
#: includes/fields/class-acf-field-icon_picker.php:797
msgid "%s is a required property of acf."
msgstr "%s es una propiedad obligatoria de acf."

#: includes/fields/class-acf-field-icon_picker.php:773
msgid "The value of icon to save."
msgstr "El valor del ícono a guardar."

#: includes/fields/class-acf-field-icon_picker.php:767
msgid "The type of icon to save."
msgstr "El tipo de ícono a guardar."

#: includes/fields/class-acf-field-icon_picker.php:745
msgid "Yes Icon"
msgstr "Ícono de sí"

#: includes/fields/class-acf-field-icon_picker.php:742
msgid "WordPress Icon"
msgstr "Ícono de WordPress"

#: includes/fields/class-acf-field-icon_picker.php:734
msgid "Warning Icon"
msgstr "Ícono de advertencia"

#: includes/fields/class-acf-field-icon_picker.php:733
msgid "Visibility Icon"
msgstr "Ícono de visibilidad"

#: includes/fields/class-acf-field-icon_picker.php:729
msgid "Vault Icon"
msgstr "Ícono de la bóveda"

#: includes/fields/class-acf-field-icon_picker.php:728
msgid "Upload Icon"
msgstr "Ícono de subida"

#: includes/fields/class-acf-field-icon_picker.php:726
msgid "Update Icon"
msgstr "Ícono de actualización"

#: includes/fields/class-acf-field-icon_picker.php:725
msgid "Unlock Icon"
msgstr "Ícono de desbloqueo"

#: includes/fields/class-acf-field-icon_picker.php:723
msgid "Universal Access Icon"
msgstr "Ícono de acceso universal"

#: includes/fields/class-acf-field-icon_picker.php:722
msgid "Undo Icon"
msgstr "Ícono de deshacer"

#: includes/fields/class-acf-field-icon_picker.php:720
msgid "Twitter Icon"
msgstr "Ícono de Twitter"

#: includes/fields/class-acf-field-icon_picker.php:718
msgid "Trash Icon"
msgstr "Ícono de papelera"

#: includes/fields/class-acf-field-icon_picker.php:717
msgid "Translation Icon"
msgstr "Ícono de traducción"

#: includes/fields/class-acf-field-icon_picker.php:714
msgid "Tickets Icon"
msgstr "Ícono de tiques"

#: includes/fields/class-acf-field-icon_picker.php:713
msgid "Thumbs Up Icon"
msgstr "Ícono de pulgar hacia arriba"

#: includes/fields/class-acf-field-icon_picker.php:712
msgid "Thumbs Down Icon"
msgstr "Ícono de pulgar hacia abajo"

#: includes/fields/class-acf-field-icon_picker.php:633
#: includes/fields/class-acf-field-icon_picker.php:710
msgid "Text Icon"
msgstr "Ícono de texto"

#: includes/fields/class-acf-field-icon_picker.php:709
msgid "Testimonial Icon"
msgstr "Ícono de testimonio"

#: includes/fields/class-acf-field-icon_picker.php:708
msgid "Tagcloud Icon"
msgstr "Ícono de nube de etiquetas"

#: includes/fields/class-acf-field-icon_picker.php:707
msgid "Tag Icon"
msgstr "Ícono de etiqueta"

#: includes/fields/class-acf-field-icon_picker.php:706
msgid "Tablet Icon"
msgstr "Ícono de la tableta"

#: includes/fields/class-acf-field-icon_picker.php:697
msgid "Store Icon"
msgstr "Ícono de la tienda"

#: includes/fields/class-acf-field-icon_picker.php:696
msgid "Sticky Icon"
msgstr "Ícono fijo"

#: includes/fields/class-acf-field-icon_picker.php:695
msgid "Star Half Icon"
msgstr "Ícono media estrella"

#: includes/fields/class-acf-field-icon_picker.php:694
msgid "Star Filled Icon"
msgstr "Ícono de estrella rellena"

#: includes/fields/class-acf-field-icon_picker.php:693
msgid "Star Empty Icon"
msgstr "Ícono de estrella vacía"

#: includes/fields/class-acf-field-icon_picker.php:691
msgid "Sos Icon"
msgstr "Ícono Sos"

#: includes/fields/class-acf-field-icon_picker.php:690
msgid "Sort Icon"
msgstr "Ícono de ordenación"

#: includes/fields/class-acf-field-icon_picker.php:689
msgid "Smiley Icon"
msgstr "Ícono sonriente"

#: includes/fields/class-acf-field-icon_picker.php:688
msgid "Smartphone Icon"
msgstr "Ícono de smartphone"

#: includes/fields/class-acf-field-icon_picker.php:687
msgid "Slides Icon"
msgstr "Ícono de diapositivas"

#: includes/fields/class-acf-field-icon_picker.php:684
msgid "Shield Icon"
msgstr "Ícono de escudo"

#: includes/fields/class-acf-field-icon_picker.php:681
msgid "Share Icon"
msgstr "Ícono de compartir"

#: includes/fields/class-acf-field-icon_picker.php:680
msgid "Search Icon"
msgstr "Ícono de búsqueda"

#: includes/fields/class-acf-field-icon_picker.php:679
msgid "Screen Options Icon"
msgstr "Ícono de opciones de pantalla"

#: includes/fields/class-acf-field-icon_picker.php:678
msgid "Schedule Icon"
msgstr "Ícono de horario"

#: includes/fields/class-acf-field-icon_picker.php:673
msgid "Redo Icon"
msgstr "Ícono de rehacer"

#: includes/fields/class-acf-field-icon_picker.php:671
msgid "Randomize Icon"
msgstr "Ícono de aleatorio"

#: includes/fields/class-acf-field-icon_picker.php:670
msgid "Products Icon"
msgstr "Ícono de productos"

#: includes/fields/class-acf-field-icon_picker.php:667
msgid "Pressthis Icon"
msgstr "Ícono de presiona esto"

#: includes/fields/class-acf-field-icon_picker.php:666
msgid "Post Status Icon"
msgstr "Ícono de estado de la entrada"

#: includes/fields/class-acf-field-icon_picker.php:665
msgid "Portfolio Icon"
msgstr "Ícono de porfolio"

#: includes/fields/class-acf-field-icon_picker.php:661
msgid "Plus Icon"
msgstr "Ícono de más"

#: includes/fields/class-acf-field-icon_picker.php:659
msgid "Playlist Video Icon"
msgstr "Ícono de lista de reproducción de video"

#: includes/fields/class-acf-field-icon_picker.php:658
msgid "Playlist Audio Icon"
msgstr "Ícono de lista de reproducción de audio"

#: includes/fields/class-acf-field-icon_picker.php:656
msgid "Phone Icon"
msgstr "Ícono de teléfono"

#: includes/fields/class-acf-field-icon_picker.php:654
msgid "Performance Icon"
msgstr "Ícono de rendimiento"

#: includes/fields/class-acf-field-icon_picker.php:652
msgid "Paperclip Icon"
msgstr "Ícono del clip"

#: includes/fields/class-acf-field-icon_picker.php:648
msgid "No Icon"
msgstr "Sin ícono"

#: includes/fields/class-acf-field-icon_picker.php:647
msgid "Networking Icon"
msgstr "Ícono de red de contactos"

#: includes/fields/class-acf-field-icon_picker.php:646
msgid "Nametag Icon"
msgstr "Ícono de etiqueta de nombre"

#: includes/fields/class-acf-field-icon_picker.php:645
msgid "Move Icon"
msgstr "Ícono de mover"

#: includes/fields/class-acf-field-icon_picker.php:643
msgid "Money Icon"
msgstr "Ícono de dinero"

#: includes/fields/class-acf-field-icon_picker.php:642
msgid "Minus Icon"
msgstr "Ícono de menos"

#: includes/fields/class-acf-field-icon_picker.php:641
msgid "Migrate Icon"
msgstr "Ícono de migrar"

#: includes/fields/class-acf-field-icon_picker.php:640
msgid "Microphone Icon"
msgstr "Ícono de micrófono"

#: includes/fields/class-acf-field-icon_picker.php:635
msgid "Megaphone Icon"
msgstr "Ícono del megáfono"

#: includes/fields/class-acf-field-icon_picker.php:625
msgid "Marker Icon"
msgstr "Ícono del marcador"

#: includes/fields/class-acf-field-icon_picker.php:624
msgid "Lock Icon"
msgstr "Ícono de candado"

#: includes/fields/class-acf-field-icon_picker.php:622
msgid "Location Icon"
msgstr "Ícono de ubicación"

#: includes/fields/class-acf-field-icon_picker.php:621
msgid "List View Icon"
msgstr "Ícono de vista de lista"

#: includes/fields/class-acf-field-icon_picker.php:619
msgid "Lightbulb Icon"
msgstr "Ícono de bombilla"

#: includes/fields/class-acf-field-icon_picker.php:618
msgid "Left Right Icon"
msgstr "Ícono izquierda derecha"

#: includes/fields/class-acf-field-icon_picker.php:617
msgid "Layout Icon"
msgstr "Ícono de disposición"

#: includes/fields/class-acf-field-icon_picker.php:616
msgid "Laptop Icon"
msgstr "Ícono del portátil"

#: includes/fields/class-acf-field-icon_picker.php:610
msgid "Info Icon"
msgstr "Ícono de información"

#: includes/fields/class-acf-field-icon_picker.php:609
msgid "Index Card Icon"
msgstr "Ícono de tarjeta índice"

#: includes/fields/class-acf-field-icon_picker.php:598
msgid "ID Icon"
msgstr "Ícono de ID"

#: includes/fields/class-acf-field-icon_picker.php:595
msgid "Hidden Icon"
msgstr "Ícono de oculto"

#: includes/fields/class-acf-field-icon_picker.php:594
msgid "Heart Icon"
msgstr "Ícono del corazón"

#: includes/fields/class-acf-field-icon_picker.php:592
msgid "Hammer Icon"
msgstr "Ícono del martillo"

#: includes/fields/class-acf-field-icon_picker.php:470
#: includes/fields/class-acf-field-icon_picker.php:591
msgid "Groups Icon"
msgstr "Ícono de grupos"

#: includes/fields/class-acf-field-icon_picker.php:590
msgid "Grid View Icon"
msgstr "Ícono de vista en cuadrícula"

#: includes/fields/class-acf-field-icon_picker.php:585
msgid "Forms Icon"
msgstr "Ícono de formularios"

#: includes/fields/class-acf-field-icon_picker.php:575
msgid "Flag Icon"
msgstr "Ícono de bandera"

#: includes/fields/class-acf-field-icon_picker.php:574
#: includes/fields/class-acf-field-icon_picker.php:601
msgid "Filter Icon"
msgstr "Ícono del filtro"

#: includes/fields/class-acf-field-icon_picker.php:573
msgid "Feedback Icon"
msgstr "Ícono de respuestas"

#: includes/fields/class-acf-field-icon_picker.php:572
msgid "Facebook (alt) Icon"
msgstr "Ícono de Facebook alt"

#: includes/fields/class-acf-field-icon_picker.php:571
msgid "Facebook Icon"
msgstr "Ícono de Facebook"

#: includes/fields/class-acf-field-icon_picker.php:570
msgid "External Icon"
msgstr "Ícono externo"

#: includes/fields/class-acf-field-icon_picker.php:561
msgid "Email (alt) Icon"
msgstr "Ícono del correo electrónico alt"

#: includes/fields/class-acf-field-icon_picker.php:560
msgid "Email Icon"
msgstr "Ícono del correo electrónico"

#: includes/fields/class-acf-field-icon_picker.php:558
#: includes/fields/class-acf-field-icon_picker.php:584
#: includes/fields/class-acf-field-icon_picker.php:634
msgid "Video Icon"
msgstr "Ícono de video"

#: includes/fields/class-acf-field-icon_picker.php:557
msgid "Unlink Icon"
msgstr "Ícono de desenlazar"

#: includes/fields/class-acf-field-icon_picker.php:556
msgid "Underline Icon"
msgstr "Ícono de subrayado"

#: includes/fields/class-acf-field-icon_picker.php:554
msgid "Text Color Icon"
msgstr "Ícono de color de texto"

#: includes/fields/class-acf-field-icon_picker.php:553
msgid "Table Icon"
msgstr "Ícono de tabla"

#: includes/fields/class-acf-field-icon_picker.php:552
msgid "Strikethrough Icon"
msgstr "Ícono de tachado"

#: includes/fields/class-acf-field-icon_picker.php:551
msgid "Spellcheck Icon"
msgstr "Ícono del corrector ortográfico"

#: includes/fields/class-acf-field-icon_picker.php:549
msgid "Remove Formatting Icon"
msgstr "Ícono de quitar el formato"

#: includes/fields/class-acf-field-icon_picker.php:548
#: includes/fields/class-acf-field-icon_picker.php:582
msgid "Quote Icon"
msgstr "Ícono de cita"

#: includes/fields/class-acf-field-icon_picker.php:547
msgid "Paste Word Icon"
msgstr "Ícono de pegar palabra"

#: includes/fields/class-acf-field-icon_picker.php:546
msgid "Paste Text Icon"
msgstr "Ícono de pegar texto"

#: includes/fields/class-acf-field-icon_picker.php:545
msgid "Paragraph Icon"
msgstr "Ícono de párrafo"

#: includes/fields/class-acf-field-icon_picker.php:544
msgid "Outdent Icon"
msgstr "Ícono saliente"

#: includes/fields/class-acf-field-icon_picker.php:540
msgid "Kitchen Sink Icon"
msgstr "Ícono del fregadero"

#: includes/fields/class-acf-field-icon_picker.php:539
msgid "Justify Icon"
msgstr "Ícono de justificar"

#: includes/fields/class-acf-field-icon_picker.php:538
msgid "Italic Icon"
msgstr "Ícono cursiva"

#: includes/fields/class-acf-field-icon_picker.php:537
msgid "Insert More Icon"
msgstr "Ícono de insertar más"

#: includes/fields/class-acf-field-icon_picker.php:536
msgid "Indent Icon"
msgstr "Ícono de sangría"

#: includes/fields/class-acf-field-icon_picker.php:535
msgid "Help Icon"
msgstr "Ícono de ayuda"

#: includes/fields/class-acf-field-icon_picker.php:534
msgid "Expand Icon"
msgstr "Ícono de expandir"

#: includes/fields/class-acf-field-icon_picker.php:532
msgid "Contract Icon"
msgstr "Ícono de contrato"

#: includes/fields/class-acf-field-icon_picker.php:531
#: includes/fields/class-acf-field-icon_picker.php:628
msgid "Code Icon"
msgstr "Ícono de código"

#: includes/fields/class-acf-field-icon_picker.php:530
msgid "Break Icon"
msgstr "Ícono de rotura"

#: includes/fields/class-acf-field-icon_picker.php:529
msgid "Bold Icon"
msgstr "Ícono de negrita"

#: includes/fields/class-acf-field-icon_picker.php:523
msgid "Edit Icon"
msgstr "Ícono de editar"

#: includes/fields/class-acf-field-icon_picker.php:521
msgid "Download Icon"
msgstr "Ícono de descargar"

#: includes/fields/class-acf-field-icon_picker.php:520
msgid "Dismiss Icon"
msgstr "Ícono de descartar"

#: includes/fields/class-acf-field-icon_picker.php:519
msgid "Desktop Icon"
msgstr "Ícono del escritorio"

#: includes/fields/class-acf-field-icon_picker.php:512
msgid "Dashboard Icon"
msgstr "Ícono del escritorio"

#: includes/fields/class-acf-field-icon_picker.php:495
msgid "Cloud Icon"
msgstr "Ícono de nube"

#: includes/fields/class-acf-field-icon_picker.php:494
msgid "Clock Icon"
msgstr "Ícono de reloj"

#: includes/fields/class-acf-field-icon_picker.php:493
msgid "Clipboard Icon"
msgstr "Ícono del portapapeles"

#: includes/fields/class-acf-field-icon_picker.php:492
msgid "Chart Pie Icon"
msgstr "Ícono de gráfico de tarta"

#: includes/fields/class-acf-field-icon_picker.php:491
msgid "Chart Line Icon"
msgstr "Ícono de gráfico de líneas"

#: includes/fields/class-acf-field-icon_picker.php:490
msgid "Chart Bar Icon"
msgstr "Ícono de gráfico de barras"

#: includes/fields/class-acf-field-icon_picker.php:489
msgid "Chart Area Icon"
msgstr "Ícono de gráfico de área"

#: includes/fields/class-acf-field-icon_picker.php:488
msgid "Category Icon"
msgstr "Ícono de categoría"

#: includes/fields/class-acf-field-icon_picker.php:487
msgid "Cart Icon"
msgstr "Ícono del carrito"

#: includes/fields/class-acf-field-icon_picker.php:486
msgid "Carrot Icon"
msgstr "Ícono de zanahoria"

#: includes/fields/class-acf-field-icon_picker.php:483
msgid "Camera Icon"
msgstr "Ícono de cámara"

#: includes/fields/class-acf-field-icon_picker.php:482
msgid "Calendar (alt) Icon"
msgstr "Ícono de calendario alt"

#: includes/fields/class-acf-field-icon_picker.php:481
msgid "Calendar Icon"
msgstr "Ícono de calendario"

#: includes/fields/class-acf-field-icon_picker.php:478
msgid "Businesswoman Icon"
msgstr "Ícono de hombre de negocios"

#: includes/fields/class-acf-field-icon_picker.php:475
msgid "Building Icon"
msgstr "Ícono de edificio"

#: includes/fields/class-acf-field-icon_picker.php:462
msgid "Book Icon"
msgstr "Ícono del libro"

#: includes/fields/class-acf-field-icon_picker.php:457
msgid "Backup Icon"
msgstr "Ícono de copia de seguridad"

#: includes/fields/class-acf-field-icon_picker.php:456
msgid "Awards Icon"
msgstr "Ícono de premios"

#: includes/fields/class-acf-field-icon_picker.php:455
msgid "Art Icon"
msgstr "Ícono de arte"

#: includes/fields/class-acf-field-icon_picker.php:452
msgid "Arrow Up Icon"
msgstr "Ícono flecha arriba"

#: includes/fields/class-acf-field-icon_picker.php:449
msgid "Arrow Right Icon"
msgstr "Ícono flecha derecha"

#: includes/fields/class-acf-field-icon_picker.php:446
msgid "Arrow Left Icon"
msgstr "Ícono flecha izquierda"

#: includes/fields/class-acf-field-icon_picker.php:443
msgid "Arrow Down Icon"
msgstr "Ícono de flecha hacia abajo"

#: includes/fields/class-acf-field-icon_picker.php:442
#: includes/fields/class-acf-field-icon_picker.php:626
msgid "Archive Icon"
msgstr "Ícono de archivo"

#: includes/fields/class-acf-field-icon_picker.php:441
msgid "Analytics Icon"
msgstr "Ícono de análisis"

#: includes/fields/class-acf-field-icon_picker.php:438
#: includes/fields/class-acf-field-icon_picker.php:528
msgid "Align Right Icon"
msgstr "Ícono alinear a la derecha"

#: includes/fields/class-acf-field-icon_picker.php:435
msgid "Align None Icon"
msgstr "Ícono no alinear"

#: includes/fields/class-acf-field-icon_picker.php:434
#: includes/fields/class-acf-field-icon_picker.php:527
msgid "Align Left Icon"
msgstr "Ícono alinear a la izquierda"

#: includes/fields/class-acf-field-icon_picker.php:432
#: includes/fields/class-acf-field-icon_picker.php:526
msgid "Align Center Icon"
msgstr "Ícono alinear al centro"

#: includes/fields/class-acf-field-icon_picker.php:431
msgid "Album Icon"
msgstr "Ícono de álbum"

#: includes/fields/class-acf-field-icon_picker.php:429
msgid "Users Icon"
msgstr "Ícono de usuarios"

#: includes/fields/class-acf-field-icon_picker.php:428
msgid "Tools Icon"
msgstr "Ícono de herramientas"

#: includes/fields/class-acf-field-icon_picker.php:424
msgid "Site Icon"
msgstr "Ícono del sitio"

#: includes/fields/class-acf-field-icon_picker.php:423
msgid "Settings Icon"
msgstr "Ícono de ajustes"

#: includes/fields/class-acf-field-icon_picker.php:422
msgid "Post Icon"
msgstr "Ícono de la entrada"

#: includes/fields/class-acf-field-icon_picker.php:421
msgid "Plugins Icon"
msgstr "Ícono de plugins"

#: includes/fields/class-acf-field-icon_picker.php:420
msgid "Page Icon"
msgstr "Ícono de página"

#: includes/fields/class-acf-field-icon_picker.php:419
msgid "Network Icon"
msgstr "Ícono de red"

#: includes/fields/class-acf-field-icon_picker.php:418
msgid "Multisite Icon"
msgstr "Ícono multisitio"

#: includes/fields/class-acf-field-icon_picker.php:417
msgid "Media Icon"
msgstr "Ícono de medios"

#: includes/fields/class-acf-field-icon_picker.php:416
msgid "Links Icon"
msgstr "Ícono de enlaces"

#: includes/fields/class-acf-field-icon_picker.php:415
msgid "Home Icon"
msgstr "Ícono de inicio"

#: includes/fields/class-acf-field-icon_picker.php:413
msgid "Customizer Icon"
msgstr "Ícono del personalizador"

#: includes/fields/class-acf-field-icon_picker.php:412
#: includes/fields/class-acf-field-icon_picker.php:736
msgid "Comments Icon"
msgstr "Ícono de comentarios"

#: includes/fields/class-acf-field-icon_picker.php:411
msgid "Collapse Icon"
msgstr "Ícono de plegado"

#: includes/fields/class-acf-field-icon_picker.php:410
msgid "Appearance Icon"
msgstr "Ícono de apariencia"

#: includes/fields/class-acf-field-icon_picker.php:414
msgid "Generic Icon"
msgstr "Ícono genérico"

#: includes/fields/class-acf-field-icon_picker.php:346
msgid "Icon picker requires a value."
msgstr "El selector de íconos requiere un valor."

#: includes/fields/class-acf-field-icon_picker.php:341
msgid "Icon picker requires an icon type."
msgstr "El selector de íconos requiere un tipo de ícono."

#: includes/fields/class-acf-field-icon_picker.php:310
msgid ""
"The available icons matching your search query have been updated in the icon "
"picker below."
msgstr ""
"Los íconos disponibles que coinciden con tu consulta se han actualizado en "
"el selector de íconos de abajo."

#: includes/fields/class-acf-field-icon_picker.php:309
msgid "No results found for that search term"
msgstr "No se han encontrado resultados para ese término de búsqueda"

#: includes/fields/class-acf-field-icon_picker.php:291
msgid "Array"
msgstr "Array"

#: includes/fields/class-acf-field-icon_picker.php:290
msgid "String"
msgstr "Cadena"

#. translators: %s - link to documentation
#: includes/fields/class-acf-field-icon_picker.php:278
msgid "Specify the return format for the icon. %s"
msgstr "Especifica el formato de retorno del ícono. %s"

#: includes/fields/class-acf-field-icon_picker.php:263
msgid "Select where content editors can choose the icon from."
msgstr "Selecciona de dónde pueden elegir el ícono los editores de contenidos."

#: includes/fields/class-acf-field-icon_picker.php:224
msgid "The URL to the icon you'd like to use, or svg as Data URI"
msgstr "La URL del ícono que quieres utilizar, o svg como URI de datos"

#: includes/fields/class-acf-field-icon_picker.php:207
msgid "Browse Media Library"
msgstr "Explorar la biblioteca de medios"

#: includes/fields/class-acf-field-icon_picker.php:198
msgid "The currently selected image preview"
msgstr "La vista previa de la imagen seleccionada actualmente"

#: includes/fields/class-acf-field-icon_picker.php:189
msgid "Click to change the icon in the Media Library"
msgstr "Haz clic para cambiar el ícono de la biblioteca de medios"

#: includes/fields/class-acf-field-icon_picker.php:84
msgid "Search icons..."
msgstr "Buscar íconos…"

#: includes/fields/class-acf-field-icon_picker.php:53
msgid "Media Library"
msgstr "Biblioteca de medios"

#: includes/fields/class-acf-field-icon_picker.php:49
msgid "Dashicons"
msgstr "Dashicons"

#: includes/fields/class-acf-field-icon_picker.php:26
msgid ""
"An interactive UI for selecting an icon. Select from Dashicons, the media "
"library, or a standalone URL input."
msgstr ""
"Una interfaz de usuario interactiva para seleccionar un ícono. Selecciona "
"entre dashicons, la biblioteca de medios o una entrada URL independiente."

#: includes/fields/class-acf-field-icon_picker.php:23
msgid "Icon Picker"
msgstr "Selector de íconos"

#: src/Site_Health/Site_Health.php:781
msgid "JSON Load Paths"
msgstr "Rutas de carga JSON"

#: src/Site_Health/Site_Health.php:775
msgid "JSON Save Paths"
msgstr "Rutas de guardado JSON"

#: src/Site_Health/Site_Health.php:766
msgid "Registered ACF Forms"
msgstr "Formularios ACF registrados"

#: src/Site_Health/Site_Health.php:760
msgid "Shortcode Enabled"
msgstr "Shortcode activado"

#: src/Site_Health/Site_Health.php:752
msgid "Field Settings Tabs Enabled"
msgstr "Pestañas de ajustes de campo activadas"

#: src/Site_Health/Site_Health.php:744
msgid "Field Type Modal Enabled"
msgstr "Tipo de campo emergente activado"

#: src/Site_Health/Site_Health.php:736
msgid "Admin UI Enabled"
msgstr "Interfaz de administrador activada"

#: src/Site_Health/Site_Health.php:727
msgid "Block Preloading Enabled"
msgstr "Precarga de bloques activada"

#: src/Site_Health/Site_Health.php:715
msgid "Blocks Per ACF Block Version"
msgstr "Bloques por versión de bloque ACF"

#: src/Site_Health/Site_Health.php:710
msgid "Blocks Per API Version"
msgstr "Bloques por versión de API"

#: src/Site_Health/Site_Health.php:683
msgid "Registered ACF Blocks"
msgstr "Bloques ACF registrados"

#: src/Site_Health/Site_Health.php:677
msgid "Light"
msgstr "Claro"

#: src/Site_Health/Site_Health.php:677
msgid "Standard"
msgstr "Estándar"

#: src/Site_Health/Site_Health.php:676
msgid "REST API Format"
msgstr "Formato de la API REST"

#: src/Site_Health/Site_Health.php:668
msgid "Registered Options Pages (PHP)"
msgstr "Páginas de opciones registradas (PHP)"

#: src/Site_Health/Site_Health.php:654
msgid "Registered Options Pages (JSON)"
msgstr "Páginas de opciones registradas (JSON)"

#: src/Site_Health/Site_Health.php:649
msgid "Registered Options Pages (UI)"
msgstr "Páginas de opciones registradas (IU)"

#: src/Site_Health/Site_Health.php:619
msgid "Options Pages UI Enabled"
msgstr "Interfaz de usuario de las páginas de opciones activada"

#: src/Site_Health/Site_Health.php:611
msgid "Registered Taxonomies (JSON)"
msgstr "Taxonomías registradas (JSON)"

#: src/Site_Health/Site_Health.php:599
msgid "Registered Taxonomies (UI)"
msgstr "Taxonomías registradas (IU)"

#: src/Site_Health/Site_Health.php:587
msgid "Registered Post Types (JSON)"
msgstr "Tipos de contenido registrados (JSON)"

#: src/Site_Health/Site_Health.php:575
msgid "Registered Post Types (UI)"
msgstr "Tipos de contenido registrados (UI)"

#: src/Site_Health/Site_Health.php:562
msgid "Post Types and Taxonomies Enabled"
msgstr "Tipos de contenido y taxonomías activados"

#: src/Site_Health/Site_Health.php:555
msgid "Number of Third Party Fields by Field Type"
msgstr "Número de campos de terceros por tipo de campo"

#: src/Site_Health/Site_Health.php:550
msgid "Number of Fields by Field Type"
msgstr "Número de campos por tipo de campo"

#: src/Site_Health/Site_Health.php:449
msgid "Field Groups Enabled for GraphQL"
msgstr "Grupos de campos activados para GraphQL"

#: src/Site_Health/Site_Health.php:436
msgid "Field Groups Enabled for REST API"
msgstr "Grupos de campos activados para la API REST"

#: src/Site_Health/Site_Health.php:424
msgid "Registered Field Groups (JSON)"
msgstr "Grupos de campos registrados (JSON)"

#: src/Site_Health/Site_Health.php:412
msgid "Registered Field Groups (PHP)"
msgstr "Grupos de campos registrados (PHP)"

#: src/Site_Health/Site_Health.php:400
msgid "Registered Field Groups (UI)"
msgstr "Grupos de campos registrados (UI)"

#: src/Site_Health/Site_Health.php:388
msgid "Active Plugins"
msgstr "Plugins activos"

#: src/Site_Health/Site_Health.php:362
msgid "Parent Theme"
msgstr "Tema principal"

#: src/Site_Health/Site_Health.php:351
msgid "Active Theme"
msgstr "Tema activo"

#: src/Site_Health/Site_Health.php:342
msgid "Is Multisite"
msgstr "Es multisitio"

#: src/Site_Health/Site_Health.php:337
msgid "MySQL Version"
msgstr "Versión de MySQL"

#: src/Site_Health/Site_Health.php:332
msgid "WordPress Version"
msgstr "Versión de WordPress"

#: src/Site_Health/Site_Health.php:325
msgid "Subscription Expiry Date"
msgstr "Fecha de caducidad de la suscripción"

#: src/Site_Health/Site_Health.php:317
msgid "License Status"
msgstr "Estado de la licencia"

#: src/Site_Health/Site_Health.php:312
msgid "License Type"
msgstr "Tipo de licencia"

#: src/Site_Health/Site_Health.php:307
msgid "Licensed URL"
msgstr "URL con licencia"

#: src/Site_Health/Site_Health.php:301
msgid "License Activated"
msgstr "Licencia activada"

#: src/Site_Health/Site_Health.php:290
msgid "Free"
msgstr "Gratis"

#: src/Site_Health/Site_Health.php:289
msgid "Plugin Type"
msgstr "Tipo de plugin"

#: src/Site_Health/Site_Health.php:284
msgid "Plugin Version"
msgstr "Versión del plugin"

#: src/Site_Health/Site_Health.php:255
msgid ""
"This section contains debug information about your ACF configuration which "
"can be useful to provide to support."
msgstr ""
"Esta sección contiene información de depuración sobre la configuración de tu "
"ACF que puede ser útil proporcionar al servicio de asistencia."

#: includes/assets.php:373
msgid "An ACF Block on this page requires attention before you can save."
msgstr ""
"Un bloque de ACF en esta página requiere atención antes de que puedas "
"guardar."

#. translators: %s - The clear log button opening HTML tag. %s - The closing
#. HTML tag.
#: includes/admin/views/escaped-html-notice.php:63
msgid ""
"This data is logged as we detect values that have been changed during "
"output. %1$sClear log and dismiss%2$s after escaping the values in your "
"code. The notice will reappear if we detect changed values again."
msgstr ""
"Estos datos se registran a medida que detectamos valores que se han "
"modificado durante la salida. %1$sVaciar registro y descartar%2$s después de "
"escapar los valores en tu código. El aviso volverá a aparecer si volvemos a "
"detectar valores cambiados."

#: includes/admin/views/escaped-html-notice.php:25
msgid "Dismiss permanently"
msgstr "Descartar permanentemente"

#: includes/admin/views/acf-field-group/field.php:220
msgid "Instructions for content editors. Shown when submitting data."
msgstr ""
"Instrucciones para los editores de contenidos. Se muestra al enviar los "
"datos."

#: includes/admin/post-types/admin-field-group.php:143
msgid "Has no term selected"
msgstr "No tiene ningún término seleccionado"

#: includes/admin/post-types/admin-field-group.php:142
msgid "Has any term selected"
msgstr "¿Ha seleccionado algún término?"

#: includes/admin/post-types/admin-field-group.php:141
msgid "Terms do not contain"
msgstr "Los términos no contienen"

#: includes/admin/post-types/admin-field-group.php:140
msgid "Terms contain"
msgstr "Los términos contienen"

#: includes/admin/post-types/admin-field-group.php:139
msgid "Term is not equal to"
msgstr "El término no es igual a"

#: includes/admin/post-types/admin-field-group.php:138
msgid "Term is equal to"
msgstr "El término es igual a"

#: includes/admin/post-types/admin-field-group.php:137
msgid "Has no user selected"
msgstr "No tiene usuario seleccionado"

#: includes/admin/post-types/admin-field-group.php:136
msgid "Has any user selected"
msgstr "¿Ha seleccionado algún usuario"

#: includes/admin/post-types/admin-field-group.php:135
msgid "Users do not contain"
msgstr "Los usuarios no contienen"

#: includes/admin/post-types/admin-field-group.php:134
msgid "Users contain"
msgstr "Los usuarios contienen"

#: includes/admin/post-types/admin-field-group.php:133
msgid "User is not equal to"
msgstr "Usuario no es igual a"

#: includes/admin/post-types/admin-field-group.php:132
msgid "User is equal to"
msgstr "Usuario es igual a"

#: includes/admin/post-types/admin-field-group.php:131
msgid "Has no page selected"
msgstr "No tiene página seleccionada"

#: includes/admin/post-types/admin-field-group.php:130
msgid "Has any page selected"
msgstr "¿Has seleccionado alguna página?"

#: includes/admin/post-types/admin-field-group.php:129
msgid "Pages do not contain"
msgstr "Las páginas no contienen"

#: includes/admin/post-types/admin-field-group.php:128
msgid "Pages contain"
msgstr "Las páginas contienen"

#: includes/admin/post-types/admin-field-group.php:127
msgid "Page is not equal to"
msgstr "Página no es igual a"

#: includes/admin/post-types/admin-field-group.php:126
msgid "Page is equal to"
msgstr "Página es igual a"

#: includes/admin/post-types/admin-field-group.php:125
msgid "Has no relationship selected"
msgstr "No tiene ninguna relación seleccionada"

#: includes/admin/post-types/admin-field-group.php:124
msgid "Has any relationship selected"
msgstr "¿Ha seleccionado alguna relación?"

#: includes/admin/post-types/admin-field-group.php:123
msgid "Has no post selected"
msgstr "No tiene ninguna entrada seleccionada"

#: includes/admin/post-types/admin-field-group.php:122
msgid "Has any post selected"
msgstr "¿Has seleccionado alguna entrada?"

#: includes/admin/post-types/admin-field-group.php:121
msgid "Posts do not contain"
msgstr "Las entradas no contienen"

#: includes/admin/post-types/admin-field-group.php:120
msgid "Posts contain"
msgstr "Las entradas contienen"

#: includes/admin/post-types/admin-field-group.php:119
msgid "Post is not equal to"
msgstr "Entrada no es igual a"

#: includes/admin/post-types/admin-field-group.php:118
msgid "Post is equal to"
msgstr "Entrada es igual a"

#: includes/admin/post-types/admin-field-group.php:117
msgid "Relationships do not contain"
msgstr "Las relaciones no contienen"

#: includes/admin/post-types/admin-field-group.php:116
msgid "Relationships contain"
msgstr "Las relaciones contienen"

#: includes/admin/post-types/admin-field-group.php:115
msgid "Relationship is not equal to"
msgstr "La relación no es igual a"

#: includes/admin/post-types/admin-field-group.php:114
msgid "Relationship is equal to"
msgstr "La relación es igual a"

#: src/Blocks/Bindings.php:38
msgctxt "The core ACF block binding source name for fields on the current page"
msgid "ACF Fields"
msgstr "Campos de ACF"

#: includes/admin/views/browse-fields-modal.php:14
msgid "ACF PRO Feature"
msgstr "Característica de ACF PRO"

#: includes/admin/views/browse-fields-modal.php:10
msgid "Renew PRO to Unlock"
msgstr "Renueva PRO para desbloquear"

#: includes/admin/views/browse-fields-modal.php:8
msgid "Renew PRO License"
msgstr "Renovar licencia PRO"

#: includes/admin/views/acf-field-group/field.php:41
msgid "PRO fields cannot be edited without an active license."
msgstr "Los campos PRO no se pueden editar sin una licencia activa."

#: includes/admin/admin-internal-post-type-list.php:232
msgid ""
"Please activate your ACF PRO license to edit field groups assigned to an ACF "
"Block."
msgstr ""
"Activa tu licencia de ACF PRO para editar los grupos de campos asignados a "
"un Bloque ACF."

#: includes/admin/admin-internal-post-type-list.php:231
msgid "Please activate your ACF PRO license to edit this options page."
msgstr "Activa tu licencia de ACF PRO para editar esta página de opciones."

#: includes/api/api-template.php:385 includes/api/api-template.php:439
msgid ""
"Returning escaped HTML values is only possible when format_value is also "
"true. The field values have not been returned for security."
msgstr ""
"Devolver valores HTML escapados sólo es posible cuando format_value también "
"es rue. Los valores de los campos no se devuelven por seguridad."

#: includes/api/api-template.php:46 includes/api/api-template.php:251
#: includes/api/api-template.php:947
msgid ""
"Returning an escaped HTML value is only possible when format_value is also "
"true. The field value has not been returned for security."
msgstr ""
"Devolver un valor HTML escapado sólo es posible cuando format_value también "
"es true. El valor del campo no se devuelve por seguridad."

#. translators: %1$s - name of the ACF plugin. %2$s - Link to documentation.
#: includes/admin/views/escaped-html-notice.php:32
msgid ""
"%1$s ACF now automatically escapes unsafe HTML when rendered by "
"<code>the_field</code> or the ACF shortcode. We've detected the output of "
"some of your fields has been modified by this change, but this may not be a "
"breaking change. %2$s."
msgstr ""
"ACF %1$s ahora escapa automáticamente el HTML no seguro cuando es mostrado "
"por <code>the_field</code> o el shortcode de ACF. Hemos detectado que la "
"salida de algunos de tus campos se verá modificada por este cambio. %2$s."

#: includes/admin/views/escaped-html-notice.php:27
msgid "Please contact your site administrator or developer for more details."
msgstr ""
"Para más detalles, ponte en contacto con el administrador o desarrollador de "
"tu web."

#: includes/admin/views/escaped-html-notice.php:5
msgid "Learn&nbsp;more"
msgstr "Más&nbsp;información"

#: includes/admin/admin.php:63
msgid "Hide&nbsp;details"
msgstr "Ocultar&nbsp;detalles"

#: includes/admin/admin.php:62 includes/admin/views/escaped-html-notice.php:24
msgid "Show&nbsp;details"
msgstr "Mostrar&nbsp;detalles"

#. translators: %1$s - The selector used  %2$s The field name  3%$s The parent
#. function name
#: includes/admin/views/escaped-html-notice.php:49
msgid "%1$s (%2$s) - rendered via %3$s"
msgstr "%1$s (%2$s) - renderizado mediante %3$s"

#: includes/admin/views/global/navigation.php:229
msgid "Renew ACF PRO License"
msgstr "Renovar licencia ACF PRO"

#: includes/admin/views/acf-field-group/pro-features.php:17
msgid "Renew License"
msgstr "Renovar licencia"

#: includes/admin/views/acf-field-group/pro-features.php:14
msgid "Manage License"
msgstr "Gestionar la licencia"

#: includes/admin/views/acf-field-group/options.php:102
msgid "'High' position not supported in the Block Editor"
msgstr "No se admite la posición “Alta” en el editor de bloques"

#: includes/admin/views/options-page-preview.php:30
msgid "Upgrade to ACF PRO"
msgstr "Actualizar a ACF PRO"

#. translators: %s URL to ACF options pages documentation
#: includes/admin/views/options-page-preview.php:7
msgid ""
"ACF <a href=\"%s\" target=\"_blank\">options pages</a> are custom admin "
"pages for managing global settings via fields. You can create multiple pages "
"and sub-pages."
msgstr ""
"Las <a href=\"%s\" target=\"_blank\">páginas de opciones</a> de ACF son "
"páginas de administrador personalizadas para gestionar ajustes globales a "
"través de campos. Puedes crear múltiples páginas y subpáginas."

#: includes/admin/views/global/header.php:35
msgid "Add Options Page"
msgstr "Agregar página de opciones"

#: includes/admin/views/acf-post-type/advanced-settings.php:708
msgid "In the editor used as the placeholder of the title."
msgstr "En el editor utilizado como marcador de posición del título."

#: includes/admin/views/acf-post-type/advanced-settings.php:707
msgid "Title Placeholder"
msgstr "Marcador de posición del título"

#: includes/admin/views/global/navigation.php:97
msgid "4 Months Free"
msgstr "4 meses gratis"

#. translators: %s - A singular label for a post type or taxonomy.
#: includes/admin/views/global/form-top.php:59
msgid "(Duplicated from %s)"
msgstr "(Duplicado de %s)"

#: includes/admin/tools/class-acf-admin-tool-export.php:289
msgid "Select Options Pages"
msgstr "Seleccionar páginas de opciones"

#: includes/admin/post-types/admin-taxonomy.php:107
msgid "Duplicate taxonomy"
msgstr "Duplicar texonomía"

#: includes/admin/post-types/admin-post-type.php:106
#: includes/admin/post-types/admin-taxonomy.php:106
msgid "Create taxonomy"
msgstr "Crear taxonomía"

#: includes/admin/post-types/admin-post-type.php:105
msgid "Duplicate post type"
msgstr "Duplicar tipo de contenido"

#: includes/admin/post-types/admin-post-type.php:104
#: includes/admin/post-types/admin-taxonomy.php:108
msgid "Create post type"
msgstr "Crear tipo de contenido"

#: includes/admin/post-types/admin-post-type.php:103
#: includes/admin/post-types/admin-taxonomy.php:105
msgid "Link field groups"
msgstr "Enlazar grupos de campos"

#: includes/admin/post-types/admin-post-type.php:102
#: includes/admin/post-types/admin-taxonomy.php:104
msgid "Add fields"
msgstr "Agregar campos"

#: includes/admin/post-types/admin-field-group.php:147
msgid "This Field"
msgstr "Este campo"

#: includes/admin/admin.php:361
msgid "ACF PRO"
msgstr "ACF PRO"

#: includes/admin/admin.php:359
msgid "Feedback"
msgstr "Comentarios"

#: includes/admin/admin.php:357
msgid "Support"
msgstr "Soporte"

#. translators: This text is prepended by a link to ACF's website, and appended
#. by a link to WP Engine's website.
#: includes/admin/admin.php:332
msgid "is developed and maintained by"
msgstr "es desarrollado y mantenido por"

#. translators: %s - either "post type" or "taxonomy"
#: includes/admin/admin-internal-post-type.php:313
msgid "Add this %s to the location rules of the selected field groups."
msgstr ""
"Agregar %s actual a las reglas de localización de los grupos de campos "
"seleccionados."

#. translators: %s the URL to ACF's bidirectional relationship documentation
#: includes/acf-bidirectional-functions.php:272
msgid ""
"Enabling the bidirectional setting allows you to update a value in the "
"target fields for each value selected for this field, adding or removing the "
"Post ID, Taxonomy ID or User ID of the item being updated. For more "
"information, please read the <a href=\"%s\" target=\"_blank\">documentation</"
"a>."
msgstr ""
"Activar el ajuste bidireccional te permite actualizar un valor en los campos "
"de destino por cada valor seleccionado para este campo, agregando o "
"eliminando el ID de entrada, el ID de taxonomía o el ID de usuario del "
"elemento que se está actualizando. Para más información, lee la <a "
"href=\"%s\" target=\"_blank\">documentación</a>."

#: includes/acf-bidirectional-functions.php:248
msgid ""
"Select field(s) to store the reference back to the item being updated. You "
"may select this field. Target fields must be compatible with where this "
"field is being displayed. For example, if this field is displayed on a "
"Taxonomy, your target field should be of type Taxonomy"
msgstr ""
"Selecciona el/los campo/s para almacenar la referencia al artículo que se "
"está actualizando. Puedes seleccionar este campo. Los campos de destino "
"deben ser compatibles con el lugar donde se está mostrando este campo. Por "
"ejemplo, si este campo se muestra en una taxonomía, tu campo de destino debe "
"ser del tipo taxonomía"

#: includes/acf-bidirectional-functions.php:247
msgid "Target Field"
msgstr "Campo de destino"

#: includes/acf-bidirectional-functions.php:221
msgid "Update a field on the selected values, referencing back to this ID"
msgstr ""
"Actualiza un campo en los valores seleccionados, haciendo referencia a este "
"ID"

#: includes/acf-bidirectional-functions.php:220
msgid "Bidirectional"
msgstr "Bidireccional"

#. translators: %s A field type name, such as "Relationship"
#: includes/acf-bidirectional-functions.php:193
msgid "%s Field"
msgstr "Campo %s"

#: includes/fields/class-acf-field-page_link.php:498
#: includes/fields/class-acf-field-post_object.php:411
#: includes/fields/class-acf-field-select.php:378
#: includes/fields/class-acf-field-user.php:111
msgid "Select Multiple"
msgstr "Seleccionar varios"

#: includes/admin/views/global/navigation.php:241
msgid "WP Engine logo"
msgstr "Logotipo de WP Engine"

#: includes/admin/views/acf-taxonomy/basic-settings.php:58
msgid "Lower case letters, underscores and dashes only, Max 32 characters."
msgstr "Solo minúsculas, subrayados y guiones. Máximo 32 caracteres."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1156
msgid "The capability name for assigning terms of this taxonomy."
msgstr "El nombre de la capacidad para asignar términos de esta taxonomía."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1155
msgid "Assign Terms Capability"
msgstr "Capacidad de asignar términos"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1139
msgid "The capability name for deleting terms of this taxonomy."
msgstr "El nombre de la capacidad para borrar términos de esta taxonomía."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1138
msgid "Delete Terms Capability"
msgstr "Capacidad de eliminar términos"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1122
msgid "The capability name for editing terms of this taxonomy."
msgstr "El nombre de la capacidad para editar términos de esta taxonomía."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1121
msgid "Edit Terms Capability"
msgstr "Capacidad de editar términos"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1105
msgid "The capability name for managing terms of this taxonomy."
msgstr "El nombre de la capacidad para gestionar términos de esta taxonomía."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1104
msgid "Manage Terms Capability"
msgstr "Gestionar las capacidades para términos"

#: includes/admin/views/acf-post-type/advanced-settings.php:929
msgid ""
"Sets whether posts should be excluded from search results and taxonomy "
"archive pages."
msgstr ""
"Establece si las entradas deben excluirse de los resultados de búsqueda y de "
"las páginas de archivo de taxonomía."

#: includes/admin/views/acf-field-group/pro-features.php:78
msgid "More Tools from WP Engine"
msgstr "Más herramientas de WP Engine"

#. translators: %s - WP Engine logo
#: includes/admin/views/acf-field-group/pro-features.php:73
msgid "Built for those that build with WordPress, by the team at %s"
msgstr "Construido para los que construyen con WordPress, por el equipo de %s"

#: includes/admin/views/acf-field-group/pro-features.php:6
msgid "View Pricing & Upgrade"
msgstr "Ver precios y actualizar"

#: includes/admin/views/acf-field-group/pro-features.php:3
#: includes/admin/views/options-page-preview.php:29
#: includes/fields/class-acf-field-icon_picker.php:273
msgid "Learn More"
msgstr "Aprender más"

#: includes/admin/views/acf-field-group/pro-features.php:28
msgid ""
"Speed up your workflow and develop better websites with features like ACF "
"Blocks and Options Pages, and sophisticated field types like Repeater, "
"Flexible Content, Clone, and Gallery."
msgstr ""
"Acelera tu flujo de trabajo y desarrolla mejores sitios web con funciones "
"como Bloques ACF y Páginas de opciones, y sofisticados tipos de campo como "
"Repetidor, Contenido Flexible, Clonar y Galería."

#: includes/admin/views/acf-field-group/pro-features.php:2
msgid "Unlock Advanced Features and Build Even More with ACF PRO"
msgstr "Desbloquea funciones avanzadas y construye aún más con ACF PRO"

#. translators: %s - singular label of post type/taxonomy, i.e. "Movie"/"Genre"
#: includes/admin/views/global/form-top.php:19
msgid "%s fields"
msgstr "%s campos"

#: includes/admin/post-types/admin-taxonomies.php:267
msgid "No terms"
msgstr "Sin términos"

#: includes/admin/post-types/admin-taxonomies.php:240
msgid "No post types"
msgstr "Sin tipos de contenido"

#: includes/admin/post-types/admin-post-types.php:264
msgid "No posts"
msgstr "Sin entradas"

#: includes/admin/post-types/admin-post-types.php:238
msgid "No taxonomies"
msgstr "Sin taxonomías"

#: includes/admin/post-types/admin-post-types.php:183
#: includes/admin/post-types/admin-taxonomies.php:182
msgid "No field groups"
msgstr "Sin grupos de campos"

#: includes/admin/post-types/admin-field-groups.php:255
msgid "No fields"
msgstr "Sin campos"

#: includes/admin/post-types/admin-field-groups.php:128
#: includes/admin/post-types/admin-post-types.php:147
#: includes/admin/post-types/admin-taxonomies.php:146
msgid "No description"
msgstr "Sin descripción"

#: includes/fields/class-acf-field-page_link.php:465
#: includes/fields/class-acf-field-post_object.php:374
#: includes/fields/class-acf-field-relationship.php:573
msgid "Any post status"
msgstr "Cualquier estado de entrada"

#: includes/post-types/class-acf-taxonomy.php:288
msgid ""
"This taxonomy key is already in use by another taxonomy registered outside "
"of ACF and cannot be used."
msgstr ""
"Esta clave de taxonomía ya está siendo utilizada por otra taxonomía "
"registrada fuera de ACF y no puede utilizarse."

#: includes/post-types/class-acf-taxonomy.php:284
msgid ""
"This taxonomy key is already in use by another taxonomy in ACF and cannot be "
"used."
msgstr ""
"Esta clave de taxonomía ya está siendo utilizada por otra taxonomía en ACF y "
"no puede utilizarse."

#: includes/post-types/class-acf-taxonomy.php:256
msgid ""
"The taxonomy key must only contain lower case alphanumeric characters, "
"underscores or dashes."
msgstr ""
"La clave de taxonomía sólo debe contener caracteres alfanuméricos en "
"minúsculas, guiones bajos o guiones."

#: includes/post-types/class-acf-taxonomy.php:251
msgid "The taxonomy key must be under 32 characters."
msgstr "La clave taxonómica debe tener menos de 32 caracteres."

#: includes/post-types/class-acf-taxonomy.php:99
msgid "No Taxonomies found in Trash"
msgstr "No se han encontrado taxonomías en la papelera"

#: includes/post-types/class-acf-taxonomy.php:98
msgid "No Taxonomies found"
msgstr "No se han encontrado taxonomías"

#: includes/post-types/class-acf-taxonomy.php:97
msgid "Search Taxonomies"
msgstr "Buscar taxonomías"

#: includes/post-types/class-acf-taxonomy.php:96
msgid "View Taxonomy"
msgstr "Ver taxonomía"

#: includes/post-types/class-acf-taxonomy.php:95
msgid "New Taxonomy"
msgstr "Nueva taxonomía"

#: includes/post-types/class-acf-taxonomy.php:94
msgid "Edit Taxonomy"
msgstr "Editar taxonomía"

#: includes/post-types/class-acf-taxonomy.php:93
msgid "Add New Taxonomy"
msgstr "Agregar nueva taxonomía"

#: includes/post-types/class-acf-post-type.php:100
msgid "No Post Types found in Trash"
msgstr "No se han encontrado tipos de contenido en la papelera"

#: includes/post-types/class-acf-post-type.php:99
msgid "No Post Types found"
msgstr "No se han encontrado tipos de contenido"

#: includes/post-types/class-acf-post-type.php:98
msgid "Search Post Types"
msgstr "Buscar tipos de contenido"

#: includes/post-types/class-acf-post-type.php:97
msgid "View Post Type"
msgstr "Ver tipo de contenido"

#: includes/post-types/class-acf-post-type.php:96
msgid "New Post Type"
msgstr "Nuevo tipo de contenido"

#: includes/post-types/class-acf-post-type.php:95
msgid "Edit Post Type"
msgstr "Editar tipo de contenido"

#: includes/post-types/class-acf-post-type.php:94
msgid "Add New Post Type"
msgstr "Agregar nuevo tipo de contenido"

#: includes/post-types/class-acf-post-type.php:366
msgid ""
"This post type key is already in use by another post type registered outside "
"of ACF and cannot be used."
msgstr ""
"Esta clave de tipo de contenido ya está siendo utilizada por otro tipo de "
"contenido registrado fuera de ACF y no puede utilizarse."

#: includes/post-types/class-acf-post-type.php:361
msgid ""
"This post type key is already in use by another post type in ACF and cannot "
"be used."
msgstr ""
"Esta clave de tipo de contenido ya está siendo utilizada por otro tipo de "
"contenido en ACF y no puede utilizarse."

#. translators: %s a link to WordPress.org's Reserved Terms page
#: includes/post-types/class-acf-post-type.php:339
#: includes/post-types/class-acf-taxonomy.php:262
msgid ""
"This field must not be a WordPress <a href=\"%s\" target=\"_blank\">reserved "
"term</a>."
msgstr ""
"Este campo no debe ser un <a href=\"%s\" target=\"_blank\">término "
"reservado</a> de WordPress."

#: includes/post-types/class-acf-post-type.php:333
msgid ""
"The post type key must only contain lower case alphanumeric characters, "
"underscores or dashes."
msgstr ""
"La clave del tipo de contenido sólo debe contener caracteres alfanuméricos "
"en minúsculas, guiones bajos o guiones."

#: includes/post-types/class-acf-post-type.php:328
msgid "The post type key must be under 20 characters."
msgstr "La clave del tipo de contenido debe tener menos de 20 caracteres."

#: includes/fields/class-acf-field-wysiwyg.php:24
msgid "We do not recommend using this field in ACF Blocks."
msgstr "No recomendamos utilizar este campo en los ACF Blocks."

#: includes/fields/class-acf-field-wysiwyg.php:24
msgid ""
"Displays the WordPress WYSIWYG editor as seen in Posts and Pages allowing "
"for a rich text-editing experience that also allows for multimedia content."
msgstr ""
"Muestra el editor WYSIWYG de WordPress tal y como se ve en las Entradas y "
"Páginas, permitiendo una experiencia de edición de texto enriquecida que "
"también permite contenido multimedia."

#: includes/fields/class-acf-field-wysiwyg.php:22
msgid "WYSIWYG Editor"
msgstr "Editor WYSIWYG"

#: includes/fields/class-acf-field-user.php:17
msgid ""
"Allows the selection of one or more users which can be used to create "
"relationships between data objects."
msgstr ""
"Permite seleccionar uno o varios usuarios que pueden utilizarse para crear "
"relaciones entre objetos de datos."

#: includes/fields/class-acf-field-url.php:20
msgid "A text input specifically designed for storing web addresses."
msgstr ""
"Una entrada de texto diseñada específicamente para almacenar direcciones web."

#: includes/fields/class-acf-field-icon_picker.php:56
#: includes/fields/class-acf-field-url.php:19
msgid "URL"
msgstr "URL"

#: includes/fields/class-acf-field-true_false.php:24
msgid ""
"A toggle that allows you to pick a value of 1 or 0 (on or off, true or "
"false, etc). Can be presented as a stylized switch or checkbox."
msgstr ""
"Un conmutador que te permite elegir un valor de 1 ó 0 (encendido o apagado, "
"verdadero o falso, etc.). Puede presentarse como un interruptor estilizado o "
"una casilla de verificación."

#: includes/fields/class-acf-field-time_picker.php:24
msgid ""
"An interactive UI for picking a time. The time format can be customized "
"using the field settings."
msgstr ""
"Una interfaz de usuario interactiva para elegir una hora. El formato de la "
"hora se puede personalizar mediante los ajustes del campo."

#: includes/fields/class-acf-field-textarea.php:23
msgid "A basic textarea input for storing paragraphs of text."
msgstr "Una entrada de área de texto básica para almacenar párrafos de texto."

#: includes/fields/class-acf-field-text.php:23
msgid "A basic text input, useful for storing single string values."
msgstr ""
"Una entrada de texto básica, útil para almacenar valores de una sola cadena."

#: includes/fields/class-acf-field-taxonomy.php:22
msgid ""
"Allows the selection of one or more taxonomy terms based on the criteria and "
"options specified in the fields settings."
msgstr ""
"Permite seleccionar uno o varios términos de taxonomía en función de los "
"criterios y opciones especificados en los ajustes de los campos."

#: includes/fields/class-acf-field-tab.php:25
msgid ""
"Allows you to group fields into tabbed sections in the edit screen. Useful "
"for keeping fields organized and structured."
msgstr ""
"Te permite agrupar campos en secciones con pestañas en la pantalla de "
"edición. Útil para mantener los campos organizados y estructurados."

#: includes/fields/class-acf-field-select.php:18
msgid "A dropdown list with a selection of choices that you specify."
msgstr ""
"Una lista desplegable con una selección de opciones que tú especifiques."

#: includes/fields/class-acf-field-relationship.php:19
msgid ""
"A dual-column interface to select one or more posts, pages, or custom post "
"type items to create a relationship with the item that you're currently "
"editing. Includes options to search and filter."
msgstr ""
"Una interfaz de doble columna para seleccionar una o más entradas, páginas o "
"elementos de tipo contenido personalizado para crear una relación con el "
"elemento que estás editando en ese momento. Incluye opciones para buscar y "
"filtrar."

#: includes/fields/class-acf-field-range.php:23
msgid ""
"An input for selecting a numerical value within a specified range using a "
"range slider element."
msgstr ""
"Un campo para seleccionar un valor numérico dentro de un rango especificado "
"mediante un elemento deslizante de rango."

#: includes/fields/class-acf-field-radio.php:24
msgid ""
"A group of radio button inputs that allows the user to make a single "
"selection from values that you specify."
msgstr ""
"Un grupo de entradas de botón de opción que permite al usuario hacer una "
"única selección entre los valores que especifiques."

#: includes/fields/class-acf-field-post_object.php:17
msgid ""
"An interactive and customizable UI for picking one or many posts, pages or "
"post type items with the option to search. "
msgstr ""
"Una interfaz de usuario interactiva y personalizable para seleccionar una o "
"varias entradas, páginas o elementos de tipo contenido con la opción de "
"buscar. "

#: includes/fields/class-acf-field-password.php:23
msgid "An input for providing a password using a masked field."
msgstr ""
"Una entrada para proporcionar una contraseña utilizando un campo enmascarado."

#: includes/fields/class-acf-field-page_link.php:457
#: includes/fields/class-acf-field-post_object.php:366
#: includes/fields/class-acf-field-relationship.php:565
msgid "Filter by Post Status"
msgstr "Filtrar por estado de publicación"

#: includes/fields/class-acf-field-page_link.php:24
msgid ""
"An interactive dropdown to select one or more posts, pages, custom post type "
"items or archive URLs, with the option to search."
msgstr ""
"Un desplegable interactivo para seleccionar una o más entradas, páginas, "
"elementos de tipo contenido personalizad o URL de archivo, con la opción de "
"buscar."

#: includes/fields/class-acf-field-oembed.php:24
msgid ""
"An interactive component for embedding videos, images, tweets, audio and "
"other content by making use of the native WordPress oEmbed functionality."
msgstr ""
"Un componente interactivo para incrustar videos, imágenes, tweets, audio y "
"otros contenidos haciendo uso de la funcionalidad oEmbed nativa de WordPress."

#: includes/fields/class-acf-field-number.php:23
msgid "An input limited to numerical values."
msgstr "Una entrada limitada a valores numéricos."

#: includes/fields/class-acf-field-message.php:25
msgid ""
"Used to display a message to editors alongside other fields. Useful for "
"providing additional context or instructions around your fields."
msgstr ""
"Se utiliza para mostrar un mensaje a los editores junto a otros campos. Es "
"útil para proporcionar contexto adicional o instrucciones sobre tus campos."

#: includes/fields/class-acf-field-link.php:24
msgid ""
"Allows you to specify a link and its properties such as title and target "
"using the WordPress native link picker."
msgstr ""
"Te permite especificar un enlace y sus propiedades, como el título y el "
"destino, utilizando el selector de enlaces nativo de WordPress."

#: includes/fields/class-acf-field-image.php:24
msgid "Uses the native WordPress media picker to upload, or choose images."
msgstr ""
"Utiliza el selector de medios nativo de WordPress para subir o elegir "
"imágenes."

#: includes/fields/class-acf-field-group.php:24
msgid ""
"Provides a way to structure fields into groups to better organize the data "
"and the edit screen."
msgstr ""
"Proporciona una forma de estructurar los campos en grupos para organizar "
"mejor los datos y la pantalla de edición."

#: includes/fields/class-acf-field-google-map.php:24
msgid ""
"An interactive UI for selecting a location using Google Maps. Requires a "
"Google Maps API key and additional configuration to display correctly."
msgstr ""
"Una interfaz de usuario interactiva para seleccionar una ubicación "
"utilizando Google Maps. Requiere una clave API de Google Maps y "
"configuración adicional para mostrarse correctamente."

#: includes/fields/class-acf-field-file.php:24
msgid "Uses the native WordPress media picker to upload, or choose files."
msgstr ""
"Utiliza el selector de medios nativo de WordPress para subir o elegir "
"archivos."

#: includes/fields/class-acf-field-email.php:23
msgid "A text input specifically designed for storing email addresses."
msgstr ""
"Un campo de texto diseñado específicamente para almacenar direcciones de "
"correo electrónico."

#: includes/fields/class-acf-field-date_time_picker.php:24
msgid ""
"An interactive UI for picking a date and time. The date return format can be "
"customized using the field settings."
msgstr ""
"Una interfaz de usuario interactiva para elegir una fecha y una hora. El "
"formato de devolución de la fecha puede personalizarse mediante los ajustes "
"del campo."

#: includes/fields/class-acf-field-date_picker.php:24
msgid ""
"An interactive UI for picking a date. The date return format can be "
"customized using the field settings."
msgstr ""
"Una interfaz de usuario interactiva para elegir una fecha. El formato de "
"devolución de la fecha se puede personalizar mediante los ajustes del campo."

#: includes/fields/class-acf-field-color_picker.php:24
msgid "An interactive UI for selecting a color, or specifying a Hex value."
msgstr ""
"Una interfaz de usuario interactiva para seleccionar un color o especificar "
"un valor hexadecimal."

#: includes/fields/class-acf-field-checkbox.php:24
msgid ""
"A group of checkbox inputs that allow the user to select one, or multiple "
"values that you specify."
msgstr ""
"Un grupo de casillas de verificación que permiten al usuario seleccionar uno "
"o varios valores que tú especifiques."

#: includes/fields/class-acf-field-button-group.php:25
msgid ""
"A group of buttons with values that you specify, users can choose one option "
"from the values provided."
msgstr ""
"Un grupo de botones con valores que tú especifiques, los usuarios pueden "
"elegir una opción de entre los valores proporcionados."

#: includes/fields/class-acf-field-accordion.php:26
msgid ""
"Allows you to group and organize custom fields into collapsable panels that "
"are shown while editing content. Useful for keeping large datasets tidy."
msgstr ""
"Te permite agrupar y organizar campos personalizados en paneles plegables "
"que se muestran al editar el contenido. Útil para mantener ordenados grandes "
"conjuntos de datos."

#: includes/fields.php:449
msgid ""
"This provides a solution for repeating content such as slides, team members, "
"and call-to-action tiles, by acting as a parent to a set of subfields which "
"can be repeated again and again."
msgstr ""
"Esto proporciona una solución para repetir contenidos como diapositivas, "
"miembros del equipo y fichas de llamada a la acción, actuando como principal "
"de un conjunto de subcampos que pueden repetirse una y otra vez."

#: includes/fields.php:439
msgid ""
"This provides an interactive interface for managing a collection of "
"attachments. Most settings are similar to the Image field type. Additional "
"settings allow you to specify where new attachments are added in the gallery "
"and the minimum/maximum number of attachments allowed."
msgstr ""
"Proporciona una interfaz interactiva para gestionar una colección de "
"archivos adjuntos. La mayoría de los ajustes son similares a los del tipo de "
"campo Imagen. Los ajustes adicionales te permiten especificar dónde se "
"agregan los nuevos adjuntos en la galería y el número mínimo/máximo de "
"adjuntos permitidos."

#: includes/fields.php:429
msgid ""
"This provides a simple, structured, layout-based editor. The Flexible "
"Content field allows you to define, create and manage content with total "
"control by using layouts and subfields to design the available blocks."
msgstr ""
"Esto proporciona un editor sencillo, estructurado y basado en diseños. El "
"campo Contenido flexible te permite definir, crear y gestionar contenidos "
"con un control total, utilizando maquetas y subcampos para diseñar los "
"bloques disponibles."

#: includes/fields.php:419
msgid ""
"This allows you to select and display existing fields. It does not duplicate "
"any fields in the database, but loads and displays the selected fields at "
"run-time. The Clone field can either replace itself with the selected fields "
"or display the selected fields as a group of subfields."
msgstr ""
"Te permite seleccionar y mostrar los campos existentes. No duplica ningún "
"campo de la base de datos, sino que carga y muestra los campos seleccionados "
"en tiempo de ejecución. El campo Clonar puede sustituirse a sí mismo por los "
"campos seleccionados o mostrar los campos seleccionados como un grupo de "
"subcampos."

#: includes/fields.php:416
msgctxt "noun"
msgid "Clone"
msgstr "Clon"

#: includes/admin/views/global/navigation.php:86 includes/fields.php:331
#: src/Site_Health/Site_Health.php:290
msgid "PRO"
msgstr "PRO"

#: includes/fields.php:329 includes/fields.php:386
msgid "Advanced"
msgstr "Avanzados"

#: includes/ajax/class-acf-ajax-local-json-diff.php:90
msgid "JSON (newer)"
msgstr "JSON (nuevo)"

#: includes/ajax/class-acf-ajax-local-json-diff.php:86
msgid "Original"
msgstr "Original"

#: includes/ajax/class-acf-ajax-local-json-diff.php:60
msgid "Invalid post ID."
msgstr "ID de publicación no válido."

#: includes/ajax/class-acf-ajax-local-json-diff.php:52
msgid "Invalid post type selected for review."
msgstr "Tipo de contenido no válido seleccionado para revisión."

#: includes/admin/views/global/navigation.php:192
msgid "More"
msgstr "Más"

#: includes/admin/views/browse-fields-modal.php:96
msgid "Tutorial"
msgstr "Tutorial"

#: includes/admin/views/browse-fields-modal.php:73
msgid "Select Field"
msgstr "Seleccionar campo"

#. translators: %s: A link to the popular fields used in ACF
#: includes/admin/views/browse-fields-modal.php:60
msgid "Try a different search term or browse %s"
msgstr "Prueba con otro término de búsqueda o explora %s"

#: includes/admin/views/browse-fields-modal.php:57
msgid "Popular fields"
msgstr "Campos populares"

#. translators: %s: The invalid search term
#: includes/admin/views/browse-fields-modal.php:50
#: includes/fields/class-acf-field-icon_picker.php:97
msgid "No search results for '%s'"
msgstr "No hay resultados de búsqueda para “%s”"

#: includes/admin/views/browse-fields-modal.php:23
msgid "Search fields..."
msgstr "Buscar campos..."

#: includes/admin/views/browse-fields-modal.php:21
msgid "Select Field Type"
msgstr "Selecciona el tipo de campo"

#: includes/admin/views/browse-fields-modal.php:4
msgid "Popular"
msgstr "Populares"

#: includes/admin/views/acf-taxonomy/list-empty.php:15
msgid "Add Taxonomy"
msgstr "Agregar taxonomía"

#: includes/admin/views/acf-taxonomy/list-empty.php:14
msgid "Create custom taxonomies to classify post type content"
msgstr ""
"Crear taxonomías personalizadas para clasificar el contenido del tipo de "
"contenido"

#: includes/admin/views/acf-taxonomy/list-empty.php:13
msgid "Add Your First Taxonomy"
msgstr "Agrega tu primera taxonomía"

#: includes/admin/views/acf-taxonomy/basic-settings.php:122
msgid "Hierarchical taxonomies can have descendants (like categories)."
msgstr ""
"Las taxonomías jerárquicas pueden tener descendientes (como las categorías)."

#: includes/admin/views/acf-taxonomy/basic-settings.php:107
msgid "Makes a taxonomy visible on the frontend and in the admin dashboard."
msgstr ""
"Hace que una taxonomía sea visible en la parte pública de la web y en el "
"escritorio."

#: includes/admin/views/acf-taxonomy/basic-settings.php:91
msgid "One or many post types that can be classified with this taxonomy."
msgstr ""
"Uno o varios tipos de contenido que pueden clasificarse con esta taxonomía."

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:60
msgid "genre"
msgstr "género"

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:42
msgid "Genre"
msgstr "Género"

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:25
msgid "Genres"
msgstr "Géneros"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1231
msgid ""
"Optional custom controller to use instead of `WP_REST_Terms_Controller `."
msgstr ""
"Controlador personalizado opcional para utilizar en lugar de "
"`WP_REST_Terms_Controller`."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1175
msgid "Expose this post type in the REST API."
msgstr "Exponer este tipo de contenido en la REST API."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1075
msgid "Customize the query variable name"
msgstr "Personaliza el nombre de la variable de consulta"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1048
msgid ""
"Terms can be accessed using the non-pretty permalink, e.g., {query_var}"
"={term_slug}."
msgstr ""
"Se puede acceder a los términos utilizando el permalink no bonito, por "
"ejemplo, {query_var}={term_slug}."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1001
msgid "Parent-child terms in URLs for hierarchical taxonomies."
msgstr "Términos principal-hijo en URLs para taxonomías jerárquicas."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:961
msgid "Customize the slug used in the URL"
msgstr "Personalizar el slug utilizado en la URL"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:944
msgid "Permalinks for this taxonomy are disabled."
msgstr "Los enlaces permanentes de esta taxonomía están desactivados."

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-taxonomy/advanced-settings.php:941
msgid ""
"Rewrite the URL using the taxonomy key as the slug. Your permalink structure "
"will be"
msgstr ""
"Reescribe la URL utilizando la clave de taxonomía como slug. Tu estructura "
"de enlace permanente será"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:933
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1050
#: includes/admin/views/acf-taxonomy/basic-settings.php:57
msgid "Taxonomy Key"
msgstr "Clave de la taxonomía"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:931
msgid "Select the type of permalink to use for this taxonomy."
msgstr ""
"Selecciona el tipo de enlace permanente a utilizar para esta taxonomía."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:916
msgid "Display a column for the taxonomy on post type listing screens."
msgstr ""
"Mostrar una columna para la taxonomía en las pantallas de listado de tipos "
"de contenido."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:915
msgid "Show Admin Column"
msgstr "Mostrar columna de administración"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:902
msgid "Show the taxonomy in the quick/bulk edit panel."
msgstr "Mostrar la taxonomía en el panel de edición rápida/masiva."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:901
msgid "Quick Edit"
msgstr "Edición rápida"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:888
msgid "List the taxonomy in the Tag Cloud Widget controls."
msgstr "Muestra la taxonomía en los controles del widget nube de etiquetas."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:887
msgid "Tag Cloud"
msgstr "Nube de etiquetas"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:842
msgid ""
"A PHP function name to be called for sanitizing taxonomy data saved from a "
"meta box."
msgstr ""
"Un nombre de función PHP al que llamar para sanear los datos de taxonomía "
"guardados desde una meta box."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:841
msgid "Meta Box Sanitization Callback"
msgstr "Llamada a función de saneamiento de la caja meta"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:821
msgid "Register Meta Box Callback"
msgstr "Registrar llamada a función de caja meta"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:752
msgid "No Meta Box"
msgstr "Sin caja meta"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:751
msgid "Custom Meta Box"
msgstr "Caja meta personalizada"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:768
msgid ""
"Controls the meta box on the content editor screen. By default, the "
"Categories meta box is shown for hierarchical taxonomies, and the Tags meta "
"box is shown for non-hierarchical taxonomies."
msgstr ""
"Controla la caja meta en la pantalla del editor de contenidos. Por defecto, "
"la caja meta Categorías se muestra para las taxonomías jerárquicas, y la "
"meta caja Etiquetas se muestra para las taxonomías no jerárquicas."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:767
msgid "Meta Box"
msgstr "Caja meta"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:746
#: includes/admin/views/acf-taxonomy/advanced-settings.php:773
msgid "Categories Meta Box"
msgstr "Caja meta de categorías"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:745
#: includes/admin/views/acf-taxonomy/advanced-settings.php:772
msgid "Tags Meta Box"
msgstr "Caja meta de etiquetas"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:704
msgid "A link to a tag"
msgstr "Un enlace a una etiqueta"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:703
msgid "Describes a navigation link block variation used in the block editor."
msgstr ""
"Describe una variación del bloque de enlaces de navegación utilizada en el "
"editor de bloques."

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:698
msgid "A link to a %s"
msgstr "Un enlace a un %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:683
msgid "Tag Link"
msgstr "Enlace a etiqueta"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:682
msgid ""
"Assigns a title for navigation link block variation used in the block editor."
msgstr ""
"Asigna un título a la variación del bloque de enlaces de navegación "
"utilizada en el editor de bloques."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:663
msgid "← Go to tags"
msgstr "← Ir a las etiquetas"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:662
msgid ""
"Assigns the text used to link back to the main index after updating a term."
msgstr ""
"Asigna el texto utilizado para volver al índice principal tras actualizar un "
"término."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:661
msgid "Back To Items"
msgstr "Volver a los elementos"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:657
msgid "← Go to %s"
msgstr "← Ir a %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:642
msgid "Tags list"
msgstr "Lista de etiquetas"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:641
msgid "Assigns text to the table hidden heading."
msgstr "Asigna texto a la cabecera oculta de la tabla."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:622
msgid "Tags list navigation"
msgstr "Navegación de lista de etiquetas"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:621
msgid "Assigns text to the table pagination hidden heading."
msgstr "Asigna texto al encabezado oculto de la paginación de la tabla."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:597
msgid "Filter by category"
msgstr "Filtrar por categoría"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:596
msgid "Assigns text to the filter button in the posts lists table."
msgstr ""
"Asigna texto al botón de filtro en la tabla de listas de publicaciones."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:595
msgid "Filter By Item"
msgstr "Filtrar por elemento"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:591
msgid "Filter by %s"
msgstr "Filtrar por %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:575
#: includes/admin/views/acf-taxonomy/advanced-settings.php:576
msgid ""
"The description is not prominent by default; however, some themes may show "
"it."
msgstr ""
"La descripción no es prominente de forma predeterminada; Sin embargo, "
"algunos temas pueden mostrarlo."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:574
msgid "Describes the Description field on the Edit Tags screen."
msgstr "Describe el campo Descripción de la pantalla Editar etiquetas."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:573
msgid "Description Field Description"
msgstr "Descripción del campo Descripción"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:554
#: includes/admin/views/acf-taxonomy/advanced-settings.php:555
msgid ""
"Assign a parent term to create a hierarchy. The term Jazz, for example, "
"would be the parent of Bebop and Big Band"
msgstr ""
"Asigna un término superior para crear una jerarquía. El término Jazz, por "
"ejemplo, sería el principal de Bebop y Big Band"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:553
msgid "Describes the Parent field on the Edit Tags screen."
msgstr "Describe el campo superior de la pantalla Editar etiquetas."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:552
msgid "Parent Field Description"
msgstr "Descripción del campo principal"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:538
#: includes/admin/views/acf-taxonomy/advanced-settings.php:539
msgid ""
"The \"slug\" is the URL-friendly version of the name. It is usually all "
"lower case and contains only letters, numbers, and hyphens."
msgstr ""
"El “slug” es la versión apta para URLs del nombre. Normalmente se escribe "
"todo en minúsculas y sólo contiene letras, números y guiones."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:537
msgid "Describes the Slug field on the Edit Tags screen."
msgstr "Describe el campo slug de la pantalla editar etiquetas."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:536
msgid "Slug Field Description"
msgstr "Descripción del campo slug"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:522
#: includes/admin/views/acf-taxonomy/advanced-settings.php:523
msgid "The name is how it appears on your site"
msgstr "El nombre es como aparece en tu web"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:521
msgid "Describes the Name field on the Edit Tags screen."
msgstr "Describe el campo Nombre de la pantalla Editar etiquetas."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:520
msgid "Name Field Description"
msgstr "Descripción del campo nombre"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:507
msgid "No tags"
msgstr "No hay etiquetas"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:506
msgid ""
"Assigns the text displayed in the posts and media list tables when no tags "
"or categories are available."
msgstr ""
"Asigna el texto que se muestra en las tablas de entradas y lista de medios "
"cuando no hay etiquetas o categorías disponibles."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:505
msgid "No Terms"
msgstr "No hay términos"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:501
msgid "No %s"
msgstr "No hay %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:486
msgid "No tags found"
msgstr "No se han encontrado etiquetas"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:485
msgid ""
"Assigns the text displayed when clicking the 'choose from most used' text in "
"the taxonomy meta box when no tags are available, and assigns the text used "
"in the terms list table when there are no items for a taxonomy."
msgstr ""
"Asigna el texto que se muestra al hacer clic en “elegir entre los más "
"utilizados” en el cuadro meta de la taxonomía cuando no hay etiquetas "
"disponibles, y asigna el texto utilizado en la tabla de lista de términos "
"cuando no hay elementos para una taxonomía."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:484
msgid "Not Found"
msgstr "No encontrado"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:463
msgid "Assigns text to the Title field of the Most Used tab."
msgstr "Asigna texto al campo de título de la pestaña “Más usados”."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:462
#: includes/admin/views/acf-taxonomy/advanced-settings.php:464
#: includes/admin/views/acf-taxonomy/advanced-settings.php:465
msgid "Most Used"
msgstr "Más usados"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:444
msgid "Choose from the most used tags"
msgstr "Elige entre las etiquetas más utilizadas"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:443
msgid ""
"Assigns the 'choose from most used' text used in the meta box when "
"JavaScript is disabled. Only used on non-hierarchical taxonomies."
msgstr ""
"Asigna el texto “elige entre los más usados” que se utiliza en la meta caja "
"cuando JavaScript está desactivado. Sólo se utiliza en taxonomías no "
"jerárquicas."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:442
msgid "Choose From Most Used"
msgstr "Elige entre los más usados"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:438
msgid "Choose from the most used %s"
msgstr "Elige entre los %s más usados"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:418
msgid "Add or remove tags"
msgstr "Agregar o quitar etiquetas"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:417
msgid ""
"Assigns the add or remove items text used in the meta box when JavaScript is "
"disabled. Only used on non-hierarchical taxonomies"
msgstr ""
"Asigna el texto de agregar o eliminar elementos utilizado en la meta caja "
"cuando JavaScript está desactivado. Sólo se utiliza en taxonomías no "
"jerárquicas"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:416
msgid "Add Or Remove Items"
msgstr "Agregar o quitar elementos"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:412
msgid "Add or remove %s"
msgstr "Agregar o quitar %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:392
msgid "Separate tags with commas"
msgstr "Separa las etiquetas con comas"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:391
msgid ""
"Assigns the separate item with commas text used in the taxonomy meta box. "
"Only used on non-hierarchical taxonomies."
msgstr ""
"Asigna al elemento separado con comas el texto utilizado en la caja meta de "
"taxonomía. Sólo se utiliza en taxonomías no jerárquicas."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:390
msgid "Separate Items With Commas"
msgstr "Separa los elementos con comas"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:386
msgid "Separate %s with commas"
msgstr "Separa los %s con comas"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:366
msgid "Popular Tags"
msgstr "Etiquetas populares"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:365
msgid "Assigns popular items text. Only used for non-hierarchical taxonomies."
msgstr ""
"Asigna texto a los elementos populares. Sólo se utiliza en taxonomías no "
"jerárquicas."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:364
msgid "Popular Items"
msgstr "Elementos populares"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:361
msgid "Popular %s"
msgstr "%s populares"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:347
msgid "Search Tags"
msgstr "Buscar etiquetas"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:346
msgid "Assigns search items text."
msgstr "Asigna el texto de buscar elementos."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:323
msgid "Parent Category:"
msgstr "Categoría superior:"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:322
msgid "Assigns parent item text, but with a colon (:) added to the end."
msgstr ""
"Asigna el texto del elemento superior, pero agregando dos puntos (:) al "
"final."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:321
msgid "Parent Item With Colon"
msgstr "Elemento superior con dos puntos"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:298
msgid "Parent Category"
msgstr "Categoría superior"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:297
msgid "Assigns parent item text. Only used on hierarchical taxonomies."
msgstr ""
"Asigna el texto del elemento superior. Sólo se utiliza en taxonomías "
"jerárquicas."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:296
msgid "Parent Item"
msgstr "Elemento superior"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:293
msgid "Parent %s"
msgstr "%s superior"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:278
msgid "New Tag Name"
msgstr "Nombre de la nueva etiqueta"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:277
msgid "Assigns the new item name text."
msgstr "Asigna el texto del nombre del nuevo elemento."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:276
msgid "New Item Name"
msgstr "Nombre del nuevo elemento"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:273
msgid "New %s Name"
msgstr "Nombre del nuevo %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:258
msgid "Add New Tag"
msgstr "Agregar nueva etiqueta"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:257
msgid "Assigns the add new item text."
msgstr "Asigna el texto de agregar nuevo elemento."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:238
msgid "Update Tag"
msgstr "Actualizar etiqueta"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:237
msgid "Assigns the update item text."
msgstr "Asigna el texto del actualizar elemento."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:236
msgid "Update Item"
msgstr "Actualizar elemento"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:233
msgid "Update %s"
msgstr "Actualizar %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:218
msgid "View Tag"
msgstr "Ver etiqueta"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:217
msgid "In the admin bar to view term during editing."
msgstr "En la barra de administración para ver el término durante la edición."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:198
msgid "Edit Tag"
msgstr "Editar etiqueta"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:197
msgid "At the top of the editor screen when editing a term."
msgstr "En la parte superior de la pantalla del editor, al editar un término."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:178
msgid "All Tags"
msgstr "Todas las etiquetas"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:177
msgid "Assigns the all items text."
msgstr "Asigna el texto de todos los elementos."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:158
msgid "Assigns the menu name text."
msgstr "Asigna el texto del nombre del menú."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:157
msgid "Menu Label"
msgstr "Etiqueta de menú"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:131
msgid "Active taxonomies are enabled and registered with WordPress."
msgstr "Las taxonomías activas están activadas y registradas en WordPress."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:115
msgid "A descriptive summary of the taxonomy."
msgstr "Un resumen descriptivo de la taxonomía."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:95
msgid "A descriptive summary of the term."
msgstr "Un resumen descriptivo del término."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:94
msgid "Term Description"
msgstr "Descripción del término"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:76
msgid "Single word, no spaces. Underscores and dashes allowed."
msgstr "Una sola palabra, sin espacios. Se permiten guiones bajos y guiones."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:75
msgid "Term Slug"
msgstr "Slug de término"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:56
msgid "The name of the default term."
msgstr "El nombre del término por defecto."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:55
msgid "Term Name"
msgstr "Nombre del término"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:41
msgid ""
"Create a term for the taxonomy that cannot be deleted. It will not be "
"selected for posts by default."
msgstr ""
"Crea un término para la taxonomía que no se pueda eliminar. No se "
"seleccionará por defecto para las entradas."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:40
msgid "Default Term"
msgstr "Término por defecto"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:28
msgid ""
"Whether terms in this taxonomy should be sorted in the order they are "
"provided to `wp_set_object_terms()`."
msgstr ""
"Si los términos de esta taxonomía deben ordenarse en el orden en que se "
"proporcionan a `wp_set_object_terms()`."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:27
msgid "Sort Terms"
msgstr "Ordenar términos"

#: includes/admin/views/acf-post-type/list-empty.php:14
msgid "Add Post Type"
msgstr "Agregar tipo de contenido"

#: includes/admin/views/acf-post-type/list-empty.php:13
msgid ""
"Expand the functionality of WordPress beyond standard posts and pages with "
"custom post types."
msgstr ""
"Amplía la funcionalidad de WordPress más allá de las entradas y páginas "
"estándar con tipos de contenido personalizados."

#: includes/admin/views/acf-post-type/list-empty.php:12
msgid "Add Your First Post Type"
msgstr "Agrega tu primer tipo de contenido"

#: includes/admin/views/acf-post-type/basic-settings.php:136
#: includes/admin/views/acf-taxonomy/basic-settings.php:135
msgid "I know what I'm doing, show me all the options."
msgstr "Sé lo que hago, muéstrame todas las opciones."

#: includes/admin/views/acf-post-type/basic-settings.php:135
#: includes/admin/views/acf-taxonomy/basic-settings.php:134
msgid "Advanced Configuration"
msgstr "Configuración avanzada"

#: includes/admin/views/acf-post-type/basic-settings.php:123
msgid "Hierarchical post types can have descendants (like pages)."
msgstr ""
"Los tipos de entrada jerárquicos pueden tener descendientes (como las "
"páginas)."

#: includes/admin/views/acf-post-type/basic-settings.php:122
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1000
#: includes/admin/views/acf-taxonomy/basic-settings.php:121
msgid "Hierarchical"
msgstr "Jerárquico"

#: includes/admin/views/acf-post-type/basic-settings.php:107
msgid "Visible on the frontend and in the admin dashboard."
msgstr "Visible en la parte pública de la web y en el escritorio."

#: includes/admin/views/acf-post-type/basic-settings.php:106
#: includes/admin/views/acf-taxonomy/basic-settings.php:106
msgid "Public"
msgstr "Público"

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:59
msgid "movie"
msgstr "pelicula"

#: includes/admin/views/acf-post-type/basic-settings.php:57
msgid "Lower case letters, underscores and dashes only, Max 20 characters."
msgstr ""
"Sólo letras minúsculas, guiones bajos y guiones, 20 caracteres como máximo."

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:41
msgid "Movie"
msgstr "Película"

#: includes/admin/views/acf-post-type/basic-settings.php:39
#: includes/admin/views/acf-taxonomy/basic-settings.php:40
msgid "Singular Label"
msgstr "Etiqueta singular"

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:24
msgid "Movies"
msgstr "Películas"

#: includes/admin/views/acf-post-type/basic-settings.php:22
#: includes/admin/views/acf-taxonomy/basic-settings.php:23
msgid "Plural Label"
msgstr "Etiqueta plural"

#: includes/admin/views/acf-post-type/advanced-settings.php:1313
msgid ""
"Optional custom controller to use instead of `WP_REST_Posts_Controller`."
msgstr ""
"Controlador personalizado opcional para utilizar en lugar de "
"`WP_REST_Posts_Controller`."

#: includes/admin/views/acf-post-type/advanced-settings.php:1312
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1230
msgid "Controller Class"
msgstr "Clase de controlador"

#: includes/admin/views/acf-post-type/advanced-settings.php:1294
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1211
msgid "The namespace part of the REST API URL."
msgstr "La parte del espacio de nombres de la URL de la API REST."

#: includes/admin/views/acf-post-type/advanced-settings.php:1293
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1210
msgid "Namespace Route"
msgstr "Ruta del espacio de nombres"

#: includes/admin/views/acf-post-type/advanced-settings.php:1275
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1192
msgid "The base URL for the post type REST API URLs."
msgstr "La URL base para las URL de la REST API del tipo de contenido."

#: includes/admin/views/acf-post-type/advanced-settings.php:1274
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1191
msgid "Base URL"
msgstr "URL base"

#: includes/admin/views/acf-post-type/advanced-settings.php:1260
msgid ""
"Exposes this post type in the REST API. Required to use the block editor."
msgstr ""
"Expone este tipo de contenido en la REST API. Necesario para utilizar el "
"editor de bloques."

#: includes/admin/views/acf-post-type/advanced-settings.php:1259
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1174
msgid "Show In REST API"
msgstr "Mostrar en REST API"

#: includes/admin/views/acf-post-type/advanced-settings.php:1238
msgid "Customize the query variable name."
msgstr "Personaliza el nombre de la variable de consulta."

#: includes/admin/views/acf-post-type/advanced-settings.php:1237
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1074
msgid "Query Variable"
msgstr "Variable de consulta"

#: includes/admin/views/acf-post-type/advanced-settings.php:1215
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1052
msgid "No Query Variable Support"
msgstr "No admite variables de consulta"

#: includes/admin/views/acf-post-type/advanced-settings.php:1214
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1051
msgid "Custom Query Variable"
msgstr "Variable de consulta personalizada"

#: includes/admin/views/acf-post-type/advanced-settings.php:1211
msgid ""
"Items can be accessed using the non-pretty permalink, eg. {post_type}"
"={post_slug}."
msgstr ""
"Se puede acceder a los elementos utilizando el enlace permanente no bonito, "
"por ejemplo {post_type}={post_slug}."

#: includes/admin/views/acf-post-type/advanced-settings.php:1210
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1047
msgid "Query Variable Support"
msgstr "Compatibilidad con variables de consulta"

#: includes/admin/views/acf-post-type/advanced-settings.php:1185
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1023
msgid "URLs for an item and items can be accessed with a query string."
msgstr ""
"Se puede acceder a las URL de un elemento y de los elementos mediante una "
"cadena de consulta."

#: includes/admin/views/acf-post-type/advanced-settings.php:1184
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1022
msgid "Publicly Queryable"
msgstr "Consultable públicamente"

#: includes/admin/views/acf-post-type/advanced-settings.php:1163
msgid "Custom slug for the Archive URL."
msgstr "Slug personalizado para la URL del Archivo."

#: includes/admin/views/acf-post-type/advanced-settings.php:1162
msgid "Archive Slug"
msgstr "Slug del archivo"

#: includes/admin/views/acf-post-type/advanced-settings.php:1149
msgid ""
"Has an item archive that can be customized with an archive template file in "
"your theme."
msgstr ""
"Tiene un archivo de elementos que se puede personalizar con un archivo de "
"plantilla de archivo en tu tema."

#: includes/admin/views/acf-post-type/advanced-settings.php:1148
msgid "Archive"
msgstr "Archivo"

#: includes/admin/views/acf-post-type/advanced-settings.php:1128
msgid "Pagination support for the items URLs such as the archives."
msgstr ""
"Compatibilidad de paginación para las URL de los elementos, como los "
"archivos."

#: includes/admin/views/acf-post-type/advanced-settings.php:1127
msgid "Pagination"
msgstr "Paginación"

#: includes/admin/views/acf-post-type/advanced-settings.php:1110
msgid "RSS feed URL for the post type items."
msgstr "URL del feed RSS para los elementos del tipo de contenido."

#: includes/admin/views/acf-post-type/advanced-settings.php:1109
msgid "Feed URL"
msgstr "URL del Feed"

#: includes/admin/views/acf-post-type/advanced-settings.php:1091
#: includes/admin/views/acf-taxonomy/advanced-settings.php:981
msgid ""
"Alters the permalink structure to add the `WP_Rewrite::$front` prefix to "
"URLs."
msgstr ""
"Altera la estructura de enlaces permanentes para agregar el prefijo "
"`WP_Rewrite::$front` a las URLs."

#: includes/admin/views/acf-post-type/advanced-settings.php:1090
#: includes/admin/views/acf-taxonomy/advanced-settings.php:980
msgid "Front URL Prefix"
msgstr "Prefijo de las URLs"

#: includes/admin/views/acf-post-type/advanced-settings.php:1071
msgid "Customize the slug used in the URL."
msgstr "Personaliza el slug utilizado en la URL."

#: includes/admin/views/acf-post-type/advanced-settings.php:1070
#: includes/admin/views/acf-taxonomy/advanced-settings.php:960
msgid "URL Slug"
msgstr "Slug de la URL"

#: includes/admin/views/acf-post-type/advanced-settings.php:1054
msgid "Permalinks for this post type are disabled."
msgstr ""
"Los enlaces permanentes para este tipo de contenido están desactivados."

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-post-type/advanced-settings.php:1053
#: includes/admin/views/acf-taxonomy/advanced-settings.php:943
msgid ""
"Rewrite the URL using a custom slug defined in the input below. Your "
"permalink structure will be"
msgstr ""
"Reescribe la URL utilizando un slug personalizado definido en el campo de "
"abajo. Tu estructura de enlace permanente será"

#: includes/admin/views/acf-post-type/advanced-settings.php:1045
#: includes/admin/views/acf-taxonomy/advanced-settings.php:935
msgid "No Permalink (prevent URL rewriting)"
msgstr "Sin enlace permanente (evita la reescritura de URL)"

#: includes/admin/views/acf-post-type/advanced-settings.php:1044
#: includes/admin/views/acf-taxonomy/advanced-settings.php:934
msgid "Custom Permalink"
msgstr "Enlace permanente personalizado"

#: includes/admin/views/acf-post-type/advanced-settings.php:1043
#: includes/admin/views/acf-post-type/advanced-settings.php:1213
#: includes/admin/views/acf-post-type/basic-settings.php:56
msgid "Post Type Key"
msgstr "Clave de tipo de contenido"

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-post-type/advanced-settings.php:1041
#: includes/admin/views/acf-post-type/advanced-settings.php:1051
msgid ""
"Rewrite the URL using the post type key as the slug. Your permalink "
"structure will be"
msgstr ""
"Reescribe la URL utilizando la clave del tipo de entrada como slug. Tu "
"estructura de enlace permanente será"

#: includes/admin/views/acf-post-type/advanced-settings.php:1039
#: includes/admin/views/acf-taxonomy/advanced-settings.php:930
msgid "Permalink Rewrite"
msgstr "Reescritura de enlace permanente"

#: includes/admin/views/acf-post-type/advanced-settings.php:1025
msgid "Delete items by a user when that user is deleted."
msgstr "Borrar elementos de un usuario cuando ese usuario se borra."

#: includes/admin/views/acf-post-type/advanced-settings.php:1024
msgid "Delete With User"
msgstr "Borrar con usuario"

#: includes/admin/views/acf-post-type/advanced-settings.php:1010
msgid "Allow the post type to be exported from 'Tools' > 'Export'."
msgstr ""
"Permite que el tipo de contenido se pueda exportar desde 'Herramientas' > "
"'Exportar'."

#: includes/admin/views/acf-post-type/advanced-settings.php:1009
msgid "Can Export"
msgstr "Se puede exportar"

#: includes/admin/views/acf-post-type/advanced-settings.php:978
msgid "Optionally provide a plural to be used in capabilities."
msgstr ""
"Opcionalmente, proporciona un plural para utilizarlo en las capacidades."

#: includes/admin/views/acf-post-type/advanced-settings.php:977
msgid "Plural Capability Name"
msgstr "Nombre de la capacidad en plural"

#: includes/admin/views/acf-post-type/advanced-settings.php:959
msgid "Choose another post type to base the capabilities for this post type."
msgstr ""
"Elige otro tipo de contenido para basar las capacidades de este tipo de "
"contenido."

#: includes/admin/views/acf-post-type/advanced-settings.php:958
msgid "Singular Capability Name"
msgstr "Nombre de la capacidad en singular"

#: includes/admin/views/acf-post-type/advanced-settings.php:944
msgid ""
"By default the capabilities of the post type will inherit the 'Post' "
"capability names, eg. edit_post, delete_posts. Enable to use post type "
"specific capabilities, eg. edit_{singular}, delete_{plural}."
msgstr ""
"Por defecto, las capacidades del tipo de entrada heredarán los nombres de "
"las capacidades de 'Entrada', p. ej. edit_post, delete_posts. Actívalo para "
"utilizar capacidades específicas del tipo de contenido, por ejemplo, "
"edit_{singular}, delete_{plural}."

#: includes/admin/views/acf-post-type/advanced-settings.php:943
msgid "Rename Capabilities"
msgstr "Renombrar capacidades"

#: includes/admin/views/acf-post-type/advanced-settings.php:928
msgid "Exclude From Search"
msgstr "Excluir de la búsqueda"

#: includes/admin/views/acf-post-type/advanced-settings.php:915
#: includes/admin/views/acf-taxonomy/advanced-settings.php:874
msgid ""
"Allow items to be added to menus in the 'Appearance' > 'Menus' screen. Must "
"be turned on in 'Screen options'."
msgstr ""
"Permite agregar elementos a los menús en la pantalla 'Apariencia' > 'Menús'. "
"Debe estar activado en 'Opciones de pantalla'."

#: includes/admin/views/acf-post-type/advanced-settings.php:914
#: includes/admin/views/acf-taxonomy/advanced-settings.php:873
msgid "Appearance Menus Support"
msgstr "Compatibilidad con menús de apariencia"

#: includes/admin/views/acf-post-type/advanced-settings.php:896
msgid "Appears as an item in the 'New' menu in the admin bar."
msgstr ""
"Aparece como un elemento en el menú 'Nuevo' de la barra de administración."

#: includes/admin/views/acf-post-type/advanced-settings.php:895
msgid "Show In Admin Bar"
msgstr "Mostrar en la barra administración"

#: includes/admin/views/acf-post-type/advanced-settings.php:861
msgid "Custom Meta Box Callback"
msgstr "Llamada a función de caja meta personalizada"

#: includes/admin/views/acf-post-type/advanced-settings.php:822
#: includes/fields/class-acf-field-icon_picker.php:636
msgid "Menu Icon"
msgstr "Ícono de menú"

#: includes/admin/views/acf-post-type/advanced-settings.php:778
msgid "The position in the sidebar menu in the admin dashboard."
msgstr ""
"La posición en el menú de la barra lateral en el panel de control del "
"escritorio."

#: includes/admin/views/acf-post-type/advanced-settings.php:777
msgid "Menu Position"
msgstr "Posición en el menú"

#: includes/admin/views/acf-post-type/advanced-settings.php:759
msgid ""
"By default the post type will get a new top level item in the admin menu. If "
"an existing top level item is supplied here, the post type will be added as "
"a submenu item under it."
msgstr ""
"Por defecto, el tipo de contenido obtendrá un nuevo elemento de nivel "
"superior en el menú de administración. Si se proporciona aquí un elemento de "
"nivel superior existente, el tipo de entrada se agregará como un elemento de "
"submenú debajo de él."

#: includes/admin/views/acf-post-type/advanced-settings.php:758
msgid "Admin Menu Parent"
msgstr "Menú de administración principal"

#: includes/admin/views/acf-post-type/advanced-settings.php:739
#: includes/admin/views/acf-taxonomy/advanced-settings.php:734
msgid "Admin editor navigation in the sidebar menu."
msgstr ""
"Navegación del editor de administración en el menú de la barra lateral."

#: includes/admin/views/acf-post-type/advanced-settings.php:738
#: includes/admin/views/acf-taxonomy/advanced-settings.php:733
msgid "Show In Admin Menu"
msgstr "Mostrar en el menú de administración"

#: includes/admin/views/acf-post-type/advanced-settings.php:725
#: includes/admin/views/acf-taxonomy/advanced-settings.php:719
msgid "Items can be edited and managed in the admin dashboard."
msgstr ""
"Los elementos se pueden editar y gestionar en el panel de control del "
"administrador."

#: includes/admin/views/acf-post-type/advanced-settings.php:724
#: includes/admin/views/acf-taxonomy/advanced-settings.php:718
msgid "Show In UI"
msgstr "Mostrar en IU"

#: includes/admin/views/acf-post-type/advanced-settings.php:694
msgid "A link to a post."
msgstr "Un enlace a una publicación."

#: includes/admin/views/acf-post-type/advanced-settings.php:693
msgid "Description for a navigation link block variation."
msgstr "Descripción de una variación del bloque de enlaces de navegación."

#: includes/admin/views/acf-post-type/advanced-settings.php:692
#: includes/admin/views/acf-taxonomy/advanced-settings.php:702
msgid "Item Link Description"
msgstr "Descripción del enlace al elemento"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:688
msgid "A link to a %s."
msgstr "Un enlace a un %s."

#: includes/admin/views/acf-post-type/advanced-settings.php:673
msgid "Post Link"
msgstr "Enlace a publicación"

#: includes/admin/views/acf-post-type/advanced-settings.php:672
msgid "Title for a navigation link block variation."
msgstr "Título para una variación del bloque de enlaces de navegación."

#: includes/admin/views/acf-post-type/advanced-settings.php:671
#: includes/admin/views/acf-taxonomy/advanced-settings.php:681
msgid "Item Link"
msgstr "Enlace a elemento"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:668
#: includes/admin/views/acf-taxonomy/advanced-settings.php:678
msgid "%s Link"
msgstr "Enlace a %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:653
msgid "Post updated."
msgstr "Publicación actualizada."

#: includes/admin/views/acf-post-type/advanced-settings.php:652
msgid "In the editor notice after an item is updated."
msgstr "En el aviso del editor después de actualizar un elemento."

#: includes/admin/views/acf-post-type/advanced-settings.php:651
msgid "Item Updated"
msgstr "Elemento actualizado"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:648
msgid "%s updated."
msgstr "%s actualizado."

#: includes/admin/views/acf-post-type/advanced-settings.php:633
msgid "Post scheduled."
msgstr "Publicación programada."

#: includes/admin/views/acf-post-type/advanced-settings.php:632
msgid "In the editor notice after scheduling an item."
msgstr "En el aviso del editor después de programar un elemento."

#: includes/admin/views/acf-post-type/advanced-settings.php:631
msgid "Item Scheduled"
msgstr "Elemento programado"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:628
msgid "%s scheduled."
msgstr "%s programados."

#: includes/admin/views/acf-post-type/advanced-settings.php:613
msgid "Post reverted to draft."
msgstr "Publicación devuelta a borrador."

#: includes/admin/views/acf-post-type/advanced-settings.php:612
msgid "In the editor notice after reverting an item to draft."
msgstr "En el aviso del editor después de devolver un elemento a borrador."

#: includes/admin/views/acf-post-type/advanced-settings.php:611
msgid "Item Reverted To Draft"
msgstr "Elemento devuelto a borrador"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:608
msgid "%s reverted to draft."
msgstr "%s devuelto a borrador."

#: includes/admin/views/acf-post-type/advanced-settings.php:593
msgid "Post published privately."
msgstr "Publicación publicada de forma privada."

#: includes/admin/views/acf-post-type/advanced-settings.php:592
msgid "In the editor notice after publishing a private item."
msgstr "En el aviso del editor después de publicar un elemento privado."

#: includes/admin/views/acf-post-type/advanced-settings.php:591
msgid "Item Published Privately"
msgstr "Elemento publicado de forma privada"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:588
msgid "%s published privately."
msgstr "%s publicado de forma privada."

#: includes/admin/views/acf-post-type/advanced-settings.php:573
msgid "Post published."
msgstr "Entrada publicada."

#: includes/admin/views/acf-post-type/advanced-settings.php:572
msgid "In the editor notice after publishing an item."
msgstr "En el aviso del editor después de publicar un elemento."

#: includes/admin/views/acf-post-type/advanced-settings.php:571
msgid "Item Published"
msgstr "Elemento publicado"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:568
msgid "%s published."
msgstr "%s publicado."

#: includes/admin/views/acf-post-type/advanced-settings.php:553
msgid "Posts list"
msgstr "Lista de publicaciones"

#: includes/admin/views/acf-post-type/advanced-settings.php:552
msgid "Used by screen readers for the items list on the post type list screen."
msgstr ""
"Utilizado por los lectores de pantalla para la lista de elementos de la "
"pantalla de lista de tipos de contenido."

#: includes/admin/views/acf-post-type/advanced-settings.php:551
#: includes/admin/views/acf-taxonomy/advanced-settings.php:640
msgid "Items List"
msgstr "Lista de elementos"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:548
#: includes/admin/views/acf-taxonomy/advanced-settings.php:637
msgid "%s list"
msgstr "Lista de %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:533
msgid "Posts list navigation"
msgstr "Navegación por lista de publicaciones"

#: includes/admin/views/acf-post-type/advanced-settings.php:532
msgid ""
"Used by screen readers for the filter list pagination on the post type list "
"screen."
msgstr ""
"Utilizado por los lectores de pantalla para la paginación de la lista de "
"filtros en la pantalla de la lista de tipos de contenido."

#: includes/admin/views/acf-post-type/advanced-settings.php:531
#: includes/admin/views/acf-taxonomy/advanced-settings.php:620
msgid "Items List Navigation"
msgstr "Navegación por la lista de elementos"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:528
#: includes/admin/views/acf-taxonomy/advanced-settings.php:617
msgid "%s list navigation"
msgstr "Navegación por la lista de %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:512
msgid "Filter posts by date"
msgstr "Filtrar publicaciones por fecha"

#: includes/admin/views/acf-post-type/advanced-settings.php:511
msgid ""
"Used by screen readers for the filter by date heading on the post type list "
"screen."
msgstr ""
"Utilizado por los lectores de pantalla para el encabezado de filtrar por "
"fecha en la pantalla de lista de tipos de contenido."

#: includes/admin/views/acf-post-type/advanced-settings.php:510
msgid "Filter Items By Date"
msgstr "Filtrar elementos por fecha"

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:506
msgid "Filter %s by date"
msgstr "Filtrar %s por fecha"

#: includes/admin/views/acf-post-type/advanced-settings.php:491
msgid "Filter posts list"
msgstr "Filtrar la lista de publicaciones"

#: includes/admin/views/acf-post-type/advanced-settings.php:490
msgid ""
"Used by screen readers for the filter links heading on the post type list "
"screen."
msgstr ""
"Utilizado por los lectores de pantalla para el encabezado de los enlaces de "
"filtro en la pantalla de la lista de tipos de contenido."

#: includes/admin/views/acf-post-type/advanced-settings.php:489
msgid "Filter Items List"
msgstr "Filtrar lista de elementos"

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:485
msgid "Filter %s list"
msgstr "Filtrar lista de %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:469
msgid "In the media modal showing all media uploaded to this item."
msgstr ""
"En la ventana emergente de medios se muestran todos los medios subidos a "
"este elemento."

#: includes/admin/views/acf-post-type/advanced-settings.php:468
msgid "Uploaded To This Item"
msgstr "Subido a este elemento"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:464
msgid "Uploaded to this %s"
msgstr "Subido a este %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:449
msgid "Insert into post"
msgstr "Insertar en publicación"

#: includes/admin/views/acf-post-type/advanced-settings.php:448
msgid "As the button label when adding media to content."
msgstr "Como etiqueta del botón al agregar medios al contenido."

#: includes/admin/views/acf-post-type/advanced-settings.php:447
msgid "Insert Into Media Button"
msgstr "Botón Insertar en medios"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:443
msgid "Insert into %s"
msgstr "Insertar en %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:428
msgid "Use as featured image"
msgstr "Usar como imagen destacada"

#: includes/admin/views/acf-post-type/advanced-settings.php:427
msgid ""
"As the button label for selecting to use an image as the featured image."
msgstr ""
"Como etiqueta del botón para seleccionar el uso de una imagen como imagen "
"destacada."

#: includes/admin/views/acf-post-type/advanced-settings.php:426
msgid "Use Featured Image"
msgstr "Usar imagen destacada"

#: includes/admin/views/acf-post-type/advanced-settings.php:413
msgid "Remove featured image"
msgstr "Eliminar la imagen destacada"

#: includes/admin/views/acf-post-type/advanced-settings.php:412
msgid "As the button label when removing the featured image."
msgstr "Como etiqueta del botón al eliminar la imagen destacada."

#: includes/admin/views/acf-post-type/advanced-settings.php:411
msgid "Remove Featured Image"
msgstr "Eliminar imagen destacada"

#: includes/admin/views/acf-post-type/advanced-settings.php:398
msgid "Set featured image"
msgstr "Establecer imagen destacada"

#: includes/admin/views/acf-post-type/advanced-settings.php:397
msgid "As the button label when setting the featured image."
msgstr "Como etiqueta del botón al establecer la imagen destacada."

#: includes/admin/views/acf-post-type/advanced-settings.php:396
msgid "Set Featured Image"
msgstr "Establecer imagen destacada"

#: includes/admin/views/acf-post-type/advanced-settings.php:383
msgid "Featured image"
msgstr "Imagen destacada"

#: includes/admin/views/acf-post-type/advanced-settings.php:382
msgid "In the editor used for the title of the featured image meta box."
msgstr ""
"En el editor utilizado para el título de la caja meta de la imagen destacada."

#: includes/admin/views/acf-post-type/advanced-settings.php:381
msgid "Featured Image Meta Box"
msgstr "Caja meta de imagen destacada"

#: includes/admin/views/acf-post-type/advanced-settings.php:368
msgid "Post Attributes"
msgstr "Atributos de publicación"

#: includes/admin/views/acf-post-type/advanced-settings.php:367
msgid "In the editor used for the title of the post attributes meta box."
msgstr ""
"En el editor utilizado para el título de la caja meta de atributos de la "
"publicación."

#: includes/admin/views/acf-post-type/advanced-settings.php:366
msgid "Attributes Meta Box"
msgstr "Caja meta de atributos"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:363
msgid "%s Attributes"
msgstr "Atributos de %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:348
msgid "Post Archives"
msgstr "Archivo de publicaciones"

#: includes/admin/views/acf-post-type/advanced-settings.php:347
msgid ""
"Adds 'Post Type Archive' items with this label to the list of posts shown "
"when adding items to an existing menu in a CPT with archives enabled. Only "
"appears when editing menus in 'Live Preview' mode and a custom archive slug "
"has been provided."
msgstr ""
"Agrega elementos 'Archivo de tipo de contenido' con esta etiqueta a la lista "
"de publicaciones que se muestra al agregar elementos a un menú existente en "
"un CPT con archivos activados. Sólo aparece cuando se editan menús en modo "
"'Vista previa en vivo' y se ha proporcionado un slug de archivo "
"personalizado."

#: includes/admin/views/acf-post-type/advanced-settings.php:346
msgid "Archives Nav Menu"
msgstr "Menú de navegación de archivos"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:343
msgid "%s Archives"
msgstr "Archivo de %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:328
msgid "No posts found in Trash"
msgstr "No hay publicaciones en la papelera"

#: includes/admin/views/acf-post-type/advanced-settings.php:327
msgid ""
"At the top of the post type list screen when there are no posts in the trash."
msgstr ""
"En la parte superior de la pantalla de la lista de tipos de contenido cuando "
"no hay publicaciones en la papelera."

#: includes/admin/views/acf-post-type/advanced-settings.php:326
msgid "No Items Found in Trash"
msgstr "No se hay elementos en la papelera"

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:322
msgid "No %s found in Trash"
msgstr "No hay %s en la papelera"

#: includes/admin/views/acf-post-type/advanced-settings.php:307
msgid "No posts found"
msgstr "No se han encontrado publicaciones"

#: includes/admin/views/acf-post-type/advanced-settings.php:306
msgid ""
"At the top of the post type list screen when there are no posts to display."
msgstr ""
"En la parte superior de la pantalla de la lista de tipos de contenido cuando "
"no hay publicaciones que mostrar."

#: includes/admin/views/acf-post-type/advanced-settings.php:305
msgid "No Items Found"
msgstr "No se han encontrado elementos"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:301
#: includes/admin/views/acf-taxonomy/advanced-settings.php:480
msgid "No %s found"
msgstr "No se han encontrado %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:286
msgid "Search Posts"
msgstr "Buscar publicaciones"

#: includes/admin/views/acf-post-type/advanced-settings.php:285
msgid "At the top of the items screen when searching for an item."
msgstr ""
"En la parte superior de la pantalla de elementos, al buscar un elemento."

#: includes/admin/views/acf-post-type/advanced-settings.php:284
#: includes/admin/views/acf-taxonomy/advanced-settings.php:345
msgid "Search Items"
msgstr "Buscar elementos"

#. translators: %s Singular form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:281
#: includes/admin/views/acf-taxonomy/advanced-settings.php:342
msgid "Search %s"
msgstr "Buscar %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:266
msgid "Parent Page:"
msgstr "Página superior:"

#: includes/admin/views/acf-post-type/advanced-settings.php:265
msgid "For hierarchical types in the post type list screen."
msgstr "Para tipos jerárquicos en la pantalla de lista de tipos de contenido."

#: includes/admin/views/acf-post-type/advanced-settings.php:264
msgid "Parent Item Prefix"
msgstr "Prefijo del artículo superior"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:261
#: includes/admin/views/acf-taxonomy/advanced-settings.php:318
msgid "Parent %s:"
msgstr "%s superior:"

#: includes/admin/views/acf-post-type/advanced-settings.php:246
msgid "New Post"
msgstr "Nueva publicación"

#: includes/admin/views/acf-post-type/advanced-settings.php:244
msgid "New Item"
msgstr "Nuevo elemento"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:241
msgid "New %s"
msgstr "Nuevo %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:206
#: includes/admin/views/acf-post-type/advanced-settings.php:226
msgid "Add New Post"
msgstr "Agregar nueva publicación"

#: includes/admin/views/acf-post-type/advanced-settings.php:205
msgid "At the top of the editor screen when adding a new item."
msgstr ""
"En la parte superior de la pantalla del editor, al agregar un nuevo elemento."

#: includes/admin/views/acf-post-type/advanced-settings.php:204
#: includes/admin/views/acf-taxonomy/advanced-settings.php:256
msgid "Add New Item"
msgstr "Agregar nuevo elemento"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:201
#: includes/admin/views/acf-post-type/advanced-settings.php:221
#: includes/admin/views/acf-taxonomy/advanced-settings.php:253
msgid "Add New %s"
msgstr "Agregar nuevo %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:186
msgid "View Posts"
msgstr "Ver publicaciones"

#: includes/admin/views/acf-post-type/advanced-settings.php:185
msgid ""
"Appears in the admin bar in the 'All Posts' view, provided the post type "
"supports archives and the home page is not an archive of that post type."
msgstr ""
"Aparece en la barra de administración en la vista “Todas las publicaciones”, "
"siempre que el tipo de contenido admita archivos y la página de inicio no "
"sea un archivo de ese tipo de contenido."

#: includes/admin/views/acf-post-type/advanced-settings.php:184
msgid "View Items"
msgstr "Ver elementos"

#: includes/admin/views/acf-post-type/advanced-settings.php:166
msgid "View Post"
msgstr "Ver publicacion"

#: includes/admin/views/acf-post-type/advanced-settings.php:165
msgid "In the admin bar to view item when editing it."
msgstr "En la barra de administración para ver el elemento al editarlo."

#: includes/admin/views/acf-post-type/advanced-settings.php:164
#: includes/admin/views/acf-taxonomy/advanced-settings.php:216
msgid "View Item"
msgstr "Ver elemento"

#. translators: %s Singular form of post type name
#. translators: %s Plural form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:161
#: includes/admin/views/acf-post-type/advanced-settings.php:181
#: includes/admin/views/acf-taxonomy/advanced-settings.php:213
msgid "View %s"
msgstr "Ver %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:146
msgid "Edit Post"
msgstr "Editar publicación"

#: includes/admin/views/acf-post-type/advanced-settings.php:145
msgid "At the top of the editor screen when editing an item."
msgstr "En la parte superior de la pantalla del editor, al editar un elemento."

#: includes/admin/views/acf-post-type/advanced-settings.php:144
#: includes/admin/views/acf-taxonomy/advanced-settings.php:196
msgid "Edit Item"
msgstr "Editar elemento"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:141
#: includes/admin/views/acf-taxonomy/advanced-settings.php:193
msgid "Edit %s"
msgstr "Editar %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:126
msgid "All Posts"
msgstr "Todas las entradas"

#: includes/admin/views/acf-post-type/advanced-settings.php:125
#: includes/admin/views/acf-post-type/advanced-settings.php:225
#: includes/admin/views/acf-post-type/advanced-settings.php:245
msgid "In the post type submenu in the admin dashboard."
msgstr "En el submenú de tipo de contenido del escritorio."

#: includes/admin/views/acf-post-type/advanced-settings.php:124
#: includes/admin/views/acf-taxonomy/advanced-settings.php:176
msgid "All Items"
msgstr "Todos los elementos"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:121
#: includes/admin/views/acf-taxonomy/advanced-settings.php:173
msgid "All %s"
msgstr "Todos %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:105
msgid "Admin menu name for the post type."
msgstr "Nombre del menú de administración para el tipo de contenido."

#: includes/admin/views/acf-post-type/advanced-settings.php:104
msgid "Menu Name"
msgstr "Nombre del menú"

#: includes/admin/views/acf-post-type/advanced-settings.php:90
#: includes/admin/views/acf-taxonomy/advanced-settings.php:142
msgid "Regenerate all labels using the Singular and Plural labels"
msgstr ""
"Regenera todas las etiquetas utilizando las etiquetas singular y plural"

#: includes/admin/views/acf-post-type/advanced-settings.php:88
#: includes/admin/views/acf-taxonomy/advanced-settings.php:140
msgid "Regenerate"
msgstr "Regenerar"

#: includes/admin/views/acf-post-type/advanced-settings.php:79
msgid "Active post types are enabled and registered with WordPress."
msgstr ""
"Los tipos de entrada activos están activados y registrados en WordPress."

#: includes/admin/views/acf-post-type/advanced-settings.php:63
msgid "A descriptive summary of the post type."
msgstr "Un resumen descriptivo del tipo de contenido."

#: includes/admin/views/acf-post-type/advanced-settings.php:48
msgid "Add Custom"
msgstr "Agregar personalizado"

#: includes/admin/views/acf-post-type/advanced-settings.php:42
msgid "Enable various features in the content editor."
msgstr "Activa varias funciones en el editor de contenido."

#: includes/admin/views/acf-post-type/advanced-settings.php:31
msgid "Post Formats"
msgstr "Formatos de entrada"

#: includes/admin/views/acf-post-type/advanced-settings.php:25
msgid "Editor"
msgstr "Editor"

#: includes/admin/views/acf-post-type/advanced-settings.php:24
msgid "Trackbacks"
msgstr "Trackbacks"

#: includes/admin/views/acf-post-type/basic-settings.php:87
msgid "Select existing taxonomies to classify items of the post type."
msgstr ""
"Selecciona las taxonomías existentes para clasificar los elementos del tipo "
"de contenido."

#: includes/admin/views/acf-field-group/field.php:158
msgid "Browse Fields"
msgstr "Explorar campos"

#: includes/admin/tools/class-acf-admin-tool-import.php:287
msgid "Nothing to import"
msgstr "Nada que importar"

#: includes/admin/tools/class-acf-admin-tool-import.php:282
msgid ". The Custom Post Type UI plugin can be deactivated."
msgstr ". El plugin Custom Post Type UI se puede desactivar."

#. translators: %d - number of items imported from CPTUI
#: includes/admin/tools/class-acf-admin-tool-import.php:273
msgid "Imported %d item from Custom Post Type UI -"
msgid_plural "Imported %d items from Custom Post Type UI -"
msgstr[0] "Importado %d elemento de la interfaz de Custom Post Type UI -"
msgstr[1] "Importados %d elementos de la interfaz de Custom Post Type UI -"

#: includes/admin/tools/class-acf-admin-tool-import.php:257
msgid "Failed to import taxonomies."
msgstr "Error al importar taxonomías."

#: includes/admin/tools/class-acf-admin-tool-import.php:239
msgid "Failed to import post types."
msgstr "Error al importar tipos de contenido."

#: includes/admin/tools/class-acf-admin-tool-import.php:228
msgid "Nothing from Custom Post Type UI plugin selected for import."
msgstr ""
"No se ha seleccionado nada del plugin Custom Post Type UI para importar."

#: includes/admin/tools/class-acf-admin-tool-import.php:204
msgid "Imported 1 item"
msgid_plural "Imported %s items"
msgstr[0] "1 elementos importado"
msgstr[1] "%s elementos importados"

#: includes/admin/tools/class-acf-admin-tool-import.php:119
msgid ""
"Importing a Post Type or Taxonomy with the same key as one that already "
"exists will overwrite the settings for the existing Post Type or Taxonomy "
"with those of the import."
msgstr ""
"Al importar un tipo de contenido o taxonomía con la misma clave que uno ya "
"existente, se sobrescribirán los ajustes del tipo de contenido o taxonomía "
"existentes con los de la importación."

#: includes/admin/tools/class-acf-admin-tool-import.php:108
#: includes/admin/tools/class-acf-admin-tool-import.php:124
msgid "Import from Custom Post Type UI"
msgstr "Importar desde Custom Post Type UI"

#: includes/admin/tools/class-acf-admin-tool-export.php:354
msgid ""
"The following code can be used to register a local version of the selected "
"items. Storing field groups, post types, or taxonomies locally can provide "
"many benefits such as faster load times, version control & dynamic fields/"
"settings. Simply copy and paste the following code to your theme's "
"functions.php file or include it within an external file, then deactivate or "
"delete the items from the ACF admin."
msgstr ""
"El siguiente código puede utilizarse para registrar una versión local de los "
"elementos seleccionados. Almacenar grupos de campos, tipos de contenido o "
"taxonomías localmente puede proporcionar muchas ventajas, como tiempos de "
"carga más rápidos, control de versiones y campos/ajustes dinámicos. "
"Simplemente copia y pega el siguiente código en el archivo functions.php de "
"tu tema o inclúyelo dentro de un archivo externo, y luego desactiva o "
"elimina los elementos desde la administración de ACF."

#: includes/admin/tools/class-acf-admin-tool-export.php:353
msgid "Export - Generate PHP"
msgstr "Exportar - Generar PHP"

#: includes/admin/tools/class-acf-admin-tool-export.php:330
msgid "Export"
msgstr "Exportar"

#: includes/admin/tools/class-acf-admin-tool-export.php:264
msgid "Select Taxonomies"
msgstr "Selecciona taxonomías"

#: includes/admin/tools/class-acf-admin-tool-export.php:239
msgid "Select Post Types"
msgstr "Selecciona tipos de contenido"

#: includes/admin/tools/class-acf-admin-tool-export.php:155
msgid "Exported 1 item."
msgid_plural "Exported %s items."
msgstr[0] "1 elemento exportado."
msgstr[1] "%s elementos exportados."

#: includes/admin/post-types/admin-taxonomy.php:127
msgid "Category"
msgstr "Categoría"

#: includes/admin/post-types/admin-taxonomy.php:125
msgid "Tag"
msgstr "Etiqueta"

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:82
msgid "%s taxonomy created"
msgstr "Taxonomía %s creada"

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:76
msgid "%s taxonomy updated"
msgstr "Taxonomía %s actualizada"

#: includes/admin/post-types/admin-taxonomy.php:56
msgid "Taxonomy draft updated."
msgstr "Borrador de taxonomía actualizado."

#: includes/admin/post-types/admin-taxonomy.php:55
msgid "Taxonomy scheduled for."
msgstr "Taxonomía programada para."

#: includes/admin/post-types/admin-taxonomy.php:54
msgid "Taxonomy submitted."
msgstr "Taxonomía enviada."

#: includes/admin/post-types/admin-taxonomy.php:53
msgid "Taxonomy saved."
msgstr "Taxonomía guardada."

#: includes/admin/post-types/admin-taxonomy.php:49
msgid "Taxonomy deleted."
msgstr "Taxonomía borrada."

#: includes/admin/post-types/admin-taxonomy.php:48
msgid "Taxonomy updated."
msgstr "Taxonomía actualizada."

#: includes/admin/post-types/admin-taxonomies.php:351
#: includes/admin/post-types/admin-taxonomy.php:153
msgid ""
"This taxonomy could not be registered because its key is in use by another "
"taxonomy registered by another plugin or theme."
msgstr ""
"Esta taxonomía no se pudo registrar porque su clave está siendo utilizada "
"por otra taxonomía registrada por otro plugin o tema."

#. translators: %s number of taxonomies synchronized
#: includes/admin/post-types/admin-taxonomies.php:333
msgid "Taxonomy synchronized."
msgid_plural "%s taxonomies synchronized."
msgstr[0] "Taxonomía sincronizada."
msgstr[1] "%s taxonomías sincronizadas."

#. translators: %s number of taxonomies duplicated
#: includes/admin/post-types/admin-taxonomies.php:326
msgid "Taxonomy duplicated."
msgid_plural "%s taxonomies duplicated."
msgstr[0] "Taxonomía duplicada."
msgstr[1] "%s taxonomías duplicadas."

#. translators: %s number of taxonomies deactivated
#: includes/admin/post-types/admin-taxonomies.php:319
msgid "Taxonomy deactivated."
msgid_plural "%s taxonomies deactivated."
msgstr[0] "Taxonomía desactivada."
msgstr[1] "%s taxonomías desactivadas."

#. translators: %s number of taxonomies activated
#: includes/admin/post-types/admin-taxonomies.php:312
msgid "Taxonomy activated."
msgid_plural "%s taxonomies activated."
msgstr[0] "Taxonomía activada."
msgstr[1] "%s taxonomías activadas."

#: includes/admin/post-types/admin-taxonomies.php:113
msgid "Terms"
msgstr "Términos"

#. translators: %s number of post types synchronized
#: includes/admin/post-types/admin-post-types.php:327
msgid "Post type synchronized."
msgid_plural "%s post types synchronized."
msgstr[0] "Tipo de contenido sincronizado."
msgstr[1] "%s tipos de contenido sincronizados."

#. translators: %s number of post types duplicated
#: includes/admin/post-types/admin-post-types.php:320
msgid "Post type duplicated."
msgid_plural "%s post types duplicated."
msgstr[0] "Tipo de contenido duplicado."
msgstr[1] "%s tipos de contenido duplicados."

#. translators: %s number of post types deactivated
#: includes/admin/post-types/admin-post-types.php:313
msgid "Post type deactivated."
msgid_plural "%s post types deactivated."
msgstr[0] "Tipo de contenido desactivado."
msgstr[1] "%s tipos de contenido desactivados."

#. translators: %s number of post types activated
#: includes/admin/post-types/admin-post-types.php:306
msgid "Post type activated."
msgid_plural "%s post types activated."
msgstr[0] "Tipo de contenido activado."
msgstr[1] "%s tipos de contenido activados."

#: includes/admin/post-types/admin-post-types.php:87
#: includes/admin/post-types/admin-taxonomies.php:111
#: includes/admin/tools/class-acf-admin-tool-import.php:79
#: includes/admin/views/acf-taxonomy/basic-settings.php:82
#: includes/post-types/class-acf-post-type.php:91
msgid "Post Types"
msgstr "Tipos de contenido"

#: includes/admin/post-types/admin-post-type.php:158
#: includes/admin/post-types/admin-taxonomy.php:160
msgid "Advanced Settings"
msgstr "Ajustes avanzados"

#: includes/admin/post-types/admin-post-type.php:157
#: includes/admin/post-types/admin-taxonomy.php:159
msgid "Basic Settings"
msgstr "Ajustes básicos"

#: includes/admin/post-types/admin-post-type.php:151
#: includes/admin/post-types/admin-post-types.php:345
msgid ""
"This post type could not be registered because its key is in use by another "
"post type registered by another plugin or theme."
msgstr ""
"Este tipo de contenido no se pudo registrar porque su clave está siendo "
"utilizada por otro tipo de contenido registrado por otro plugin o tema."

#: includes/admin/post-types/admin-post-type.php:126
msgid "Pages"
msgstr "Páginas"

#: includes/admin/admin-internal-post-type.php:347
msgid "Link Existing Field Groups"
msgstr "Enlazar grupos de campos existentes"

#. translators: %s post type name
#: includes/admin/post-types/admin-post-type.php:80
msgid "%s post type created"
msgstr "%s tipo de contenido creado"

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:78
msgid "Add fields to %s"
msgstr "Agregar campos a %s"

#. translators: %s post type name
#: includes/admin/post-types/admin-post-type.php:76
msgid "%s post type updated"
msgstr "Tipo de contenido %s actualizado"

#: includes/admin/post-types/admin-post-type.php:56
msgid "Post type draft updated."
msgstr "Borrador de tipo de contenido actualizado."

#: includes/admin/post-types/admin-post-type.php:55
msgid "Post type scheduled for."
msgstr "Tipo de contenido programado para."

#: includes/admin/post-types/admin-post-type.php:54
msgid "Post type submitted."
msgstr "Tipo de contenido enviado."

#: includes/admin/post-types/admin-post-type.php:53
msgid "Post type saved."
msgstr "Tipo de contenido guardado."

#: includes/admin/post-types/admin-post-type.php:50
msgid "Post type updated."
msgstr "Tipo de contenido actualizado."

#: includes/admin/post-types/admin-post-type.php:49
msgid "Post type deleted."
msgstr "Tipo de contenido eliminado."

#: includes/admin/post-types/admin-field-group.php:146
msgid "Type to search..."
msgstr "Escribe para buscar..."

#: includes/admin/post-types/admin-field-group.php:101
msgid "PRO Only"
msgstr "Solo en PRO"

#: includes/admin/post-types/admin-field-group.php:93
msgid "Field groups linked successfully."
msgstr "Grupos de campos enlazados correctamente."

#. translators: %s - URL to ACF tools page.
#: includes/admin/admin.php:199
msgid ""
"Import Post Types and Taxonomies registered with Custom Post Type UI and "
"manage them with ACF. <a href=\"%s\">Get Started</a>."
msgstr ""
"Importa tipos de contenido y taxonomías registrados con Custom Post Type UI "
"y gestiónalos con ACF. <a href=\"%s\">Empieza aquí</a>."

#: includes/admin/admin.php:46 includes/admin/admin.php:361
#: src/Site_Health/Site_Health.php:254
msgid "ACF"
msgstr "ACF"

#: includes/admin/admin-internal-post-type.php:314
msgid "taxonomy"
msgstr "taxonomía"

#: includes/admin/admin-internal-post-type.php:314
msgid "post type"
msgstr "tipo de contenido"

#: includes/admin/admin-internal-post-type.php:338
msgid "Done"
msgstr "Hecho"

#: includes/admin/admin-internal-post-type.php:324
msgid "Field Group(s)"
msgstr "Grupo(s) de campo(s)"

#: includes/admin/admin-internal-post-type.php:323
msgid "Select one or many field groups..."
msgstr "Selecciona uno o varios grupos de campos..."

#: includes/admin/admin-internal-post-type.php:322
msgid "Please select the field groups to link."
msgstr "Selecciona los grupos de campos que quieras enlazar."

#: includes/admin/admin-internal-post-type.php:280
msgid "Field group linked successfully."
msgid_plural "Field groups linked successfully."
msgstr[0] "Grupo de campos enlazado correctamente."
msgstr[1] "Grupos de campos enlazados correctamente."

#: includes/admin/admin-internal-post-type-list.php:277
#: includes/admin/post-types/admin-post-types.php:346
#: includes/admin/post-types/admin-taxonomies.php:352
msgctxt "post status"
msgid "Registration Failed"
msgstr "Error de registro"

#: includes/admin/admin-internal-post-type-list.php:276
msgid ""
"This item could not be registered because its key is in use by another item "
"registered by another plugin or theme."
msgstr ""
"Este elemento no se pudo registrar porque su clave está siendo utilizada por "
"otro elemento registrado por otro plugin o tema."

#: includes/acf-internal-post-type-functions.php:509
#: includes/acf-internal-post-type-functions.php:538
msgid "REST API"
msgstr "REST API"

#: includes/acf-internal-post-type-functions.php:508
#: includes/acf-internal-post-type-functions.php:537
#: includes/acf-internal-post-type-functions.php:564
msgid "Permissions"
msgstr "Permisos"

#: includes/acf-internal-post-type-functions.php:507
#: includes/acf-internal-post-type-functions.php:536
msgid "URLs"
msgstr "URLs"

#: includes/acf-internal-post-type-functions.php:506
#: includes/acf-internal-post-type-functions.php:535
#: includes/acf-internal-post-type-functions.php:562
msgid "Visibility"
msgstr "Visibilidad"

#: includes/acf-internal-post-type-functions.php:505
#: includes/acf-internal-post-type-functions.php:534
#: includes/acf-internal-post-type-functions.php:563
msgid "Labels"
msgstr "Etiquetas"

#: includes/admin/post-types/admin-field-group.php:279
msgid "Field Settings Tabs"
msgstr "Pestañas de ajustes de campos"

#. Author URI of the plugin
#: acf.php
msgid ""
"https://wpengine.com/?"
"utm_source=wordpress.org&utm_medium=referral&utm_campaign=plugin_directory&utm_content=advanced_custom_fields"
msgstr ""
"https://wpengine.com/?"
"utm_source=wordpress.org&utm_medium=referral&utm_campaign=plugin_directory&utm_content=advanced_custom_fields"

#: includes/api/api-template.php:1027
msgid "[ACF shortcode value disabled for preview]"
msgstr "[valor del shortcode de ACF desactivado en la vista previa]"

#: includes/admin/admin-internal-post-type.php:290
#: includes/admin/post-types/admin-field-group.php:572
msgid "Close Modal"
msgstr "Cerrar ventana emergente"

#: includes/admin/post-types/admin-field-group.php:92
msgid "Field moved to other group"
msgstr "Campo movido a otro grupo"

#: includes/admin/post-types/admin-field-group.php:91
msgid "Close modal"
msgstr "Cerrar ventana emergente"

#: includes/fields/class-acf-field-tab.php:122
msgid "Start a new group of tabs at this tab."
msgstr "Empieza un nuevo grupo de pestañas en esta pestaña"

#: includes/fields/class-acf-field-tab.php:121
msgid "New Tab Group"
msgstr "Nuevo grupo de pestañas"

#: includes/fields/class-acf-field-select.php:421
#: includes/fields/class-acf-field-true_false.php:188
msgid "Use a stylized checkbox using select2"
msgstr "Usa una casilla de verificación estilizada utilizando select2"

#: includes/fields/class-acf-field-radio.php:250
msgid "Save Other Choice"
msgstr "Guardar la opción “Otro”"

#: includes/fields/class-acf-field-radio.php:239
msgid "Allow Other Choice"
msgstr "Permitir la opción “Otro”"

#: includes/fields/class-acf-field-checkbox.php:420
msgid "Add Toggle All"
msgstr "Agrega un “Alternar todos”"

#: includes/fields/class-acf-field-checkbox.php:379
msgid "Save Custom Values"
msgstr "Guardar los valores personalizados"

#: includes/fields/class-acf-field-checkbox.php:368
msgid "Allow Custom Values"
msgstr "Permitir valores personalizados"

#: includes/fields/class-acf-field-checkbox.php:134
msgid "Checkbox custom values cannot be empty. Uncheck any empty values."
msgstr ""
"Los valores personalizados de la casilla de verificación no pueden estar "
"vacíos. Desmarca cualquier valor vacío."

#: includes/admin/views/global/navigation.php:256
msgid "Updates"
msgstr "Actualizaciones"

#: includes/admin/views/global/navigation.php:180
#: includes/admin/views/global/navigation.php:184
msgid "Advanced Custom Fields logo"
msgstr "Logo de Advanced Custom Fields"

#: includes/admin/views/global/form-top.php:92
msgid "Save Changes"
msgstr "Guardar cambios"

#: includes/admin/views/global/form-top.php:79
msgid "Field Group Title"
msgstr "Título del grupo de campos"

#: includes/admin/views/acf-post-type/advanced-settings.php:709
#: includes/admin/views/global/form-top.php:3
msgid "Add title"
msgstr "Agregar título"

#. translators: %s url to getting started guide
#: includes/admin/views/acf-field-group/list-empty.php:30
#: includes/admin/views/acf-post-type/list-empty.php:20
#: includes/admin/views/acf-taxonomy/list-empty.php:21
#: includes/admin/views/options-page-preview.php:13
msgid ""
"New to ACF? Take a look at our <a href=\"%s\" target=\"_blank\">getting "
"started guide</a>."
msgstr ""
"¿Nuevo en ACF? Echa un vistazo a nuestra <a href=\"%s\" "
"target=\"_blank\">guía para comenzar</a>."

#: includes/admin/views/acf-field-group/list-empty.php:24
msgid "Add Field Group"
msgstr "Agregar grupo de campos"

#. translators: %s url to creating a field group page
#: includes/admin/views/acf-field-group/list-empty.php:18
msgid ""
"ACF uses <a href=\"%s\" target=\"_blank\">field groups</a> to group custom "
"fields together, and then attach those fields to edit screens."
msgstr ""
"ACF utiliza <a href=\"%s\" target=\"_blank\">grupos de campos</a> para "
"agrupar campos personalizados juntos, y después agregar esos campos a las "
"pantallas de edición."

#: includes/admin/views/acf-field-group/list-empty.php:12
msgid "Add Your First Field Group"
msgstr "Agrega tu primer grupo de campos"

#: includes/admin/admin-options-pages-preview.php:28
#: includes/admin/views/acf-field-group/pro-features.php:58
#: includes/admin/views/global/navigation.php:86
#: includes/admin/views/global/navigation.php:258
msgid "Options Pages"
msgstr "Páginas de opciones"

#: includes/admin/views/acf-field-group/pro-features.php:54
msgid "ACF Blocks"
msgstr "ACF Blocks"

#: includes/admin/views/acf-field-group/pro-features.php:62
msgid "Gallery Field"
msgstr "Campo galería"

#: includes/admin/views/acf-field-group/pro-features.php:42
msgid "Flexible Content Field"
msgstr "Campo de contenido flexible"

#: includes/admin/views/acf-field-group/pro-features.php:46
msgid "Repeater Field"
msgstr "Campo repetidor"

#: includes/admin/views/global/navigation.php:218
msgid "Unlock Extra Features with ACF PRO"
msgstr "Desbloquea las características extra con ACF PRO"

#: includes/admin/views/acf-field-group/options.php:267
msgid "Delete Field Group"
msgstr "Borrar grupo de campos"

#. translators: 1: Post creation date 2: Post creation time
#: includes/admin/views/acf-field-group/options.php:261
msgid "Created on %1$s at %2$s"
msgstr "Creado el %1$s a las %2$s"

#: includes/acf-field-group-functions.php:497
msgid "Group Settings"
msgstr "Ajustes de grupo"

#: includes/acf-field-group-functions.php:495
msgid "Location Rules"
msgstr "Reglas de ubicación"

#. translators: %s url to field types list
#: includes/admin/views/acf-field-group/fields.php:73
msgid ""
"Choose from over 30 field types. <a href=\"%s\" target=\"_blank\">Learn "
"more</a>."
msgstr ""
"Elige de entre más de 30 tipos de campos. <a href=\"%s\" "
"target=\"_blank\">Aprende más</a>."

#: includes/admin/views/acf-field-group/fields.php:65
msgid ""
"Get started creating new custom fields for your posts, pages, custom post "
"types and other WordPress content."
msgstr ""
"Comienza creando nuevos campos personalizados para tus entradas, páginas, "
"tipos de contenido personalizados y otros contenidos de WordPress."

#: includes/admin/views/acf-field-group/fields.php:64
msgid "Add Your First Field"
msgstr "Agrega tu primer campo"

#. translators: A symbol (or text, if not available in your locale) meaning
#. "Order Number", in terms of positional placement.
#: includes/admin/views/acf-field-group/fields.php:43
msgid "#"
msgstr "#"

#: includes/admin/views/acf-field-group/fields.php:33
#: includes/admin/views/acf-field-group/fields.php:67
#: includes/admin/views/acf-field-group/fields.php:101
#: includes/admin/views/global/form-top.php:88
msgid "Add Field"
msgstr "Agregar campo"

#: includes/acf-field-group-functions.php:496 includes/fields.php:384
msgid "Presentation"
msgstr "Presentación"

#: includes/fields.php:383
msgid "Validation"
msgstr "Validación"

#: includes/acf-internal-post-type-functions.php:504
#: includes/acf-internal-post-type-functions.php:533 includes/fields.php:382
msgid "General"
msgstr "General"

#: includes/admin/tools/class-acf-admin-tool-import.php:67
msgid "Import JSON"
msgstr "Importar JSON"

#: includes/admin/tools/class-acf-admin-tool-export.php:338
msgid "Export As JSON"
msgstr "Exportar como JSON"

#. translators: %s number of field groups deactivated
#: includes/admin/post-types/admin-field-groups.php:360
msgid "Field group deactivated."
msgid_plural "%s field groups deactivated."
msgstr[0] "Grupo de campos desactivado."
msgstr[1] "%s grupos de campos desactivados."

#. translators: %s number of field groups activated
#: includes/admin/post-types/admin-field-groups.php:353
msgid "Field group activated."
msgid_plural "%s field groups activated."
msgstr[0] "Grupo de campos activado."
msgstr[1] "%s grupos de campos activados."

#: includes/admin/admin-internal-post-type-list.php:470
#: includes/admin/admin-internal-post-type-list.php:496
msgid "Deactivate"
msgstr "Desactivar"

#: includes/admin/admin-internal-post-type-list.php:470
msgid "Deactivate this item"
msgstr "Desactiva este elemento"

#: includes/admin/admin-internal-post-type-list.php:466
#: includes/admin/admin-internal-post-type-list.php:495
msgid "Activate"
msgstr "Activar"

#: includes/admin/admin-internal-post-type-list.php:466
msgid "Activate this item"
msgstr "Activa este elemento"

#: includes/admin/post-types/admin-field-group.php:88
msgid "Move field group to trash?"
msgstr "¿Mover este grupo de campos a la papelera?"

#: acf.php:520 includes/admin/admin-internal-post-type-list.php:264
#: includes/admin/post-types/admin-field-group.php:304
#: includes/admin/post-types/admin-post-type.php:282
#: includes/admin/post-types/admin-taxonomy.php:284
msgctxt "post status"
msgid "Inactive"
msgstr "Inactivo"

#. Author of the plugin
#: acf.php includes/admin/views/global/navigation.php:240
msgid "WP Engine"
msgstr "WP Engine"

#: acf.php:578
msgid ""
"Advanced Custom Fields and Advanced Custom Fields PRO should not be active "
"at the same time. We've automatically deactivated Advanced Custom Fields PRO."
msgstr ""
"Advanced Custom Fields y Advanced Custom Fields PRO no deberían estar "
"activos al mismo tiempo. Hemos desactivado automáticamente Advanced Custom "
"Fields PRO."

#: acf.php:576
msgid ""
"Advanced Custom Fields and Advanced Custom Fields PRO should not be active "
"at the same time. We've automatically deactivated Advanced Custom Fields."
msgstr ""
"Advanced Custom Fields y Advanced Custom Fields PRO no deberían estar "
"activos al mismo tiempo. Hemos desactivado automáticamente Advanced Custom "
"Fields."

#: includes/fields/class-acf-field-user.php:578
msgid "%1$s must have a user with the %2$s role."
msgid_plural "%1$s must have a user with one of the following roles: %2$s"
msgstr[0] "%1$s debe tener un usuario con el perfil %2$s."
msgstr[1] "%1$s debe tener un usuario con uno de los siguientes perfiles: %2$s"

#: includes/fields/class-acf-field-user.php:569
msgid "%1$s must have a valid user ID."
msgstr "%1$s debe tener un ID de usuario válido."

#: includes/fields/class-acf-field-user.php:408
msgid "Invalid request."
msgstr "Petición no válida."

#: includes/fields/class-acf-field-select.php:689
msgid "%1$s is not one of %2$s"
msgstr "%1$s no es ninguna de las siguientes %2$s"

#: includes/fields/class-acf-field-post_object.php:660
msgid "%1$s must have term %2$s."
msgid_plural "%1$s must have one of the following terms: %2$s"
msgstr[0] "%1$s debe tener un término %2$s."
msgstr[1] "%1$s debe tener uno de los siguientes términos: %2$s"

#: includes/fields/class-acf-field-post_object.php:644
msgid "%1$s must be of post type %2$s."
msgid_plural "%1$s must be of one of the following post types: %2$s"
msgstr[0] "%1$s debe ser del tipo de contenido %2$s."
msgstr[1] "%1$s debe ser de uno de los siguientes tipos de contenido: %2$s"

#: includes/fields/class-acf-field-post_object.php:635
msgid "%1$s must have a valid post ID."
msgstr "%1$s debe tener un ID de entrada válido."

#: includes/fields/class-acf-field-file.php:447
msgid "%s requires a valid attachment ID."
msgstr "%s necesita un ID de adjunto válido."

#: includes/admin/views/acf-field-group/options.php:233
msgid "Show in REST API"
msgstr "Mostrar en la API REST"

#: includes/fields/class-acf-field-color_picker.php:156
msgid "Enable Transparency"
msgstr "Activar la transparencia"

#: includes/fields/class-acf-field-color_picker.php:175
msgid "RGBA Array"
msgstr "Array RGBA"

#: includes/fields/class-acf-field-color_picker.php:92
msgid "RGBA String"
msgstr "Cadena RGBA"

#: includes/fields/class-acf-field-color_picker.php:91
#: includes/fields/class-acf-field-color_picker.php:174
msgid "Hex String"
msgstr "Cadena hexadecimal"

#: includes/admin/views/browse-fields-modal.php:12
msgid "Upgrade to PRO"
msgstr "Actualizar a la versión Pro"

#: includes/admin/post-types/admin-field-group.php:304
#: includes/admin/post-types/admin-post-type.php:282
#: includes/admin/post-types/admin-taxonomy.php:284
msgctxt "post status"
msgid "Active"
msgstr "Activo"

#: includes/fields/class-acf-field-email.php:166
msgid "'%s' is not a valid email address"
msgstr "“%s” no es una dirección de correo electrónico válida"

#: includes/fields/class-acf-field-color_picker.php:70
msgid "Color value"
msgstr "Valor del color"

#: includes/fields/class-acf-field-color_picker.php:68
msgid "Select default color"
msgstr "Seleccionar el color por defecto"

#: includes/fields/class-acf-field-color_picker.php:66
msgid "Clear color"
msgstr "Vaciar el color"

#: includes/acf-wp-functions.php:90
msgid "Blocks"
msgstr "Bloques"

#: includes/acf-wp-functions.php:86
msgid "Options"
msgstr "Opciones"

#: includes/acf-wp-functions.php:82
msgid "Users"
msgstr "Usuarios"

#: includes/acf-wp-functions.php:78
msgid "Menu items"
msgstr "Elementos del menú"

#: includes/acf-wp-functions.php:70
msgid "Widgets"
msgstr "Widgets"

#: includes/acf-wp-functions.php:62
msgid "Attachments"
msgstr "Adjuntos"

#: includes/acf-wp-functions.php:57
#: includes/admin/post-types/admin-post-types.php:112
#: includes/admin/post-types/admin-taxonomies.php:86
#: includes/admin/tools/class-acf-admin-tool-import.php:90
#: includes/admin/views/acf-post-type/basic-settings.php:86
#: includes/post-types/class-acf-taxonomy.php:90
#: includes/post-types/class-acf-taxonomy.php:91
msgid "Taxonomies"
msgstr "Taxonomías"

#: includes/acf-wp-functions.php:44
#: includes/admin/post-types/admin-post-type.php:124
#: includes/admin/post-types/admin-post-types.php:114
#: includes/admin/views/acf-post-type/advanced-settings.php:106
msgid "Posts"
msgstr "Entradas"

#: includes/ajax/class-acf-ajax-local-json-diff.php:81
msgid "Last updated: %s"
msgstr "Última actualización: %s"

#: includes/ajax/class-acf-ajax-local-json-diff.php:75
msgid "Sorry, this post is unavailable for diff comparison."
msgstr ""
"Lo siento, este grupo de campos no está disponible para la comparación diff."

#: includes/ajax/class-acf-ajax-local-json-diff.php:47
msgid "Invalid field group parameter(s)."
msgstr "Parámetro(s) de grupo de campos no válido(s)"

#: includes/admin/admin-internal-post-type-list.php:429
msgid "Awaiting save"
msgstr "Esperando el guardado"

#: includes/admin/admin-internal-post-type-list.php:426
msgid "Saved"
msgstr "Guardado"

#: includes/admin/admin-internal-post-type-list.php:422
#: includes/admin/tools/class-acf-admin-tool-import.php:46
msgid "Import"
msgstr "Importar"

#: includes/admin/admin-internal-post-type-list.php:418
msgid "Review changes"
msgstr "Revisar los cambios"

#: includes/admin/admin-internal-post-type-list.php:394
msgid "Located in: %s"
msgstr "Localizado en: %s"

#: includes/admin/admin-internal-post-type-list.php:391
msgid "Located in plugin: %s"
msgstr "Localizado en el plugin: %s"

#: includes/admin/admin-internal-post-type-list.php:388
msgid "Located in theme: %s"
msgstr "Localizado en el tema: %s"

#: includes/admin/post-types/admin-field-groups.php:235
msgid "Various"
msgstr "Varios"

#: includes/admin/admin-internal-post-type-list.php:230
#: includes/admin/admin-internal-post-type-list.php:503
msgid "Sync changes"
msgstr "Sincronizar cambios"

#: includes/admin/admin-internal-post-type-list.php:229
msgid "Loading diff"
msgstr "Cargando diff"

#: includes/admin/admin-internal-post-type-list.php:228
msgid "Review local JSON changes"
msgstr "Revisar cambios de JSON local"

#: includes/admin/admin.php:174
msgid "Visit website"
msgstr "Visitar web"

#: includes/admin/admin.php:173
msgid "View details"
msgstr "Ver detalles"

#: includes/admin/admin.php:172
msgid "Version %s"
msgstr "Versión %s"

#: includes/admin/admin.php:171
msgid "Information"
msgstr "Información"

#: includes/admin/admin.php:162
msgid ""
"<a href=\"%s\" target=\"_blank\">Help Desk</a>. The support professionals on "
"our Help Desk will assist with your more in depth, technical challenges."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Centro de ayuda</a>. Los profesionales de "
"soporte de nuestro centro de ayuda te ayudarán más en profundidad con los "
"retos técnicos."

#: includes/admin/admin.php:158
msgid ""
"<a href=\"%s\" target=\"_blank\">Discussions</a>. We have an active and "
"friendly community on our Community Forums who may be able to help you "
"figure out the 'how-tos' of the ACF world."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Debates</a>. Tenemos una comunidad activa y "
"amistosa, en nuestros foros de la comunidad, que pueden ayudarte a descubrir "
"cómo hacer todo en el mundo de ACF."

#: includes/admin/admin.php:154
msgid ""
"<a href=\"%s\" target=\"_blank\">Documentation</a>. Our extensive "
"documentation contains references and guides for most situations you may "
"encounter."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Documentación</a>. Nuestra amplia "
"documentación contiene referencias y guías para la mayoría de situaciones en "
"las que puedas encontrarte."

#: includes/admin/admin.php:151
msgid ""
"We are fanatical about support, and want you to get the best out of your "
"website with ACF. If you run into any difficulties, there are several places "
"you can find help:"
msgstr ""
"Somos fanáticos del soporte, y queremos que consigas el máximo en tu web con "
"ACF. Si te encuentras con alguna dificultad, hay varios lugares donde puedes "
"encontrar ayuda:"

#: includes/admin/admin.php:148 includes/admin/admin.php:150
msgid "Help & Support"
msgstr "Ayuda y soporte"

#: includes/admin/admin.php:139
msgid ""
"Please use the Help & Support tab to get in touch should you find yourself "
"requiring assistance."
msgstr ""
"Por favor, usa la pestaña de ayuda y soporte para contactar si descubres que "
"necesitas ayuda."

#: includes/admin/admin.php:136
msgid ""
"Before creating your first Field Group, we recommend first reading our <a "
"href=\"%s\" target=\"_blank\">Getting started</a> guide to familiarize "
"yourself with the plugin's philosophy and best practises."
msgstr ""
"Antes de crear tu primer grupo de campos te recomendamos que primero leas "
"nuestra <a href=\"%s\" target=\"_blank\">guía de primeros pasos</a> para "
"familiarizarte con la filosofía y buenas prácticas del plugin."

#: includes/admin/admin.php:134
msgid ""
"The Advanced Custom Fields plugin provides a visual form builder to "
"customize WordPress edit screens with extra fields, and an intuitive API to "
"display custom field values in any theme template file."
msgstr ""
"El plugin Advanced Custom Fields ofrece un constructor visual con el que "
"personalizar las pantallas de WordPress con campos adicionales, y una API "
"intuitiva parra mostrar valores de campos personalizados en cualquier "
"archivo de plantilla de cualquier tema."

#: includes/admin/admin.php:131 includes/admin/admin.php:133
msgid "Overview"
msgstr "Resumen"

#. translators: %s the name of the location type
#: includes/locations.php:38
msgid "Location type \"%s\" is already registered."
msgstr "El tipo de ubicación “%s” ya está registrado."

#. translators: %s class name for a location that could not be found
#: includes/locations.php:26
msgid "Class \"%s\" does not exist."
msgstr "La clase “%s” no existe."

#: includes/ajax/class-acf-ajax-query-users.php:43
#: includes/ajax/class-acf-ajax.php:157
msgid "Invalid nonce."
msgstr "Nonce no válido."

#: includes/fields/class-acf-field-user.php:400
msgid "Error loading field."
msgstr "Error al cargar el campo."

#: includes/forms/form-user.php:328
msgid "<strong>Error</strong>: %s"
msgstr "<strong>Error</strong>: %s"

#: includes/locations/class-acf-location-widget.php:22
msgid "Widget"
msgstr "Widget"

#: includes/locations/class-acf-location-user-role.php:24
msgid "User Role"
msgstr "Perfil de usuario"

#: includes/locations/class-acf-location-comment.php:22
msgid "Comment"
msgstr "Comentario"

#: includes/locations/class-acf-location-post-format.php:22
msgid "Post Format"
msgstr "Formato de entrada"

#: includes/locations/class-acf-location-nav-menu-item.php:22
msgid "Menu Item"
msgstr "Elemento de menú"

#: includes/locations/class-acf-location-post-status.php:22
msgid "Post Status"
msgstr "Estado de entrada"

#: includes/acf-wp-functions.php:74
#: includes/locations/class-acf-location-nav-menu.php:89
msgid "Menus"
msgstr "Menús"

#: includes/locations/class-acf-location-nav-menu.php:80
msgid "Menu Locations"
msgstr "Ubicaciones de menú"

#: includes/locations/class-acf-location-nav-menu.php:22
msgid "Menu"
msgstr "Menú"

#: includes/locations/class-acf-location-post-taxonomy.php:22
msgid "Post Taxonomy"
msgstr "Taxonomía de entrada"

#: includes/locations/class-acf-location-page-type.php:114
msgid "Child Page (has parent)"
msgstr "Página hija (tiene superior)"

#: includes/locations/class-acf-location-page-type.php:113
msgid "Parent Page (has children)"
msgstr "Página superior (con hijos)"

#: includes/locations/class-acf-location-page-type.php:112
msgid "Top Level Page (no parent)"
msgstr "Página de nivel superior (sin principals)"

#: includes/locations/class-acf-location-page-type.php:111
msgid "Posts Page"
msgstr "Página de entradas"

#: includes/locations/class-acf-location-page-type.php:110
msgid "Front Page"
msgstr "Página de inicio"

#: includes/locations/class-acf-location-page-type.php:22
msgid "Page Type"
msgstr "Tipo de página"

#: includes/locations/class-acf-location-current-user.php:73
msgid "Viewing back end"
msgstr "Viendo el escritorio"

#: includes/locations/class-acf-location-current-user.php:72
msgid "Viewing front end"
msgstr "Viendo la web"

#: includes/locations/class-acf-location-current-user.php:71
msgid "Logged in"
msgstr "Conectado"

#: includes/locations/class-acf-location-current-user.php:22
msgid "Current User"
msgstr "Usuario actual"

#: includes/locations/class-acf-location-page-template.php:22
msgid "Page Template"
msgstr "Plantilla de página"

#: includes/locations/class-acf-location-user-form.php:74
msgid "Register"
msgstr "Registro"

#: includes/locations/class-acf-location-user-form.php:73
msgid "Add / Edit"
msgstr "Agregar / Editar"

#: includes/locations/class-acf-location-user-form.php:22
msgid "User Form"
msgstr "Formulario de usuario"

#: includes/locations/class-acf-location-page-parent.php:22
msgid "Page Parent"
msgstr "Página superior"

#: includes/locations/class-acf-location-current-user-role.php:77
msgid "Super Admin"
msgstr "Super administrador"

#: includes/locations/class-acf-location-current-user-role.php:22
msgid "Current User Role"
msgstr "Rol del usuario actual"

#: includes/locations/class-acf-location-page-template.php:73
#: includes/locations/class-acf-location-post-template.php:85
msgid "Default Template"
msgstr "Plantilla predeterminada"

#: includes/locations/class-acf-location-post-template.php:22
msgid "Post Template"
msgstr "Plantilla de entrada"

#: includes/locations/class-acf-location-post-category.php:22
msgid "Post Category"
msgstr "Categoría de entrada"

#: includes/locations/class-acf-location-attachment.php:84
msgid "All %s formats"
msgstr "Todo los formatos de %s"

#: includes/locations/class-acf-location-attachment.php:22
msgid "Attachment"
msgstr "Adjunto"

#: includes/validation.php:323
msgid "%s value is required"
msgstr "El valor de %s es obligatorio"

#: includes/admin/views/acf-field-group/conditional-logic.php:64
msgid "Show this field if"
msgstr "Mostrar este campo si"

#: includes/admin/views/acf-field-group/conditional-logic.php:25
#: includes/admin/views/acf-field-group/field.php:122 includes/fields.php:385
msgid "Conditional Logic"
msgstr "Lógica condicional"

#: includes/admin/views/acf-field-group/conditional-logic.php:169
#: includes/admin/views/acf-field-group/location-rule.php:84
msgid "and"
msgstr "y"

#: includes/admin/post-types/admin-field-groups.php:97
#: includes/admin/post-types/admin-post-types.php:118
#: includes/admin/post-types/admin-taxonomies.php:117
msgid "Local JSON"
msgstr "JSON Local"

#: includes/admin/views/acf-field-group/pro-features.php:50
msgid "Clone Field"
msgstr "Campo clon"

#. translators: %s a list of plugin
#: includes/admin/views/upgrade/notice.php:32
msgid ""
"Please also check all premium add-ons (%s) are updated to the latest version."
msgstr ""
"Por favor, comprueba también que todas las extensiones premium (%s) estén "
"actualizados a la última versión."

#: includes/admin/views/upgrade/notice.php:29
msgid ""
"This version contains improvements to your database and requires an upgrade."
msgstr ""
"Esta versión contiene mejoras en su base de datos y requiere una "
"actualización."

#. translators: %1 plugin name, %2 version number
#: includes/admin/views/upgrade/notice.php:28
msgid "Thank you for updating to %1$s v%2$s!"
msgstr "¡Gracias por actualizar a %1$s v%2$s!"

#: includes/admin/views/upgrade/notice.php:26
msgid "Database Upgrade Required"
msgstr "Es necesario actualizar la base de datos"

#: includes/admin/post-types/admin-field-group.php:159
#: includes/admin/views/upgrade/notice.php:17
msgid "Options Page"
msgstr "Página de opciones"

#: includes/admin/views/upgrade/notice.php:14 includes/fields.php:436
msgid "Gallery"
msgstr "Galería"

#: includes/admin/views/upgrade/notice.php:11 includes/fields.php:426
msgid "Flexible Content"
msgstr "Contenido flexible"

#: includes/admin/views/upgrade/notice.php:8 includes/fields.php:446
msgid "Repeater"
msgstr "Repetidor"

#: includes/admin/views/tools/tools.php:16
msgid "Back to all tools"
msgstr "Volver a todas las herramientas"

#: includes/admin/views/acf-field-group/options.php:195
msgid ""
"If multiple field groups appear on an edit screen, the first field group's "
"options will be used (the one with the lowest order number)"
msgstr ""
"Si aparecen múltiples grupos de campos en una pantalla de edición, se "
"utilizarán las opciones del primer grupo (el que tenga el número de orden "
"menor)"

#: includes/admin/views/acf-field-group/options.php:195
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr ""
"<b>Selecciona</b> los elementos que <b>ocultar</b> de la pantalla de edición."

#: includes/admin/views/acf-field-group/options.php:194
msgid "Hide on screen"
msgstr "Ocultar en pantalla"

#: includes/admin/views/acf-field-group/options.php:186
msgid "Send Trackbacks"
msgstr "Enviar trackbacks"

#: includes/admin/post-types/admin-taxonomy.php:126
#: includes/admin/views/acf-field-group/options.php:185
#: includes/admin/views/acf-taxonomy/advanced-settings.php:159
msgid "Tags"
msgstr "Etiquetas"

#: includes/admin/post-types/admin-taxonomy.php:128
#: includes/admin/views/acf-field-group/options.php:184
msgid "Categories"
msgstr "Categorías"

#: includes/admin/views/acf-field-group/options.php:182
#: includes/admin/views/acf-post-type/advanced-settings.php:28
msgid "Page Attributes"
msgstr "Atributos de página"

#: includes/admin/views/acf-field-group/options.php:181
msgid "Format"
msgstr "Formato"

#: includes/admin/views/acf-field-group/options.php:180
#: includes/admin/views/acf-post-type/advanced-settings.php:22
msgid "Author"
msgstr "Autor"

#: includes/admin/views/acf-field-group/options.php:179
msgid "Slug"
msgstr "Slug"

#: includes/admin/views/acf-field-group/options.php:178
#: includes/admin/views/acf-post-type/advanced-settings.php:27
msgid "Revisions"
msgstr "Revisiones"

#: includes/acf-wp-functions.php:66
#: includes/admin/views/acf-field-group/options.php:177
#: includes/admin/views/acf-post-type/advanced-settings.php:23
msgid "Comments"
msgstr "Comentarios"

#: includes/admin/views/acf-field-group/options.php:176
msgid "Discussion"
msgstr "Discusión"

#: includes/admin/views/acf-field-group/options.php:174
#: includes/admin/views/acf-post-type/advanced-settings.php:26
msgid "Excerpt"
msgstr "Extracto"

#: includes/admin/views/acf-field-group/options.php:173
msgid "Content Editor"
msgstr "Editor de contenido"

#: includes/admin/views/acf-field-group/options.php:172
msgid "Permalink"
msgstr "Enlace permanente"

#: includes/admin/views/acf-field-group/options.php:250
msgid "Shown in field group list"
msgstr "Mostrado en lista de grupos de campos"

#: includes/admin/views/acf-field-group/options.php:157
msgid "Field groups with a lower order will appear first"
msgstr "Los grupos de campos con menor orden aparecerán primero"

#: includes/admin/views/acf-field-group/options.php:156
msgid "Order No."
msgstr "N.º de orden"

#: includes/admin/views/acf-field-group/options.php:147
msgid "Below fields"
msgstr "Debajo de los campos"

#: includes/admin/views/acf-field-group/options.php:146
msgid "Below labels"
msgstr "Debajo de las etiquetas"

#: includes/admin/views/acf-field-group/options.php:139
msgid "Instruction Placement"
msgstr "Colocación de la instrucción"

#: includes/admin/views/acf-field-group/options.php:122
msgid "Label Placement"
msgstr "Ubicación de la etiqueta"

#: includes/admin/views/acf-field-group/options.php:110
msgid "Side"
msgstr "Lateral"

#: includes/admin/views/acf-field-group/options.php:109
msgid "Normal (after content)"
msgstr "Normal (después del contenido)"

#: includes/admin/views/acf-field-group/options.php:108
msgid "High (after title)"
msgstr "Alta (después del título)"

#: includes/admin/views/acf-field-group/options.php:101
msgid "Position"
msgstr "Posición"

#: includes/admin/views/acf-field-group/options.php:92
msgid "Seamless (no metabox)"
msgstr "Directo (sin caja meta)"

#: includes/admin/views/acf-field-group/options.php:91
msgid "Standard (WP metabox)"
msgstr "Estándar (caja meta de WP)"

#: includes/admin/views/acf-field-group/options.php:84
msgid "Style"
msgstr "Estilo"

#: includes/admin/views/acf-field-group/fields.php:55
msgid "Type"
msgstr "Tipo"

#: includes/admin/post-types/admin-field-groups.php:91
#: includes/admin/post-types/admin-post-types.php:111
#: includes/admin/post-types/admin-taxonomies.php:110
#: includes/admin/views/acf-field-group/fields.php:54
msgid "Key"
msgstr "Clave"

#. translators: Hidden accessibility text for the positional order number of
#. the field.
#: includes/admin/views/acf-field-group/fields.php:48
msgid "Order"
msgstr "Orden"

#: includes/admin/views/acf-field-group/field.php:321
msgid "Close Field"
msgstr "Cerrar campo"

#: includes/admin/views/acf-field-group/field.php:252
msgid "id"
msgstr "id"

#: includes/admin/views/acf-field-group/field.php:236
msgid "class"
msgstr "class"

#: includes/admin/views/acf-field-group/field.php:278
msgid "width"
msgstr "ancho"

#: includes/admin/views/acf-field-group/field.php:272
msgid "Wrapper Attributes"
msgstr "Atributos del contenedor"

#: includes/fields/class-acf-field.php:312
msgid "Required"
msgstr "Obligatorio"

#: includes/admin/views/acf-field-group/field.php:219
msgid "Instructions"
msgstr "Instrucciones"

#: includes/admin/views/acf-field-group/field.php:142
msgid "Field Type"
msgstr "Tipo de campo"

#: includes/admin/views/acf-field-group/field.php:183
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr "Una sola palabra, sin espacios. Se permiten guiones y guiones bajos"

#: includes/admin/views/acf-field-group/field.php:182
msgid "Field Name"
msgstr "Nombre del campo"

#: includes/admin/views/acf-field-group/field.php:170
msgid "This is the name which will appear on the EDIT page"
msgstr "Este es el nombre que aparecerá en la página EDITAR"

#: includes/admin/views/acf-field-group/field.php:169
#: includes/admin/views/browse-fields-modal.php:69
msgid "Field Label"
msgstr "Etiqueta del campo"

#: includes/admin/views/acf-field-group/field.php:94
msgid "Delete"
msgstr "Borrar"

#: includes/admin/views/acf-field-group/field.php:94
msgid "Delete field"
msgstr "Borrar campo"

#: includes/admin/views/acf-field-group/field.php:92
msgid "Move"
msgstr "Mover"

#: includes/admin/views/acf-field-group/field.php:92
msgid "Move field to another group"
msgstr "Mover campo a otro grupo"

#: includes/admin/views/acf-field-group/field.php:90
msgid "Duplicate field"
msgstr "Duplicar campo"

#: includes/admin/views/acf-field-group/field.php:86
#: includes/admin/views/acf-field-group/field.php:89
msgid "Edit field"
msgstr "Editar campo"

#: includes/admin/views/acf-field-group/field.php:82
msgid "Drag to reorder"
msgstr "Arrastra para reordenar"

#: includes/admin/post-types/admin-field-group.php:99
#: includes/admin/views/acf-field-group/location-group.php:3
msgid "Show this field group if"
msgstr "Mostrar este grupo de campos si"

#: includes/admin/views/upgrade/upgrade.php:93
#: includes/ajax/class-acf-ajax-upgrade.php:34
msgid "No updates available."
msgstr "No hay actualizaciones disponibles."

#. translators: %s the url to the field group page.
#: includes/admin/views/upgrade/upgrade.php:32
msgid "Database upgrade complete. <a href=\"%s\">See what's new</a>"
msgstr ""
"Actualización de la base de datos completa. <a href=\"%s\">Ver las "
"novedades</a>"

#: includes/admin/views/upgrade/upgrade.php:27
msgid "Reading upgrade tasks..."
msgstr "Leyendo tareas de actualización..."

#: includes/admin/views/upgrade/network.php:165
#: includes/admin/views/upgrade/upgrade.php:64
msgid "Upgrade failed."
msgstr "Fallo al actualizar."

#: includes/admin/views/upgrade/network.php:162
msgid "Upgrade complete."
msgstr "Actualización completa."

#. translators: %s the version being upgraded to.
#. translators: %s the new ACF version
#: includes/admin/views/upgrade/network.php:148
#: includes/admin/views/upgrade/upgrade.php:29
msgid "Upgrading data to version %s"
msgstr "Actualizando datos a la versión %s"

#: includes/admin/views/upgrade/network.php:120
#: includes/admin/views/upgrade/notice.php:46
msgid ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"
msgstr ""
"Es muy recomendable que hagas una copia de seguridad de tu base de datos "
"antes de continuar. ¿Estás seguro que quieres ejecutar ya la actualización?"

#: includes/admin/views/upgrade/network.php:116
msgid "Please select at least one site to upgrade."
msgstr "Por favor, selecciona al menos un sitio para actualizarlo."

#. translators: %s admin dashboard url page
#: includes/admin/views/upgrade/network.php:96
msgid ""
"Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr ""
"Actualización de base de datos completa. <a href=\"%s\">Volver al escritorio "
"de red</a>"

#: includes/admin/views/upgrade/network.php:79
msgid "Site is up to date"
msgstr "El sitio está actualizado"

#. translators: %1 current db version, %2 available db version
#: includes/admin/views/upgrade/network.php:77
msgid "Site requires database upgrade from %1$s to %2$s"
msgstr "El sitio necesita actualizar la base de datos de %1$s a %2$s"

#: includes/admin/views/upgrade/network.php:34
#: includes/admin/views/upgrade/network.php:45
msgid "Site"
msgstr "Sitio"

#. translators: %s The button label name, translated seperately
#: includes/admin/views/upgrade/network.php:24
#: includes/admin/views/upgrade/network.php:25
#: includes/admin/views/upgrade/network.php:94
msgid "Upgrade Sites"
msgstr "Actualizar los sitios"

#. translators: %s The button label name, translated seperately
#: includes/admin/views/upgrade/network.php:24
msgid ""
"The following sites require a DB upgrade. Check the ones you want to update "
"and then click %s."
msgstr ""
"Es necesario actualizar la base de datos de los siguientes sitios. Marca los "
"que quieras actualizar y haz clic en %s."

#: includes/admin/views/acf-field-group/conditional-logic.php:184
#: includes/admin/views/acf-field-group/locations.php:37
msgid "Add rule group"
msgstr "Agregar grupo de reglas"

#: includes/admin/views/acf-field-group/locations.php:10
msgid ""
"Create a set of rules to determine which edit screens will use these "
"advanced custom fields"
msgstr ""
"Crea un conjunto de reglas para determinar qué pantallas de edición "
"utilizarán estos campos personalizados"

#: includes/admin/views/acf-field-group/locations.php:9
msgid "Rules"
msgstr "Reglas"

#: includes/admin/tools/class-acf-admin-tool-export.php:449
msgid "Copied"
msgstr "Copiado"

#: includes/admin/tools/class-acf-admin-tool-export.php:425
msgid "Copy to clipboard"
msgstr "Copiar al portapapeles"

#: includes/admin/tools/class-acf-admin-tool-export.php:331
msgid ""
"Select the items you would like to export and then select your export "
"method. Export As JSON to export to a .json file which you can then import "
"to another ACF installation. Generate PHP to export to PHP code which you "
"can place in your theme."
msgstr ""
"Selecciona los grupos de campos que te gustaría exportar y luego elige tu "
"método de exportación. Exporta como JSON para exportar un archivo .json que "
"puedes importar en otra instalación de ACF. Genera PHP para exportar a "
"código PHP que puedes incluir en tu tema."

#: includes/admin/tools/class-acf-admin-tool-export.php:215
msgid "Select Field Groups"
msgstr "Selecciona grupos de campos"

#: includes/admin/tools/class-acf-admin-tool-export.php:88
#: includes/admin/tools/class-acf-admin-tool-export.php:121
msgid "No field groups selected"
msgstr "Ningún grupo de campos seleccionado"

#: includes/admin/tools/class-acf-admin-tool-export.php:38
#: includes/admin/tools/class-acf-admin-tool-export.php:339
#: includes/admin/tools/class-acf-admin-tool-export.php:363
msgid "Generate PHP"
msgstr "Generar PHP"

#: includes/admin/tools/class-acf-admin-tool-export.php:34
msgid "Export Field Groups"
msgstr "Exportar grupos de campos"

#: includes/admin/tools/class-acf-admin-tool-import.php:172
msgid "Import file empty"
msgstr "Archivo de imporación vacío"

#: includes/admin/tools/class-acf-admin-tool-import.php:163
msgid "Incorrect file type"
msgstr "Tipo de campo incorrecto"

#: includes/admin/tools/class-acf-admin-tool-import.php:158
msgid "Error uploading file. Please try again"
msgstr "Error al subir el archivo. Por favor, inténtalo de nuevo"

#: includes/admin/tools/class-acf-admin-tool-import.php:47
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the items in that file."
msgstr ""
"Selecciona el archivo JSON de Advanced Custom Fields que te gustaría "
"importar. Cuando hagas clic en el botón importar de abajo, ACF importará los "
"grupos de campos."

#: includes/admin/tools/class-acf-admin-tool-import.php:26
msgid "Import Field Groups"
msgstr "Importar grupo de campos"

#: includes/admin/admin-internal-post-type-list.php:417
msgid "Sync"
msgstr "Sincronizar"

#. translators: %s: field group title
#: includes/admin/admin-internal-post-type-list.php:960
msgid "Select %s"
msgstr "Selecciona %s"

#: includes/admin/admin-internal-post-type-list.php:460
#: includes/admin/admin-internal-post-type-list.php:492
#: includes/admin/views/acf-field-group/field.php:90
msgid "Duplicate"
msgstr "Duplicar"

#: includes/admin/admin-internal-post-type-list.php:460
msgid "Duplicate this item"
msgstr "Duplicar este elemento"

#: includes/admin/views/acf-post-type/advanced-settings.php:41
msgid "Supports"
msgstr "Supports"

#: includes/admin/admin.php:355
#: includes/admin/views/browse-fields-modal.php:102
msgid "Documentation"
msgstr "Documentación"

#: includes/admin/post-types/admin-field-groups.php:90
#: includes/admin/post-types/admin-post-types.php:110
#: includes/admin/post-types/admin-taxonomies.php:109
#: includes/admin/views/acf-field-group/options.php:249
#: includes/admin/views/acf-post-type/advanced-settings.php:62
#: includes/admin/views/acf-taxonomy/advanced-settings.php:114
#: includes/admin/views/upgrade/network.php:36
#: includes/admin/views/upgrade/network.php:47
msgid "Description"
msgstr "Descripción"

#: includes/admin/admin-internal-post-type-list.php:414
#: includes/admin/admin-internal-post-type-list.php:832
msgid "Sync available"
msgstr "Sincronización disponible"

#. translators: %s number of field groups synchronized
#: includes/admin/post-types/admin-field-groups.php:374
msgid "Field group synchronized."
msgid_plural "%s field groups synchronized."
msgstr[0] "Grupo de campos sincronizado."
msgstr[1] "%s grupos de campos sincronizados."

#. translators: %s number of field groups duplicated
#: includes/admin/post-types/admin-field-groups.php:367
msgid "Field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] "Grupo de campos duplicado."
msgstr[1] "%s grupos de campos duplicados."

#: includes/admin/admin-internal-post-type-list.php:155
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] "Activo <span class=\"count\">(%s)</span>"
msgstr[1] "Activos <span class=\"count\">(%s)</span>"

#: includes/admin/admin-upgrade.php:251
msgid "Review sites & upgrade"
msgstr "Revisar sitios y actualizar"

#: includes/admin/admin-upgrade.php:59 includes/admin/admin-upgrade.php:90
#: includes/admin/admin-upgrade.php:91 includes/admin/admin-upgrade.php:227
#: includes/admin/views/upgrade/network.php:21
#: includes/admin/views/upgrade/upgrade.php:23
msgid "Upgrade Database"
msgstr "Actualizar base de datos"

#: includes/admin/views/acf-field-group/options.php:175
#: includes/admin/views/acf-post-type/advanced-settings.php:30
msgid "Custom Fields"
msgstr "Campos personalizados"

#: includes/admin/post-types/admin-field-group.php:609
msgid "Move Field"
msgstr "Mover campo"

#: includes/admin/post-types/admin-field-group.php:602
#: includes/admin/post-types/admin-field-group.php:606
msgid "Please select the destination for this field"
msgstr "Por favor, selecciona el destino para este campo"

#. translators: Confirmation message once a field has been moved to a different
#. field group.
#: includes/admin/post-types/admin-field-group.php:568
msgid "The %1$s field can now be found in the %2$s field group"
msgstr "El campo %1$s ahora se puede encontrar en el grupo de campos %2$s"

#: includes/admin/post-types/admin-field-group.php:565
msgid "Move Complete."
msgstr "Movimiento completo."

#: includes/admin/views/acf-field-group/field.php:52
#: includes/admin/views/acf-field-group/options.php:217
#: includes/admin/views/acf-post-type/advanced-settings.php:78
#: includes/admin/views/acf-taxonomy/advanced-settings.php:130
msgid "Active"
msgstr "Activo"

#: includes/admin/post-types/admin-field-group.php:276
msgid "Field Keys"
msgstr "Claves de campo"

#: includes/admin/post-types/admin-field-group.php:180
msgid "Settings"
msgstr "Ajustes"

#: includes/admin/post-types/admin-field-groups.php:92
msgid "Location"
msgstr "Ubicación"

#: includes/admin/post-types/admin-field-group.php:100
msgid "Null"
msgstr "Null"

#: includes/admin/post-types/admin-field-group.php:97
#: includes/class-acf-internal-post-type.php:728
#: includes/post-types/class-acf-field-group.php:345
msgid "copy"
msgstr "copiar"

#: includes/admin/post-types/admin-field-group.php:96
msgid "(this field)"
msgstr "(este campo)"

#: includes/admin/post-types/admin-field-group.php:94
msgid "Checked"
msgstr "Seleccionado"

#: includes/admin/post-types/admin-field-group.php:90
msgid "Move Custom Field"
msgstr "Mover campo personalizado"

#: includes/admin/post-types/admin-field-group.php:89
msgid "No toggle fields available"
msgstr "No hay campos de conmutación disponibles"

#: includes/admin/post-types/admin-field-group.php:87
msgid "Field group title is required"
msgstr "El título del grupo de campos es obligatorio"

#: includes/admin/post-types/admin-field-group.php:86
msgid "This field cannot be moved until its changes have been saved"
msgstr "Este campo no se puede mover hasta que sus cambios se hayan guardado"

#: includes/admin/post-types/admin-field-group.php:85
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr ""
"La cadena \"field_\" no se debe utilizar al comienzo de un nombre de campo"

#: includes/admin/post-types/admin-field-group.php:69
msgid "Field group draft updated."
msgstr "Borrador del grupo de campos actualizado."

#: includes/admin/post-types/admin-field-group.php:68
msgid "Field group scheduled for."
msgstr "Grupo de campos programado."

#: includes/admin/post-types/admin-field-group.php:67
msgid "Field group submitted."
msgstr "Grupo de campos enviado."

#: includes/admin/post-types/admin-field-group.php:66
msgid "Field group saved."
msgstr "Grupo de campos guardado."

#: includes/admin/post-types/admin-field-group.php:65
msgid "Field group published."
msgstr "Grupo de campos publicado."

#: includes/admin/post-types/admin-field-group.php:62
msgid "Field group deleted."
msgstr "Grupo de campos eliminado."

#: includes/admin/post-types/admin-field-group.php:60
#: includes/admin/post-types/admin-field-group.php:61
#: includes/admin/post-types/admin-field-group.php:63
msgid "Field group updated."
msgstr "Grupo de campos actualizado."

#: includes/admin/admin-tools.php:107
#: includes/admin/views/global/navigation.php:254
#: includes/admin/views/tools/tools.php:14
msgid "Tools"
msgstr "Herramientas"

#: includes/locations/abstract-acf-location.php:105
msgid "is not equal to"
msgstr "no es igual a"

#: includes/locations/abstract-acf-location.php:104
msgid "is equal to"
msgstr "es igual a"

#: includes/locations.php:104
msgid "Forms"
msgstr "Formularios"

#: includes/admin/post-types/admin-post-type.php:125 includes/locations.php:102
#: includes/locations/class-acf-location-page.php:22
msgid "Page"
msgstr "Página"

#: includes/admin/post-types/admin-post-type.php:123 includes/locations.php:101
#: includes/locations/class-acf-location-post.php:22
msgid "Post"
msgstr "Entrada"

#: includes/fields.php:328
msgid "Relational"
msgstr "Relación"

#: includes/fields.php:327
msgid "Choice"
msgstr "Elección"

#: includes/fields.php:325
msgid "Basic"
msgstr "Básico"

#: includes/fields.php:276
msgid "Unknown"
msgstr "Desconocido"

#: includes/fields.php:276
msgid "Field type does not exist"
msgstr "El tipo de campo no existe"

#: includes/forms/form-front.php:220
msgid "Spam Detected"
msgstr "Spam detectado"

#: includes/forms/form-front.php:103
msgid "Post updated"
msgstr "Publicación actualizada"

#: includes/forms/form-front.php:102
msgid "Update"
msgstr "Actualizar"

#: includes/forms/form-front.php:63
msgid "Validate Email"
msgstr "Validar correo electrónico"

#: includes/fields.php:326 includes/forms/form-front.php:55
msgid "Content"
msgstr "Contenido"

#: includes/admin/views/acf-post-type/advanced-settings.php:21
#: includes/forms/form-front.php:46
msgid "Title"
msgstr "Título"

#: includes/assets.php:376 includes/forms/form-comment.php:140
msgid "Edit field group"
msgstr "Editar grupo de campos"

#: includes/admin/post-types/admin-field-group.php:113
msgid "Selection is less than"
msgstr "La selección es menor que"

#: includes/admin/post-types/admin-field-group.php:112
msgid "Selection is greater than"
msgstr "La selección es mayor que"

#: includes/admin/post-types/admin-field-group.php:111
msgid "Value is less than"
msgstr "El valor es menor que"

#: includes/admin/post-types/admin-field-group.php:110
msgid "Value is greater than"
msgstr "El valor es mayor que"

#: includes/admin/post-types/admin-field-group.php:109
msgid "Value contains"
msgstr "El valor contiene"

#: includes/admin/post-types/admin-field-group.php:108
msgid "Value matches pattern"
msgstr "El valor coincide con el patrón"

#: includes/admin/post-types/admin-field-group.php:107
msgid "Value is not equal to"
msgstr "El valor no es igual a"

#: includes/admin/post-types/admin-field-group.php:106
msgid "Value is equal to"
msgstr "El valor es igual a"

#: includes/admin/post-types/admin-field-group.php:105
msgid "Has no value"
msgstr "No tiene ningún valor"

#: includes/admin/post-types/admin-field-group.php:104
msgid "Has any value"
msgstr "No tiene algún valor"

#: includes/admin/admin-internal-post-type.php:337
#: includes/admin/views/browse-fields-modal.php:72 includes/assets.php:354
msgid "Cancel"
msgstr "Cancelar"

#: includes/assets.php:350
msgid "Are you sure?"
msgstr "¿Estás seguro?"

#: includes/assets.php:370
msgid "%d fields require attention"
msgstr "%d campos requieren atención"

#: includes/assets.php:369
msgid "1 field requires attention"
msgstr "1 campo requiere atención"

#: includes/assets.php:368 includes/validation.php:257
#: includes/validation.php:265
msgid "Validation failed"
msgstr "Validación fallida"

#: includes/assets.php:367
msgid "Validation successful"
msgstr "Validación correcta"

#: includes/media.php:54
msgid "Restricted"
msgstr "Restringido"

#: includes/media.php:53
msgid "Collapse Details"
msgstr "Contraer detalles"

#: includes/media.php:52
msgid "Expand Details"
msgstr "Ampliar detalles"

#: includes/admin/views/acf-post-type/advanced-settings.php:470
#: includes/media.php:51
msgid "Uploaded to this post"
msgstr "Subido a esta publicación"

#: includes/media.php:50
msgctxt "verb"
msgid "Update"
msgstr "Actualizar"

#: includes/media.php:49
msgctxt "verb"
msgid "Edit"
msgstr "Editar"

#: includes/assets.php:364
msgid "The changes you made will be lost if you navigate away from this page"
msgstr "Los cambios que has realizado se perderán si navegas hacia otra página"

#: includes/api/api-helpers.php:3000
msgid "File type must be %s."
msgstr "El tipo de archivo debe ser %s."

#: includes/admin/post-types/admin-field-group.php:98
#: includes/admin/views/acf-field-group/conditional-logic.php:64
#: includes/admin/views/acf-field-group/conditional-logic.php:182
#: includes/admin/views/acf-field-group/location-group.php:3
#: includes/admin/views/acf-field-group/locations.php:35
#: includes/api/api-helpers.php:2997
msgid "or"
msgstr "o"

#: includes/api/api-helpers.php:2973
msgid "File size must not exceed %s."
msgstr "El tamaño del archivo no debe ser mayor de %s."

#: includes/api/api-helpers.php:2969
msgid "File size must be at least %s."
msgstr "El tamaño de archivo debe ser al menos %s."

#: includes/api/api-helpers.php:2956
msgid "Image height must not exceed %dpx."
msgstr "La altura de la imagen no debe exceder %dpx."

#: includes/api/api-helpers.php:2952
msgid "Image height must be at least %dpx."
msgstr "La altura de la imagen debe ser al menos %dpx."

#: includes/api/api-helpers.php:2940
msgid "Image width must not exceed %dpx."
msgstr "El ancho de la imagen no debe exceder %dpx."

#: includes/api/api-helpers.php:2936
msgid "Image width must be at least %dpx."
msgstr "El ancho de la imagen debe ser al menos %dpx."

#: includes/api/api-helpers.php:1425 includes/api/api-term.php:140
msgid "(no title)"
msgstr "(sin título)"

#: includes/api/api-helpers.php:781
msgid "Full Size"
msgstr "Tamaño completo"

#: includes/api/api-helpers.php:746
msgid "Large"
msgstr "Grande"

#: includes/api/api-helpers.php:745
msgid "Medium"
msgstr "Mediano"

#: includes/api/api-helpers.php:744
msgid "Thumbnail"
msgstr "Miniatura"

#: includes/acf-field-functions.php:854
#: includes/admin/post-types/admin-field-group.php:95
msgid "(no label)"
msgstr "(sin etiqueta)"

#: includes/fields/class-acf-field-textarea.php:135
msgid "Sets the textarea height"
msgstr "Establece la altura del área de texto"

#: includes/fields/class-acf-field-textarea.php:134
msgid "Rows"
msgstr "Filas"

#: includes/fields/class-acf-field-textarea.php:22
msgid "Text Area"
msgstr "Área de texto"

#: includes/fields/class-acf-field-checkbox.php:421
msgid "Prepend an extra checkbox to toggle all choices"
msgstr ""
"Anteponer una casilla de verificación extra para cambiar todas las opciones"

#: includes/fields/class-acf-field-checkbox.php:383
msgid "Save 'custom' values to the field's choices"
msgstr "Guardar los valores “personalizados” a las opciones del campo"

#: includes/fields/class-acf-field-checkbox.php:372
msgid "Allow 'custom' values to be added"
msgstr "Permite agregar valores personalizados"

#: includes/fields/class-acf-field-checkbox.php:35
msgid "Add new choice"
msgstr "Agregar nueva opción"

#: includes/fields/class-acf-field-checkbox.php:157
msgid "Toggle All"
msgstr "Invertir todos"

#: includes/fields/class-acf-field-page_link.php:487
msgid "Allow Archives URLs"
msgstr "Permitir las URLs de los archivos"

#: includes/fields/class-acf-field-page_link.php:196
msgid "Archives"
msgstr "Archivo"

#: includes/fields/class-acf-field-page_link.php:22
msgid "Page Link"
msgstr "Enlace a página"

#: includes/fields/class-acf-field-taxonomy.php:884
#: includes/locations/class-acf-location-user-form.php:72
msgid "Add"
msgstr "Agregar"

#: includes/admin/views/acf-field-group/fields.php:53
#: includes/fields/class-acf-field-taxonomy.php:854
msgid "Name"
msgstr "Nombre"

#: includes/fields/class-acf-field-taxonomy.php:839
msgid "%s added"
msgstr "%s agregado/s"

#: includes/fields/class-acf-field-taxonomy.php:803
msgid "%s already exists"
msgstr "%s ya existe"

#: includes/fields/class-acf-field-taxonomy.php:791
msgid "User unable to add new %s"
msgstr "El usuario no puede agregar nuevos %s"

#: includes/fields/class-acf-field-taxonomy.php:678
msgid "Term ID"
msgstr "ID de término"

#: includes/fields/class-acf-field-taxonomy.php:677
msgid "Term Object"
msgstr "Objeto de término"

#: includes/fields/class-acf-field-taxonomy.php:662
msgid "Load value from posts terms"
msgstr "Cargar el valor de los términos de la publicación"

#: includes/fields/class-acf-field-taxonomy.php:661
msgid "Load Terms"
msgstr "Cargar términos"

#: includes/fields/class-acf-field-taxonomy.php:651
msgid "Connect selected terms to the post"
msgstr "Conectar los términos seleccionados con la publicación"

#: includes/fields/class-acf-field-taxonomy.php:650
msgid "Save Terms"
msgstr "Guardar términos"

#: includes/fields/class-acf-field-taxonomy.php:640
msgid "Allow new terms to be created whilst editing"
msgstr "Permitir la creación de nuevos términos mientras se edita"

#: includes/fields/class-acf-field-taxonomy.php:639
msgid "Create Terms"
msgstr "Crear términos"

#: includes/fields/class-acf-field-taxonomy.php:698
msgid "Radio Buttons"
msgstr "Botones de radio"

#: includes/fields/class-acf-field-taxonomy.php:697
msgid "Single Value"
msgstr "Valor único"

#: includes/fields/class-acf-field-taxonomy.php:695
msgid "Multi Select"
msgstr "Selección múltiple"

#: includes/fields/class-acf-field-checkbox.php:22
#: includes/fields/class-acf-field-taxonomy.php:694
msgid "Checkbox"
msgstr "Casilla de verificación"

#: includes/fields/class-acf-field-taxonomy.php:693
msgid "Multiple Values"
msgstr "Valores múltiples"

#: includes/fields/class-acf-field-taxonomy.php:688
msgid "Select the appearance of this field"
msgstr "Selecciona la apariencia de este campo"

#: includes/fields/class-acf-field-taxonomy.php:687
msgid "Appearance"
msgstr "Apariencia"

#: includes/fields/class-acf-field-taxonomy.php:629
msgid "Select the taxonomy to be displayed"
msgstr "Selecciona la taxonomía a mostrar"

#: includes/fields/class-acf-field-taxonomy.php:593
msgctxt "No Terms"
msgid "No %s"
msgstr "Ningún %s"

#: includes/fields/class-acf-field-number.php:240
msgid "Value must be equal to or lower than %d"
msgstr "El valor debe ser menor o igual a %d"

#: includes/fields/class-acf-field-number.php:235
msgid "Value must be equal to or higher than %d"
msgstr "El valor debe ser mayor o igual a %d"

#: includes/fields/class-acf-field-number.php:223
msgid "Value must be a number"
msgstr "El valor debe ser un número"

#: includes/fields/class-acf-field-number.php:22
msgid "Number"
msgstr "Número"

#: includes/fields/class-acf-field-radio.php:254
msgid "Save 'other' values to the field's choices"
msgstr "Guardar los valores de 'otros' en las opciones del campo"

#: includes/fields/class-acf-field-radio.php:243
msgid "Add 'other' choice to allow for custom values"
msgstr "Agrega la opción 'otros' para permitir valores personalizados"

#: includes/admin/views/global/navigation.php:202
msgid "Other"
msgstr "Otros"

#: includes/fields/class-acf-field-radio.php:22
msgid "Radio Button"
msgstr "Botón de radio"

#: includes/fields/class-acf-field-accordion.php:106
msgid ""
"Define an endpoint for the previous accordion to stop. This accordion will "
"not be visible."
msgstr ""
"Define un punto final para que el acordeón anterior se detenga. Este "
"acordeón no será visible."

#: includes/fields/class-acf-field-accordion.php:95
msgid "Allow this accordion to open without closing others."
msgstr "Permita que este acordeón se abra sin cerrar otros."

#: includes/fields/class-acf-field-accordion.php:94
msgid "Multi-Expand"
msgstr "Multi-Expand"

#: includes/fields/class-acf-field-accordion.php:84
msgid "Display this accordion as open on page load."
msgstr "Muestra este acordeón como abierto en la carga de la página."

#: includes/fields/class-acf-field-accordion.php:83
msgid "Open"
msgstr "Abrir"

#: includes/fields/class-acf-field-accordion.php:24
msgid "Accordion"
msgstr "Acordeón"

#: includes/fields/class-acf-field-file.php:253
#: includes/fields/class-acf-field-file.php:265
msgid "Restrict which files can be uploaded"
msgstr "Restringir qué archivos que se pueden subir"

#: includes/fields/class-acf-field-file.php:207
msgid "File ID"
msgstr "ID del archivo"

#: includes/fields/class-acf-field-file.php:206
msgid "File URL"
msgstr "URL del archivo"

#: includes/fields/class-acf-field-file.php:205
msgid "File Array"
msgstr "Array del archivo"

#: includes/fields/class-acf-field-file.php:176
msgid "Add File"
msgstr "Agregar archivo"

#: includes/admin/tools/class-acf-admin-tool-import.php:151
#: includes/fields/class-acf-field-file.php:176
msgid "No file selected"
msgstr "Ningún archivo seleccionado"

#: includes/fields/class-acf-field-file.php:140
msgid "File name"
msgstr "Nombre del archivo"

#: includes/fields/class-acf-field-file.php:57
msgid "Update File"
msgstr "Actualizar archivo"

#: includes/fields/class-acf-field-file.php:56
msgid "Edit File"
msgstr "Editar archivo"

#: includes/admin/tools/class-acf-admin-tool-import.php:55
#: includes/fields/class-acf-field-file.php:55
msgid "Select File"
msgstr "Seleccionar archivo"

#: includes/fields/class-acf-field-file.php:22
msgid "File"
msgstr "Archivo"

#: includes/fields/class-acf-field-password.php:22
msgid "Password"
msgstr "Contraseña"

#: includes/fields/class-acf-field-select.php:363
msgid "Specify the value returned"
msgstr "Especifica el valor devuelto"

#: includes/fields/class-acf-field-select.php:431
msgid "Use AJAX to lazy load choices?"
msgstr "¿Usar AJAX para hacer cargar las opciones de forma asíncrona?"

#: includes/fields/class-acf-field-checkbox.php:333
#: includes/fields/class-acf-field-select.php:352
msgid "Enter each default value on a new line"
msgstr "Agrega cada valor en una nueva línea"

#: includes/fields/class-acf-field-select.php:217 includes/media.php:48
msgctxt "verb"
msgid "Select"
msgstr "Selecciona"

#: includes/fields/class-acf-field-select.php:95
msgctxt "Select2 JS load_fail"
msgid "Loading failed"
msgstr "Error al cargar"

#: includes/fields/class-acf-field-select.php:94
msgctxt "Select2 JS searching"
msgid "Searching&hellip;"
msgstr "Buscando&hellip;"

#: includes/fields/class-acf-field-select.php:93
msgctxt "Select2 JS load_more"
msgid "Loading more results&hellip;"
msgstr "Cargando más resultados&hellip;"

#. translators: %d - maximum number of items that can be selected in the select
#. field
#: includes/fields/class-acf-field-select.php:92
msgctxt "Select2 JS selection_too_long_n"
msgid "You can only select %d items"
msgstr "Solo puedes seleccionar %d elementos"

#: includes/fields/class-acf-field-select.php:90
msgctxt "Select2 JS selection_too_long_1"
msgid "You can only select 1 item"
msgstr "Solo puedes seleccionar 1 elemento"

#. translators: %d - number of characters that should be removed from select
#. field
#: includes/fields/class-acf-field-select.php:89
msgctxt "Select2 JS input_too_long_n"
msgid "Please delete %d characters"
msgstr "Por favor, borra %d caracteres"

#: includes/fields/class-acf-field-select.php:87
msgctxt "Select2 JS input_too_long_1"
msgid "Please delete 1 character"
msgstr "Por favor, borra 1 carácter"

#. translators: %d - number of characters to enter into select field input
#: includes/fields/class-acf-field-select.php:86
msgctxt "Select2 JS input_too_short_n"
msgid "Please enter %d or more characters"
msgstr "Por favor, introduce %d o más caracteres"

#: includes/fields/class-acf-field-select.php:84
msgctxt "Select2 JS input_too_short_1"
msgid "Please enter 1 or more characters"
msgstr "Por favor, introduce 1 o más caracteres"

#: includes/fields/class-acf-field-select.php:83
msgctxt "Select2 JS matches_0"
msgid "No matches found"
msgstr "No se han encontrado coincidencias"

#. translators: %d - number of results available in select field
#: includes/fields/class-acf-field-select.php:82
msgctxt "Select2 JS matches_n"
msgid "%d results are available, use up and down arrow keys to navigate."
msgstr ""
"%d resultados disponibles, utiliza las flechas arriba y abajo para navegar "
"por los resultados."

#: includes/fields/class-acf-field-select.php:80
msgctxt "Select2 JS matches_1"
msgid "One result is available, press enter to select it."
msgstr "Hay un resultado disponible, pulsa enter para seleccionarlo."

#: includes/fields/class-acf-field-select.php:16
#: includes/fields/class-acf-field-taxonomy.php:699
msgctxt "noun"
msgid "Select"
msgstr "Selección"

#: includes/fields/class-acf-field-user.php:102
msgid "User ID"
msgstr "ID del usuario"

#: includes/fields/class-acf-field-user.php:101
msgid "User Object"
msgstr "Grupo de objetos"

#: includes/fields/class-acf-field-user.php:100
msgid "User Array"
msgstr "Grupo de usuarios"

#: includes/fields/class-acf-field-user.php:88
msgid "All user roles"
msgstr "Todos los roles de usuario"

#: includes/fields/class-acf-field-user.php:80
msgid "Filter by Role"
msgstr "Filtrar por función"

#: includes/fields/class-acf-field-user.php:15 includes/locations.php:103
msgid "User"
msgstr "Usuario"

#: includes/fields/class-acf-field-separator.php:22
msgid "Separator"
msgstr "Separador"

#: includes/fields/class-acf-field-color_picker.php:69
msgid "Select Color"
msgstr "Seleccionar color"

#: includes/admin/post-types/admin-post-type.php:127
#: includes/admin/post-types/admin-taxonomy.php:129
#: includes/fields/class-acf-field-color_picker.php:67
msgid "Default"
msgstr "Por defecto"

#: includes/admin/views/acf-post-type/advanced-settings.php:89
#: includes/admin/views/acf-taxonomy/advanced-settings.php:141
#: includes/fields/class-acf-field-color_picker.php:65
msgid "Clear"
msgstr "Vaciar"

#: includes/fields/class-acf-field-color_picker.php:22
msgid "Color Picker"
msgstr "Selector de color"

#: includes/fields/class-acf-field-date_time_picker.php:82
msgctxt "Date Time Picker JS pmTextShort"
msgid "P"
msgstr "P"

#: includes/fields/class-acf-field-date_time_picker.php:81
msgctxt "Date Time Picker JS pmText"
msgid "PM"
msgstr "PM"

#: includes/fields/class-acf-field-date_time_picker.php:78
msgctxt "Date Time Picker JS amTextShort"
msgid "A"
msgstr "A"

#: includes/fields/class-acf-field-date_time_picker.php:77
msgctxt "Date Time Picker JS amText"
msgid "AM"
msgstr "AM"

#: includes/fields/class-acf-field-date_time_picker.php:75
msgctxt "Date Time Picker JS selectText"
msgid "Select"
msgstr "Seleccionar"

#: includes/fields/class-acf-field-date_time_picker.php:74
msgctxt "Date Time Picker JS closeText"
msgid "Done"
msgstr "Hecho"

#: includes/fields/class-acf-field-date_time_picker.php:73
msgctxt "Date Time Picker JS currentText"
msgid "Now"
msgstr "Ahora"

#: includes/fields/class-acf-field-date_time_picker.php:72
msgctxt "Date Time Picker JS timezoneText"
msgid "Time Zone"
msgstr "Zona horaria"

#: includes/fields/class-acf-field-date_time_picker.php:71
msgctxt "Date Time Picker JS microsecText"
msgid "Microsecond"
msgstr "Microsegundo"

#: includes/fields/class-acf-field-date_time_picker.php:70
msgctxt "Date Time Picker JS millisecText"
msgid "Millisecond"
msgstr "Milisegundo"

#: includes/fields/class-acf-field-date_time_picker.php:69
msgctxt "Date Time Picker JS secondText"
msgid "Second"
msgstr "Segundo"

#: includes/fields/class-acf-field-date_time_picker.php:68
msgctxt "Date Time Picker JS minuteText"
msgid "Minute"
msgstr "Minuto"

#: includes/fields/class-acf-field-date_time_picker.php:67
msgctxt "Date Time Picker JS hourText"
msgid "Hour"
msgstr "Hora"

#: includes/fields/class-acf-field-date_time_picker.php:66
msgctxt "Date Time Picker JS timeText"
msgid "Time"
msgstr "Hora"

#: includes/fields/class-acf-field-date_time_picker.php:65
msgctxt "Date Time Picker JS timeOnlyTitle"
msgid "Choose Time"
msgstr "Elegir hora"

#: includes/fields/class-acf-field-date_time_picker.php:22
msgid "Date Time Picker"
msgstr "Selector de fecha y hora"

#: includes/fields/class-acf-field-accordion.php:105
msgid "Endpoint"
msgstr "Variable"

#: includes/admin/views/acf-field-group/options.php:130
#: includes/fields/class-acf-field-tab.php:112
msgid "Left aligned"
msgstr "Alineada a la izquierda"

#: includes/admin/views/acf-field-group/options.php:129
#: includes/fields/class-acf-field-tab.php:111
msgid "Top aligned"
msgstr "Alineada arriba"

#: includes/fields/class-acf-field-tab.php:107
msgid "Placement"
msgstr "Ubicación"

#: includes/fields/class-acf-field-tab.php:23
msgid "Tab"
msgstr "Pestaña"

#: includes/fields/class-acf-field-url.php:138
msgid "Value must be a valid URL"
msgstr "El valor debe ser una URL válida"

#: includes/fields/class-acf-field-link.php:153
msgid "Link URL"
msgstr "URL del enlace"

#: includes/fields/class-acf-field-link.php:152
msgid "Link Array"
msgstr "Array de enlaces"

#: includes/fields/class-acf-field-link.php:124
msgid "Opens in a new window/tab"
msgstr "Abrir en una nueva ventana/pestaña"

#: includes/fields/class-acf-field-link.php:119
msgid "Select Link"
msgstr "Elige el enlace"

#: includes/fields/class-acf-field-link.php:22
msgid "Link"
msgstr "Enlace"

#: includes/fields/class-acf-field-email.php:22
msgid "Email"
msgstr "Correo electrónico"

#: includes/fields/class-acf-field-number.php:173
#: includes/fields/class-acf-field-range.php:206
msgid "Step Size"
msgstr "Tamaño de paso"

#: includes/fields/class-acf-field-number.php:143
#: includes/fields/class-acf-field-range.php:184
msgid "Maximum Value"
msgstr "Valor máximo"

#: includes/fields/class-acf-field-number.php:133
#: includes/fields/class-acf-field-range.php:173
msgid "Minimum Value"
msgstr "Valor mínimo"

#: includes/fields/class-acf-field-range.php:22
msgid "Range"
msgstr "Rango"

#: includes/fields/class-acf-field-button-group.php:165
#: includes/fields/class-acf-field-checkbox.php:350
#: includes/fields/class-acf-field-radio.php:210
#: includes/fields/class-acf-field-select.php:370
msgid "Both (Array)"
msgstr "Ambos (Array)"

#: includes/admin/views/acf-field-group/fields.php:52
#: includes/fields/class-acf-field-button-group.php:164
#: includes/fields/class-acf-field-checkbox.php:349
#: includes/fields/class-acf-field-radio.php:209
#: includes/fields/class-acf-field-select.php:369
msgid "Label"
msgstr "Etiqueta"

#: includes/fields/class-acf-field-button-group.php:163
#: includes/fields/class-acf-field-checkbox.php:348
#: includes/fields/class-acf-field-radio.php:208
#: includes/fields/class-acf-field-select.php:368
msgid "Value"
msgstr "Valor"

#: includes/fields/class-acf-field-button-group.php:211
#: includes/fields/class-acf-field-checkbox.php:411
#: includes/fields/class-acf-field-radio.php:282
msgid "Vertical"
msgstr "Vertical"

#: includes/fields/class-acf-field-button-group.php:210
#: includes/fields/class-acf-field-checkbox.php:412
#: includes/fields/class-acf-field-radio.php:283
msgid "Horizontal"
msgstr "Horizontal"

#: includes/fields/class-acf-field-button-group.php:138
#: includes/fields/class-acf-field-checkbox.php:323
#: includes/fields/class-acf-field-radio.php:183
#: includes/fields/class-acf-field-select.php:341
msgid "red : Red"
msgstr "rojo : Rojo"

#: includes/fields/class-acf-field-button-group.php:138
#: includes/fields/class-acf-field-checkbox.php:323
#: includes/fields/class-acf-field-radio.php:183
#: includes/fields/class-acf-field-select.php:341
msgid "For more control, you may specify both a value and label like this:"
msgstr ""
"Para más control, puedes especificar tanto un valor como una etiqueta, así:"

#: includes/fields/class-acf-field-button-group.php:138
#: includes/fields/class-acf-field-checkbox.php:323
#: includes/fields/class-acf-field-radio.php:183
#: includes/fields/class-acf-field-select.php:341
msgid "Enter each choice on a new line."
msgstr "Agrega cada opción en una nueva línea."

#: includes/fields/class-acf-field-button-group.php:137
#: includes/fields/class-acf-field-checkbox.php:322
#: includes/fields/class-acf-field-radio.php:182
#: includes/fields/class-acf-field-select.php:340
msgid "Choices"
msgstr "Opciones"

#: includes/fields/class-acf-field-button-group.php:23
msgid "Button Group"
msgstr "Grupo de botones"

#: includes/fields/class-acf-field-button-group.php:183
#: includes/fields/class-acf-field-page_link.php:519
#: includes/fields/class-acf-field-post_object.php:432
#: includes/fields/class-acf-field-radio.php:228
#: includes/fields/class-acf-field-select.php:399
#: includes/fields/class-acf-field-taxonomy.php:708
#: includes/fields/class-acf-field-user.php:132
msgid "Allow Null"
msgstr "Permitir nulo"

#: includes/fields/class-acf-field-page_link.php:273
#: includes/fields/class-acf-field-post_object.php:254
#: includes/fields/class-acf-field-taxonomy.php:872
msgid "Parent"
msgstr "Superior"

#: includes/fields/class-acf-field-wysiwyg.php:367
msgid "TinyMCE will not be initialized until field is clicked"
msgstr "TinyMCE no se inicializará hasta que se haga clic en el campo"

#: includes/fields/class-acf-field-wysiwyg.php:366
msgid "Delay Initialization"
msgstr "Inicialización del retardo"

#: includes/fields/class-acf-field-wysiwyg.php:355
msgid "Show Media Upload Buttons"
msgstr "Mostrar botones para subir archivos multimedia"

#: includes/fields/class-acf-field-wysiwyg.php:339
msgid "Toolbar"
msgstr "Barra de herramientas"

#: includes/fields/class-acf-field-wysiwyg.php:331
msgid "Text Only"
msgstr "Sólo texto"

#: includes/fields/class-acf-field-wysiwyg.php:330
msgid "Visual Only"
msgstr "Sólo visual"

#: includes/fields/class-acf-field-wysiwyg.php:329
msgid "Visual & Text"
msgstr "Visual y Texto"

#: includes/fields/class-acf-field-icon_picker.php:262
#: includes/fields/class-acf-field-wysiwyg.php:324
msgid "Tabs"
msgstr "Pestañas"

#: includes/fields/class-acf-field-wysiwyg.php:268
msgid "Click to initialize TinyMCE"
msgstr "Haz clic para iniciar TinyMCE"

#: includes/fields/class-acf-field-wysiwyg.php:262
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr "Texto"

#: includes/fields/class-acf-field-wysiwyg.php:261
msgid "Visual"
msgstr "Visual"

#: includes/fields/class-acf-field-text.php:181
#: includes/fields/class-acf-field-textarea.php:217
msgid "Value must not exceed %d characters"
msgstr "El valor no debe exceder los %d caracteres"

#: includes/fields/class-acf-field-text.php:116
#: includes/fields/class-acf-field-textarea.php:114
msgid "Leave blank for no limit"
msgstr "Déjalo en blanco para ilimitado"

#: includes/fields/class-acf-field-text.php:115
#: includes/fields/class-acf-field-textarea.php:113
msgid "Character Limit"
msgstr "Límite de caracteres"

#: includes/fields/class-acf-field-email.php:144
#: includes/fields/class-acf-field-number.php:194
#: includes/fields/class-acf-field-password.php:95
#: includes/fields/class-acf-field-range.php:228
#: includes/fields/class-acf-field-text.php:156
msgid "Appears after the input"
msgstr "Aparece después del campo"

#: includes/fields/class-acf-field-email.php:143
#: includes/fields/class-acf-field-number.php:193
#: includes/fields/class-acf-field-password.php:94
#: includes/fields/class-acf-field-range.php:227
#: includes/fields/class-acf-field-text.php:155
msgid "Append"
msgstr "Anexar"

#: includes/fields/class-acf-field-email.php:134
#: includes/fields/class-acf-field-number.php:184
#: includes/fields/class-acf-field-password.php:85
#: includes/fields/class-acf-field-range.php:218
#: includes/fields/class-acf-field-text.php:146
msgid "Appears before the input"
msgstr "Aparece antes del campo"

#: includes/fields/class-acf-field-email.php:133
#: includes/fields/class-acf-field-number.php:183
#: includes/fields/class-acf-field-password.php:84
#: includes/fields/class-acf-field-range.php:217
#: includes/fields/class-acf-field-text.php:145
msgid "Prepend"
msgstr "Anteponer"

#: includes/fields/class-acf-field-email.php:124
#: includes/fields/class-acf-field-number.php:164
#: includes/fields/class-acf-field-password.php:75
#: includes/fields/class-acf-field-text.php:136
#: includes/fields/class-acf-field-textarea.php:146
#: includes/fields/class-acf-field-url.php:105
msgid "Appears within the input"
msgstr "Aparece en el campo"

#: includes/fields/class-acf-field-email.php:123
#: includes/fields/class-acf-field-number.php:163
#: includes/fields/class-acf-field-password.php:74
#: includes/fields/class-acf-field-text.php:135
#: includes/fields/class-acf-field-textarea.php:145
#: includes/fields/class-acf-field-url.php:104
msgid "Placeholder Text"
msgstr "Texto del marcador de posición"

#: includes/fields/class-acf-field-button-group.php:148
#: includes/fields/class-acf-field-email.php:104
#: includes/fields/class-acf-field-number.php:114
#: includes/fields/class-acf-field-radio.php:193
#: includes/fields/class-acf-field-range.php:154
#: includes/fields/class-acf-field-text.php:96
#: includes/fields/class-acf-field-textarea.php:94
#: includes/fields/class-acf-field-url.php:85
#: includes/fields/class-acf-field-wysiwyg.php:292
msgid "Appears when creating a new post"
msgstr "Aparece cuando se está creando una nueva entrada"

#: includes/fields/class-acf-field-text.php:22
msgid "Text"
msgstr "Texto"

#: includes/fields/class-acf-field-relationship.php:753
msgid "%1$s requires at least %2$s selection"
msgid_plural "%1$s requires at least %2$s selections"
msgstr[0] "%1$s necesita al menos %2$s selección"
msgstr[1] "%1$s necesita al menos %2$s selecciones"

#: includes/fields/class-acf-field-post_object.php:402
#: includes/fields/class-acf-field-relationship.php:616
msgid "Post ID"
msgstr "ID de publicación"

#: includes/fields/class-acf-field-post_object.php:15
#: includes/fields/class-acf-field-post_object.php:401
#: includes/fields/class-acf-field-relationship.php:615
msgid "Post Object"
msgstr "Objeto de publicación"

#: includes/fields/class-acf-field-relationship.php:648
msgid "Maximum Posts"
msgstr "Número máximo de entradas"

#: includes/fields/class-acf-field-relationship.php:638
msgid "Minimum Posts"
msgstr "Número mínimo de entradas"

#: includes/admin/views/acf-field-group/options.php:183
#: includes/admin/views/acf-post-type/advanced-settings.php:29
#: includes/fields/class-acf-field-relationship.php:673
msgid "Featured Image"
msgstr "Imagen destacada"

#: includes/fields/class-acf-field-relationship.php:669
msgid "Selected elements will be displayed in each result"
msgstr "Los elementos seleccionados se mostrarán en cada resultado"

#: includes/fields/class-acf-field-relationship.php:668
msgid "Elements"
msgstr "Elementos"

#: includes/fields/class-acf-field-relationship.php:602
#: includes/fields/class-acf-field-taxonomy.php:20
#: includes/fields/class-acf-field-taxonomy.php:628
#: includes/locations/class-acf-location-taxonomy.php:22
msgid "Taxonomy"
msgstr "Taxonomía"

#: includes/fields/class-acf-field-relationship.php:601
#: includes/locations/class-acf-location-post-type.php:22
#: includes/post-types/class-acf-post-type.php:92
msgid "Post Type"
msgstr "Tipo de contenido"

#: includes/fields/class-acf-field-relationship.php:595
msgid "Filters"
msgstr "Filtros"

#: includes/fields/class-acf-field-page_link.php:480
#: includes/fields/class-acf-field-post_object.php:389
#: includes/fields/class-acf-field-relationship.php:588
msgid "All taxonomies"
msgstr "Todas las taxonomías"

#: includes/fields/class-acf-field-page_link.php:472
#: includes/fields/class-acf-field-post_object.php:381
#: includes/fields/class-acf-field-relationship.php:580
msgid "Filter by Taxonomy"
msgstr "Filtrar por taxonomía"

#: includes/fields/class-acf-field-page_link.php:450
#: includes/fields/class-acf-field-post_object.php:359
#: includes/fields/class-acf-field-relationship.php:558
msgid "All post types"
msgstr "Todos los tipos de contenido"

#: includes/fields/class-acf-field-page_link.php:442
#: includes/fields/class-acf-field-post_object.php:351
#: includes/fields/class-acf-field-relationship.php:550
msgid "Filter by Post Type"
msgstr "Filtrar por tipo de contenido"

#: includes/fields/class-acf-field-relationship.php:450
msgid "Search..."
msgstr "Buscar..."

#: includes/fields/class-acf-field-relationship.php:380
msgid "Select taxonomy"
msgstr "Selecciona taxonomía"

#: includes/fields/class-acf-field-relationship.php:372
msgid "Select post type"
msgstr "Seleccionar tipo de contenido"

#: includes/fields/class-acf-field-relationship.php:78
msgid "No matches found"
msgstr "No se han encontrado coincidencias"

#: includes/fields/class-acf-field-relationship.php:77
msgid "Loading"
msgstr "Cargando"

#: includes/fields/class-acf-field-relationship.php:76
msgid "Maximum values reached ( {max} values )"
msgstr "Valores máximos alcanzados ( {max} valores )"

#: includes/fields/class-acf-field-relationship.php:17
msgid "Relationship"
msgstr "Relación"

#: includes/fields/class-acf-field-file.php:277
#: includes/fields/class-acf-field-image.php:307
msgid "Comma separated list. Leave blank for all types"
msgstr "Lista separada por comas. Déjalo en blanco para todos los tipos"

#: includes/fields/class-acf-field-file.php:276
#: includes/fields/class-acf-field-image.php:306
msgid "Allowed File Types"
msgstr "Tipos de archivo permitidos"

#: includes/fields/class-acf-field-file.php:264
#: includes/fields/class-acf-field-image.php:270
msgid "Maximum"
msgstr "Máximo"

#: includes/fields/class-acf-field-file.php:144
#: includes/fields/class-acf-field-file.php:256
#: includes/fields/class-acf-field-file.php:268
#: includes/fields/class-acf-field-image.php:261
#: includes/fields/class-acf-field-image.php:297
msgid "File size"
msgstr "Tamaño del archivo"

#: includes/fields/class-acf-field-image.php:235
#: includes/fields/class-acf-field-image.php:271
msgid "Restrict which images can be uploaded"
msgstr "Restringir qué imágenes se pueden subir"

#: includes/fields/class-acf-field-file.php:252
#: includes/fields/class-acf-field-image.php:234
msgid "Minimum"
msgstr "Mínimo"

#: includes/fields/class-acf-field-file.php:222
#: includes/fields/class-acf-field-image.php:200
msgid "Uploaded to post"
msgstr "Subidos al contenido"

#: includes/fields/class-acf-field-file.php:221
#: includes/fields/class-acf-field-image.php:199
#: includes/locations/class-acf-location-attachment.php:73
#: includes/locations/class-acf-location-comment.php:61
#: includes/locations/class-acf-location-nav-menu.php:74
#: includes/locations/class-acf-location-taxonomy.php:63
#: includes/locations/class-acf-location-user-form.php:71
#: includes/locations/class-acf-location-user-role.php:78
#: includes/locations/class-acf-location-widget.php:65
msgid "All"
msgstr "Todos"

#: includes/fields/class-acf-field-file.php:216
#: includes/fields/class-acf-field-image.php:194
msgid "Limit the media library choice"
msgstr "Limitar las opciones de la biblioteca de medios"

#: includes/fields/class-acf-field-file.php:215
#: includes/fields/class-acf-field-image.php:193
msgid "Library"
msgstr "Biblioteca"

#: includes/fields/class-acf-field-image.php:326
msgid "Preview Size"
msgstr "Tamaño de vista previa"

#: includes/fields/class-acf-field-image.php:185
msgid "Image ID"
msgstr "ID de imagen"

#: includes/fields/class-acf-field-image.php:184
msgid "Image URL"
msgstr "URL de imagen"

#: includes/fields/class-acf-field-image.php:183
msgid "Image Array"
msgstr "Array de imágenes"

#: includes/fields/class-acf-field-button-group.php:158
#: includes/fields/class-acf-field-checkbox.php:343
#: includes/fields/class-acf-field-file.php:200
#: includes/fields/class-acf-field-link.php:147
#: includes/fields/class-acf-field-radio.php:203
msgid "Specify the returned value on front end"
msgstr "Especificar el valor devuelto en la web"

#: includes/fields/class-acf-field-button-group.php:157
#: includes/fields/class-acf-field-checkbox.php:342
#: includes/fields/class-acf-field-file.php:199
#: includes/fields/class-acf-field-link.php:146
#: includes/fields/class-acf-field-radio.php:202
#: includes/fields/class-acf-field-taxonomy.php:672
msgid "Return Value"
msgstr "Valor de retorno"

#: includes/fields/class-acf-field-image.php:155
msgid "Add Image"
msgstr "Agregar imagen"

#: includes/fields/class-acf-field-image.php:155
msgid "No image selected"
msgstr "No hay ninguna imagen seleccionada"

#: includes/assets.php:353 includes/fields/class-acf-field-file.php:152
#: includes/fields/class-acf-field-image.php:135
#: includes/fields/class-acf-field-link.php:124
msgid "Remove"
msgstr "Quitar"

#: includes/admin/views/acf-field-group/field.php:89
#: includes/fields/class-acf-field-file.php:150
#: includes/fields/class-acf-field-image.php:133
#: includes/fields/class-acf-field-link.php:124
msgid "Edit"
msgstr "Editar"

#: includes/fields/class-acf-field-image.php:63 includes/media.php:55
msgid "All images"
msgstr "Todas las imágenes"

#: includes/fields/class-acf-field-image.php:62
msgid "Update Image"
msgstr "Actualizar imagen"

#: includes/fields/class-acf-field-image.php:61
msgid "Edit Image"
msgstr "Editar imagen"

#: includes/fields/class-acf-field-image.php:60
msgid "Select Image"
msgstr "Seleccionar imagen"

#: includes/fields/class-acf-field-image.php:22
msgid "Image"
msgstr "Imagen"

#: includes/fields/class-acf-field-message.php:113
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr ""
"Permitir que el maquetado HTML se muestre como texto visible en vez de "
"interpretarlo"

#: includes/fields/class-acf-field-message.php:112
msgid "Escape HTML"
msgstr "Escapar HTML"

#: includes/fields/class-acf-field-message.php:104
#: includes/fields/class-acf-field-textarea.php:162
msgid "No Formatting"
msgstr "Sin formato"

#: includes/fields/class-acf-field-message.php:103
#: includes/fields/class-acf-field-textarea.php:161
msgid "Automatically add &lt;br&gt;"
msgstr "Agregar &lt;br&gt; automáticamente"

#: includes/fields/class-acf-field-message.php:102
#: includes/fields/class-acf-field-textarea.php:160
msgid "Automatically add paragraphs"
msgstr "Agregar párrafos automáticamente"

#: includes/fields/class-acf-field-message.php:98
#: includes/fields/class-acf-field-textarea.php:156
msgid "Controls how new lines are rendered"
msgstr "Controla cómo se muestran los saltos de línea"

#: includes/fields/class-acf-field-message.php:97
#: includes/fields/class-acf-field-textarea.php:155
msgid "New Lines"
msgstr "Nuevas líneas"

#: includes/fields/class-acf-field-date_picker.php:221
#: includes/fields/class-acf-field-date_time_picker.php:208
msgid "Week Starts On"
msgstr "La semana comienza el"

#: includes/fields/class-acf-field-date_picker.php:190
msgid "The format used when saving a value"
msgstr "El formato utilizado cuando se guarda un valor"

#: includes/fields/class-acf-field-date_picker.php:189
msgid "Save Format"
msgstr "Guardar formato"

#: includes/fields/class-acf-field-date_picker.php:61
msgctxt "Date Picker JS weekHeader"
msgid "Wk"
msgstr "Sem"

#: includes/fields/class-acf-field-date_picker.php:60
msgctxt "Date Picker JS prevText"
msgid "Prev"
msgstr "Anterior"

#: includes/fields/class-acf-field-date_picker.php:59
msgctxt "Date Picker JS nextText"
msgid "Next"
msgstr "Siguiente"

#: includes/fields/class-acf-field-date_picker.php:58
msgctxt "Date Picker JS currentText"
msgid "Today"
msgstr "Hoy"

#: includes/fields/class-acf-field-date_picker.php:57
msgctxt "Date Picker JS closeText"
msgid "Done"
msgstr "Listo"

#: includes/fields/class-acf-field-date_picker.php:22
msgid "Date Picker"
msgstr "Selector de fecha"

#: includes/fields/class-acf-field-image.php:238
#: includes/fields/class-acf-field-image.php:274
#: includes/fields/class-acf-field-oembed.php:241
msgid "Width"
msgstr "Ancho"

#: includes/fields/class-acf-field-oembed.php:238
#: includes/fields/class-acf-field-oembed.php:250
msgid "Embed Size"
msgstr "Tamaño de incrustación"

#: includes/fields/class-acf-field-oembed.php:198
msgid "Enter URL"
msgstr "Introduce la URL"

#: includes/fields/class-acf-field-oembed.php:22
msgid "oEmbed"
msgstr "oEmbed"

#: includes/fields/class-acf-field-true_false.php:172
msgid "Text shown when inactive"
msgstr "Texto mostrado cuando está inactivo"

#: includes/fields/class-acf-field-true_false.php:171
msgid "Off Text"
msgstr "Texto desactivado"

#: includes/fields/class-acf-field-true_false.php:156
msgid "Text shown when active"
msgstr "Texto mostrado cuando está activo"

#: includes/fields/class-acf-field-true_false.php:155
msgid "On Text"
msgstr "Texto activado"

#: includes/fields/class-acf-field-select.php:420
#: includes/fields/class-acf-field-true_false.php:187
msgid "Stylized UI"
msgstr "UI estilizada"

#: includes/fields/class-acf-field-button-group.php:147
#: includes/fields/class-acf-field-checkbox.php:332
#: includes/fields/class-acf-field-color_picker.php:144
#: includes/fields/class-acf-field-email.php:103
#: includes/fields/class-acf-field-number.php:113
#: includes/fields/class-acf-field-radio.php:192
#: includes/fields/class-acf-field-range.php:153
#: includes/fields/class-acf-field-select.php:351
#: includes/fields/class-acf-field-text.php:95
#: includes/fields/class-acf-field-textarea.php:93
#: includes/fields/class-acf-field-true_false.php:135
#: includes/fields/class-acf-field-url.php:84
#: includes/fields/class-acf-field-wysiwyg.php:291
msgid "Default Value"
msgstr "Valor por defecto"

#: includes/fields/class-acf-field-true_false.php:126
msgid "Displays text alongside the checkbox"
msgstr "Muestra el texto junto a la casilla de verificación"

#: includes/fields/class-acf-field-message.php:23
#: includes/fields/class-acf-field-message.php:87
#: includes/fields/class-acf-field-true_false.php:125
msgid "Message"
msgstr "Mensaje"

#: includes/assets.php:352 includes/fields/class-acf-field-true_false.php:79
#: includes/fields/class-acf-field-true_false.php:175
#: src/Site_Health/Site_Health.php:281 src/Site_Health/Site_Health.php:343
msgid "No"
msgstr "No"

#: includes/assets.php:351 includes/fields/class-acf-field-true_false.php:76
#: includes/fields/class-acf-field-true_false.php:159
#: src/Site_Health/Site_Health.php:280 src/Site_Health/Site_Health.php:343
msgid "Yes"
msgstr "Sí"

#: includes/fields/class-acf-field-true_false.php:22
msgid "True / False"
msgstr "Verdadero / Falso"

#: includes/fields/class-acf-field-group.php:415
msgid "Row"
msgstr "Fila"

#: includes/fields/class-acf-field-group.php:414
msgid "Table"
msgstr "Tabla"

#: includes/admin/post-types/admin-field-group.php:158
#: includes/fields/class-acf-field-group.php:413
msgid "Block"
msgstr "Bloque"

#: includes/fields/class-acf-field-group.php:408
msgid "Specify the style used to render the selected fields"
msgstr ""
"Especifica el estilo utilizado para representar los campos seleccionados"

#: includes/fields.php:330 includes/fields/class-acf-field-button-group.php:204
#: includes/fields/class-acf-field-checkbox.php:405
#: includes/fields/class-acf-field-group.php:407
#: includes/fields/class-acf-field-radio.php:276
msgid "Layout"
msgstr "Estructura"

#: includes/fields/class-acf-field-group.php:391
msgid "Sub Fields"
msgstr "Subcampos"

#: includes/fields/class-acf-field-group.php:22
msgid "Group"
msgstr "Grupo"

#: includes/fields/class-acf-field-google-map.php:222
msgid "Customize the map height"
msgstr "Personalizar la altura del mapa"

#: includes/fields/class-acf-field-google-map.php:221
#: includes/fields/class-acf-field-image.php:249
#: includes/fields/class-acf-field-image.php:285
#: includes/fields/class-acf-field-oembed.php:253
msgid "Height"
msgstr "Altura"

#: includes/fields/class-acf-field-google-map.php:210
msgid "Set the initial zoom level"
msgstr "Establecer el nivel inicial de zoom"

#: includes/fields/class-acf-field-google-map.php:209
msgid "Zoom"
msgstr "Zoom"

#: includes/fields/class-acf-field-google-map.php:183
#: includes/fields/class-acf-field-google-map.php:196
msgid "Center the initial map"
msgstr "Centrar inicialmente el mapa"

#: includes/fields/class-acf-field-google-map.php:182
#: includes/fields/class-acf-field-google-map.php:195
msgid "Center"
msgstr "Centro"

#: includes/fields/class-acf-field-google-map.php:154
msgid "Search for address..."
msgstr "Buscar dirección..."

#: includes/fields/class-acf-field-google-map.php:151
msgid "Find current location"
msgstr "Encontrar ubicación actual"

#: includes/fields/class-acf-field-google-map.php:150
msgid "Clear location"
msgstr "Borrar ubicación"

#: includes/fields/class-acf-field-google-map.php:149
#: includes/fields/class-acf-field-relationship.php:600
msgid "Search"
msgstr "Buscar"

#: includes/fields/class-acf-field-google-map.php:57
msgid "Sorry, this browser does not support geolocation"
msgstr "Lo siento, este navegador no es compatible con la geolocalización"

#: includes/fields/class-acf-field-google-map.php:22
msgid "Google Map"
msgstr "Mapa de Google"

#: includes/fields/class-acf-field-date_picker.php:201
#: includes/fields/class-acf-field-date_time_picker.php:189
#: includes/fields/class-acf-field-time_picker.php:122
msgid "The format returned via template functions"
msgstr "El formato devuelto por de las funciones del tema"

#: includes/fields/class-acf-field-color_picker.php:168
#: includes/fields/class-acf-field-date_picker.php:200
#: includes/fields/class-acf-field-date_time_picker.php:188
#: includes/fields/class-acf-field-icon_picker.php:285
#: includes/fields/class-acf-field-image.php:177
#: includes/fields/class-acf-field-post_object.php:396
#: includes/fields/class-acf-field-relationship.php:610
#: includes/fields/class-acf-field-select.php:362
#: includes/fields/class-acf-field-time_picker.php:121
#: includes/fields/class-acf-field-user.php:95
msgid "Return Format"
msgstr "Formato de retorno"

#: includes/fields/class-acf-field-date_picker.php:179
#: includes/fields/class-acf-field-date_picker.php:210
#: includes/fields/class-acf-field-date_time_picker.php:180
#: includes/fields/class-acf-field-date_time_picker.php:198
#: includes/fields/class-acf-field-time_picker.php:113
#: includes/fields/class-acf-field-time_picker.php:129
msgid "Custom:"
msgstr "Personalizado:"

#: includes/fields/class-acf-field-date_picker.php:171
#: includes/fields/class-acf-field-date_time_picker.php:171
#: includes/fields/class-acf-field-time_picker.php:106
msgid "The format displayed when editing a post"
msgstr "El formato mostrado cuando se edita una publicación"

#: includes/fields/class-acf-field-date_picker.php:170
#: includes/fields/class-acf-field-date_time_picker.php:170
#: includes/fields/class-acf-field-time_picker.php:105
msgid "Display Format"
msgstr "Formato de visualización"

#: includes/fields/class-acf-field-time_picker.php:22
msgid "Time Picker"
msgstr "Selector de hora"

#. translators: counts for inactive field groups
#: acf.php:526
msgid "Inactive <span class=\"count\">(%s)</span>"
msgid_plural "Inactive <span class=\"count\">(%s)</span>"
msgstr[0] "Inactivo <span class=\"count\">(%s)</span>"
msgstr[1] "Inactivos <span class=\"count\">(%s)</span>"

#: acf.php:487
msgid "No Fields found in Trash"
msgstr "No se han encontrado campos en la papelera"

#: acf.php:486
msgid "No Fields found"
msgstr "No se han encontrado campos"

#: acf.php:485
msgid "Search Fields"
msgstr "Buscar campos"

#: acf.php:484
msgid "View Field"
msgstr "Ver campo"

#: acf.php:483 includes/admin/views/acf-field-group/fields.php:113
msgid "New Field"
msgstr "Nuevo campo"

#: acf.php:482
msgid "Edit Field"
msgstr "Editar campo"

#: acf.php:481
msgid "Add New Field"
msgstr "Agregar nuevo campo"

#: acf.php:479
msgid "Field"
msgstr "Campo"

#: acf.php:478 includes/admin/post-types/admin-field-group.php:179
#: includes/admin/post-types/admin-field-groups.php:93
#: includes/admin/views/acf-field-group/fields.php:32
msgid "Fields"
msgstr "Campos"

#: acf.php:453
msgid "No Field Groups found in Trash"
msgstr "No se han encontrado grupos de campos en la papelera"

#: acf.php:452
msgid "No Field Groups found"
msgstr "No se han encontrado grupos de campos"

#: acf.php:451
msgid "Search Field Groups"
msgstr "Buscar grupo de campos"

#: acf.php:450
msgid "View Field Group"
msgstr "Ver grupo de campos"

#: acf.php:449
msgid "New Field Group"
msgstr "Nuevo grupo de campos"

#: acf.php:448
msgid "Edit Field Group"
msgstr "Editar grupo de campos"

#: acf.php:447
msgid "Add New Field Group"
msgstr "Agregar nuevo grupo de campos"

#: acf.php:446 acf.php:480
#: includes/admin/views/acf-post-type/advanced-settings.php:224
#: includes/post-types/class-acf-post-type.php:93
#: includes/post-types/class-acf-taxonomy.php:92
msgid "Add New"
msgstr "Agregar nuevo"

#: acf.php:445
msgid "Field Group"
msgstr "Grupo de campos"

#: acf.php:444 includes/admin/post-types/admin-field-groups.php:55
#: includes/admin/post-types/admin-post-types.php:113
#: includes/admin/post-types/admin-taxonomies.php:112
msgid "Field Groups"
msgstr "Grupos de campos"

#. Description of the plugin
#: acf.php
msgid "Customize WordPress with powerful, professional and intuitive fields."
msgstr "Personaliza WordPress con campos potentes, profesionales e intuitivos."

#. Plugin URI of the plugin
#: acf.php
msgid "https://www.advancedcustomfields.com"
msgstr "https://www.advancedcustomfields.com"

#. Plugin Name of the plugin
#: acf.php acf.php:290
msgid "Advanced Custom Fields"
msgstr "Advanced Custom Fields"
