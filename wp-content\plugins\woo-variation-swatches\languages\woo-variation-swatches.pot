# Copyright (C) 2025 Variation Swatches for WooCommerce
# This file is distributed under the same license as the Variation Swatches for WooCommerce package.
msgid ""
msgstr ""
"Project-Id-Version: Variation Swatches for WooCommerce\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language-Team: <PERSON><PERSON> <<EMAIL>>\n"
"POT-Creation-Date: 2025-02-16 11:38+0000\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-KeywordsList: __;_e;_ex:1,2c;_n:1,2;_n_noop:1,2;_nx:1,2,4c;_nx_noop:1,2,3c;_x:1,2c;esc_attr__;esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.js\n"
"X-Poedit-SourceCharset: UTF-8\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: ../includes/class-woo-variation-swatches-backend.php:117
msgid "View documentation"
msgstr ""

#: ../includes/class-woo-variation-swatches-backend.php:117, ../includes/html-settings-sidebar.php:7
msgid "Documentation"
msgstr ""

#: ../includes/class-woo-variation-swatches-backend.php:118, ../includes/class-woo-variation-swatches-backend.php:118
msgid "Video Tutorials"
msgstr ""

#: ../includes/class-woo-variation-swatches-backend.php:119, ../includes/class-woo-variation-swatches-backend.php:119
msgid "Help & Support"
msgstr ""

#: ../includes/class-woo-variation-swatches-backend.php:127
msgid "View Swatches settings"
msgstr ""

#: ../includes/class-woo-variation-swatches-backend.php:127
msgid "Settings"
msgstr ""

#: ../includes/class-woo-variation-swatches-backend.php:132, ../includes/class-woo-variation-swatches-backend.php:132
msgid "Go Pro"
msgstr ""

#: ../includes/class-woo-variation-swatches-backend.php:161
msgid "Select values"
msgstr ""

#: ../includes/class-woo-variation-swatches-backend.php:182
msgid "Select all"
msgstr ""

#: ../includes/class-woo-variation-swatches-backend.php:183
msgid "Select none"
msgstr ""

#: ../includes/class-woo-variation-swatches-backend.php:184
msgid "Create value"
msgstr ""

#: ../includes/class-woo-variation-swatches-backend.php:221, ../includes/class-woo-variation-swatches-backend.php:310
msgid "Choose an Image"
msgstr ""

#: ../includes/class-woo-variation-swatches-backend.php:222
msgid "Add Attribute"
msgstr ""

#: ../includes/class-woo-variation-swatches-backend.php:223
msgid "Add"
msgstr ""

#: ../includes/class-woo-variation-swatches-backend.php:224
msgid "Cancel"
msgstr ""

#: ../includes/class-woo-variation-swatches-backend.php:225
msgid "Use Image"
msgstr ""

#: ../includes/class-woo-variation-swatches-backend.php:226
msgid "Add Media"
msgstr ""

#: ../includes/class-woo-variation-swatches-backend.php:230
msgid "Variation Swatches Settings"
msgstr ""

#: ../includes/class-woo-variation-swatches-backend.php:232
msgid "Are you sure you want to reset it to default setting?"
msgstr ""

#: ../includes/class-woo-variation-swatches-backend.php:233
msgid "Please save changed first."
msgstr ""

#: ../includes/class-woo-variation-swatches-backend.php:240
msgid "Select"
msgstr ""

#: ../includes/class-woo-variation-swatches-backend.php:241, ../includes/class-woo-variation-swatches-backend.php:300
msgid "Color"
msgstr ""

#: ../includes/class-woo-variation-swatches-backend.php:242, ../includes/class-woo-variation-swatches-backend.php:309
msgid "Image"
msgstr ""

#: ../includes/class-woo-variation-swatches-backend.php:243
msgid "Button"
msgstr ""

#: ../includes/class-woo-variation-swatches-backend.php:244
msgid "Radio"
msgstr ""

#: ../includes/class-woo-variation-swatches-backend.php:252
msgid "Mixed"
msgstr ""

#: ../includes/class-woo-variation-swatches-backend.php:301
msgid "Choose a color"
msgstr ""

#: ../includes/class-woo-variation-swatches-deactivate-feedback.php:48
msgid "It's a temporary deactivation."
msgstr ""

#: ../includes/class-woo-variation-swatches-deactivate-feedback.php:53
msgid "I couldn't understand how to make it work."
msgstr ""

#: ../includes/class-woo-variation-swatches-deactivate-feedback.php:55
msgid "It converts variation select box to beautiful swatches.<br><a target=\"_blank\" href=\"https://demo.getwooplugins.com/woocommerce-variation-swatches/\">Please check live demo</a>."
msgstr ""

#: ../includes/class-woo-variation-swatches-deactivate-feedback.php:59
msgid "I no longer need the plugin."
msgstr ""

#: ../includes/class-woo-variation-swatches-deactivate-feedback.php:64
msgid "I found a better plugin."
msgstr ""

#: ../includes/class-woo-variation-swatches-deactivate-feedback.php:65
msgid "Please share which plugin."
msgstr ""

#: ../includes/class-woo-variation-swatches-deactivate-feedback.php:69
msgid "The plugin <strong>broke my layout</strong> or some functionality."
msgstr ""

#: ../includes/class-woo-variation-swatches-deactivate-feedback.php:71
msgid "<a target=\"_blank\" href=\"https://getwooplugins.com/tickets/\">Please open a support ticket</a>, we will fix it immediately."
msgstr ""

#: ../includes/class-woo-variation-swatches-deactivate-feedback.php:75
msgid "I need someone to <strong>setup this plugin.</strong>"
msgstr ""

#: ../includes/class-woo-variation-swatches-deactivate-feedback.php:76
msgid "Your email address."
msgstr ""

#: ../includes/class-woo-variation-swatches-deactivate-feedback.php:78
msgid "Please provide your email address to contact with you <br>and help you to set up and configure this plugin."
msgstr ""

#: ../includes/class-woo-variation-swatches-deactivate-feedback.php:82
msgid "The plugin is <strong>too complicated to configure.</strong>"
msgstr ""

#: ../includes/class-woo-variation-swatches-deactivate-feedback.php:84
msgid "<a target=\"_blank\" href=\"https://getwooplugins.com/documentation/woocommerce-variation-swatches/\">Have you checked our documentation?</a>."
msgstr ""

#: ../includes/class-woo-variation-swatches-deactivate-feedback.php:88
msgid "I need specific feature that you don't support."
msgstr ""

#: ../includes/class-woo-variation-swatches-deactivate-feedback.php:89
msgid "Please share with us."
msgstr ""

#: ../includes/class-woo-variation-swatches-deactivate-feedback.php:94
msgid "Other"
msgstr ""

#: ../includes/class-woo-variation-swatches-deactivate-feedback.php:95
msgid "Please share the reason"
msgstr ""

#. translators: %s: image URL
#: ../includes/class-woo-variation-swatches-export-import.php:253
msgid "Not able to attach \"%s\"."
msgstr ""

#. translators: %s: image URL
#: ../includes/class-woo-variation-swatches-export-import.php:262
msgid "Unable to use image \"%s\"."
msgstr ""

#: ../includes/class-woo-variation-swatches-manage-cache.php:83
msgid "Clear swatches transient"
msgstr ""

#: ../includes/class-woo-variation-swatches-product-edit-panel.php:31, ../includes/class-woo-variation-swatches-settings.php:29
msgid "Swatches Settings"
msgstr ""

#. translators: %s Product Stock Left.
#: ../includes/class-woo-variation-swatches-product-page.php:131
msgid "%s left"
msgstr ""

#: ../includes/class-woo-variation-swatches-product-page.php:827, ../includes/class-woo-variation-swatches-product-page.php:853
msgid "Choose an option"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:25
msgid "Variation Swatches"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:33
msgid "Variation Swatches for WooCommerce Settings"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:87, ../includes/class-woo-variation-swatches-settings.php:106
msgid "See Documentation"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:89, ../includes/class-woo-variation-swatches-settings.php:220
msgid "Help &amp; Support"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:103
msgid "Buy Now"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:115
msgid "Swatches Shape Style"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:121
msgid "Swatches Default To Button"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:127
msgid "Swatches Default To Image"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:133
msgid "Swatches Clear on Reselect"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:139
msgid "Swatches Hide Out Of Stock"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:145
msgid "Swatches Clickable Out Of Stock"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:151
msgid "Swatches Show variation stock info."
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:157
msgid "Swatches Attribute Display Limit"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:163
msgid "Swatches Show Product Availability"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:169
msgid "Swatches Display Position"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:175
msgid "Swatches Display On Widget"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:181
msgid "Swatches Show as catalog mode"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:187, ../includes/class-woo-variation-swatches-settings.php:193, ../includes/class-woo-variation-swatches-settings.php:199
msgid "Swatches Show variation preview"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:205
msgid "Swatches Disabled Attribute style"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:211
msgid "Swatches Generate Sharable URL"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:218
msgid "Get license"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:224
msgid "Swatches License"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:230
msgid "Swatches On Archive Page"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:236
msgid "Swatches Archive Default Selected"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:242
msgid "Swatches Settings Reset."
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:296, ../includes/getwooplugins/class-getwooplugins-settings-page.php:128
msgid "General"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:297
msgid "Advanced"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:298
msgid "Styling"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:299
msgid "Product Page"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:300
msgid "Archive / Shop"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:301
msgid "Special Attributes"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:302, ../includes/class-woo-variation-swatches-term-meta.php:195
msgid "Group"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:304
msgid "License"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:307
msgid "Tutorial"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:312
msgid "Useful Free Plugins"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:337
msgid "General options"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:344
msgid "Enable Stylesheet"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:345
msgid "Enable default stylesheet."
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:352
msgid "Enable Tooltip"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:353
msgid "Enable tooltip on each product attribute."
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:360
msgid "Shape Style"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:362
msgid "This controls which shape style used by default."
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:366
msgid "Rounded Shape"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:367
msgid "Squared Shape"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:374
msgid "Dropdowns to Button"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:375
msgid "Convert default dropdowns to button."
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:384
msgid "Dropdowns to Image"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:385
msgid "Convert default dropdowns to image type if variation has an image."
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:402
msgid "Media Settings"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:403
msgid "Regenerate Thumbnails"
msgstr ""

#. translators: %s: filter name
#: ../includes/class-woo-variation-swatches-settings.php:406
msgid "Attribute image size can be changed by %s filter hook. So this option will not apply any effect."
msgstr ""

#. translators: %1$s = Media Settings Link, %2$s = Regenerate Thumbnails Plugin name
#: ../includes/class-woo-variation-swatches-settings.php:408
msgid "Choose attribute image size. %1$s or use %2$s plugin."
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:419
msgid "Advanced options"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:426
msgid "Clear on Reselect"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:427
msgid "Clear selected attribute on select again."
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:435
msgid "Disable Out of Stock"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:436
msgid "Disable Out Of Stock item."
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:445
msgid "Clickable Out Of Stock"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:446
msgid "Clickable Out Of Stock item."
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:455
msgid "Disabled Attribute style"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:456
msgid "Disabled / Out Of Stock attribute will be hide / blur / crossed."
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:459
msgid "Blur with cross"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:460
msgid "Blur without cross"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:461
msgid "Hide"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:471
msgid "Attribute image size"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:494
msgid "Swatches indicator"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:495
msgid "Change swatches indicator color"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:501
msgid "Tick Color"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:502
msgid "Swatches Selected tick color. Default is: #ffffff"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:513
msgid "Cross Color"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:514
msgid "Swatches cross color. Default is: #ff0000"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:531
msgid "Product Page Swatches Size"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:532
msgid "Change swatches style on product page"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:538
msgid "Width"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:539
msgid "Single product variation item width. Default is: 30"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:553
msgid "Height"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:554
msgid "Single product variation item height. Default is: 30"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:568
msgid "Font Size"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:569
msgid "Single product variation item font size. Default is: 16"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:595
msgid "Single Product Page"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:596
msgid "Settings for single product page"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:602
msgid "Show selected attribute"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:603
msgid "Show selected attribute variation name beside the title."
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:611
msgid "Variation label separator"
msgstr ""

#. translators: %s: default separator :
#: ../includes/class-woo-variation-swatches-settings.php:613
msgid "Variation label separator. Default: %s."
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:629, ../includes/class-woo-variation-swatches-settings.php:704
msgid "Enable Preloader"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:630
msgid "Enable single product page swatches preloader."
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:638
msgid "Generate variation url"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:639
msgid "Generate sharable url based on selected variation attributes."
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:647
msgid "Variation stock info"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:648
msgid "Show variation product stock info."
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:657
msgid "Attribute display limit"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:658
msgid "Single Product page attribute display limit. Default is 0. Means no limit."
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:678
msgid "Note:"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:679
msgid "please open a ticket"
msgstr ""

#. translators: %1$s = Note markup, %2$s = Open ticket link.
#: ../includes/class-woo-variation-swatches-settings.php:681
msgid "Show archive swatches position. %1$s Some theme remove default woocommerce hooks that why it's may not work as expected. For theme compatibility %2$s."
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:688
msgid "Visual Section"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:689
msgid "Advanced change some visual styles on shop / archive page"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:695
msgid "Enable Swatches"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:696
msgid "Show swatches on archive / shop page."
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:705
msgid "Enable archive page swatches preloader."
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:713
msgid "Show Product Availability"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:714
msgid "Show Product availability stock info."
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:725
msgid "Show default selected"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:726
msgid "Show default selected attribute swatches on archive / shop page."
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:735
msgid "Display position"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:740
msgid "Before add to cart button"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:741
msgid "After add to cart button"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:750
msgid "Swatches align"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:751
msgid "Swatches align on archive page"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:756
msgid "Left"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:757
msgid "Center"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:758
msgid "Right"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:766
msgid "Show on filter widget"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:767
msgid "Show variation swatches on filter widget."
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:788
msgid "Catalog mode"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:789
msgid "Show single attribute as catalog mode on shop / archive pages. Catalog mode only change image based on selected variation."
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:795
msgid "Show Single Attribute"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:796
msgid "Show Single Attribute taxonomies on archive page."
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:809
msgid "Single Variation Image Preview"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:810
msgid "Switch variation image when single attribute selected on product page."
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:816
msgid "Variation Image Preview"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:817
msgid "Show single attribute variation image based on first attribute select on product page."
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:831
msgid "Large Size Attribute Section"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:832
msgid "Make a attribute taxonomies size large on single product"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:838
msgid "Show First Attribute In Large Size"
msgstr ""

#: ../includes/class-woo-variation-swatches-settings.php:839
msgid "Show Attribute taxonomies in large size."
msgstr ""

#: ../includes/class-woo-variation-swatches-term-meta.php:418
msgid "Upload / Add image"
msgstr ""

#: ../includes/class-woo-variation-swatches-term-meta.php:424
msgid "Remove image"
msgstr ""

#. translators: %1$s = Template location, %2$s = Template Name.
#: ../includes/class-woo-variation-swatches.php:240
msgid "\"Variation Swatches for WooCommerce\" Plugin try to load \"%1$s\" but template \"%2$s\" was not found."
msgstr ""

#: ../includes/html-product-settings-panel.php:60, ../includes/html-settings-sidebar.php:29
msgid "Variation Swatches for WooCommerce"
msgstr ""

#. translators: %s: Pro Link
#: ../includes/html-product-settings-panel.php:62
msgid "With the premium version of %s, you can do:"
msgstr ""

#: ../includes/html-product-settings-panel.php:68
msgid "Individual Product Basis Attribute Variation Swatches Customization"
msgstr ""

#: ../includes/html-product-settings-panel.php:71, ../includes/html-product-settings-panel.php:79, ../includes/html-product-settings-panel.php:94, ../includes/html-product-settings-panel.php:102, ../includes/html-product-settings-panel.php:110, ../includes/html-product-settings-panel.php:118, ../includes/html-product-settings-panel.php:126, ../includes/html-product-settings-panel.php:134, ../includes/html-product-settings-panel.php:142, ../includes/html-product-settings-panel.php:150, ../includes/html-product-settings-panel.php:165, ../includes/html-product-settings-panel.php:180
msgid "Live Demo"
msgstr ""

#: ../includes/html-product-settings-panel.php:72, ../includes/html-product-settings-panel.php:80, ../includes/html-product-settings-panel.php:87, ../includes/html-product-settings-panel.php:95, ../includes/html-product-settings-panel.php:103, ../includes/html-product-settings-panel.php:111, ../includes/html-product-settings-panel.php:119, ../includes/html-product-settings-panel.php:127, ../includes/html-product-settings-panel.php:135, ../includes/html-product-settings-panel.php:143, ../includes/html-product-settings-panel.php:151, ../includes/html-product-settings-panel.php:166, ../includes/html-product-settings-panel.php:181
msgid "Video Tutorial"
msgstr ""

#: ../includes/html-product-settings-panel.php:77
msgid "Show Image, Color, Button Variation Swatches in Same Attribute"
msgstr ""

#: ../includes/html-product-settings-panel.php:85
msgid "Convert Manually Created Attribute Variations Into Color, Image, and Label Swatches"
msgstr ""

#: ../includes/html-product-settings-panel.php:92
msgid "Group based swatches display."
msgstr ""

#: ../includes/html-product-settings-panel.php:100
msgid "Convert attribute variations into radio button."
msgstr ""

#: ../includes/html-product-settings-panel.php:108
msgid "Show Entire Color, Image, Label And Radio Attributes Swatches In Catalog/ Category / Archive / Store/ Shop Pages"
msgstr ""

#: ../includes/html-product-settings-panel.php:116
msgid "Show Selected Single Color or Image Or Label Attribute Swatches In Catelog/ Category / Archive / Store / Shop Pages"
msgstr ""

#: ../includes/html-product-settings-panel.php:124
msgid "Change Variation Product Gallery After Selecting Single Attribute Like Amazon Or AliExpress"
msgstr ""

#: ../includes/html-product-settings-panel.php:132
msgid "Generate Selected Attribute Variation Link"
msgstr ""

#: ../includes/html-product-settings-panel.php:140
msgid "Option to Select ROUNDED and SQUARED Attribute Variation Swatches Shape In the Same Product."
msgstr ""

#: ../includes/html-product-settings-panel.php:148
msgid "Blur Or Hide Or Show Cross Sign For Out of Stock Variation Swatches (Unlimited Variations Without hiding out of stock item from catalog)"
msgstr ""

#: ../includes/html-product-settings-panel.php:156
msgid "Shop Page Swatches Size Control"
msgstr ""

#: ../includes/html-product-settings-panel.php:158, ../includes/html-product-settings-panel.php:173
msgid "Live Preview"
msgstr ""

#: ../includes/html-product-settings-panel.php:163
msgid "Make Selected Attribute Variation Swatches Size Larger Than Other Default Attribute Variations"
msgstr ""

#: ../includes/html-product-settings-panel.php:171
msgid "Show Custom Text in Variation Tooltip In Product and Shop Page"
msgstr ""

#: ../includes/html-product-settings-panel.php:178
msgid "Show Custom Image in Variation Swatches Tooltip In Product And Shop Page"
msgstr ""

#: ../includes/html-product-settings-panel.php:186
msgid "Archive page swatches positioning."
msgstr ""

#: ../includes/html-product-settings-panel.php:189
msgid "Archive page swatches alignment."
msgstr ""

#: ../includes/html-product-settings-panel.php:192
msgid "Tooltip display setting on archive/shop page."
msgstr ""

#: ../includes/html-product-settings-panel.php:195
msgid "Variation clear button display setting."
msgstr ""

#: ../includes/html-product-settings-panel.php:198
msgid "Customize tooltip text and background color."
msgstr ""

#: ../includes/html-product-settings-panel.php:201
msgid "Customize tooltip image and image size."
msgstr ""

#: ../includes/html-product-settings-panel.php:204
msgid "Customize font size, swatches height and width."
msgstr ""

#: ../includes/html-product-settings-panel.php:207
msgid "Customize swatches colors, background and border sizes."
msgstr ""

#: ../includes/html-product-settings-panel.php:210
msgid "Automatic updates and exclusive technical support."
msgstr ""

#: ../includes/html-product-settings-panel.php:215
msgid "Ok, I need this feature!"
msgstr ""

#: ../includes/html-settings-group.php:5
msgid "Swatches Group"
msgstr ""

#: ../includes/html-settings-group.php:11
msgid "Show your swatches individually or in groups. Split variation swatches into group to make it much more user friendly."
msgstr ""

#: ../includes/html-settings-group.php:14
msgid "Show me, How it works!"
msgstr ""

#: ../includes/html-settings-sidebar.php:9
msgid "Get started by spending some time with the"
msgstr ""

#: ../includes/html-settings-sidebar.php:10
msgid "documentation"
msgstr ""

#: ../includes/html-settings-sidebar.php:11
msgid "Check Official"
msgstr ""

#: ../includes/html-settings-sidebar.php:12
msgid "demo"
msgstr ""

#: ../includes/html-settings-sidebar.php:17
msgid "Facing issue?"
msgstr ""

#: ../includes/html-settings-sidebar.php:19
msgid "Stuck with something?"
msgstr ""

#: ../includes/html-settings-sidebar.php:20
msgid "Please open a ticket"
msgstr ""

#: ../includes/html-settings-sidebar.php:20
msgid "For emergency case join our live chat."
msgstr ""

#: ../includes/html-settings-sidebar.php:25
msgid "Love Our Plugin?"
msgstr ""

#: ../includes/html-settings-sidebar.php:28
msgid "Thank you for choosing"
msgstr ""

#: ../includes/html-settings-sidebar.php:30
msgid "If you have found our plugin useful and makes you smile,"
msgstr ""

#: ../includes/html-settings-sidebar.php:31
msgid "please consider giving us a 5-star rating."
msgstr ""

#: ../includes/html-settings-sidebar.php:31
msgid "It will help us to grow."
msgstr ""

#: ../includes/html-settings-tutorial.php:6
msgid "How to tutorials"
msgstr ""

#: ../includes/html-settings-tutorial.php:107, ../includes/html-settings-tutorial.php:145, ../includes/html-settings-tutorial.php:156, ../includes/html-settings-tutorial.php:195, ../includes/html-settings-tutorial.php:206, ../includes/html-settings-tutorial.php:244, ../includes/html-settings-tutorial.php:255, ../includes/html-settings-tutorial.php:293, ../includes/html-settings-tutorial.php:304, ../includes/html-settings-tutorial.php:342, ../includes/html-settings-tutorial.php:353, ../includes/html-settings-tutorial.php:391, ../includes/html-settings-tutorial.php:402, ../includes/html-settings-tutorial.php:469
msgid "PRO"
msgstr ""

#: ../includes/getwooplugins/class-getwooplugins-admin-menus.php:129, ../includes/getwooplugins/class-getwooplugins-plugin-deactivate-feedback.php:60
msgid "The changes you made will be lost if you navigate away from this page."
msgstr ""

#: ../includes/getwooplugins/class-getwooplugins-admin-menus.php:145, ../includes/getwooplugins/class-getwooplugins-admin-menus.php:171
msgid "GetWooPlugins Settings"
msgstr ""

#: ../includes/getwooplugins/class-getwooplugins-admin-menus.php:145
msgid "GetWooPlugins"
msgstr ""

#: ../includes/getwooplugins/class-getwooplugins-admin-menus.php:171
msgid "Home"
msgstr ""

#: ../includes/getwooplugins/class-getwooplugins-admin-settings.php:65
msgid "Your settings have been saved."
msgstr ""

#: ../includes/getwooplugins/class-getwooplugins-admin-settings.php:215
msgid "Check how this feature works."
msgstr ""

#: ../includes/getwooplugins/class-getwooplugins-admin-settings.php:220
msgid "See how this feature works"
msgstr ""

#: ../includes/getwooplugins/html/deactive-feedback-dialog.php:18
msgid "QUICK FEEDBACK"
msgstr ""

#: ../includes/getwooplugins/html/deactive-feedback-dialog.php:20, ../includes/getwooplugins/html/dialog.php:22
msgid "Close modal panel"
msgstr ""

#: ../includes/getwooplugins/html/deactive-feedback-dialog.php:26
msgid "May we have a little info about why you are deactivating?"
msgstr ""

#: ../includes/getwooplugins/html/deactive-feedback-dialog.php:53, ../includes/getwooplugins/html/deactive-feedback-dialog.php:53
msgid "Send feedback &amp; Deactivate"
msgstr ""

#: ../includes/getwooplugins/html/deactive-feedback-dialog.php:53
msgid "Deactivating..."
msgstr ""

#: ../includes/getwooplugins/html/deactive-feedback-dialog.php:57
msgid "Skip &amp; Deactivate"
msgstr ""

#: ../includes/getwooplugins/html/settings-page.php:85, ../includes/getwooplugins/html/settings-page.php:85
msgid "Save changes"
msgstr ""

#: ../includes/getwooplugins/html/settings-page.php:86
msgid "Are you sure to reset?"
msgstr ""

#: ../includes/getwooplugins/html/settings-page.php:86
msgid "Reset all"
msgstr ""

#: ../woo-variation-swatches.php:65
msgid "<strong>Variation Swatches for WooCommerce</strong> is an add-on of "
msgstr ""

#: ../woo-variation-swatches.php:67
msgid "WooCommerce"
msgstr ""

#. translators: %s: Pro Plugin Version
#: ../woo-variation-swatches.php:140
msgid "You are running older version of \"Variation Swatches for WooCommerce - Pro\". Please upgrade to %s or upper and continue."
msgstr ""

#. translators: %s: Pro Plugin Version
#: ../woo-variation-swatches.php:158
msgid "You are running older version of \"Variation Swatches for WooCommerce - Pro\". Please upgrade to %s or upper."
msgstr ""
