<div class="custom-size-fields mt-3">
    <div class="row">
        <div class="col-md-6">
            <h5>Shirt Measurements</h5>
            <div class="customSizeCont mt-3">
                <div class="customSizeDiagramBox">
                    <div class="customSizeDiagram position-relative">
                        <img src="<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/Shirt-Outline.webp" class="img-fluid" alt="Shirt Diagram">
                        <div class="diagramLabel shirtLengthLabel"></div>
                        <div class="diagramLabel sholuderLabel"></div>
                        <div class="diagramLabel sleevLengthLabel"></div>
                        <div class="diagramLabel wristLabel"></div>
                        <div class="diagramLabel chestLabel"></div>
                        <div class="diagramLabel waistLabel"></div>
                        <div class="diagramLabel shirtHipLabel"></div>
                        <div class="diagramLabel shirtBottomLabel"></div>
                        <div class="diagramLabel armHoleLabel"></div>
                    </div>
                </div>
                <div class="customSizeFields">
                    <div class="form-floating">
                        <input type="text" class="form-control" id="shirtLengthInput" placeholder="Shirt Length" name="shirtLength" value="<?php echo esc_attr($custom_fields['shirtLength'] ?? ''); ?>">
                        <label for="shirtLength">Shirt Length</label>
                    </div>
                    <div class="form-floating">
                        <input type="text" class="form-control" id="sholuderInput" placeholder="Shoulder" name="sholuderLength" value="<?php echo esc_attr($custom_fields['sholuderLength'] ?? ''); ?>">
                        <label for="sholuderLength">Shoulder</label>
                    </div>
                    <div class="form-floating">
                        <input type="text" class="form-control" id="sleevLengthInput" placeholder="Sleeve Length" name="sleevLength" value="<?php echo esc_attr($custom_fields['sleevLength'] ?? ''); ?>">
                        <label for="sleevLength">Sleeve Length</label>
                    </div>
                    <div class="form-floating">
                        <input type="text" class="form-control" id="wristInput" placeholder="Wrist" name="wrist" value="<?php echo esc_attr($custom_fields['wrist'] ?? ''); ?>">
                        <label for="wrist">Wrist</label>
                    </div>
                    <div class="form-floating">
                        <input type="text" class="form-control" id="chestInput" placeholder="Chest" name="chest" value="<?php echo esc_attr($custom_fields['chest'] ?? ''); ?>">
                        <label for="chest">Chest</label>
                    </div>
                    <div class="form-floating">
                        <input type="text" class="form-control" id="waistInput" placeholder="Waist" name="waist" value="<?php echo esc_attr($custom_fields['waist'] ?? ''); ?>">
                        <label for="waist">Waist</label>
                    </div>
                    <div class="form-floating">
                        <input type="text" class="form-control" id="shirtHipInput" placeholder="Hip" name="shirtHip" value="<?php echo esc_attr($custom_fields['shirtHip'] ?? ''); ?>">
                        <label for="shirtHip">Hip</label>
                    </div>
                    <div class="form-floating">
                        <input type="text" class="form-control" id="shirtBottomInput" placeholder="Bottom" name="shirtBottom" value="<?php echo esc_attr($custom_fields['shirtBottom'] ?? ''); ?>">
                        <label for="shirtBottom">Bottom</label>
                    </div>
                    <div class="form-floating">
                        <input type="text" class="form-control" id="armHoleInput" placeholder="Arm Hole" name="armHole" value="<?php echo esc_attr($custom_fields['armHole'] ?? ''); ?>">
                        <label for="armHole">Arm Hole</label>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <h5>Trouser Measurements</h5>
            <div class="customSizeCont">
                <div class="customSizeDiagramBox">
                    <div class="customSizeDiagram position-relative">
                        <img src="<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/trouser-diagram.webp" class="img-fluid" alt="Trouser Diagram">
                        <div class="diagramLabel trouserLengthLabel"></div>
                        <div class="diagramLabel trouserWaistLabel"></div>
                        <div class="diagramLabel trouserHipLabel"></div>
                        <div class="diagramLabel trouserThighLabel"></div>
                        <div class="diagramLabel trouserKneeLabel"></div>
                        <div class="diagramLabel trouserBottomLabel"></div>
                    </div>
                </div>
                <div class="customSizeFields">
                    <div class="form-floating">
                        <input type="text" class="form-control" id="trouserLengthInput" placeholder="Length" name="trouserLength" value="<?php echo esc_attr($custom_fields['trouserLength'] ?? ''); ?>">
                        <label for="trouserLength">Length</label>
                    </div>
                    <div class="form-floating">
                        <input type="text" class="form-control" id="trouserWaistInput" placeholder="Trouser Waist" name="trouserWaist" value="<?php echo esc_attr($custom_fields['trouserWaist'] ?? ''); ?>">
                        <label for="trouserWaist">Trouser Waist</label>
                    </div>
                    <div class="form-floating">
                        <input type="text" class="form-control" id="trouserHipInput" placeholder="Hip" name="trouserHip" value="<?php echo esc_attr($custom_fields['trouserHip'] ?? ''); ?>">
                        <label for="trouserHip">Hip</label>
                    </div>
                    <div class="form-floating">
                        <input type="text" class="form-control" id="trouserThighInput" placeholder="Thigh" name="trouserThigh" value="<?php echo esc_attr($custom_fields['trouserThigh'] ?? ''); ?>">
                        <label for="trouserThigh">Thigh</label>
                    </div>
                    <div class="form-floating">
                        <input type="text" class="form-control" id="trouserKneeInput" placeholder="Knee" name="trouserKnee" value="<?php echo esc_attr($custom_fields['trouserKnee'] ?? ''); ?>">
                        <label for="trouserKnee">Knee</label>
                    </div>
                    <div class="form-floating">
                        <input type="text" class="form-control" id="trouserBottomInput" placeholder="Bottom" name="trouserBottom" value="<?php echo esc_attr($custom_fields['trouserBottom'] ?? ''); ?>">
                        <label for="trouserBottom">Bottom</label>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<script>
document.addEventListener('DOMContentLoaded', function() {
    // Function to safely add event listeners
    function addInputListener(inputId, labelClass) {
        const input = document.getElementById(inputId);
        const label = document.querySelector('.' + labelClass);
        if (input && label) {
            input.addEventListener('input', function() {
                label.textContent = this.value;
            });
        }
    }

    // Add listeners for shirt measurements
    addInputListener('shirtLengthInput', 'shirtLengthLabel');
    addInputListener('sholuderInput', 'sholuderLabel');
    addInputListener('sleevLengthInput', 'sleevLengthLabel');
    addInputListener('wristInput', 'wristLabel');
    addInputListener('chestInput', 'chestLabel');
    addInputListener('waistInput', 'waistLabel');
    addInputListener('shirtHipInput', 'shirtHipLabel');
    addInputListener('shirtBottomInput', 'shirtBottomLabel');
    addInputListener('armHoleInput', 'armHoleLabel');

    // Add listeners for trouser measurements
    addInputListener('trouserLengthInput', 'trouserLengthLabel');
    addInputListener('trouserWaistInput', 'trouserWaistLabel');
    addInputListener('trouserHipInput', 'trouserHipLabel');
    addInputListener('trouserThighInput', 'trouserThighLabel');
    addInputListener('trouserKneeInput', 'trouserKneeLabel');
    addInputListener('trouserBottomInput', 'trouserBottomLabel');
});
</script>