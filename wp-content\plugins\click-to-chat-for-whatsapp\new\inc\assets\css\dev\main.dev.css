.ht_ctc_chat_greetings_box *:not(ul):not(ol):not(.ht_ctc_default):not(.ht_ctc_defaults *) {
    padding: 0px;
    margin: 0px;
    box-sizing: border-box;
}

.ht_ctc_chat_greetings_box ul,
.ht_ctc_chat_greetings_box ol {
    margin-top: 0px;
    margin-bottom: 0px;
}

/* Backdrop */
/* element auto creates from js file */
.ht_ctc_modal_backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.3);
    /* background: rgba(0, 0, 0, 0.4); */
    z-index: 9;
    backdrop-filter: blur(1px);
    /* backdrop-filter: blur(2px); */
    /* transition: opacity 2s ease; */
    animation: fadeIn 0.2s ease;
}

.ht_ctc_chat_greetings_box_layout {
    clear: both;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

/* Scroll Lock */
/* body.ht_ctc_modal_open {
    overflow: hidden;
} */

.ctc_g_content,
.ctc_g_sentbutton,
.ctc_g_bottom {
    margin-top: -0.9px !important;
}

.ctc_g_content {
    max-height: calc(80vh - 140px);
    overflow-y: auto;
}

.greetings_header_image img {
    object-fit: cover;
}

.greetings_header_image {
    position: relative;
    display: inline-block;
}

/* rtl left: 0px; added with in php page. */
.g_header_badge_online {
    position: absolute;
    bottom: 0px;
    right: 0px;
    z-index: 1;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    /* background-color: #06e376;
    border: 2px solid #075e54; */
}

/* Desktop like: above 420 */
@media only screen and (min-width: 420px) {}

/* for mobile */
@media only screen and (max-width: 420px) {

    /* for mobile if widget is at left. then call to action, greetings dialog with close button positions */
    /* greetings */
    .ctc_side_positions .ctc_m_p_left {
        right: unset !important;
        left: 0 !important;
    }

    .ctc_side_positions .ctc_m_p_right {
        left: unset !important;
        right: 0 !important;
    }

    .ctc_side_positions .ctc_m_p_left .ctc_greetings_close_btn {
        float: left !important;
    }

    .ctc_side_positions .ctc_m_p_right .ctc_greetings_close_btn {
        float: right !important;
    }

    /* call to action */
    .ctc_side_positions .ctc_m_cta_order_0 {
        order: 0 !important;
    }

    .ctc_side_positions .ctc_m_cta_order_1 {
        order: 1 !important;
    }

    .ht_ctc_greetings.ctc_m_full_width {
        position: fixed !important;
        bottom: 0px !important;
        right: 0px !important;
    }

    .ht_ctc_greetings.ctc_m_full_width .ht_ctc_chat_greetings_box {
        position: unset !important;
        margin: 7px !important;
        min-width: 80vw !important;
        width: calc(100vw - 14px) !important;
    }

    .ctc_m_full_width .ctc_g_message_box_width {
        max-width: 85% !important;
    }

    /* fix: animation and greetings in mobile devices not working properly.  clear animation-fill-mode for .ht_ctc_animation only if child have .ht_ctc_greetings */
    .ht_ctc_animation:has(.ht_ctc_greetings) {
        animation-fill-mode: none !important;
    }
}

/* above 900 */
@media only screen and (min-width: 900px) {

    .ctc_g_content::-webkit-scrollbar {
        width: 2.8px;
    }

    .ctc_g_content::-webkit-scrollbar-thumb {
        background: #a5a5a5;
    }
}