(()=>{var t;(t=jQuery)(function(){try{document.querySelector(".single_add_to_cart_button")||document.querySelector(".add_to_cart_button")?(o=document.querySelector(".single_add_to_cart_button"),c=document.querySelector(".button.add_to_cart_button")||document.querySelector(".add_to_cart_button"),r(".ctc_woo_single_cart_layout .s1_btn",o),r(".ctc_woo_shop_cart_layout .s1_btn",c,!0),n(".ctc_woo_shop_cart_layout .s_8",c),n(".ctc_woo_single_cart_layout .s_8",o)):document.querySelector(".ctc_woo_place")&&e()}catch(t){}var o,c;function e(){document.querySelector(".ctc_woo_schedule")||(t(".ctc_woo_place").css({display:t(".ctc_woo_place").attr("data-dt")}),t(".ctc_woo_place").show())}function r(o,c){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2]?document.querySelectorAll(o):[document.querySelector(o)];c&&r.length&&r[0]&&(r.forEach(function(o){var e,r,n;e=c,r=o,n=window.getComputedStyle(e),Array.from(n).forEach(function(t){r.style.setProperty(t,n.getPropertyValue(t),n.getPropertyPriority(t))});var _=t(o).css("color"),a=t(o).css("background-color");t(o).css({display:"inline-flex",width:"fit-content","align-items":"center",color:_,"background-color":a})}),e())}function n(o,c){var r=document.querySelectorAll(o);r.length&&c&&(r.forEach(function(o){t(o).css({"min-height":t(c).css("min-height"),"font-size":t(c).css("font-size"),"font-weight":t(c).css("font-weight"),"letter-spacing":t(c).css("letter-spacing"),"border-radius":t(c).css("border-radius"),width:"fit-content"})}),e())}})})();