<?php
return ['domain'=>NULL,'plural-forms'=>NULL,'language'=>'zh_CN','project-id-version'=>'Advanced Custom Fields','pot-creation-date'=>'2025-07-21T18:41:15+00:00','po-revision-date'=>'2025-07-21T18:29:30+00:00','x-generator'=>'gettext','messages'=>['Update Source'=>'更新来源','By default only admin users can edit this setting.'=>'默认情况下，只有管理员用户可以编辑此设置。','By default only super admin users can edit this setting.'=>'默认情况下，只有超级管理员用户才能编辑此设置。','Close and Add Field'=>'关闭并添加字段','A PHP function name to be called to handle the content of a meta box on your taxonomy. For security, this callback will be executed in a special context without access to any superglobals like $_POST or $_GET.'=>'用于处理分类法中元框内容的 PHP 函数名称。为了安全起见，该回调将在一个特殊的上下文中执行，不能访问任何超级全局变量，如 $_POST 或 $_GET。','A PHP function name to be called when setting up the meta boxes for the edit screen. For security, this callback will be executed in a special context without access to any superglobals like $_POST or $_GET.'=>'当设置编辑屏幕的元框时，将调用一个 PHP 函数名称。为了安全起见，该回调将在一个特殊的上下文中执行，不能访问任何超级全局变量，如 $_POST 或 $_GET。','wordpress.org'=>'wordpress.org','Allow Access to Value in Editor UI'=>'允许访问编辑器用户界面中的值','Learn more.'=>'了解更多信息。','Allow content editors to access and display the field value in the editor UI using Block Bindings or the ACF Shortcode. %s'=>'允许内容编辑器访问和 显示编辑器 UI 中使用块绑定或 ACF 简码的字段值。%s','The requested ACF field type does not support output in Block Bindings or the ACF shortcode.'=>'请求的 ACF 字段类型不支持块绑定或 ACF 简码中的输出。','The requested ACF field is not allowed to be output in bindings or the ACF Shortcode.'=>'所请求的 ACF 字段不允许在绑定或 ACF 简码中输出。','The requested ACF field type does not support output in bindings or the ACF Shortcode.'=>'请求的 ACF 字段类型不支持在绑定或 ACF 简码中输出。','[The ACF shortcode cannot display fields from non-public posts]'=>'[ACF 简码无法显示非公开文章中的字段］','[The ACF shortcode is disabled on this site]'=>'[本网站已禁用 ACF 简码］','Businessman Icon'=>'商人图标','Forums Icon'=>'论坛图标','YouTube Icon'=>'YouTube 图标','Yes (alt) Icon'=>'Yes （alt）图标','Xing Icon'=>'Xing 图标','WordPress (alt) Icon'=>'WordPress（备选）图标','WhatsApp Icon'=>'WhatsApp 图标','Write Blog Icon'=>'写博客图标','Widgets Menus Icon'=>'小工具菜单图标','View Site Icon'=>'查看网站图标','Learn More Icon'=>'了解更多信息图标','Add Page Icon'=>'添加页面图标','Video (alt3) Icon'=>'视频（alt3）图标','Video (alt2) Icon'=>'视频（alt2）图标','Video (alt) Icon'=>'视频（alt）图标','Update (alt) Icon'=>'更新图标','Universal Access (alt) Icon'=>'通用访问（alt）图标','Twitter (alt) Icon'=>'推特（alt）图标','Twitch Icon'=>'Twitch 图标','Tide Icon'=>'潮汐图标','Tickets (alt) Icon'=>'门票图标','Text Page Icon'=>'文本页面图标','Table Row Delete Icon'=>'表格行删除图标','Table Row Before Icon'=>'表格行之前图标','Table Row After Icon'=>'表格行后图标','Table Col Delete Icon'=>'删除表格列图标','Table Col Before Icon'=>'表列图标前','Table Col After Icon'=>'表列之后的图标','Superhero (alt) Icon'=>'超级英雄（alt）图标','Superhero Icon'=>'超级英雄图标','Spotify Icon'=>'Spotify 图标','Shortcode Icon'=>'简码图标','Shield (alt) Icon'=>'盾牌（alt）图标','Share (alt2) Icon'=>'共享（alt2）图标','Share (alt) Icon'=>'共享（alt）图标','Saved Icon'=>'保存图标','RSS Icon'=>'RSS 图标','REST API Icon'=>'REST API 图标','Remove Icon'=>'删除图标','Reddit Icon'=>'Reddit 图标','Privacy Icon'=>'隐私图标','Printer Icon'=>'打印机图标','Podio Icon'=>'跑道图标','Plus (alt2) Icon'=>'加号（alt2）图标','Plus (alt) Icon'=>'加号（alt）图标','Plugins Checked Icon'=>'已检查插件图标','Pinterest Icon'=>'Pinterest 图标','Pets Icon'=>'宠物图标','PDF Icon'=>'PDF 图标','Palm Tree Icon'=>'棕榈树图标','Open Folder Icon'=>'打开文件夹图标','No (alt) Icon'=>'无（alt）图标','Money (alt) Icon'=>'金钱（alt）图标','Menu (alt3) Icon'=>'菜单（alt3）图标','Menu (alt2) Icon'=>'菜单（alt2）图标','Menu (alt) Icon'=>'菜单（alt）图标','Spreadsheet Icon'=>'电子表格图标','Interactive Icon'=>'互动图标','Document Icon'=>'文档图标','Default Icon'=>'默认图标','Location (alt) Icon'=>'位置（alt）图标','LinkedIn Icon'=>'LinkedIn 图标','Instagram Icon'=>'Instagram 图标','Insert Before Icon'=>'插入前图标','Insert After Icon'=>'插入后图标','Insert Icon'=>'插入图标','Info Outline Icon'=>'信息轮廓图标','Images (alt2) Icon'=>'图像（alt2）图标','Images (alt) Icon'=>'图像（alt）图标','Rotate Right Icon'=>'向右旋转图标','Rotate Left Icon'=>'向左旋转图标','Rotate Icon'=>'旋转图标','Flip Vertical Icon'=>'垂直翻转图标','Flip Horizontal Icon'=>'水平翻转图标','Crop Icon'=>'裁剪图标','ID (alt) Icon'=>'ID（alt）图标','HTML Icon'=>'HTML 图标','Hourglass Icon'=>'沙漏图标','Heading Icon'=>'标题图标','Google Icon'=>'谷歌图标','Games Icon'=>'游戏图标','Fullscreen Exit (alt) Icon'=>'退出全屏（alt）图标','Fullscreen (alt) Icon'=>'全屏（alt）图标','Status Icon'=>'状态图标','Image Icon'=>'图像图标','Gallery Icon'=>'图库图标','Chat Icon'=>'聊天图标','Audio Icon'=>'音频图标','Aside Icon'=>'其他图标','Food Icon'=>'食物图标','Exit Icon'=>'退出图标','Excerpt View Icon'=>'摘录视图图标','Embed Video Icon'=>'嵌入视频图标','Embed Post Icon'=>'嵌入文章图标','Embed Photo Icon'=>'嵌入照片图标','Embed Generic Icon'=>'嵌入通用图标','Embed Audio Icon'=>'嵌入音频图标','Email (alt2) Icon'=>'电子邮件（alt2）图标','Ellipsis Icon'=>'省略图标','Unordered List Icon'=>'无序列表图标','RTL Icon'=>'RTL 图标','Ordered List RTL Icon'=>'有序列表 RTL 图标','Ordered List Icon'=>'有序列表图标','LTR Icon'=>'LTR 图标','Custom Character Icon'=>'自定义字符图标','Edit Page Icon'=>'编辑页面图标','Edit Large Icon'=>'编辑大图标','Drumstick Icon'=>'鼓槌图标','Database View Icon'=>'数据库查看图标','Database Remove Icon'=>'删除数据库图标','Database Import Icon'=>'数据库导入图标','Database Export Icon'=>'数据库导出图标','Database Add Icon'=>'数据库添加图标','Database Icon'=>'数据库图标','Cover Image Icon'=>'封面图像图标','Volume On Icon'=>'音量打开图标','Volume Off Icon'=>'关闭音量图标','Skip Forward Icon'=>'向前跳转图标','Skip Back Icon'=>'后跳图标','Repeat Icon'=>'重复图标','Play Icon'=>'播放图标','Pause Icon'=>'暂停图标','Forward Icon'=>'前进图标','Back Icon'=>'后退图标','Columns Icon'=>'列图标','Color Picker Icon'=>'颜色选择器图标','Coffee Icon'=>'咖啡图标','Code Standards Icon'=>'代码标准图标','Cloud Upload Icon'=>'云上传图标','Cloud Saved Icon'=>'云保存图标','Car Icon'=>'汽车图标','Camera (alt) Icon'=>'相机（alt）图标','Calculator Icon'=>'计算器图标','Button Icon'=>'按钮图标','Businessperson Icon'=>'商人图标','Tracking Icon'=>'跟踪图标','Topics Icon'=>'主题图标','Replies Icon'=>'回复图标','PM Icon'=>'PM 图标','Friends Icon'=>'好友图标','Community Icon'=>'社区图标','BuddyPress Icon'=>'BuddyPress 图标','bbPress Icon'=>'bbPress 图标','Activity Icon'=>'活动图标','Book (alt) Icon'=>'图书图标','Block Default Icon'=>'块默认图标','Bell Icon'=>'贝尔图标','Beer Icon'=>'啤酒图标','Bank Icon'=>'银行图标','Arrow Up (alt2) Icon'=>'向上箭头（alt2）图标','Arrow Up (alt) Icon'=>'向上箭头（alt）图标','Arrow Right (alt2) Icon'=>'向右箭头（alt2）图标','Arrow Right (alt) Icon'=>'向右箭头（alt）图标','Arrow Left (alt2) Icon'=>'向左箭头（alt2）图标','Arrow Left (alt) Icon'=>'箭头左（alt）图标','Arrow Down (alt2) Icon'=>'向下箭头（alt2）图标','Arrow Down (alt) Icon'=>'箭头向下（alt）图标','Amazon Icon'=>'亚马逊图标','Align Wide Icon'=>'宽对齐图标','Align Pull Right Icon'=>'右拉对齐图标','Align Pull Left Icon'=>'左拉对齐图标','Align Full Width Icon'=>'全宽对齐图标','Airplane Icon'=>'飞机图标','Site (alt3) Icon'=>'网站（alt3）图标','Site (alt2) Icon'=>'网站（alt2）图标','Site (alt) Icon'=>'网站（alt）图标','Upgrade to ACF PRO to create options pages in just a few clicks'=>'升级至 ACF PRO，只需点击几下即可创建选项页面','Invalid request args.'=>'无效请求参数。','Sorry, you do not have permission to do that.'=>'很抱歉，您没有权限来这样做。','Blocks Using Post Meta'=>'使用文章元数据的块','ACF PRO logo'=>'ACF PRO logo','ACF PRO Logo'=>'ACF PRO Logo','%s requires a valid attachment ID when type is set to media_library.'=>'当类型设置为 media_library 时，%s 需要一个有效的附件 ID。','%s is a required property of acf.'=>'%s 是 acf 的必备属性。','The value of icon to save.'=>'图标的值改为保存。','The type of icon to save.'=>'图标的类型改为保存。','Yes Icon'=>'Yes 图标','WordPress Icon'=>'WordPress 图标','Warning Icon'=>'警告图标','Visibility Icon'=>'可见性图标','Vault Icon'=>'保险库图标','Upload Icon'=>'上传图标','Update Icon'=>'更新图标','Unlock Icon'=>'解锁图标','Universal Access Icon'=>'通用访问图标','Undo Icon'=>'撤销图标','Twitter Icon'=>'推特图标','Trash Icon'=>'回收站图标','Translation Icon'=>'翻译图标','Tickets Icon'=>'门票图标','Thumbs Up Icon'=>'大拇指向上图标','Thumbs Down Icon'=>'大拇指向下图标','Text Icon'=>'文本图标','Testimonial Icon'=>'推荐图标','Tagcloud Icon'=>'标签云图标','Tag Icon'=>'标签图标','Tablet Icon'=>'平板图标','Store Icon'=>'商店图标','Sticky Icon'=>'粘贴图标','Star Half Icon'=>'半星图标','Star Filled Icon'=>'星形填充图标','Star Empty Icon'=>'星空图标','Sos Icon'=>'Sos 图标','Sort Icon'=>'排序图标','Smiley Icon'=>'笑脸图标','Smartphone Icon'=>'智能手机图标','Slides Icon'=>'幻灯片图标','Shield Icon'=>'盾牌图标','Share Icon'=>'共享图标','Search Icon'=>'搜索图标','Screen Options Icon'=>'屏幕选项图标','Schedule Icon'=>'日程图标','Redo Icon'=>'重做图标','Randomize Icon'=>'随机化图标','Products Icon'=>'产品图标','Pressthis Icon'=>'发布图标','Post Status Icon'=>'文章状态图标','Portfolio Icon'=>'组合图标','Plus Icon'=>'加号图标','Playlist Video Icon'=>'播放列表视频图标','Playlist Audio Icon'=>'播放列表音频图标','Phone Icon'=>'电话图标','Performance Icon'=>'表演图标','Paperclip Icon'=>'回形针图标','No Icon'=>'无图标','Networking Icon'=>'联网图标','Nametag Icon'=>'名签图标','Move Icon'=>'移动图标','Money Icon'=>'金钱图标','Minus Icon'=>'减号图标','Migrate Icon'=>'迁移图标','Microphone Icon'=>'麦克风图标','Megaphone Icon'=>'扩音器图标','Marker Icon'=>'标记图标','Lock Icon'=>'锁定图标','Location Icon'=>'位置图标','List View Icon'=>'列表视图图标','Lightbulb Icon'=>'灯泡图标','Left Right Icon'=>'左右图标','Layout Icon'=>'布局图标','Laptop Icon'=>'笔记本电脑图标','Info Icon'=>'信息图标','Index Card Icon'=>'索引卡图标','ID Icon'=>'ID 图标','Hidden Icon'=>'隐藏图标','Heart Icon'=>'心形图标','Hammer Icon'=>'锤子图标','Groups Icon'=>'群组图标','Grid View Icon'=>'网格视图图标','Forms Icon'=>'表格图标','Flag Icon'=>'旗帜图标','Filter Icon'=>'筛选图标','Feedback Icon'=>'反馈图标','Facebook (alt) Icon'=>'Facebook（alt）图标','Facebook Icon'=>'Facebook 图标','External Icon'=>'外部图标','Email (alt) Icon'=>'电子邮件（alt）图标','Email Icon'=>'电子邮件图标','Video Icon'=>'视频图标','Unlink Icon'=>'取消链接图标','Underline Icon'=>'下划线图标','Text Color Icon'=>'文本颜色图标','Table Icon'=>'表格图标','Strikethrough Icon'=>'删除线图标','Spellcheck Icon'=>'拼写检查图标','Remove Formatting Icon'=>'移除格式图标','Quote Icon'=>'引用图标','Paste Word Icon'=>'粘贴文字图标','Paste Text Icon'=>'粘贴文本图标','Paragraph Icon'=>'段落图标','Outdent Icon'=>'缩进图标','Kitchen Sink Icon'=>'厨房水槽图标','Justify Icon'=>'对齐图标','Italic Icon'=>'斜体图标','Insert More Icon'=>'插入更多图标','Indent Icon'=>'缩进图标','Help Icon'=>'帮助图标','Expand Icon'=>'展开图标','Contract Icon'=>'合同图标','Code Icon'=>'代码图标','Break Icon'=>'中断图标','Bold Icon'=>'粗体图标','Edit Icon'=>'编辑图标','Download Icon'=>'下载图标','Dismiss Icon'=>'取消图标','Desktop Icon'=>'桌面图标','Dashboard Icon'=>'仪表板图标','Cloud Icon'=>'云图标','Clock Icon'=>'时钟图标','Clipboard Icon'=>'剪贴板图标','Chart Pie Icon'=>'图表饼图标','Chart Line Icon'=>'图表线条图标','Chart Bar Icon'=>'图表条形图标','Chart Area Icon'=>'图表区域图标','Category Icon'=>'分类图标','Cart Icon'=>'购物车图标','Carrot Icon'=>'胡萝卜图标','Camera Icon'=>'相机图标','Calendar (alt) Icon'=>'日历（alt）图标','Calendar Icon'=>'日历图标','Businesswoman Icon'=>'商务人士图标','Building Icon'=>'建筑图标','Book Icon'=>'图书图标','Backup Icon'=>'备份图标','Awards Icon'=>'奖项图标','Art Icon'=>'艺术图标','Arrow Up Icon'=>'向上箭头图标','Arrow Right Icon'=>'向右箭头图标','Arrow Left Icon'=>'箭头左图标','Arrow Down Icon'=>'向下箭头图标','Archive Icon'=>'存档图标','Analytics Icon'=>'分析图标','Align Right Icon'=>'右对齐图标','Align None Icon'=>'无对齐图标','Align Left Icon'=>'向左对齐图标','Align Center Icon'=>'居中对齐图标','Album Icon'=>'相册图标','Users Icon'=>'用户图标','Tools Icon'=>'工具图标','Site Icon'=>'网站图标','Settings Icon'=>'设置图标','Post Icon'=>'文章图标','Plugins Icon'=>'插件图标','Page Icon'=>'页面图标','Network Icon'=>'网络图标','Multisite Icon'=>'多站点图标','Media Icon'=>'媒体图标','Links Icon'=>'链接图标','Home Icon'=>'主页图标','Customizer Icon'=>'自定义图标','Comments Icon'=>'评论图标','Collapse Icon'=>'折叠图标','Appearance Icon'=>'外观图标','Generic Icon'=>'通用图标','Icon picker requires a value.'=>'图标拾取器需要一个值。','Icon picker requires an icon type.'=>'图标拾取器需要一个图标类型。','The available icons matching your search query have been updated in the icon picker below.'=>'符合您搜索条件的可用图标已在下面的图标拾取器中更新。','No results found for that search term'=>'未找到该搜索词的结果','Array'=>'数组','String'=>'字符串','Specify the return format for the icon. %s'=>'指定图标的返回格式。%s','Select where content editors can choose the icon from.'=>'选择内容编辑器可以从哪里选择图标。','The URL to the icon you\'d like to use, or svg as Data URI'=>'您想使用的图标的 URL 或 svg 作为数据 URI','Browse Media Library'=>'浏览媒体库','The currently selected image preview'=>'当前选择图片预览','Click to change the icon in the Media Library'=>'单击以更改媒体库中的图标','Search icons...'=>'搜索图标...','Media Library'=>'媒体库','Dashicons'=>'大水印','An interactive UI for selecting an icon. Select from Dashicons, the media library, or a standalone URL input.'=>'选择一个图标的交互式用户界面。从 Dash 图标、媒体库或独立的 URL 输入中进行选择。','Icon Picker'=>'图标选择器','JSON Load Paths'=>'JSON 加载路径','JSON Save Paths'=>'JSON 保存路径','Registered ACF Forms'=>'已注册的 ACF 表单','Shortcode Enabled'=>'已启用简码','Field Settings Tabs Enabled'=>'已启用字段设置选项卡','Field Type Modal Enabled'=>'已启用字段类型模式','Admin UI Enabled'=>'已启用管理用户界面','Block Preloading Enabled'=>'启用块预载','Blocks Per ACF Block Version'=>'每个 ACF 块版本的块','Blocks Per API Version'=>'每个 API 版本的区块','Registered ACF Blocks'=>'已注册的 ACF 块','Light'=>'轻型','Standard'=>'标准','REST API Format'=>'REST API 格式','Registered Options Pages (PHP)'=>'注册选项页（PHP）','Registered Options Pages (JSON)'=>'注册选项页面（JSON）','Registered Options Pages (UI)'=>'注册选项页面（用户界面）','Options Pages UI Enabled'=>'已启用用户界面的选项页','Registered Taxonomies (JSON)'=>'注册分类法（JSON）','Registered Taxonomies (UI)'=>'注册分类法（用户界面）','Registered Post Types (JSON)'=>'已注册文章类型（JSON）','Registered Post Types (UI)'=>'已注册文章类型（UI）','Post Types and Taxonomies Enabled'=>'已启用的文章类型和分类法','Number of Third Party Fields by Field Type'=>'按字段类型划分的第三方字段数量','Number of Fields by Field Type'=>'按字段类型划分的字段数量','Field Groups Enabled for GraphQL'=>'为 GraphQL 启用的字段组','Field Groups Enabled for REST API'=>'为 REST API 启用的字段组','Registered Field Groups (JSON)'=>'注册字段组（JSON）','Registered Field Groups (PHP)'=>'注册字段组（PHP）','Registered Field Groups (UI)'=>'已注册字段组（UI）','Active Plugins'=>'活动插件','Parent Theme'=>'父主题','Active Theme'=>'活动主题','Is Multisite'=>'是否多站点','MySQL Version'=>'MySQL 版本','WordPress Version'=>'WordPress 版本','Subscription Expiry Date'=>'订阅到期日期','License Status'=>'许可证状态','License Type'=>'许可证类型','Licensed URL'=>'授权 URL','License Activated'=>'许可证已激活','Free'=>'免费','Plugin Type'=>'插件类型','Plugin Version'=>'插件版本','This section contains debug information about your ACF configuration which can be useful to provide to support.'=>'本节包含有关 ACF 配置的调试信息，这些信息可以提供给技术支持。','An ACF Block on this page requires attention before you can save.'=>'在您保存之前，需要注意此页面上的一个 ACF 字段。','This data is logged as we detect values that have been changed during output. %1$sClear log and dismiss%2$s after escaping the values in your code. The notice will reappear if we detect changed values again.'=>'当我们检测到输出过程中已更改的值时，会记录此数据。%1$s 清除日志并在代码中转义值后关闭 %2$s。如果我们再次检测到已更改的值，通知将重新出现。','Dismiss permanently'=>'永久关闭','Instructions for content editors. Shown when submitting data.'=>'内容编辑器的说明。提交数据时显示。','Has no term selected'=>'未选择任何术语','Has any term selected'=>'是否选择了任何术语','Terms do not contain'=>'术语不包含','Terms contain'=>'术语包含','Term is not equal to'=>'术语不等于','Term is equal to'=>'术语等于','Has no user selected'=>'没有用户选择','Has any user selected'=>'有任何用户选择','Users do not contain'=>'用户不包含','Users contain'=>'用户包含','User is not equal to'=>'用户不等于','User is equal to'=>'用户等于','Has no page selected'=>'未选择页面','Has any page selected'=>'有任何页面选择','Pages do not contain'=>'页面不包含','Pages contain'=>'页面包含','Page is not equal to'=>'页面不等于','Page is equal to'=>'页面等于','Has no relationship selected'=>'没有关系选择','Has any relationship selected'=>'有任何关系选择','Has no post selected'=>'没有选择文章','Has any post selected'=>'有任何文章选择','Posts do not contain'=>'文章不包含','Posts contain'=>'文章包含','Post is not equal to'=>'文章不等于','Post is equal to'=>'文章等于','Relationships do not contain'=>'关系不包含','Relationships contain'=>'关系包含','Relationship is not equal to'=>'关系不等于','Relationship is equal to'=>'关系等于','The core ACF block binding source name for fields on the current pageACF Fields'=>'ACF 字段','ACF PRO Feature'=>'ACF PRO 功能','Renew PRO to Unlock'=>'续订 PRO 即可解锁','Renew PRO License'=>'更新 PRO 许可证','PRO fields cannot be edited without an active license.'=>'如果没有有效许可证，则无法编辑 PRO 字段。','Please activate your ACF PRO license to edit field groups assigned to an ACF Block.'=>'请激活您的 ACF PRO 许可证以编辑分配给 ACF 块的字段组。','Please activate your ACF PRO license to edit this options page.'=>'请激活您的 ACF PRO 许可证才能编辑此选项页面。','Returning escaped HTML values is only possible when format_value is also true. The field values have not been returned for security.'=>'只有当 format_value 也为 true 时，才能返回转义 HTML 值。为安全起见，字段值不会返回。','Returning an escaped HTML value is only possible when format_value is also true. The field value has not been returned for security.'=>'只有当 format_value 也为 true 时，才能返回转义 HTML 值。为安全起见，未返回字段值。','%1$s ACF now automatically escapes unsafe HTML when rendered by <code>the_field</code> or the ACF shortcode. We\'ve detected the output of some of your fields has been modified by this change, but this may not be a breaking change. %2$s.'=>'当通过 <code>the_field</code> 或 ACF 简码进行渲染时，%1$s ACF 现在会自动转义不安全的 HTML。我们检测到您的某些字段的输出已被此更改修改，但这可能不是一个破坏性的更改。%2$s.','Please contact your site administrator or developer for more details.'=>'请联系您的网站管理员或开发者了解详情。','Learn&nbsp;more'=>'了解更多信息','Hide&nbsp;details'=>'隐藏详细信息','Show&nbsp;details'=>'显示详细信息','%1$s (%2$s) - rendered via %3$s'=>'%1$s (%2$s) - 渲染通过 %3$s','Renew ACF PRO License'=>'续订 ACF PRO 许可证','Renew License'=>'更新许可证','Manage License'=>'管理许可证','\'High\' position not supported in the Block Editor'=>'区块编辑器不支持「高」位置','Upgrade to ACF PRO'=>'升级到 ACF PRO','ACF <a href="%s" target="_blank">options pages</a> are custom admin pages for managing global settings via fields. You can create multiple pages and sub-pages.'=>'ACF<a href="%s" target="_blank">选项页</a>是通过字段管理全局设置的自定义管理页。您可以创建多个页面和子页面。','Add Options Page'=>'添加选项页面','In the editor used as the placeholder of the title.'=>'在编辑器中用作标题的占位符。','Title Placeholder'=>'标题占位符','4 Months Free'=>'4个月免费','(Duplicated from %s)'=>'(与 %s 重复)','Select Options Pages'=>'选择选项页面','Duplicate taxonomy'=>'重复分类法','Create taxonomy'=>'创建分类法','Duplicate post type'=>'复制文章类型','Create post type'=>'创建文章类型','Link field groups'=>'链接字段组','Add fields'=>'添加字段','This Field'=>'此字段','ACF PRO'=>'ACF PRO','Feedback'=>'反馈','Support'=>'支持','is developed and maintained by'=>'开发和维护者','Add this %s to the location rules of the selected field groups.'=>'将此 %s 添加到选择字段组的位置规则中。','Enabling the bidirectional setting allows you to update a value in the target fields for each value selected for this field, adding or removing the Post ID, Taxonomy ID or User ID of the item being updated. For more information, please read the <a href="%s" target="_blank">documentation</a>.'=>'启用双向设置允许您更新为此字段选择的每个值的目标字段中的值，添加或删除正在更新的项目的文章 ID、分类法 ID 或用户 ID。有关更多信息，请阅读<a href="%s" target="_blank">文档</a>。','Select field(s) to store the reference back to the item being updated. You may select this field. Target fields must be compatible with where this field is being displayed. For example, if this field is displayed on a Taxonomy, your target field should be of type Taxonomy'=>'选择字段以将引用存储回正在更新的项目。您可以选择该字段。目标字段必须与该字段的显示位置兼容。例如，如果此字段显示在分类法上，则您的目标字段应为分类法类型','Target Field'=>'目标字段','Update a field on the selected values, referencing back to this ID'=>'在选择值上更新一个字段，并引用回该 ID','Bidirectional'=>'双向','%s Field'=>'%s 字段','Select Multiple'=>'选择多个','WP Engine logo'=>'WP Engine logo','Lower case letters, underscores and dashes only, Max 32 characters.'=>'小写字母、下划线和破折号，最多 32 字符。','The capability name for assigning terms of this taxonomy.'=>'该分类法的指定术语的权限名称。','Assign Terms Capability'=>'指定术语功能','The capability name for deleting terms of this taxonomy.'=>'用于删除本分类法条款的权限名称。','Delete Terms Capability'=>'删除术语功能','The capability name for editing terms of this taxonomy.'=>'用于编辑本分类法条款的权限名称。','Edit Terms Capability'=>'编辑条款功能','The capability name for managing terms of this taxonomy.'=>'管理此分类法条款的权限名称。','Manage Terms Capability'=>'管理术语功能','Sets whether posts should be excluded from search results and taxonomy archive pages.'=>'设置是否应将文章从搜索结果和分类法归档页面中排除。','More Tools from WP Engine'=>'WP Engine 的更多工具','Built for those that build with WordPress, by the team at %s'=>'由 %s 团队专为使用 WordPress 构建的用户而构建','View Pricing & Upgrade'=>'查看定价和升级','Learn More'=>'了解更多','Speed up your workflow and develop better websites with features like ACF Blocks and Options Pages, and sophisticated field types like Repeater, Flexible Content, Clone, and Gallery.'=>'利用 ACF 块和选项页面等功能以及循环、弹性内容、克隆和图库等复杂的字段类型，加快您的工作流程并开发更好的网站。','Unlock Advanced Features and Build Even More with ACF PRO'=>'使用 ACF PRO 解锁高级功能并构建更多功能','%s fields'=>'%s 字段','No terms'=>'无术语','No post types'=>'无文章类型','No posts'=>'无文章','No taxonomies'=>'无分类法','No field groups'=>'无字段组','No fields'=>'无字段','No description'=>'无描述','Any post status'=>'任何文章状态','This taxonomy key is already in use by another taxonomy registered outside of ACF and cannot be used.'=>'此分类法关键字已被 ACF 以外的另一个分类法注册使用，因此无法使用。','This taxonomy key is already in use by another taxonomy in ACF and cannot be used.'=>'此分类法关键字已被 ACF 中的另一个分类法使用，因此无法使用。','The taxonomy key must only contain lower case alphanumeric characters, underscores or dashes.'=>'分类法关键字只能包含小写字母数字字符、下划线或破折号。','The taxonomy key must be under 32 characters.'=>'分类法关键字必须小于 32 字符。','No Taxonomies found in Trash'=>'回收站中未发现分类法','No Taxonomies found'=>'未找到分类法','Search Taxonomies'=>'搜索分类法','View Taxonomy'=>'查看分类法','New Taxonomy'=>'新分类法','Edit Taxonomy'=>'编辑分类法','Add New Taxonomy'=>'新增分类法','No Post Types found in Trash'=>'回收站中未发现文章类型','No Post Types found'=>'未找到文章类型','Search Post Types'=>'搜索文章类型','View Post Type'=>'查看文章类型','New Post Type'=>'新建文章类型','Edit Post Type'=>'编辑文章类型','Add New Post Type'=>'添加新文章类型','This post type key is already in use by another post type registered outside of ACF and cannot be used.'=>'此文章类型关键字已被 ACF 以外的另一个文章类型关键字使用，因此无法使用。','This post type key is already in use by another post type in ACF and cannot be used.'=>'此文章类型关键字已被 ACF 中的另一个文章类型使用，因此无法使用。','This field must not be a WordPress <a href="%s" target="_blank">reserved term</a>.'=>'这个字段不能是 WordPress <a href="%s" target="_blank">保留字</a>。','The post type key must only contain lower case alphanumeric characters, underscores or dashes.'=>'文章类型关键字只能包含小写字母数字字符、下划线或破折号。','The post type key must be under 20 characters.'=>'文章类型关键字必须小于 20 字符。','We do not recommend using this field in ACF Blocks.'=>'我们不建议在 ACF 块中使用此字段。','Displays the WordPress WYSIWYG editor as seen in Posts and Pages allowing for a rich text-editing experience that also allows for multimedia content.'=>'显示 WordPress WYSIWYG 编辑器，如在「文章」和「页面」中看到的那样，允许丰富的文本编辑体验，也允许多媒体内容。','WYSIWYG Editor'=>'所见即所得编辑器','Allows the selection of one or more users which can be used to create relationships between data objects.'=>'允许选择一个或多个用户，用于创建数据对象之间的关系。','A text input specifically designed for storing web addresses.'=>'专门用于存储网址的文本输入。','URL'=>'URL','A toggle that allows you to pick a value of 1 or 0 (on or off, true or false, etc). Can be presented as a stylized switch or checkbox.'=>'切换按钮，允许您选择 1 或 0 的值（开或关、真或假等）。可以以风格化开关或复选框的形式呈现。','An interactive UI for picking a time. The time format can be customized using the field settings.'=>'用于选择时间的交互式用户界面。时间格式可通过字段设置进行自定义。','A basic textarea input for storing paragraphs of text.'=>'用于存储文本段落的基本文本区输入。','A basic text input, useful for storing single string values.'=>'基本文本输入，用于存储单字符串值。','Allows the selection of one or more taxonomy terms based on the criteria and options specified in the fields settings.'=>'允许根据字段设置中指定的标准和选项选择一个或多个分类法术语。','Allows you to group fields into tabbed sections in the edit screen. Useful for keeping fields organized and structured.'=>'允许您在编辑屏幕中将字段分组为标签式部分。有助于保持字段的条理性和结构化。','A dropdown list with a selection of choices that you specify.'=>'下拉列表包含您指定的选项。','A dual-column interface to select one or more posts, pages, or custom post type items to create a relationship with the item that you\'re currently editing. Includes options to search and filter.'=>'选择一个或多个文章、页面或自定义文章类型项目的双栏接口，以创建与当前编辑的项目之间的关系。包括搜索和筛选选项。','An input for selecting a numerical value within a specified range using a range slider element.'=>'使用范围滑块元素在指定范围内选择数值的输入。','A group of radio button inputs that allows the user to make a single selection from values that you specify.'=>'一组单选按钮输入，允许用户从您指定的值中进行单选。','An interactive and customizable UI for picking one or many posts, pages or post type items with the option to search. '=>'一种交互式且可定制的用户界面，用于选择一个或多个文章、页面或帖子类型项目，并提供搜索选项。 ','An input for providing a password using a masked field.'=>'使用屏蔽字段提供密码的输入。','Filter by Post Status'=>'按文章状态筛选','An interactive dropdown to select one or more posts, pages, custom post type items or archive URLs, with the option to search.'=>'选择一个或多个文章、页面、自定义文章类型项目或归档 URL 的交互式下拉菜单，可使用选项进行搜索。','An interactive component for embedding videos, images, tweets, audio and other content by making use of the native WordPress oEmbed functionality.'=>'通过使用 WordPress 原生的 oEmbed 功能，嵌入视频、图片、推文、音频和其他内容的互动组件。','An input limited to numerical values.'=>'仅限于数值的输入。','Used to display a message to editors alongside other fields. Useful for providing additional context or instructions around your fields.'=>'用于显示与其他字段并列的信息。可为您的字段提供额外的上下文或说明。','Allows you to specify a link and its properties such as title and target using the WordPress native link picker.'=>'允许您使用 WordPress 本地链接拾取器指定链接及其属性，如标题和目标。','Uses the native WordPress media picker to upload, or choose images.'=>'使用 WordPress 本地媒体拾取器上传，或选择图片。','Provides a way to structure fields into groups to better organize the data and the edit screen.'=>'提供了一种将字段结构化成组的方法，以便更好地组织数据和编辑屏幕。','An interactive UI for selecting a location using Google Maps. Requires a Google Maps API key and additional configuration to display correctly.'=>'使用谷歌地图选择位置的交互式用户界面。需要 Google Maps API 密钥和额外配置才能正确显示。','Uses the native WordPress media picker to upload, or choose files.'=>'使用 WordPress 本地媒体拾取器上传，或选择文件。','A text input specifically designed for storing email addresses.'=>'专为存储地址而设计的文本输入。','An interactive UI for picking a date and time. The date return format can be customized using the field settings.'=>'用于选择日期和时间的交互式用户界面。日期返回格式可通过字段设置进行自定义。','An interactive UI for picking a date. The date return format can be customized using the field settings.'=>'用于选择日期的交互式用户界面。日期返回格式可通过字段设置进行自定义。','An interactive UI for selecting a color, or specifying a Hex value.'=>'用于选择颜色或指定十六进制值的交互式用户界面。','A group of checkbox inputs that allow the user to select one, or multiple values that you specify.'=>'一组复选框输入，允许用户选择一个或多个您指定的值。','A group of buttons with values that you specify, users can choose one option from the values provided.'=>'一组按钮，带有您指定的值，用户可以从提供的值中选择一个选项。','Allows you to group and organize custom fields into collapsable panels that are shown while editing content. Useful for keeping large datasets tidy.'=>'允许您将自定义字段分组并整理为可折叠的面板，在编辑内容时显示。它有助于保持大型数据集的整洁。','This provides a solution for repeating content such as slides, team members, and call-to-action tiles, by acting as a parent to a set of subfields which can be repeated again and again.'=>'这提供了一个用于重复内容（如幻灯片、团队成员和「行动号召」磁贴）的方法，即作为一组子字段的父字段，这些子字段可以重复显示。','This provides an interactive interface for managing a collection of attachments. Most settings are similar to the Image field type. Additional settings allow you to specify where new attachments are added in the gallery and the minimum/maximum number of attachments allowed.'=>'这为管理附件集合提供了一个交互式接口。大多数设置与图像字段类型类似。其他设置允许您指定在图库中添加新附件的位置以及允许的附件最小 / 最大数量。','This provides a simple, structured, layout-based editor. The Flexible Content field allows you to define, create and manage content with total control by using layouts and subfields to design the available blocks.'=>'这提供了一个简单、结构化、基于布局的编辑器。灵活的内容字段允许您通过使用布局和子字段来设计可用区块，以完全可控的方式定义、创建和管理内容。','This allows you to select and display existing fields. It does not duplicate any fields in the database, but loads and displays the selected fields at run-time. The Clone field can either replace itself with the selected fields or display the selected fields as a group of subfields.'=>'这样，您就可以选择和显示现有的字段。它不会复制数据库中的任何字段，但会在运行时加载并显示已选择的字段。克隆字段既可以用选择的字段替换自己，也可以将选择的字段显示为一组子字段。','nounClone'=>'克隆','PRO'=>'专业版','Advanced'=>'高级','JSON (newer)'=>'JSON （较新）','Original'=>'原文','Invalid post ID.'=>'无效文章 ID。','Invalid post type selected for review.'=>'选择进行审核的文章类型无效。','More'=>'更多信息','Tutorial'=>'教程','Select Field'=>'选择领域','Try a different search term or browse %s'=>'尝试其他搜索词或浏览 %s','Popular fields'=>'热门字段','No search results for \'%s\''=>'没有「%s」的搜索结果','Search fields...'=>'搜索字段...','Select Field Type'=>'选择字段类型','Popular'=>'热门','Add Taxonomy'=>'添加分类法','Create custom taxonomies to classify post type content'=>'创建自定义分类法来分类文章类型内容','Add Your First Taxonomy'=>'添加第一个分类法','Hierarchical taxonomies can have descendants (like categories).'=>'分层分类法可以有后代（如分类）。','Makes a taxonomy visible on the frontend and in the admin dashboard.'=>'使分类法在前台和管理后台可见。','One or many post types that can be classified with this taxonomy.'=>'一个或多个可以用该分类法分类的文章类型。','genre'=>'类型','Genre'=>'类型','Genres'=>'类型','Optional custom controller to use instead of `WP_REST_Terms_Controller `.'=>'可选的自定义控制器，用于替代「WP_REST_Terms_Controller」。','Expose this post type in the REST API.'=>'在 REST API 中公开该文章类型。','Customize the query variable name'=>'自定义查询变量名称','Terms can be accessed using the non-pretty permalink, e.g., {query_var}={term_slug}.'=>'可使用非漂亮的固定链接访问术语，例如，{query_var}={term_slug}。','Parent-child terms in URLs for hierarchical taxonomies.'=>'分层分类法 URL 中的父子术语。','Customize the slug used in the URL'=>'自定义 URL 中使用的别名','Permalinks for this taxonomy are disabled.'=>'禁用此分类法的永久链接。','Rewrite the URL using the taxonomy key as the slug. Your permalink structure will be'=>'使用分类法关键字重写 URL。您的永久链接结构将是','Taxonomy Key'=>'分类法关键字','Select the type of permalink to use for this taxonomy.'=>'选择要用于此分类法的固定链接类型。','Display a column for the taxonomy on post type listing screens.'=>'在文章类型的列表屏幕上显示分类法栏。','Show Admin Column'=>'显示管理栏','Show the taxonomy in the quick/bulk edit panel.'=>'在快速 / 批量编辑面板中显示分类法。','Quick Edit'=>'快速编辑','List the taxonomy in the Tag Cloud Widget controls.'=>'在标签云小工具控件中列出分类法。','Tag Cloud'=>'标签云','A PHP function name to be called for sanitizing taxonomy data saved from a meta box.'=>'用于对从元框中保存的分类法数据进行消毒的 PHP 函数名称。','Meta Box Sanitization Callback'=>'元框消毒回调','Register Meta Box Callback'=>'注册元框回调','No Meta Box'=>'无元框','Custom Meta Box'=>'自定义元框','Controls the meta box on the content editor screen. By default, the Categories meta box is shown for hierarchical taxonomies, and the Tags meta box is shown for non-hierarchical taxonomies.'=>'控制内容编辑器屏幕上的元框。默认情况下，分层分类法显示「分类」元框，非分层分类法显示「标签」元框。','Meta Box'=>'元框','Categories Meta Box'=>'分类元框','Tags Meta Box'=>'标签元框','A link to a tag'=>'标签的链接','Describes a navigation link block variation used in the block editor.'=>'描述块编辑器中使用的导航链接块样式变化。','A link to a %s'=>'链接到 %s','Tag Link'=>'标签链接','Assigns a title for navigation link block variation used in the block editor.'=>'为块编辑器中使用的导航块分配一个标题链接样式变化。','← Go to tags'=>'← 转到标签','Assigns the text used to link back to the main index after updating a term.'=>'指定更新术语后链接回主索引的文本。','Back To Items'=>'返回项目','← Go to %s'=>'← 转到 %s','Tags list'=>'标签列表','Assigns text to the table hidden heading.'=>'为表格隐藏标题指定文本。','Tags list navigation'=>'标签列表导航','Assigns text to the table pagination hidden heading.'=>'为表格分页隐藏标题指定文本。','Filter by category'=>'按分类筛选','Assigns text to the filter button in the posts lists table.'=>'将文本分配给文章列表中的筛选按钮。','Filter By Item'=>'按项目筛选','Filter by %s'=>'按 %s 筛选','The description is not prominent by default; however, some themes may show it.'=>'默认情况下，描述并不突出；但某些主题可能会显示描述。','Describes the Description field on the Edit Tags screen.'=>'描述「编辑标签」屏幕上的「描述字段」。','Description Field Description'=>'描述字段描述','Assign a parent term to create a hierarchy. The term Jazz, for example, would be the parent of Bebop and Big Band'=>'为创建一个层次结构指定一个父项。例如，「爵士乐」（Jazz）一词是「贝波普」（Bebop）和「大乐队」（Big Band）的父词。','Describes the Parent field on the Edit Tags screen.'=>'描述「编辑标签」屏幕上的父字段。','Parent Field Description'=>'父字段说明','The "slug" is the URL-friendly version of the name. It is usually all lower case and contains only letters, numbers, and hyphens.'=>'「别名」是名称的 URL 友好版本。通常都是小写，只包含字母、数字和连字符。','Describes the Slug field on the Edit Tags screen.'=>'描述「编辑标签」屏幕上的别名字段。','Slug Field Description'=>'别名字段描述','The name is how it appears on your site'=>'名称在网站上的显示方式','Describes the Name field on the Edit Tags screen.'=>'描述编辑标签屏幕上的名称字段。','Name Field Description'=>'名称字段说明','No tags'=>'无标签','Assigns the text displayed in the posts and media list tables when no tags or categories are available.'=>'当没有标签或分类时，在文章和媒体列表表中指定文本显示。','No Terms'=>'无术语','No %s'=>'无 %s','No tags found'=>'未找到标记','Assigns the text displayed when clicking the \'choose from most used\' text in the taxonomy meta box when no tags are available, and assigns the text used in the terms list table when there are no items for a taxonomy.'=>'当没有标签时，在分类法元框中指定文本显示，当没有项目分类法时，指定术语列表表中使用的文本。','Not Found'=>'未找到','Assigns text to the Title field of the Most Used tab.'=>'为「最常用标签」的「标题字段」指定文本。','Most Used'=>'最常用','Choose from the most used tags'=>'从最常用的标签中选择','Assigns the \'choose from most used\' text used in the meta box when JavaScript is disabled. Only used on non-hierarchical taxonomies.'=>'当 JavaScript 禁用时，指定元框中使用的「从最常用标签中选择」文本。仅用于非层次分类法。','Choose From Most Used'=>'从最常用的中选择','Choose from the most used %s'=>'从最常用的 %s 中选择','Add or remove tags'=>'添加或移除标签','Assigns the add or remove items text used in the meta box when JavaScript is disabled. Only used on non-hierarchical taxonomies'=>'当 JavaScript 禁用时，指定元框中使用的添加或移除项目文本。仅用于非层次分类法','Add Or Remove Items'=>'添加或移除项目','Add or remove %s'=>'添加或移除 %s','Separate tags with commas'=>'用逗号分隔标签','Assigns the separate item with commas text used in the taxonomy meta box. Only used on non-hierarchical taxonomies.'=>'指定分类法元框中使用逗号分隔项目文本。仅用于非层次分类法。','Separate Items With Commas'=>'用逗号分隔项目','Separate %s with commas'=>'用逗号分隔 %s','Popular Tags'=>'流行标签','Assigns popular items text. Only used for non-hierarchical taxonomies.'=>'指定流行项目文本。仅用于非层次分类法。','Popular Items'=>'热门项目','Popular %s'=>'热门 %s','Search Tags'=>'搜索标签','Assigns search items text.'=>'指定搜索项目文本。','Parent Category:'=>'父分类：','Assigns parent item text, but with a colon (:) added to the end.'=>'指定父项目文本，但在末尾加上冒号 (:) 添加。','Parent Item With Colon'=>'带冒号的父项目','Parent Category'=>'父分类','Assigns parent item text. Only used on hierarchical taxonomies.'=>'指定父项目文本。仅用于层次分类法。','Parent Item'=>'父项','Parent %s'=>'父级 %s','New Tag Name'=>'新标签名称','Assigns the new item name text.'=>'指定新项目名称文本。','New Item Name'=>'新项目名称','New %s Name'=>'新 %s 名称','Add New Tag'=>'添加新标签','Assigns the add new item text.'=>'分配添加新项目文本。','Update Tag'=>'更新标签','Assigns the update item text.'=>'指定更新项目文本。','Update Item'=>'更新项目','Update %s'=>'更新 %s','View Tag'=>'查看标签','In the admin bar to view term during editing.'=>'在管理栏中，用于在编辑时查看术语。','Edit Tag'=>'编辑标签','At the top of the editor screen when editing a term.'=>'编辑术语时位于编辑器屏幕顶部。','All Tags'=>'所有标签','Assigns the all items text.'=>'指定所有项目文本。','Assigns the menu name text.'=>'指定菜单名称文本。','Menu Label'=>'菜单标签','Active taxonomies are enabled and registered with WordPress.'=>'使用 WordPress 启用和注册的活动分类法。','A descriptive summary of the taxonomy.'=>'分类法的描述性摘要。','A descriptive summary of the term.'=>'术语的描述性摘要。','Term Description'=>'术语描述','Single word, no spaces. Underscores and dashes allowed.'=>'单词，无空格。允许使用下划线和破折号。','Term Slug'=>'术语标题','The name of the default term.'=>'默认术语的名称。','Term Name'=>'术语名称','Create a term for the taxonomy that cannot be deleted. It will not be selected for posts by default.'=>'为分类法创建一个不可删除的术语。默认情况下，文章不会选择该术语。','Default Term'=>'默认术语','Whether terms in this taxonomy should be sorted in the order they are provided to `wp_set_object_terms()`.'=>'该分类法中的术语是否应按照提供给 `wp_set_object_terms()` 的顺序排序。','Sort Terms'=>'术语排序','Add Post Type'=>'添加文章类型','Expand the functionality of WordPress beyond standard posts and pages with custom post types.'=>'使用自定义文章类型，将 WordPress 的功能扩展到标准文章和页面之外。','Add Your First Post Type'=>'添加第一个文章类型','I know what I\'m doing, show me all the options.'=>'我知道我在做什么，请告诉我所有选项。','Advanced Configuration'=>'高级配置','Hierarchical post types can have descendants (like pages).'=>'分层文章类型可以有后代（如页面）。','Hierarchical'=>'分层','Visible on the frontend and in the admin dashboard.'=>'可在前台和管理后台看到。','Public'=>'公开','movie'=>'电影','Lower case letters, underscores and dashes only, Max 20 characters.'=>'仅限小写字母、下划线和破折号，最多 20 字符。','Movie'=>'电影','Singular Label'=>'单数标签','Movies'=>'电影','Plural Label'=>'复数标签','Optional custom controller to use instead of `WP_REST_Posts_Controller`.'=>'可选的自定义控制器，用于替代 `WP_REST_Posts_Controller`。','Controller Class'=>'控制器类','The namespace part of the REST API URL.'=>'REST API URL 的命名空间部分。','Namespace Route'=>'命名空间路由','The base URL for the post type REST API URLs.'=>'文章类型 REST API URL 的基础 URL。','Base URL'=>'基本 URL','Exposes this post type in the REST API. Required to use the block editor.'=>'在 REST API 中公开该文章类型。使用区块编辑器时必须使用。','Show In REST API'=>'在 REST API 中显示','Customize the query variable name.'=>'自定义查询变量名称。','Query Variable'=>'查询变量','No Query Variable Support'=>'不支持查询变量','Custom Query Variable'=>'自定义查询变量','Items can be accessed using the non-pretty permalink, eg. {post_type}={post_slug}.'=>'可使用非漂亮的固定链接访问项目，例如：{post_type}={post_slug}。','Query Variable Support'=>'支持查询变量','URLs for an item and items can be accessed with a query string.'=>'项目和项目的 URL 可以用查询字符串访问。','Publicly Queryable'=>'可公开查询','Custom slug for the Archive URL.'=>'存档 URL 的自定义别名。','Archive Slug'=>'存档标题','Has an item archive that can be customized with an archive template file in your theme.'=>'项目归档可在您的主题中使用归档模板文件进行自定义。','Archive'=>'归档','Pagination support for the items URLs such as the archives.'=>'支持归档等项目 URL 的分页。','Pagination'=>'分页','RSS feed URL for the post type items.'=>'文章类型项目的 RSS Feed URL。','Feed URL'=>'馈送 URL','Alters the permalink structure to add the `WP_Rewrite::$front` prefix to URLs.'=>'更改添加到`WP_Rewrite::$front`前缀 URL 的永久链接结构。','Front URL Prefix'=>'前缀 URL','Customize the slug used in the URL.'=>'自定义 URL 中使用的别名。','URL Slug'=>'URL 标题','Permalinks for this post type are disabled.'=>'禁用该文章类型的永久链接。','Rewrite the URL using a custom slug defined in the input below. Your permalink structure will be'=>'使用下面输入框中定义的自定义别名重写 URL。您的永久链接结构将是','No Permalink (prevent URL rewriting)'=>'无永久链接（防止 URL 重写）','Custom Permalink'=>'自定义永久链接','Post Type Key'=>'文章类型关键字','Rewrite the URL using the post type key as the slug. Your permalink structure will be'=>'使用文章类型关键字作为别名重写 URL。您的永久链接结构将是','Permalink Rewrite'=>'固定链接重写','Delete items by a user when that user is deleted.'=>'删除用户时，删除该用户的项目。','Delete With User'=>'与用户一起删除','Allow the post type to be exported from \'Tools\' > \'Export\'.'=>'允许从「工具」>「导出」导出文章类型。','Can Export'=>'可以导出','Optionally provide a plural to be used in capabilities.'=>'可选择提供用于权限的复数。','Plural Capability Name'=>'复数能力名称','Choose another post type to base the capabilities for this post type.'=>'选择另一种文章类型，作为该文章类型的权限基础。','Singular Capability Name'=>'单项能力名称','By default the capabilities of the post type will inherit the \'Post\' capability names, eg. edit_post, delete_posts. Enable to use post type specific capabilities, eg. edit_{singular}, delete_{plural}.'=>'默认情况下，文章类型的权限将继承「文章」权限名称，如 edit_post、 delete_post。允许使用文章类型特定的权限，如 edit_{singular}、 delete_{plural}。','Rename Capabilities'=>'重命名功能','Exclude From Search'=>'从搜索中排除','Allow items to be added to menus in the \'Appearance\' > \'Menus\' screen. Must be turned on in \'Screen options\'.'=>'允许项目添加到「外观」>「菜单」屏幕中的菜单。必须在「屏幕选项」中打开。','Appearance Menus Support'=>'外观菜单支持','Appears as an item in the \'New\' menu in the admin bar.'=>'在管理栏的「新建」菜单中显示为项目。','Show In Admin Bar'=>'在管理栏中显示','Custom Meta Box Callback'=>'自定义元框回调','Menu Icon'=>'菜单图标','The position in the sidebar menu in the admin dashboard.'=>'侧边栏菜单中的位置。','Menu Position'=>'菜单位置','By default the post type will get a new top level item in the admin menu. If an existing top level item is supplied here, the post type will be added as a submenu item under it.'=>'默认情况下，文章类型将在管理菜单中获得一个新的顶级项目。如果此处提供了一个现有的顶级项目，那么该文章类型将作为子菜单项目添加到该项目下。','Admin Menu Parent'=>'管理菜单父菜单','Admin editor navigation in the sidebar menu.'=>'侧边栏菜单中的管理员编辑器导航。','Show In Admin Menu'=>'在管理菜单中显示','Items can be edited and managed in the admin dashboard.'=>'项目可在管理菜单中进行管理。','Show In UI'=>'在用户界面中显示','A link to a post.'=>'文章的链接。','Description for a navigation link block variation.'=>'导航链接块的描述样式变化。','Item Link Description'=>'项目链接描述','A link to a %s.'=>'指向 %s 的链接。','Post Link'=>'文章链接','Title for a navigation link block variation.'=>'导航链接块样式变化的标题。','Item Link'=>'项目链接','%s Link'=>'%s 链接','Post updated.'=>'文章已更新。','In the editor notice after an item is updated.'=>'在项目更新后的编辑器通知中。','Item Updated'=>'项目已更新','%s updated.'=>'%s 已更新。','Post scheduled.'=>'计划发布。','In the editor notice after scheduling an item.'=>'在计划发布项目后的编辑器通知中。','Item Scheduled'=>'项目已计划','%s scheduled.'=>'%s 已安排。','Post reverted to draft.'=>'文章已还原为草稿。','In the editor notice after reverting an item to draft.'=>'在将项目还原为草稿后的编辑器通知中。','Item Reverted To Draft'=>'项目已还原为草稿','%s reverted to draft.'=>'%s 已还原为草稿。','Post published privately.'=>'私下发布。','In the editor notice after publishing a private item.'=>'在私人发布项目后的编辑器通知中。','Item Published Privately'=>'私人发布的项目','%s published privately.'=>'%s 私人发布。','Post published.'=>'文章已发布','In the editor notice after publishing an item.'=>'在发布一个项目后的编辑器通知中。','Item Published'=>'项目已发布','%s published.'=>'%s 已发布。','Posts list'=>'文章列表','Used by screen readers for the items list on the post type list screen.'=>'由屏幕阅读器在文章类型列表屏幕上用于项目列表。','Items List'=>'项目列表','%s list'=>'%s 列表','Posts list navigation'=>'文章列表导航','Used by screen readers for the filter list pagination on the post type list screen.'=>'用于屏幕阅读器在文章类型列表屏幕上的筛选器列表分页。','Items List Navigation'=>'项目列表导航','%s list navigation'=>'%s 列表导航','Filter posts by date'=>'按日期筛选文章','Used by screen readers for the filter by date heading on the post type list screen.'=>'屏幕阅读器在「文章类型列表」屏幕上用于按日期筛选标题。','Filter Items By Date'=>'按日期筛选项目','Filter %s by date'=>'按日期筛选 %s','Filter posts list'=>'筛选文章列表','Used by screen readers for the filter links heading on the post type list screen.'=>'用于屏幕阅读器在「文章类型列表」屏幕上的「筛选链接」标题。','Filter Items List'=>'筛选项目列表','Filter %s list'=>'筛选 %s 列表','In the media modal showing all media uploaded to this item.'=>'在媒体模态窗中显示上传到此项目的所有媒体。','Uploaded To This Item'=>'上传到此项目','Uploaded to this %s'=>'上传到此 %s','Insert into post'=>'插入到文章','As the button label when adding media to content.'=>'当按钮标签添加媒体到内容时。','Insert Into Media Button'=>'插入媒体按钮','Insert into %s'=>'插入 %s','Use as featured image'=>'用作特色图片','As the button label for selecting to use an image as the featured image.'=>'作为按钮标签选择将图片作为精选图片使用。','Use Featured Image'=>'使用精选图片','Remove featured image'=>'删除精选图片','As the button label when removing the featured image.'=>'在移除精选图片时使用按钮标签。','Remove Featured Image'=>'移除精选图片','Set featured image'=>'设置精选图片','As the button label when setting the featured image.'=>'作为设置精选图片时的按钮标签。','Set Featured Image'=>'设置精选图片','Featured image'=>'精选图片','In the editor used for the title of the featured image meta box.'=>'在编辑器中用于精选图像元框的标题。','Featured Image Meta Box'=>'精选图片元框','Post Attributes'=>'文章属性','In the editor used for the title of the post attributes meta box.'=>'在用于属性元框标题的编辑器中。','Attributes Meta Box'=>'属性元框','%s Attributes'=>'%s 属性','Post Archives'=>'文章归档','Adds \'Post Type Archive\' items with this label to the list of posts shown when adding items to an existing menu in a CPT with archives enabled. Only appears when editing menus in \'Live Preview\' mode and a custom archive slug has been provided.'=>'将带有此标签的「文章类型存档」项目添加到在启用存档的情况下将项目添加到 CPT 中的现有菜单时显示的文章列表。仅当在「实时预览」模式下编辑菜单并且提供了自定义存档段时才会出现。','Archives Nav Menu'=>'档案导航菜单','%s Archives'=>'%s 归档','No posts found in Trash'=>'回收站中未发现文章','At the top of the post type list screen when there are no posts in the trash.'=>'当回收站中没有文章时，在文章列表屏幕顶部输入文章。','No Items Found in Trash'=>'回收站中未发现项目','No %s found in Trash'=>'回收站中未发现 %s','No posts found'=>'未找到文章','At the top of the post type list screen when there are no posts to display.'=>'当没有文章显示时，在「文章」页面顶部键入「列表」。','No Items Found'=>'未找到项目','No %s found'=>'未找到 %s','Search Posts'=>'搜索文章','At the top of the items screen when searching for an item.'=>'在搜索项目时出现在项目屏幕顶部。','Search Items'=>'搜索项目','Search %s'=>'搜索 %s','Parent Page:'=>'父页面：','For hierarchical types in the post type list screen.'=>'对于文章类型列表屏幕中的分层类型。','Parent Item Prefix'=>'父项目前缀','Parent %s:'=>'父级 %s：','New Post'=>'新文章','New Item'=>'新项目','New %s'=>'新 %s','Add New Post'=>'添加新文章','At the top of the editor screen when adding a new item.'=>'当添加新项目时，显示在编辑器屏幕顶部。','Add New Item'=>'添加新项目','Add New %s'=>'添加新 %s','View Posts'=>'查看文章','Appears in the admin bar in the \'All Posts\' view, provided the post type supports archives and the home page is not an archive of that post type.'=>'出现在「所有文章」视图的管理栏中，前提是该文章类型支持归档，且主页不是该文章类型的归档。','View Items'=>'查看项目','View Post'=>'查看文章','In the admin bar to view item when editing it.'=>'在管理栏中编辑项目时查看项目。','View Item'=>'查看项目','View %s'=>'查看 %s','Edit Post'=>'编辑文章','At the top of the editor screen when editing an item.'=>'编辑项目时在编辑器屏幕顶部。','Edit Item'=>'编辑项目','Edit %s'=>'编辑 %s','All Posts'=>'所有文章','In the post type submenu in the admin dashboard.'=>'在管理后台的文章类型子菜单中。','All Items'=>'所有项目','All %s'=>'所有 %s','Admin menu name for the post type.'=>'文章类型的管理菜单名称。','Menu Name'=>'菜单名称','Regenerate all labels using the Singular and Plural labels'=>'使用单数和复数标签重新生成所有标签','Regenerate'=>'再生','Active post types are enabled and registered with WordPress.'=>'活动文章类型已启用并与 WordPress 一起注册。','A descriptive summary of the post type.'=>'文章类型的描述性摘要。','Add Custom'=>'添加自定义','Enable various features in the content editor.'=>'启用内容编辑器中的各种功能。','Post Formats'=>'文章格式','Editor'=>'编辑器','Trackbacks'=>'Trackback','Select existing taxonomies to classify items of the post type.'=>'选择现有分类法对文章类型的项目进行分类。','Browse Fields'=>'浏览字段','Nothing to import'=>'无须导入','. The Custom Post Type UI plugin can be deactivated.'=>'.自定义文章类型 UI 插件可以停用。','Imported %d item from Custom Post Type UI -'=>'已从自定义文章类型 UI 导入 %d 项 -','Failed to import taxonomies.'=>'导入分类法失败。','Failed to import post types.'=>'导入文章类型失败。','Nothing from Custom Post Type UI plugin selected for import.'=>'没有从自定义文章类型 UI 插件选择导入。','Imported 1 item'=>'已导入 %s 个项目','Importing a Post Type or Taxonomy with the same key as one that already exists will overwrite the settings for the existing Post Type or Taxonomy with those of the import.'=>'导入与已存在的文章类型或分类标准具有相同关键字的文章类型或分类标准时，导入的文章类型或分类标准将覆盖现有文章类型或分类标准的设置。','Import from Custom Post Type UI'=>'从自定义文章类型 UI 导入','The following code can be used to register a local version of the selected items. Storing field groups, post types, or taxonomies locally can provide many benefits such as faster load times, version control & dynamic fields/settings. Simply copy and paste the following code to your theme\'s functions.php file or include it within an external file, then deactivate or delete the items from the ACF admin.'=>'以下代码可用于注册本地版本的选择项目。在本地存储字段组、文章类型或分类法可带来许多好处，如更快的加载时间、版本控制和动态字段 / 设置。只需将以下代码复制并粘贴到主题的 functions.php 文件或包含在外部文件中，然后停用或删除 ACF 管理中的项目即可。','Export - Generate PHP'=>'导出 - 生成 PHP','Export'=>'导出','Select Taxonomies'=>'选择分类法','Select Post Types'=>'选择文章类型','Exported 1 item.'=>'已导出 %s 个项目。','Category'=>'分类','Tag'=>'标记','%s taxonomy created'=>'已创建 %s 分类法','%s taxonomy updated'=>'已更新 %s 分类法','Taxonomy draft updated.'=>'分类法草稿已更新。','Taxonomy scheduled for.'=>'分类法计划更新。','Taxonomy submitted.'=>'分类法已提交。','Taxonomy saved.'=>'分类法已保存。','Taxonomy deleted.'=>'分类法已删除。','Taxonomy updated.'=>'分类法已更新。','This taxonomy could not be registered because its key is in use by another taxonomy registered by another plugin or theme.'=>'此分类法无法注册，因为其关键字正被另一个分类法使用。','Taxonomy synchronized.'=>'%s 个分类法已同步。','Taxonomy duplicated.'=>'%s 个分类法已复制。','Taxonomy deactivated.'=>'%s 个分类法已停用。','Taxonomy activated.'=>'%s 个分类法已激活。','Terms'=>'术语','Post type synchronized.'=>'%s 个文章类型已同步。','Post type duplicated.'=>'%s 个文章类型已复制。','Post type deactivated.'=>'%s 个文章类型已停用。','Post type activated.'=>'%s 个文章类型已激活。','Post Types'=>'文章类型','Advanced Settings'=>'高级设置','Basic Settings'=>'基本设置','This post type could not be registered because its key is in use by another post type registered by another plugin or theme.'=>'此文章类型无法注册，因为其关键字被另一个文章类型注册或主题注册使用。','Pages'=>'页面','Link Existing Field Groups'=>'链接现有字段组','%s post type created'=>'已创建 %s 文章类型','Add fields to %s'=>'添加字段到 %s','%s post type updated'=>'已更新 %s 文章类型','Post type draft updated.'=>'已更新文章类型草稿。','Post type scheduled for.'=>'已计划文章类型。','Post type submitted.'=>'已提交文章类型。','Post type saved.'=>'已保存文章类型。','Post type updated.'=>'已更新文章类型。','Post type deleted.'=>'已删除文章类型。','Type to search...'=>'键入搜索...','PRO Only'=>'仅 PRO','Field groups linked successfully.'=>'字段组成功链接。','Import Post Types and Taxonomies registered with Custom Post Type UI and manage them with ACF. <a href="%s">Get Started</a>.'=>'使用自定义文章类型用户界面导入文章类型和分类法注册，并使用 ACF 管理它们。<a href="%s">开始</a>。','ACF'=>'ACF','taxonomy'=>'分类法','post type'=>'文章类型','Done'=>'完成','Field Group(s)'=>'字段组','Select one or many field groups...'=>'选择一个或多个字段组...','Please select the field groups to link.'=>'请选择要链接的字段组。','Field group linked successfully.'=>'字段组已成功链接。','post statusRegistration Failed'=>'注册失败','This item could not be registered because its key is in use by another item registered by another plugin or theme.'=>'此项目无法注册，因为它的关键字正被另一个项目注册，或被另一个主题注册。','REST API'=>'REST API','Permissions'=>'权限','URLs'=>'URL','Visibility'=>'可见性','Labels'=>'标签','Field Settings Tabs'=>'字段设置选项卡','https://wpengine.com/?utm_source=wordpress.org&utm_medium=referral&utm_campaign=plugin_directory&utm_content=advanced_custom_fields'=>'https://wpengine.com/?utm_source=wordpress.org&utm_medium=referral&utm_campaign=plugin_directory&utm_content=advanced_custom_fields','[ACF shortcode value disabled for preview]'=>'[预览时禁用 ACF 简码值]','Close Modal'=>'关闭模态窗','Field moved to other group'=>'字段移至其他组','Close modal'=>'关闭模态窗','Start a new group of tabs at this tab.'=>'在此标签开始一个新的标签组。','New Tab Group'=>'新标签组','Use a stylized checkbox using select2'=>'使用 select2 风格化复选框','Save Other Choice'=>'保存其他选项','Allow Other Choice'=>'允许其他选择','Add Toggle All'=>'添加全部切换','Save Custom Values'=>'保存自定义值','Allow Custom Values'=>'允许自定义值','Checkbox custom values cannot be empty. Uncheck any empty values.'=>'复选框自定义值不能为空。取消选中任何空值。','Updates'=>'更新','Advanced Custom Fields logo'=>'高级自定义字段 LOGO','Save Changes'=>'保存设置','Field Group Title'=>'字段组标题','Add title'=>'添加标题','New to ACF? Take a look at our <a href="%s" target="_blank">getting started guide</a>.'=>'ACF 新手？请查看我们的<a href="%s" target="_blank">入门指南</a>。','Add Field Group'=>'添加字段组','ACF uses <a href="%s" target="_blank">field groups</a> to group custom fields together, and then attach those fields to edit screens.'=>'ACF 使用<a href="%s" target="_blank">字段组</a>将自定义字段分组在一起，然后将这些字段附加到编辑屏幕。','Add Your First Field Group'=>'添加第一个字段组','Options Pages'=>'选项页面','ACF Blocks'=>'ACF 块','Gallery Field'=>'画廊字段','Flexible Content Field'=>'弹性内容字段','Repeater Field'=>'循环字段','Unlock Extra Features with ACF PRO'=>'使用 ACF PRO 解锁额外功能','Delete Field Group'=>'删除字段组','Created on %1$s at %2$s'=>'于 %1$s 在 %2$s 创建','Group Settings'=>'组设置','Location Rules'=>'位置规则','Choose from over 30 field types. <a href="%s" target="_blank">Learn more</a>.'=>'从 30 多种字段类型中进行选择。<a href="%s" target="_blank">了解更多</a>。','Get started creating new custom fields for your posts, pages, custom post types and other WordPress content.'=>'开始为您的文章、页面、自定义文章类型和其他 WordPress 内容创建新的自定义字段。','Add Your First Field'=>'添加第一个字段','#'=>'#','Add Field'=>'添加字段','Presentation'=>'演示文稿','Validation'=>'验证','General'=>'常规','Import JSON'=>'导入 JSON','Export As JSON'=>'导出为 JSON','Field group deactivated.'=>'%s 个字段组已停用。','Field group activated.'=>'%s 个字段组已激活。','Deactivate'=>'停用','Deactivate this item'=>'停用此项目','Activate'=>'启用','Activate this item'=>'激活此项目','Move field group to trash?'=>'移动字段组到移至回收站？','post statusInactive'=>'未启用','WP Engine'=>'WP Engine','Advanced Custom Fields and Advanced Custom Fields PRO should not be active at the same time. We\'ve automatically deactivated Advanced Custom Fields PRO.'=>'高级自定义字段和高级自定义字段 PRO 不应同时处于活动状态。我们已自动停用高级自定义字段 PRO。','Advanced Custom Fields and Advanced Custom Fields PRO should not be active at the same time. We\'ve automatically deactivated Advanced Custom Fields.'=>'高级自定义字段和高级自定义字段 PRO 不应同时处于活动状态。我们已自动停用高级自定义字段。','%1$s must have a user with the %2$s role.'=>'%1$s 必须有一个 %2$s 角色的用户。','%1$s must have a valid user ID.'=>'%1$s 必须具有有效的用户 ID。','Invalid request.'=>'无效要求。','%1$s is not one of %2$s'=>'%1$s 不是 %2$s 之一','%1$s must have term %2$s.'=>'%1$s 必须有术语 %2$s。','%1$s must be of post type %2$s.'=>'%1$s 必须是文章类型 %2$s。','%1$s must have a valid post ID.'=>'%1$s 必须有一个有效的文章 ID。','%s requires a valid attachment ID.'=>'%s 需要一个有效的附件 ID。','Show in REST API'=>'在 REST API 中显示','Enable Transparency'=>'启用透明度','RGBA Array'=>'RGBA 数组','RGBA String'=>'RGBA 字符串','Hex String'=>'十六进制字符串','Upgrade to PRO'=>'升级到专业版','post statusActive'=>'已启用','\'%s\' is not a valid email address'=>'「%s」不是有效的邮件地址','Color value'=>'颜色值','Select default color'=>'选择默认颜色','Clear color'=>'清除颜色','Blocks'=>'块','Options'=>'选项','Users'=>'用户','Menu items'=>'菜单项目','Widgets'=>'小工具','Attachments'=>'附件','Taxonomies'=>'分类法','Posts'=>'文章','Last updated: %s'=>'最后更新： %s','Sorry, this post is unavailable for diff comparison.'=>'抱歉，该文章无法进行差异比较。','Invalid field group parameter(s).'=>'无效的字段组参数。','Awaiting save'=>'等待保存','Saved'=>'已保存','Import'=>'导入','Review changes'=>'查看更改','Located in: %s'=>'位于 %s','Located in plugin: %s'=>'位于插件: %s','Located in theme: %s'=>'位于主题: %s','Various'=>'各种','Sync changes'=>'同步更改','Loading diff'=>'加载差异','Review local JSON changes'=>'查看本地 JSON 更改','Visit website'=>'访问网站','View details'=>'查看详情','Version %s'=>'版本 %s','Information'=>'信息','<a href="%s" target="_blank">Help Desk</a>. The support professionals on our Help Desk will assist with your more in depth, technical challenges.'=>'<a href="%s" target="_blank">服务台</a>。我们服务台上的支持专业人员将协助您解决更深入的技术难题。','<a href="%s" target="_blank">Discussions</a>. We have an active and friendly community on our Community Forums who may be able to help you figure out the \'how-tos\' of the ACF world.'=>'<a href="%s" target="_blank">讨论</a>。我们的社区论坛上有一个活跃且友好的社区，他们也许能够帮助您了解 ACF 世界的“操作方法”。','<a href="%s" target="_blank">Documentation</a>. Our extensive documentation contains references and guides for most situations you may encounter.'=>'<a href="%s" target="_blank">文档</a>。我们详尽的文档包含您可能遇到的大多数情况的参考和指南。','We are fanatical about support, and want you to get the best out of your website with ACF. If you run into any difficulties, there are several places you can find help:'=>'我们热衷于支持，并希望您通过ACF充分利用自己的网站。如果遇到任何困难，可以在几个地方找到帮助：','Help & Support'=>'帮助和支持','Please use the Help & Support tab to get in touch should you find yourself requiring assistance.'=>'如果您需要帮助，请使用“帮助和支持”选项卡进行联系。','Before creating your first Field Group, we recommend first reading our <a href="%s" target="_blank">Getting started</a> guide to familiarize yourself with the plugin\'s philosophy and best practises.'=>'在创建您的第一个字段组之前，我们建议您先阅读<a href="%s" target="_blank">入门</a>指南，以熟悉插件的原理和最佳实践。','The Advanced Custom Fields plugin provides a visual form builder to customize WordPress edit screens with extra fields, and an intuitive API to display custom field values in any theme template file.'=>'Advanced Custom Fields 插件提供了一个可视化的表单生成器，用于自定义带有额外字段的WordPress编辑屏幕，以及一个直观的API，用于在任何主题模板文件中显示自定义字段的值。','Overview'=>'概述','Location type "%s" is already registered.'=>'位置类型「%s」 已经注册。','Class "%s" does not exist.'=>'类「%s」不存在。','Invalid nonce.'=>'无效 nonce。','Error loading field.'=>'加载字段出错。','<strong>Error</strong>: %s'=>'<strong>错误</strong>: %s','Widget'=>'小工具','User Role'=>'用户角色','Comment'=>'评论','Post Format'=>'文章格式','Menu Item'=>'菜单项','Post Status'=>'文章状态','Menus'=>'菜单','Menu Locations'=>'菜单位置','Menu'=>'菜单','Post Taxonomy'=>'文章分类法','Child Page (has parent)'=>'子页面（有父页面）','Parent Page (has children)'=>'父页面（有子页面）','Top Level Page (no parent)'=>'顶层页面（无父页面）','Posts Page'=>'文章页面','Front Page'=>'首页','Page Type'=>'页面类型','Viewing back end'=>'查看后端','Viewing front end'=>'查看前端','Logged in'=>'登录','Current User'=>'当前用户','Page Template'=>'页面模板','Register'=>'注册','Add / Edit'=>'添加 / 编辑','User Form'=>'用户表格','Page Parent'=>'页面父级','Super Admin'=>'超级管理员','Current User Role'=>'当前用户角色','Default Template'=>'默认模板','Post Template'=>'文章模板','Post Category'=>'文章分类','All %s formats'=>'所有 %s 格式','Attachment'=>'附件','%s value is required'=>'需要 %s 值','Show this field if'=>'如果显示此字段','Conditional Logic'=>'条件逻辑','and'=>'和','Local JSON'=>'本地 JSON','Clone Field'=>'克隆字段','Please also check all premium add-ons (%s) are updated to the latest version.'=>'请同时检查所有高级附加组件 (%s) 是否已更新到最新版本。','This version contains improvements to your database and requires an upgrade.'=>'此版本包含对您数据库的改进，需要升级。','Thank you for updating to %1$s v%2$s!'=>'感谢您升级到 %1$s v%2$s！','Database Upgrade Required'=>'需要升级数据库','Options Page'=>'选项页面','Gallery'=>'画廊','Flexible Content'=>'灵活的内容','Repeater'=>'中继器','Back to all tools'=>'返回所有工具','If multiple field groups appear on an edit screen, the first field group\'s options will be used (the one with the lowest order number)'=>'如果编辑界面上出现多个字段组，将使用第一个字段组的选项（顺序号最小的那个）。','<b>Select</b> items to <b>hide</b> them from the edit screen.'=>'<b>选择</b>项目，从编辑屏幕中<b>隐藏</b>它们。','Hide on screen'=>'在屏幕上隐藏','Send Trackbacks'=>'发送跟踪','Tags'=>'标记','Categories'=>'分类','Page Attributes'=>'页面属性','Format'=>'格式','Author'=>'作者','Slug'=>'别名','Revisions'=>'修订','Comments'=>'评论','Discussion'=>'讨论','Excerpt'=>'摘要','Content Editor'=>'内容编辑器','Permalink'=>'固定链接','Shown in field group list'=>'显示在字段组列表','Field groups with a lower order will appear first'=>'顺序较低的字段组将优先显示','Order No.'=>'顺序号','Below fields'=>'下字段','Below labels'=>'下方标签','Instruction Placement'=>'指令位置','Label Placement'=>'标签位置','Side'=>'侧面','Normal (after content)'=>'正常（内容之后）','High (after title)'=>'高（标题之后）','Position'=>'位置','Seamless (no metabox)'=>'无缝（无元框）','Standard (WP metabox)'=>'标准（WP 元框）','Style'=>'样式','Type'=>'类型','Key'=>'关键字','Order'=>'订购','Close Field'=>'关闭字段','id'=>'id','class'=>'类','width'=>'宽度','Wrapper Attributes'=>'包装属性','Required'=>'需要','Instructions'=>'说明','Field Type'=>'字段类型','Single word, no spaces. Underscores and dashes allowed'=>'单词，无空格。允许使用下划线和破折号','Field Name'=>'字段名称','This is the name which will appear on the EDIT page'=>'这是将出现在 EDIT 页面上的名称','Field Label'=>'字段标签','Delete'=>'删除','Delete field'=>'删除字段','Move'=>'移动','Move field to another group'=>'移动字段到另一个组','Duplicate field'=>'复制字段','Edit field'=>'编辑字段','Drag to reorder'=>'拖动以重新排序','Show this field group if'=>'如果','No updates available.'=>'无可用更新。','Database upgrade complete. <a href="%s">See what\'s new</a>'=>'数据库升级完成。<a href="%s">查看新内容</a>','Reading upgrade tasks...'=>'阅读升级任务...','Upgrade failed.'=>'升级失败。','Upgrade complete.'=>'升级完成。','Upgrading data to version %s'=>'将数据升级到 %s 版本','It is strongly recommended that you backup your database before proceeding. Are you sure you wish to run the updater now?'=>'强烈建议您在继续之前备份您的数据库。您确定现在要运行升级程序吗？','Please select at least one site to upgrade.'=>'请选择至少一个网站进行升级。','Database Upgrade complete. <a href="%s">Return to network dashboard</a>'=>'数据库升级完成。<a href="%s">返回网络控制面板</a>','Site is up to date'=>'网站已更新','Site requires database upgrade from %1$s to %2$s'=>'网站需要将数据库从 %1$s 升级到 %2$s','Site'=>'网站','Upgrade Sites'=>'升级网站','The following sites require a DB upgrade. Check the ones you want to update and then click %s.'=>'以下网站需要升级数据库。选中要升级的网站，然后单击 %s。','Add rule group'=>'添加规则组','Create a set of rules to determine which edit screens will use these advanced custom fields'=>'创建一组规则以确定自定义字段在哪个编辑界面上显示','Rules'=>'规则','Copied'=>'复制','Copy to clipboard'=>'复制到剪贴板','Select the items you would like to export and then select your export method. Export As JSON to export to a .json file which you can then import to another ACF installation. Generate PHP to export to PHP code which you can place in your theme.'=>'选择您要导出的项目，然后选择导出方法。导出为 JSON 以导出到 .json 文件，然后可以将其导入到另一个 ACF 安装中。生成 PHP 以导出到 PHP 代码，您可以将其放置在主题中。','Select Field Groups'=>'选择字段组','No field groups selected'=>'无字段组选择','Generate PHP'=>'生成 PHP','Export Field Groups'=>'导出字段组','Import file empty'=>'导入文件为空','Incorrect file type'=>'文件类型不正确','Error uploading file. Please try again'=>'上传文件时出错。请重试','Select the Advanced Custom Fields JSON file you would like to import. When you click the import button below, ACF will import the items in that file.'=>'选择您要导入的高级自定义字段 JSON 文件。当您单击下面的导入按钮时，ACF 将导入该文件中的项目。','Import Field Groups'=>'导入字段组','Sync'=>'同步','Select %s'=>'选择 %s','Duplicate'=>'复制','Duplicate this item'=>'复制此项目','Supports'=>'支持','Documentation'=>'文档','Description'=>'描述','Sync available'=>'可同步','Field group synchronized.'=>'%s 个字段组已同步。','Field group duplicated.'=>'%s 个字段组已复制。','Active <span class="count">(%s)</span>'=>'激活 <span class="count">(%s)</span>','Review sites & upgrade'=>'审查网站和升级','Upgrade Database'=>'升级数据库','Custom Fields'=>'自定义字段','Move Field'=>'移动字段','Please select the destination for this field'=>'请为此字段选择目的地','The %1$s field can now be found in the %2$s field group'=>'现在 %1$s 字段可以在 %2$s 字段组中找到。','Move Complete.'=>'移动完成。','Active'=>'已启用','Field Keys'=>'字段关键字','Settings'=>'设置','Location'=>'位置','Null'=>'无','copy'=>'复制','(this field)'=>'（此字段）','Checked'=>'检查','Move Custom Field'=>'移动自定义字段','No toggle fields available'=>'没有可用的切换字段','Field group title is required'=>'字段组标题为必填项','This field cannot be moved until its changes have been saved'=>'在保存更改之前，不能移动此字段','The string "field_" may not be used at the start of a field name'=>'字段名开头不得使用字符串「field_」。','Field group draft updated.'=>'字段组草稿更新。','Field group scheduled for.'=>'字段组已计划。','Field group submitted.'=>'字段组已提交。','Field group saved.'=>'字段组已保存。','Field group published.'=>'字段组已发布。','Field group deleted.'=>'字段组已删除。','Field group updated.'=>'字段组已更新。','Tools'=>'工具','is not equal to'=>'不等于','is equal to'=>'等于','Forms'=>'表单','Page'=>'页面','Post'=>'文章','Relational'=>'关系式','Choice'=>'选择','Basic'=>'基础','Unknown'=>'未知','Field type does not exist'=>'字段类型不存在','Spam Detected'=>'检测到垃圾邮件','Post updated'=>'发布更新','Update'=>'更新','Validate Email'=>'验证电子邮件','Content'=>'内容','Title'=>'标题','Edit field group'=>'编辑字段组','Selection is less than'=>'选择小于','Selection is greater than'=>'选择大于','Value is less than'=>'值小于','Value is greater than'=>'值大于','Value contains'=>'值包含','Value matches pattern'=>'值符合样板','Value is not equal to'=>'值不等于','Value is equal to'=>'值等于','Has no value'=>'没有值','Has any value'=>'有任何值','Cancel'=>'取消','Are you sure?'=>'您确定吗？','%d fields require attention'=>'%d 字段需要注意','1 field requires attention'=>'1 字段需要注意','Validation failed'=>'验证失败','Validation successful'=>'验证成功','Restricted'=>'受限','Collapse Details'=>'折叠详细信息','Expand Details'=>'展开详细信息','Uploaded to this post'=>'上传到此文章','verbUpdate'=>'更新','verbEdit'=>'编辑','The changes you made will be lost if you navigate away from this page'=>'如果您离开此页面，您所做的更改将丢失','File type must be %s.'=>'文件类型必须为 %s。','or'=>'或','File size must not exceed %s.'=>'文件大小不得超过 %s。','File size must be at least %s.'=>'文件大小必须至少 %s。','Image height must not exceed %dpx.'=>'图像高度不得超过 %dpx。','Image height must be at least %dpx.'=>'图像高度必须至少 %dpx。','Image width must not exceed %dpx.'=>'图像宽度不得超过 %dpx。','Image width must be at least %dpx.'=>'图像宽度必须至少 %dpx。','(no title)'=>'(无标题）','Full Size'=>'全尺寸','Large'=>'大尺寸','Medium'=>'中号','Thumbnail'=>'缩略图','(no label)'=>'(无标签）','Sets the textarea height'=>'设置文本区域高度','Rows'=>'行','Text Area'=>'文本区域','Prepend an extra checkbox to toggle all choices'=>'额外添加复选框以切换所有选项','Save \'custom\' values to the field\'s choices'=>'将「自定义」值保存到字段的选项中','Allow \'custom\' values to be added'=>'允许添加「自定义」值','Add new choice'=>'添加新选择','Toggle All'=>'切换所有','Allow Archives URLs'=>'允许存档 URL','Archives'=>'归档','Page Link'=>'页面链接','Add'=>'添加','Name'=>'字段名称','%s added'=>'%s 添加','%s already exists'=>'%s 已存在','User unable to add new %s'=>'用户无法添加新的 %s','Term ID'=>'术语 ID','Term Object'=>'术语对象','Load value from posts terms'=>'从文章术语中加载值','Load Terms'=>'加载术语','Connect selected terms to the post'=>'将选择术语连接到文章','Save Terms'=>'保存术语','Allow new terms to be created whilst editing'=>'允许在编辑时创建新术语','Create Terms'=>'创建术语','Radio Buttons'=>'单选按钮','Single Value'=>'单值','Multi Select'=>'多选','Checkbox'=>'复选框','Multiple Values'=>'多值','Select the appearance of this field'=>'选择该字段的外观','Appearance'=>'外观','Select the taxonomy to be displayed'=>'选择分类法显示','No TermsNo %s'=>'无 %s','Value must be equal to or lower than %d'=>'值必须等于或小于 %d','Value must be equal to or higher than %d'=>'值必须等于或大于 %d','Value must be a number'=>'值必须是数字','Number'=>'数值','Save \'other\' values to the field\'s choices'=>'将「其他」值保存到字段的选择中','Add \'other\' choice to allow for custom values'=>'添加「其他」选项以允许自定义值','Other'=>'其他','Radio Button'=>'单选按钮','Define an endpoint for the previous accordion to stop. This accordion will not be visible.'=>'定义上一个手风琴停止的终点。该手风琴将不可见。','Allow this accordion to open without closing others.'=>'允许打开此手风琴而不关闭其他手风琴。','Multi-Expand'=>'多重展开','Display this accordion as open on page load.'=>'在页面加载时将此手风琴显示为打开。','Open'=>'打开','Accordion'=>'手风琴','Restrict which files can be uploaded'=>'限制哪些文件可以上传','File ID'=>'文件 ID','File URL'=>'文件 URL','File Array'=>'文件阵列','Add File'=>'添加文件','No file selected'=>'未选择文件','File name'=>'文件名','Update File'=>'更新文件','Edit File'=>'编辑文件','Select File'=>'选择文件','File'=>'文件','Password'=>'密码','Specify the value returned'=>'指定返回值','Use AJAX to lazy load choices?'=>'使用 Ajax 来懒加载选择？','Enter each default value on a new line'=>'在新行中输入每个默认值','verbSelect'=>'选择','Select2 JS load_failLoading failed'=>'加载失败','Select2 JS searchingSearching&hellip;'=>'正在搜索...','Select2 JS load_moreLoading more results&hellip;'=>'加载更多结果...','Select2 JS selection_too_long_nYou can only select %d items'=>'您只能选择 %d 项','Select2 JS selection_too_long_1You can only select 1 item'=>'您只能选择 1 项','Select2 JS input_too_long_nPlease delete %d characters'=>'请删除 %d 个字符','Select2 JS input_too_long_1Please delete 1 character'=>'请删除 1 个字符','Select2 JS input_too_short_nPlease enter %d or more characters'=>'请输入 %d 或者更多字符','Select2 JS input_too_short_1Please enter 1 or more characters'=>'请输入 1 个或更多字符','Select2 JS matches_0No matches found'=>'未找到匹配项','Select2 JS matches_n%d results are available, use up and down arrow keys to navigate.'=>'有 %d 个结果，请使用上下箭头键浏览。','Select2 JS matches_1One result is available, press enter to select it.'=>'有一个结果，按输入选择。','nounSelect'=>'选择','User ID'=>'用户 ID','User Object'=>'用户对象','User Array'=>'用户数组','All user roles'=>'所有用户角色','Filter by Role'=>'按角色筛选','User'=>'用户','Separator'=>'分隔符','Select Color'=>'选择颜色','Default'=>'默认','Clear'=>'清除','Color Picker'=>'颜色选择器','Date Time Picker JS pmTextShortP'=>'P','Date Time Picker JS pmTextPM'=>'PM','Date Time Picker JS amTextShortA'=>'A','Date Time Picker JS amTextAM'=>'AM','Date Time Picker JS selectTextSelect'=>'选择','Date Time Picker JS closeTextDone'=>'完成','Date Time Picker JS currentTextNow'=>'现在','Date Time Picker JS timezoneTextTime Zone'=>'时区','Date Time Picker JS microsecTextMicrosecond'=>'微秒','Date Time Picker JS millisecTextMillisecond'=>'毫秒','Date Time Picker JS secondTextSecond'=>'秒','Date Time Picker JS minuteTextMinute'=>'分','Date Time Picker JS hourTextHour'=>'小时','Date Time Picker JS timeTextTime'=>'时间','Date Time Picker JS timeOnlyTitleChoose Time'=>'选择时间','Date Time Picker'=>'日期时间选择器','Endpoint'=>'终点','Left aligned'=>'左对齐','Top aligned'=>'顶部对齐','Placement'=>'位置','Tab'=>'标签','Value must be a valid URL'=>'值必须是有效的 URL','Link URL'=>'链接 URL','Link Array'=>'链接阵列','Opens in a new window/tab'=>'在新窗口 / 标签中打开','Select Link'=>'选择链接','Link'=>'链接','Email'=>'邮箱','Step Size'=>'步长','Maximum Value'=>'最大值','Minimum Value'=>'最小值','Range'=>'范围','Both (Array)'=>'两者（数组）','Label'=>'标签','Value'=>'值','Vertical'=>'垂直','Horizontal'=>'水平','red : Red'=>'red : 红色','For more control, you may specify both a value and label like this:'=>'为获得更多控制权，您可以像这样指定值和标签：','Enter each choice on a new line.'=>'在新行中输入每个选项。','Choices'=>'选择','Button Group'=>'按钮组','Allow Null'=>'允许空值','Parent'=>'父级','TinyMCE will not be initialized until field is clicked'=>'在点击字段之前，TinyMCE 不会被初始化。','Delay Initialization'=>'延迟初始化','Show Media Upload Buttons'=>'显示媒体上传按钮','Toolbar'=>'工具栏','Text Only'=>'仅文本','Visual Only'=>'仅可视化','Visual & Text'=>'可视化和文本','Tabs'=>'标签','Click to initialize TinyMCE'=>'点击初始化 TinyMCE','Name for the Text editor tab (formerly HTML)Text'=>'文本','Visual'=>'可视化','Value must not exceed %d characters'=>'值不得超过 %d 字符','Leave blank for no limit'=>'留空表示无限制','Character Limit'=>'字符限制','Appears after the input'=>'出现在输入后','Append'=>'附加','Appears before the input'=>'出现在输入之前','Prepend'=>'预输入','Appears within the input'=>'出现在输入内容中','Placeholder Text'=>'占位符文本','Appears when creating a new post'=>'创建新文章时显示','Text'=>'文本','%1$s requires at least %2$s selection'=>'%1$s 至少需要 %2$s 选项','Post ID'=>'文章 ID','Post Object'=>'文章对象','Maximum Posts'=>'最大文章数','Minimum Posts'=>'最小文章数','Featured Image'=>'精选图片','Selected elements will be displayed in each result'=>'选中的元素将显示在每个结果中','Elements'=>'元素','Taxonomy'=>'分类法','Post Type'=>'文章类型','Filters'=>'筛选','All taxonomies'=>'所有分类法','Filter by Taxonomy'=>'按分类筛选','All post types'=>'所有文章类型','Filter by Post Type'=>'按文章类型筛选','Search...'=>'搜索...','Select taxonomy'=>'选择分类法','Select post type'=>'选择文章类型','No matches found'=>'未找到匹配结果','Loading'=>'载入','Maximum values reached ( {max} values )'=>'达到最大值 ( {max} 值 )','Relationship'=>'关系','Comma separated list. Leave blank for all types'=>'逗号分隔列表。所有类型留空','Allowed File Types'=>'允许的文件类型','Maximum'=>'最大值','File size'=>'文件大小','Restrict which images can be uploaded'=>'限制哪些图片可以上传','Minimum'=>'最少','Uploaded to post'=>'上传到文章','All'=>'全部','Limit the media library choice'=>'限制媒体库选择','Library'=>'文件库','Preview Size'=>'预览大小','Image ID'=>'图片 ID','Image URL'=>'图片 URL','Image Array'=>'图像阵列','Specify the returned value on front end'=>'在前端指定返回值','Return Value'=>'返回值','Add Image'=>'添加图片','No image selected'=>'没有选择图片','Remove'=>'移除','Edit'=>'编辑','All images'=>'全部图片','Update Image'=>'更新图片','Edit Image'=>'编辑图片','Select Image'=>'选择图片','Image'=>'图片','Allow HTML markup to display as visible text instead of rendering'=>'允许 HTML 标记显示为可见文本，而不是渲染','Escape HTML'=>'转义 HTML','No Formatting'=>'无格式化','Automatically add &lt;br&gt;'=>'自动添加 &lt;br&gt','Automatically add paragraphs'=>'自动添加段落','Controls how new lines are rendered'=>'控制如何渲染新行','New Lines'=>'新行','Week Starts On'=>'星期开始于','The format used when saving a value'=>'保存数值时使用的格式','Save Format'=>'保存格式','Date Picker JS weekHeaderWk'=>'星期','Date Picker JS prevTextPrev'=>'上一页','Date Picker JS nextTextNext'=>'下一页','Date Picker JS currentTextToday'=>'今天','Date Picker JS closeTextDone'=>'已完成','Date Picker'=>'日期选择器','Width'=>'宽度','Embed Size'=>'嵌入大小','Enter URL'=>'输入 URL','oEmbed'=>'oEmbed','Text shown when inactive'=>'未启用时显示的文本','Off Text'=>'关闭文本','Text shown when active'=>'已启用时显示的文本','On Text'=>'启用文本','Stylized UI'=>'风格化用户界面','Default Value'=>'默认值','Displays text alongside the checkbox'=>'在复选框旁边显示文本','Message'=>'信息','No'=>'没有','Yes'=>'确认','True / False'=>'真 / 假','Row'=>'行','Table'=>'表格','Block'=>'块','Specify the style used to render the selected fields'=>'指定用于渲染选择字段的样式','Layout'=>'布局','Sub Fields'=>'子字段','Group'=>'组合','Customize the map height'=>'自定义地图高度','Height'=>'高度','Set the initial zoom level'=>'设置初始缩放级别','Zoom'=>'缩放','Center the initial map'=>'将初始地图居中','Center'=>'居中','Search for address...'=>'搜索地址...','Find current location'=>'查找当前位置','Clear location'=>'清除位置','Search'=>'搜索','Sorry, this browser does not support geolocation'=>'抱歉，此浏览器不支持地理位置定位','Google Map'=>'谷歌地图','The format returned via template functions'=>'通过模板函数返回的格式','Return Format'=>'返回格式','Custom:'=>'自定义：','The format displayed when editing a post'=>'编辑文章时显示的格式','Display Format'=>'显示格式','Time Picker'=>'时间选择器','Inactive <span class="count">(%s)</span>'=>'未激活 <span class="count">(%s)</span>','No Fields found in Trash'=>'回收站中未发现字段','No Fields found'=>'未找到字段','Search Fields'=>'搜索字段','View Field'=>'查看字段','New Field'=>'新建字段','Edit Field'=>'编辑字段','Add New Field'=>'添加新字段','Field'=>'字段','Fields'=>'字段','No Field Groups found in Trash'=>'回收站中未发现字段组','No Field Groups found'=>'未找到字段组','Search Field Groups'=>'搜索字段组','View Field Group'=>'查看字段组','New Field Group'=>'新建字段组','Edit Field Group'=>'编辑字段组','Add New Field Group'=>'添加新字段组','Add New'=>'添加新的','Field Group'=>'字段组','Field Groups'=>'字段组','Customize WordPress with powerful, professional and intuitive fields.'=>'【高级自定义字段 ACF】使用强大、专业和直观的字段自定义WordPress。','https://www.advancedcustomfields.com'=>'https://www.advancedcustomfields.com','Advanced Custom Fields'=>'高级自定义字段','Advanced Custom Fields PRO'=>'Advanced Custom Fields 专业版','Options Updated'=>'选项已更新','Check Again'=>'重新检查','Publish'=>'发布','No Custom Field Groups found for this options page. <a href="%s">Create a Custom Field Group</a>'=>'这个选项页上还没有自定义字段群组。<a href="%s">创建自定义字段群组</a>','<b>Error</b>. Could not connect to update server'=>'<b>错误</b>，不能连接到更新服务器','Display'=>'显示','Add Row'=>'添加行','layout'=>'布局','layouts'=>'布局','This field requires at least {min} {label} {identifier}'=>'这个字段需要至少 {min} {label} {identifier}','{available} {label} {identifier} available (max {max})'=>'{available} {label} {identifier} 可用 (max {max})','{required} {label} {identifier} required (min {min})'=>'{required} {label} {identifier} 需要 (min {min})','Flexible Content requires at least 1 layout'=>'灵活内容字段需要至少一个布局','Click the "%s" button below to start creating your layout'=>'点击下面的 "%s" 按钮创建布局','Add layout'=>'添加布局','Remove layout'=>'删除布局','Delete Layout'=>'删除布局','Duplicate Layout'=>'复制布局','Add New Layout'=>'添加新布局','Add Layout'=>'添加布局','Min'=>'最小','Max'=>'最大','Minimum Layouts'=>'最小布局','Maximum Layouts'=>'最大布局','Button Label'=>'按钮标签','Add Image to Gallery'=>'添加图片到相册','Maximum selection reached'=>'已到最大选择','Length'=>'长度','Caption'=>'标题','Add to gallery'=>'添加到相册','Bulk actions'=>'批量动作','Sort by date uploaded'=>'按上传日期排序','Sort by date modified'=>'按修改日期排序','Sort by title'=>'按标题排序','Reverse current order'=>'颠倒当前排序','Close'=>'关闭','Minimum Selection'=>'最小选择','Maximum Selection'=>'最大选择','Allowed file types'=>'允许的文字类型','Minimum rows not reached ({min} rows)'=>'已到最小行数 ({min} 行)','Maximum rows reached ({max} rows)'=>'已到最大行数 ({max} 行)','Minimum Rows'=>'最小行数','Maximum Rows'=>'最大行数','Click to reorder'=>'拖拽排序','Add row'=>'添加行','Remove row'=>'删除行','First Page'=>'首页','Previous Page'=>'文章页','Next Page'=>'首页','Last Page'=>'文章页','No options pages exist'=>'还没有选项页面','Deactivate License'=>'关闭许可证','Activate License'=>'激活许可证','License Key'=>'许可证号','Update Information'=>'更新信息','Current Version'=>'当前版本','Latest Version'=>'最新版本','Update Available'=>'可用更新','Upgrade Notice'=>'更新通知','Enter your license key to unlock updates'=>'在上面输入许可证号解锁更新','Update Plugin'=>'更新插件']];