(()=>{var t={1018:()=>{!function(){"use strict";acf.hooks=new function(){var t={removeFilter:function(e,i){return"string"==typeof e&&n("filters",e,i),t},applyFilters:function(){var e=Array.prototype.slice.call(arguments),n=e.shift();return"string"==typeof n?o("filters",n,e):t},addFilter:function(e,n,o,r){return"string"==typeof e&&"function"==typeof n&&i("filters",e,n,o=parseInt(o||10,10),r),t},removeAction:function(e,i){return"string"==typeof e&&n("actions",e,i),t},doAction:function(){var e=Array.prototype.slice.call(arguments),n=e.shift();return"string"==typeof n&&o("actions",n,e),t},addAction:function(e,n,o,r){return"string"==typeof e&&"function"==typeof n&&i("actions",e,n,o=parseInt(o||10,10),r),t},storage:function(){return e}},e={actions:{},filters:{}};function n(t,n,i,o){if(e[t][n])if(i){var r,a=e[t][n];if(o)for(r=a.length;r--;){var s=a[r];s.callback===i&&s.context===o&&a.splice(r,1)}else for(r=a.length;r--;)a[r].callback===i&&a.splice(r,1)}else e[t][n]=[]}function i(t,n,i,o,r){var a={callback:i,priority:o,context:r},s=e[t][n];s?(s.push(a),s=function(t){for(var e,n,i,o=1,r=t.length;o<r;o++){for(e=t[o],n=o;(i=t[n-1])&&i.priority>e.priority;)t[n]=t[n-1],--n;t[n]=e}return t}(s)):s=[a],e[t][n]=s}function o(t,n,i){var o=e[t][n];if(!o)return"filters"===t&&i[0];var r=0,a=o.length;if("filters"===t)for(;r<a;r++)i[0]=o[r].callback.apply(o[r].context,i);else for(;r<a;r++)o[r].callback.apply(o[r].context,i);return"filters"!==t||i[0]}return t}}(window)},2177:()=>{!function(t,e){acf.newTooltip=function(t){return"object"!=typeof t&&(t={text:t}),t.confirmRemove!==e?(t.textConfirm=acf.__("Remove"),t.textCancel=acf.__("Cancel"),new i(t)):t.confirm!==e?new i(t):new n(t)};var n=acf.Model.extend({data:{text:"",timeout:0,target:null},tmpl:function(){return'<div class="acf-tooltip"></div>'},setup:function(e){t.extend(this.data,e),this.$el=t(this.tmpl())},initialize:function(){this.render(),this.show(),this.position();var e=this.get("timeout");e&&setTimeout(t.proxy(this.fade,this),e)},update:function(e){t.extend(this.data,e),this.initialize()},render:function(){this.html(this.get("text"))},show:function(){t("body").append(this.$el)},hide:function(){this.$el.remove()},fade:function(){this.$el.addClass("acf-fade-up"),this.setTimeout(function(){this.remove()},250)},html:function(t){this.$el.html(t)},position:function(){var e=this.$el,n=this.get("target");if(n){e.removeClass("right left bottom top").css({top:0,left:0});var i=n.outerWidth(),o=n.outerHeight(),r=n.offset().top,a=n.offset().left,s=e.outerWidth(),c=e.outerHeight(),l=e.offset().top,u=r-c-l,f=a+i/2-s/2;f<10?(e.addClass("right"),f=a+i,u=r+o/2-c/2-l):f+s+10>t(window).width()?(e.addClass("left"),f=a-s,u=r+o/2-c/2-l):u-t(window).scrollTop()<10?(e.addClass("bottom"),u=r+o-l):e.addClass("top"),e.css({top:u,left:f})}}}),i=n.extend({data:{text:"",textConfirm:"",textCancel:"",target:null,targetConfirm:!0,confirm:function(){},cancel:function(){},context:!1},events:{'click [data-event="cancel"]':"onCancel",'click [data-event="confirm"]':"onConfirm"},addEvents:function(){acf.Model.prototype.addEvents.apply(this);var e=t(document),n=this.get("target");this.setTimeout(function(){this.on(e,"click","onCancel")}),this.get("targetConfirm")&&this.on(n,"click","onConfirm")},removeEvents:function(){acf.Model.prototype.removeEvents.apply(this);var e=t(document),n=this.get("target");this.off(e,"click"),this.off(n,"click")},render:function(){var t=[this.get("text")||acf.__("Are you sure?"),'<a href="#" data-event="confirm">'+(this.get("textConfirm")||acf.__("Yes"))+"</a>",'<a href="#" data-event="cancel">'+(this.get("textCancel")||acf.__("No"))+"</a>"].join(" ");this.html(t),this.$el.addClass("-confirm")},onCancel:function(t,e){t.preventDefault(),t.stopImmediatePropagation();var n=this.get("cancel"),i=this.get("context")||this;n.apply(i,arguments),this.remove()},onConfirm:function(t,e){t.preventDefault(),t.stopImmediatePropagation();var n=this.get("confirm"),i=this.get("context")||this;n.apply(i,arguments),this.remove()}});acf.models.Tooltip=n,acf.models.TooltipConfirm=i,new acf.Model({tooltip:!1,events:{"mouseenter .acf-js-tooltip":"showTitle","mouseup .acf-js-tooltip":"hideTitle","mouseleave .acf-js-tooltip":"hideTitle","focus .acf-js-tooltip":"showTitle","blur .acf-js-tooltip":"hideTitle","keyup .acf-js-tooltip":"onKeyUp"},showTitle:function(t,e){var n=e.attr("title");n&&(e.attr("title",""),this.tooltip?this.tooltip.update({text:n,target:e}):this.tooltip=acf.newTooltip({text:n,target:e}))},hideTitle:function(t,e){this.tooltip.hide(),e.attr("title",this.tooltip.get("text"))},onKeyUp:function(t,e){"Escape"===t.key&&this.hideTitle(t,e)}})}(jQuery)},2700:()=>{var t,e;t=jQuery,e=acf.Model.extend({data:{text:"",type:"",timeout:0,dismiss:!0,target:!1,location:"before",close:function(){}},events:{"click .acf-notice-dismiss":"onClickClose"},tmpl:function(){return'<div class="acf-notice"></div>'},setup:function(e){t.extend(this.data,e),this.$el=t(this.tmpl())},initialize:function(){this.render(),this.show()},render:function(){this.type(this.get("type")),this.html("<p>"+this.get("text")+"</p>"),this.get("dismiss")&&(this.$el.append('<a href="#" class="acf-notice-dismiss acf-icon -cancel small"></a>'),this.$el.addClass("-dismiss"));var t=this.get("timeout");t&&this.away(t)},update:function(e){t.extend(this.data,e),this.initialize(),this.removeEvents(),this.addEvents()},show:function(){var t=this.get("target"),e=this.get("location");t&&("after"===e?t.append(this.$el):t.prepend(this.$el))},hide:function(){this.$el.remove()},away:function(t){this.setTimeout(function(){acf.remove(this.$el)},t)},type:function(t){var e=this.get("type");e&&this.$el.removeClass("-"+e),this.$el.addClass("-"+t),"error"==t&&this.$el.addClass("acf-error-message")},html:function(t){this.$el.html(acf.escHtml(t))},text:function(t){this.$("p").html(acf.escHtml(t))},onClickClose:function(t,e){t.preventDefault(),this.get("close").apply(this,arguments),this.remove()}}),acf.newNotice=function(t){return"object"!=typeof t&&(t={text:t}),new e(t)},new acf.Model({wait:"prepare",priority:1,initialize:function(){t(".acf-admin-notice").each(function(){if(t(this).data("persisted")){let e=acf.getPreference("dismissed-notices");e&&"object"==typeof e&&e.includes(t(this).data("persist-id"))?t(this).remove():(t(this).show(),t(this).on("click",".notice-dismiss",function(n){e=acf.getPreference("dismissed-notices"),e&&"object"==typeof e||(e=[]),e.push(t(this).closest(".acf-admin-notice").data("persist-id")),acf.setPreference("dismissed-notices",e)}))}})}})},4204:()=>{var t;t=jQuery,acf.models.Popup=acf.Model.extend({data:{title:"",content:"",width:0,height:0,loading:!1,openedBy:null},events:{'click [data-event="close"]':"onClickClose","click .acf-close-popup":"onClickClose",keydown:"onPressEscapeClose"},setup:function(e){t.extend(this.data,e),this.$el=t(this.tmpl())},initialize:function(){this.render(),this.open(),this.focus(),this.lockFocusToPopup(!0)},tmpl:function(){return['<div id="acf-popup" role="dialog" tabindex="-1">','<div class="acf-popup-box acf-box">','<div class="title"><h3></h3><a href="#" class="acf-icon -cancel grey" data-event="close" aria-label="'+acf.__("Close modal")+'"></a></div>','<div class="inner"></div>','<div class="loading"><i class="acf-loading"></i></div>',"</div>",'<div class="bg" data-event="close"></div>',"</div>"].join("")},render:function(){var t=this.get("title"),e=this.get("content"),n=this.get("loading"),i=this.get("width"),o=this.get("height");this.title(t),this.content(e),i&&this.$(".acf-popup-box").css("width",i),o&&this.$(".acf-popup-box").css("min-height",o),this.loading(n),acf.doAction("append",this.$el)},focus:function(){this.$el.find(".acf-icon").first().trigger("focus")},lockFocusToPopup:function(e){let n=t("#wpwrap");n.length&&(n[0].inert=e,n.attr("aria-hidden",e))},update:function(t){this.data=acf.parseArgs(t,this.data),this.render()},title:function(t){this.$(".title:first h3").html(t)},content:function(t){this.$(".inner:first").html(t)},loading:function(t){var e=this.$(".loading:first");t?e.show():e.hide()},open:function(){t("body").append(this.$el)},close:function(){this.lockFocusToPopup(!1),this.returnFocusToOrigin(),this.remove()},onClickClose:function(t,e){t.preventDefault(),this.close()},onPressEscapeClose:function(t){"Escape"===t.key&&this.close()},returnFocusToOrigin:function(){this.data.openedBy instanceof t&&this.data.openedBy.closest("body").length>0&&this.data.openedBy.trigger("focus")}}),acf.newPopup=function(t){return new acf.models.Popup(t)}},5454:t=>{"use strict";const{entries:e,setPrototypeOf:n,isFrozen:i,getPrototypeOf:o,getOwnPropertyDescriptor:r}=Object;let{freeze:a,seal:s,create:c}=Object,{apply:l,construct:u}="undefined"!=typeof Reflect&&Reflect;a||(a=function(t){return t}),s||(s=function(t){return t}),l||(l=function(t,e,n){return t.apply(e,n)}),u||(u=function(t,e){return new t(...e)});const f=C(Array.prototype.forEach),d=C(Array.prototype.lastIndexOf),p=C(Array.prototype.pop),h=C(Array.prototype.push),m=C(Array.prototype.splice),g=C(String.prototype.toLowerCase),v=C(String.prototype.toString),y=C(String.prototype.match),A=C(String.prototype.replace),T=C(String.prototype.indexOf),b=C(String.prototype.trim),w=C(Object.prototype.hasOwnProperty),E=C(RegExp.prototype.test),x=(_=TypeError,function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return u(_,e)});var _;function C(t){return function(e){e instanceof RegExp&&(e.lastIndex=0);for(var n=arguments.length,i=new Array(n>1?n-1:0),o=1;o<n;o++)i[o-1]=arguments[o];return l(t,e,i)}}function S(t,e){let o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:g;n&&n(t,null);let r=e.length;for(;r--;){let n=e[r];if("string"==typeof n){const t=o(n);t!==n&&(i(e)||(e[r]=t),n=t)}t[n]=!0}return t}function k(t){for(let e=0;e<t.length;e++)w(t,e)||(t[e]=null);return t}function N(t){const n=c(null);for(const[i,o]of e(t))w(t,i)&&(Array.isArray(o)?n[i]=k(o):o&&"object"==typeof o&&o.constructor===Object?n[i]=N(o):n[i]=o);return n}function O(t,e){for(;null!==t;){const n=r(t,e);if(n){if(n.get)return C(n.get);if("function"==typeof n.value)return C(n.value)}t=o(t)}return function(){return null}}const L=a(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),R=a(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),I=a(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),D=a(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),M=a(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),F=a(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),z=a(["#text"]),U=a(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),P=a(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),j=a(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),H=a(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),$=s(/\{\{[\w\W]*|[\w\W]*\}\}/gm),B=s(/<%[\w\W]*|[\w\W]*%>/gm),W=s(/\$\{[\w\W]*/gm),G=s(/^data-[\-\w.\u00B7-\uFFFF]+$/),Y=s(/^aria-[\-\w]+$/),q=s(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp|matrix):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),Q=s(/^(?:\w+script|data):/i),X=s(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),J=s(/^html$/i),V=s(/^[a-z][.\w]*(-[.\w]+)+$/i);var K=Object.freeze({__proto__:null,ARIA_ATTR:Y,ATTR_WHITESPACE:X,CUSTOM_ELEMENT:V,DATA_ATTR:G,DOCTYPE_NAME:J,ERB_EXPR:B,IS_ALLOWED_URI:q,IS_SCRIPT_OR_DATA:Q,MUSTACHE_EXPR:$,TMPLIT_EXPR:W});const Z=function(){return"undefined"==typeof window?null:window};var tt=function t(){let n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Z();const i=e=>t(e);if(i.version="3.2.6",i.removed=[],!n||!n.document||9!==n.document.nodeType||!n.Element)return i.isSupported=!1,i;let{document:o}=n;const r=o,s=r.currentScript,{DocumentFragment:l,HTMLTemplateElement:u,Node:_,Element:C,NodeFilter:k,NamedNodeMap:$=n.NamedNodeMap||n.MozNamedAttrMap,HTMLFormElement:B,DOMParser:W,trustedTypes:G}=n,Y=C.prototype,Q=O(Y,"cloneNode"),X=O(Y,"remove"),V=O(Y,"nextSibling"),tt=O(Y,"childNodes"),et=O(Y,"parentNode");if("function"==typeof u){const t=o.createElement("template");t.content&&t.content.ownerDocument&&(o=t.content.ownerDocument)}let nt,it="";const{implementation:ot,createNodeIterator:rt,createDocumentFragment:at,getElementsByTagName:st}=o,{importNode:ct}=r;let lt={afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]};i.isSupported="function"==typeof e&&"function"==typeof et&&ot&&void 0!==ot.createHTMLDocument;const{MUSTACHE_EXPR:ut,ERB_EXPR:ft,TMPLIT_EXPR:dt,DATA_ATTR:pt,ARIA_ATTR:ht,IS_SCRIPT_OR_DATA:mt,ATTR_WHITESPACE:gt,CUSTOM_ELEMENT:vt}=K;let{IS_ALLOWED_URI:yt}=K,At=null;const Tt=S({},[...L,...R,...I,...M,...z]);let bt=null;const wt=S({},[...U,...P,...j,...H]);let Et=Object.seal(c(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),xt=null,_t=null,Ct=!0,St=!0,kt=!1,Nt=!0,Ot=!1,Lt=!0,Rt=!1,It=!1,Dt=!1,Mt=!1,Ft=!1,zt=!1,Ut=!0,Pt=!1,jt=!0,Ht=!1,$t={},Bt=null;const Wt=S({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let Gt=null;const Yt=S({},["audio","video","img","source","image","track"]);let qt=null;const Qt=S({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),Xt="http://www.w3.org/1998/Math/MathML",Jt="http://www.w3.org/2000/svg",Vt="http://www.w3.org/1999/xhtml";let Kt=Vt,Zt=!1,te=null;const ee=S({},[Xt,Jt,Vt],v);let ne=S({},["mi","mo","mn","ms","mtext"]),ie=S({},["annotation-xml"]);const oe=S({},["title","style","font","a","script"]);let re=null;const ae=["application/xhtml+xml","text/html"];let se=null,ce=null;const le=o.createElement("form"),ue=function(t){return t instanceof RegExp||t instanceof Function},fe=function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!ce||ce!==t){if(t&&"object"==typeof t||(t={}),t=N(t),re=-1===ae.indexOf(t.PARSER_MEDIA_TYPE)?"text/html":t.PARSER_MEDIA_TYPE,se="application/xhtml+xml"===re?v:g,At=w(t,"ALLOWED_TAGS")?S({},t.ALLOWED_TAGS,se):Tt,bt=w(t,"ALLOWED_ATTR")?S({},t.ALLOWED_ATTR,se):wt,te=w(t,"ALLOWED_NAMESPACES")?S({},t.ALLOWED_NAMESPACES,v):ee,qt=w(t,"ADD_URI_SAFE_ATTR")?S(N(Qt),t.ADD_URI_SAFE_ATTR,se):Qt,Gt=w(t,"ADD_DATA_URI_TAGS")?S(N(Yt),t.ADD_DATA_URI_TAGS,se):Yt,Bt=w(t,"FORBID_CONTENTS")?S({},t.FORBID_CONTENTS,se):Wt,xt=w(t,"FORBID_TAGS")?S({},t.FORBID_TAGS,se):N({}),_t=w(t,"FORBID_ATTR")?S({},t.FORBID_ATTR,se):N({}),$t=!!w(t,"USE_PROFILES")&&t.USE_PROFILES,Ct=!1!==t.ALLOW_ARIA_ATTR,St=!1!==t.ALLOW_DATA_ATTR,kt=t.ALLOW_UNKNOWN_PROTOCOLS||!1,Nt=!1!==t.ALLOW_SELF_CLOSE_IN_ATTR,Ot=t.SAFE_FOR_TEMPLATES||!1,Lt=!1!==t.SAFE_FOR_XML,Rt=t.WHOLE_DOCUMENT||!1,Mt=t.RETURN_DOM||!1,Ft=t.RETURN_DOM_FRAGMENT||!1,zt=t.RETURN_TRUSTED_TYPE||!1,Dt=t.FORCE_BODY||!1,Ut=!1!==t.SANITIZE_DOM,Pt=t.SANITIZE_NAMED_PROPS||!1,jt=!1!==t.KEEP_CONTENT,Ht=t.IN_PLACE||!1,yt=t.ALLOWED_URI_REGEXP||q,Kt=t.NAMESPACE||Vt,ne=t.MATHML_TEXT_INTEGRATION_POINTS||ne,ie=t.HTML_INTEGRATION_POINTS||ie,Et=t.CUSTOM_ELEMENT_HANDLING||{},t.CUSTOM_ELEMENT_HANDLING&&ue(t.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(Et.tagNameCheck=t.CUSTOM_ELEMENT_HANDLING.tagNameCheck),t.CUSTOM_ELEMENT_HANDLING&&ue(t.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(Et.attributeNameCheck=t.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),t.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof t.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(Et.allowCustomizedBuiltInElements=t.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),Ot&&(St=!1),Ft&&(Mt=!0),$t&&(At=S({},z),bt=[],!0===$t.html&&(S(At,L),S(bt,U)),!0===$t.svg&&(S(At,R),S(bt,P),S(bt,H)),!0===$t.svgFilters&&(S(At,I),S(bt,P),S(bt,H)),!0===$t.mathMl&&(S(At,M),S(bt,j),S(bt,H))),t.ADD_TAGS&&(At===Tt&&(At=N(At)),S(At,t.ADD_TAGS,se)),t.ADD_ATTR&&(bt===wt&&(bt=N(bt)),S(bt,t.ADD_ATTR,se)),t.ADD_URI_SAFE_ATTR&&S(qt,t.ADD_URI_SAFE_ATTR,se),t.FORBID_CONTENTS&&(Bt===Wt&&(Bt=N(Bt)),S(Bt,t.FORBID_CONTENTS,se)),jt&&(At["#text"]=!0),Rt&&S(At,["html","head","body"]),At.table&&(S(At,["tbody"]),delete xt.tbody),t.TRUSTED_TYPES_POLICY){if("function"!=typeof t.TRUSTED_TYPES_POLICY.createHTML)throw x('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if("function"!=typeof t.TRUSTED_TYPES_POLICY.createScriptURL)throw x('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');nt=t.TRUSTED_TYPES_POLICY,it=nt.createHTML("")}else void 0===nt&&(nt=function(t,e){if("object"!=typeof t||"function"!=typeof t.createPolicy)return null;let n=null;const i="data-tt-policy-suffix";e&&e.hasAttribute(i)&&(n=e.getAttribute(i));const o="dompurify"+(n?"#"+n:"");try{return t.createPolicy(o,{createHTML:t=>t,createScriptURL:t=>t})}catch(t){return console.warn("TrustedTypes policy "+o+" could not be created."),null}}(G,s)),null!==nt&&"string"==typeof it&&(it=nt.createHTML(""));a&&a(t),ce=t}},de=S({},[...R,...I,...D]),pe=S({},[...M,...F]),he=function(t){h(i.removed,{element:t});try{et(t).removeChild(t)}catch(e){X(t)}},me=function(t,e){try{h(i.removed,{attribute:e.getAttributeNode(t),from:e})}catch(t){h(i.removed,{attribute:null,from:e})}if(e.removeAttribute(t),"is"===t)if(Mt||Ft)try{he(e)}catch(t){}else try{e.setAttribute(t,"")}catch(t){}},ge=function(t){let e=null,n=null;if(Dt)t="<remove></remove>"+t;else{const e=y(t,/^[\r\n\t ]+/);n=e&&e[0]}"application/xhtml+xml"===re&&Kt===Vt&&(t='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+t+"</body></html>");const i=nt?nt.createHTML(t):t;if(Kt===Vt)try{e=(new W).parseFromString(i,re)}catch(t){}if(!e||!e.documentElement){e=ot.createDocument(Kt,"template",null);try{e.documentElement.innerHTML=Zt?it:i}catch(t){}}const r=e.body||e.documentElement;return t&&n&&r.insertBefore(o.createTextNode(n),r.childNodes[0]||null),Kt===Vt?st.call(e,Rt?"html":"body")[0]:Rt?e.documentElement:r},ve=function(t){return rt.call(t.ownerDocument||t,t,k.SHOW_ELEMENT|k.SHOW_COMMENT|k.SHOW_TEXT|k.SHOW_PROCESSING_INSTRUCTION|k.SHOW_CDATA_SECTION,null)},ye=function(t){return t instanceof B&&("string"!=typeof t.nodeName||"string"!=typeof t.textContent||"function"!=typeof t.removeChild||!(t.attributes instanceof $)||"function"!=typeof t.removeAttribute||"function"!=typeof t.setAttribute||"string"!=typeof t.namespaceURI||"function"!=typeof t.insertBefore||"function"!=typeof t.hasChildNodes)},Ae=function(t){return"function"==typeof _&&t instanceof _};function Te(t,e,n){f(t,t=>{t.call(i,e,n,ce)})}const be=function(t){let e=null;if(Te(lt.beforeSanitizeElements,t,null),ye(t))return he(t),!0;const n=se(t.nodeName);if(Te(lt.uponSanitizeElement,t,{tagName:n,allowedTags:At}),Lt&&t.hasChildNodes()&&!Ae(t.firstElementChild)&&E(/<[/\w!]/g,t.innerHTML)&&E(/<[/\w!]/g,t.textContent))return he(t),!0;if(7===t.nodeType)return he(t),!0;if(Lt&&8===t.nodeType&&E(/<[/\w]/g,t.data))return he(t),!0;if(!At[n]||xt[n]){if(!xt[n]&&Ee(n)){if(Et.tagNameCheck instanceof RegExp&&E(Et.tagNameCheck,n))return!1;if(Et.tagNameCheck instanceof Function&&Et.tagNameCheck(n))return!1}if(jt&&!Bt[n]){const e=et(t)||t.parentNode,n=tt(t)||t.childNodes;if(n&&e)for(let i=n.length-1;i>=0;--i){const o=Q(n[i],!0);o.__removalCount=(t.__removalCount||0)+1,e.insertBefore(o,V(t))}}return he(t),!0}return t instanceof C&&!function(t){let e=et(t);e&&e.tagName||(e={namespaceURI:Kt,tagName:"template"});const n=g(t.tagName),i=g(e.tagName);return!!te[t.namespaceURI]&&(t.namespaceURI===Jt?e.namespaceURI===Vt?"svg"===n:e.namespaceURI===Xt?"svg"===n&&("annotation-xml"===i||ne[i]):Boolean(de[n]):t.namespaceURI===Xt?e.namespaceURI===Vt?"math"===n:e.namespaceURI===Jt?"math"===n&&ie[i]:Boolean(pe[n]):t.namespaceURI===Vt?!(e.namespaceURI===Jt&&!ie[i])&&!(e.namespaceURI===Xt&&!ne[i])&&!pe[n]&&(oe[n]||!de[n]):!("application/xhtml+xml"!==re||!te[t.namespaceURI]))}(t)?(he(t),!0):"noscript"!==n&&"noembed"!==n&&"noframes"!==n||!E(/<\/no(script|embed|frames)/i,t.innerHTML)?(Ot&&3===t.nodeType&&(e=t.textContent,f([ut,ft,dt],t=>{e=A(e,t," ")}),t.textContent!==e&&(h(i.removed,{element:t.cloneNode()}),t.textContent=e)),Te(lt.afterSanitizeElements,t,null),!1):(he(t),!0)},we=function(t,e,n){if(Ut&&("id"===e||"name"===e)&&(n in o||n in le))return!1;if(St&&!_t[e]&&E(pt,e));else if(Ct&&E(ht,e));else if(!bt[e]||_t[e]){if(!(Ee(t)&&(Et.tagNameCheck instanceof RegExp&&E(Et.tagNameCheck,t)||Et.tagNameCheck instanceof Function&&Et.tagNameCheck(t))&&(Et.attributeNameCheck instanceof RegExp&&E(Et.attributeNameCheck,e)||Et.attributeNameCheck instanceof Function&&Et.attributeNameCheck(e))||"is"===e&&Et.allowCustomizedBuiltInElements&&(Et.tagNameCheck instanceof RegExp&&E(Et.tagNameCheck,n)||Et.tagNameCheck instanceof Function&&Et.tagNameCheck(n))))return!1}else if(qt[e]);else if(E(yt,A(n,gt,"")));else if("src"!==e&&"xlink:href"!==e&&"href"!==e||"script"===t||0!==T(n,"data:")||!Gt[t])if(kt&&!E(mt,A(n,gt,"")));else if(n)return!1;return!0},Ee=function(t){return"annotation-xml"!==t&&y(t,vt)},xe=function(t){Te(lt.beforeSanitizeAttributes,t,null);const{attributes:e}=t;if(!e||ye(t))return;const n={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:bt,forceKeepAttr:void 0};let o=e.length;for(;o--;){const r=e[o],{name:a,namespaceURI:s,value:c}=r,l=se(a),u=c;let d="value"===a?u:b(u);if(n.attrName=l,n.attrValue=d,n.keepAttr=!0,n.forceKeepAttr=void 0,Te(lt.uponSanitizeAttribute,t,n),d=n.attrValue,!Pt||"id"!==l&&"name"!==l||(me(a,t),d="user-content-"+d),Lt&&E(/((--!?|])>)|<\/(style|title)/i,d)){me(a,t);continue}if(n.forceKeepAttr)continue;if(!n.keepAttr){me(a,t);continue}if(!Nt&&E(/\/>/i,d)){me(a,t);continue}Ot&&f([ut,ft,dt],t=>{d=A(d,t," ")});const h=se(t.nodeName);if(we(h,l,d)){if(nt&&"object"==typeof G&&"function"==typeof G.getAttributeType)if(s);else switch(G.getAttributeType(h,l)){case"TrustedHTML":d=nt.createHTML(d);break;case"TrustedScriptURL":d=nt.createScriptURL(d)}if(d!==u)try{s?t.setAttributeNS(s,a,d):t.setAttribute(a,d),ye(t)?he(t):p(i.removed)}catch(e){me(a,t)}}else me(a,t)}Te(lt.afterSanitizeAttributes,t,null)},_e=function t(e){let n=null;const i=ve(e);for(Te(lt.beforeSanitizeShadowDOM,e,null);n=i.nextNode();)Te(lt.uponSanitizeShadowNode,n,null),be(n),xe(n),n.content instanceof l&&t(n.content);Te(lt.afterSanitizeShadowDOM,e,null)};return i.sanitize=function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=null,o=null,a=null,s=null;if(Zt=!t,Zt&&(t="\x3c!--\x3e"),"string"!=typeof t&&!Ae(t)){if("function"!=typeof t.toString)throw x("toString is not a function");if("string"!=typeof(t=t.toString()))throw x("dirty is not a string, aborting")}if(!i.isSupported)return t;if(It||fe(e),i.removed=[],"string"==typeof t&&(Ht=!1),Ht){if(t.nodeName){const e=se(t.nodeName);if(!At[e]||xt[e])throw x("root node is forbidden and cannot be sanitized in-place")}}else if(t instanceof _)n=ge("\x3c!----\x3e"),o=n.ownerDocument.importNode(t,!0),1===o.nodeType&&"BODY"===o.nodeName||"HTML"===o.nodeName?n=o:n.appendChild(o);else{if(!Mt&&!Ot&&!Rt&&-1===t.indexOf("<"))return nt&&zt?nt.createHTML(t):t;if(n=ge(t),!n)return Mt?null:zt?it:""}n&&Dt&&he(n.firstChild);const c=ve(Ht?t:n);for(;a=c.nextNode();)be(a),xe(a),a.content instanceof l&&_e(a.content);if(Ht)return t;if(Mt){if(Ft)for(s=at.call(n.ownerDocument);n.firstChild;)s.appendChild(n.firstChild);else s=n;return(bt.shadowroot||bt.shadowrootmode)&&(s=ct.call(r,s,!0)),s}let u=Rt?n.outerHTML:n.innerHTML;return Rt&&At["!doctype"]&&n.ownerDocument&&n.ownerDocument.doctype&&n.ownerDocument.doctype.name&&E(J,n.ownerDocument.doctype.name)&&(u="<!DOCTYPE "+n.ownerDocument.doctype.name+">\n"+u),Ot&&f([ut,ft,dt],t=>{u=A(u,t," ")}),nt&&zt?nt.createHTML(u):u},i.setConfig=function(){fe(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}),It=!0},i.clearConfig=function(){ce=null,It=!1},i.isValidAttribute=function(t,e,n){ce||fe({});const i=se(t),o=se(e);return we(i,o,n)},i.addHook=function(t,e){"function"==typeof e&&h(lt[t],e)},i.removeHook=function(t,e){if(void 0!==e){const n=d(lt[t],e);return-1===n?void 0:m(lt[t],n,1)[0]}return p(lt[t])},i.removeHooks=function(t){lt[t]=[]},i.removeAllHooks=function(){lt={afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}},i}();t.exports=tt},5751:()=>{var t;t=jQuery,acf.models.Modal=acf.Model.extend({data:{title:"",content:"",toolbar:""},events:{"click .acf-modal-close":"onClickClose"},setup:function(e){t.extend(this.data,e),this.$el=t(),this.render()},initialize:function(){this.open()},render:function(){var e=this.get("title"),n=this.get("content"),i=this.get("toolbar"),o=t(["<div>",'<div class="acf-modal">','<div class="acf-modal-title">',"<h2>"+e+"</h2>",'<button class="acf-modal-close" type="button"><span class="dashicons dashicons-no"></span></button>',"</div>",'<div class="acf-modal-content">'+n+"</div>",'<div class="acf-modal-toolbar">'+i+"</div>","</div>",'<div class="acf-modal-backdrop acf-modal-close"></div>',"</div>"].join(""));this.$el&&this.$el.replaceWith(o),this.$el=o,acf.doAction("append",o)},update:function(t){this.data=acf.parseArgs(t,this.data),this.render()},title:function(t){this.$(".acf-modal-title h2").html(t)},content:function(t){this.$(".acf-modal-content").html(t)},toolbar:function(t){this.$(".acf-modal-toolbar").html(t)},open:function(){t("body").append(this.$el)},close:function(){this.remove()},onClickClose:function(t,e){t.preventDefault(),this.close()},focus:function(){this.$el.find(".acf-icon").first().trigger("focus")},lockFocusToModal:function(e){let n=t("#wpwrap");n.length&&(n[0].inert=e,n.attr("aria-hidden",e))},returnFocusToOrigin:function(){this.data.openedBy instanceof t&&this.data.openedBy.closest("body").length>0&&this.data.openedBy.trigger("focus")}}),acf.newModal=function(t){return new acf.models.Modal(t)}},6047:(t,e,n)=>{!function(t,e){var i={};window.acf=i,i.data={},i.get=function(t){return this.data[t]||null},i.has=function(t){return null!==this.get(t)},i.set=function(t,e){return this.data[t]=e,this};var o=0;i.uniqueId=function(t){var e=++o+"";return t?t+e:e},i.uniqueArray=function(t){return t.filter(function(t,e,n){return n.indexOf(t)===e})};var r="";i.uniqid=function(t,e){var n;void 0===t&&(t="");var i=function(t,e){return e<(t=parseInt(t,10).toString(16)).length?t.slice(t.length-e):e>t.length?Array(e-t.length+1).join("0")+t:t};return r||(r=Math.floor(123456789*Math.random())),r++,n=t,n+=i(parseInt((new Date).getTime()/1e3,10),8),n+=i(r,5),e&&(n+=(10*Math.random()).toFixed(8).toString()),n},i.strReplace=function(t,e,n){return n.split(t).join(e)},i.strCamelCase=function(t){var e=t.match(/([a-zA-Z0-9]+)/g);return e?e.map(function(t,e){var n=t.charAt(0);return(0===e?n.toLowerCase():n.toUpperCase())+t.slice(1)}).join(""):""},i.strPascalCase=function(t){var e=i.strCamelCase(t);return e.charAt(0).toUpperCase()+e.slice(1)},i.strSlugify=function(t){return i.strReplace("_","-",t.toLowerCase())},i.strSanitize=function(t,n=!0){var i={À:"A",Á:"A",Â:"A",Ã:"A",Ä:"A",Å:"A",Æ:"AE",Ç:"C",È:"E",É:"E",Ê:"E",Ë:"E",Ì:"I",Í:"I",Î:"I",Ï:"I",Ð:"D",Ñ:"N",Ò:"O",Ó:"O",Ô:"O",Õ:"O",Ö:"O",Ø:"O",Ù:"U",Ú:"U",Û:"U",Ü:"U",Ý:"Y",ß:"s",à:"a",á:"a",â:"a",ã:"a",ä:"a",å:"a",æ:"ae",ç:"c",è:"e",é:"e",ê:"e",ë:"e",ì:"i",í:"i",î:"i",ï:"i",ñ:"n",ò:"o",ó:"o",ô:"o",õ:"o",ö:"o",ø:"o",ù:"u",ú:"u",û:"u",ü:"u",ý:"y",ÿ:"y",Ā:"A",ā:"a",Ă:"A",ă:"a",Ą:"A",ą:"a",Ć:"C",ć:"c",Ĉ:"C",ĉ:"c",Ċ:"C",ċ:"c",Č:"C",č:"c",Ď:"D",ď:"d",Đ:"D",đ:"d",Ē:"E",ē:"e",Ĕ:"E",ĕ:"e",Ė:"E",ė:"e",Ę:"E",ę:"e",Ě:"E",ě:"e",Ĝ:"G",ĝ:"g",Ğ:"G",ğ:"g",Ġ:"G",ġ:"g",Ģ:"G",ģ:"g",Ĥ:"H",ĥ:"h",Ħ:"H",ħ:"h",Ĩ:"I",ĩ:"i",Ī:"I",ī:"i",Ĭ:"I",ĭ:"i",Į:"I",į:"i",İ:"I",ı:"i",Ĳ:"IJ",ĳ:"ij",Ĵ:"J",ĵ:"j",Ķ:"K",ķ:"k",Ĺ:"L",ĺ:"l",Ļ:"L",ļ:"l",Ľ:"L",ľ:"l",Ŀ:"L",ŀ:"l",Ł:"l",ł:"l",Ń:"N",ń:"n",Ņ:"N",ņ:"n",Ň:"N",ň:"n",ŉ:"n",Ō:"O",ō:"o",Ŏ:"O",ŏ:"o",Ő:"O",ő:"o",Œ:"OE",œ:"oe",Ŕ:"R",ŕ:"r",Ŗ:"R",ŗ:"r",Ř:"R",ř:"r",Ś:"S",ś:"s",Ŝ:"S",ŝ:"s",Ş:"S",ş:"s",Š:"S",š:"s",Ţ:"T",ţ:"t",Ť:"T",ť:"t",Ŧ:"T",ŧ:"t",Ũ:"U",ũ:"u",Ū:"U",ū:"u",Ŭ:"U",ŭ:"u",Ů:"U",ů:"u",Ű:"U",ű:"u",Ų:"U",ų:"u",Ŵ:"W",ŵ:"w",Ŷ:"Y",ŷ:"y",Ÿ:"Y",Ź:"Z",ź:"z",Ż:"Z",ż:"z",Ž:"Z",ž:"z",ſ:"s",ƒ:"f",Ơ:"O",ơ:"o",Ư:"U",ư:"u",Ǎ:"A",ǎ:"a",Ǐ:"I",ǐ:"i",Ǒ:"O",ǒ:"o",Ǔ:"U",ǔ:"u",Ǖ:"U",ǖ:"u",Ǘ:"U",ǘ:"u",Ǚ:"U",ǚ:"u",Ǜ:"U",ǜ:"u",Ǻ:"A",ǻ:"a",Ǽ:"AE",ǽ:"ae",Ǿ:"O",ǿ:"o"," ":"_","'":"","?":"","/":"","\\":"",".":"",",":"","`":"",">":"","<":"",'"':"","[":"","]":"","|":"","{":"","}":"","(":"",")":""};return t=t.replace(/\W/g,function(t){return i[t]!==e?i[t]:t}),n&&(t=t.toLowerCase()),t},i.strMatch=function(t,e){for(var n=0,i=Math.min(t.length,e.length),o=0;o<i&&t[o]===e[o];o++)n++;return n},i.strEscape=function(t){var e={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"};return(""+t).replace(/[&<>"']/g,function(t){return e[t]})},i.strUnescape=function(t){var e={"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"};return(""+t).replace(/&amp;|&lt;|&gt;|&quot;|&#39;/g,function(t){return e[t]})},i.escAttr=i.strEscape,i.escHtml=function(t){t=""+t;const e=n(5454);if(void 0===e||!e.isSupported)return console.warn("ACF: DOMPurify not loaded or not supported. Falling back to basic HTML escaping for security."),i.strEscape(t);const o=i.applyFilters("esc_html_dompurify_config",{USE_PROFILES:{html:!0},ADD_TAGS:["audio","video"],ADD_ATTR:["controls","loop","muted","preload"],FORBID_TAGS:["script","style","iframe","object","embed","base","meta","form"],FORBID_ATTR:["style","srcset","action","background","dynsrc","lowsrc","on*"],ALLOW_DATA_ATTR:!1,ALLOW_ARIA_ATTR:!1},t);return e.sanitize(t,o)},i.decode=function(e){return t("<textarea/>").html(e).text()},i.parseArgs=function(e,n){return"object"!=typeof e&&(e={}),"object"!=typeof n&&(n={}),t.extend({},n,e)},window.acfL10n==e&&(acfL10n={}),i.__=function(t){return acfL10n[t]||t},i._x=function(t,e){return acfL10n[t+"."+e]||acfL10n[t]||t},i._n=function(t,e,n){return 1==n?i.__(t):i.__(e)},i.isArray=function(t){return Array.isArray(t)},i.isObject=function(t){return"object"==typeof t};var a=function(t,e,n){var o=(e=e.replace("[]","[%%index%%]")).match(/([^\[\]])+/g);if(o)for(var r=o.length,a=t,s=0;s<r;s++){var c=String(o[s]);s==r-1?"%%index%%"===c?a.push(n):a[c]=n:("%%index%%"===o[s+1]?i.isArray(a[c])||(a[c]=[]):i.isObject(a[c])||(a[c]={}),a=a[c])}};i.serialize=function(t,n){var o={},r=i.serializeArray(t);n!==e&&(r=r.filter(function(t){return 0===t.name.indexOf(n)}).map(function(t){return t.name=t.name.slice(n.length),t}));for(var s=0;s<r.length;s++)a(o,r[s].name,r[s].value);return o},i.serializeArray=function(t){return t.find("select, textarea, input").serializeArray()},i.serializeForAjax=function(t){var e={};return i.serializeArray(t).map(function(t){"[]"===t.name.slice(-2)?(e[t.name]=e[t.name]||[],e[t.name].push(t.value)):e[t.name]=t.value}),e},i.addAction=function(){return i.hooks.addAction.apply(this,arguments),this},i.removeAction=function(){return i.hooks.removeAction.apply(this,arguments),this};var s={};i.doAction=function(t){return s[t]=1,i.hooks.doAction.apply(this,arguments),s[t]=0,this},i.doingAction=function(t){return 1===s[t]},i.didAction=function(t){return s[t]!==e},i.currentAction=function(){for(var t in s)if(s[t])return t;return!1},i.addFilter=function(){return i.hooks.addFilter.apply(this,arguments),this},i.removeFilter=function(){return i.hooks.removeFilter.apply(this,arguments),this},i.applyFilters=function(){return i.hooks.applyFilters.apply(this,arguments)},i.arrayArgs=function(t){return Array.prototype.slice.call(t)};try{var c=JSON.parse(localStorage.getItem("acf"))||{}}catch(t){c={}}var l=function(t){return"this."===t.substring(0,5)&&(t=t.substring(5)+"-"+i.get("post_id")),t};i.getPreference=function(t){return t=l(t),c[t]||null},i.setPreference=function(t,e){t=l(t),null===e?delete c[t]:c[t]=e,localStorage.setItem("acf",JSON.stringify(c))},i.removePreference=function(t){i.setPreference(t,null)},i.remove=function(t){t instanceof jQuery&&(t={target:t}),t=i.parseArgs(t,{target:!1,endHeight:0,complete:function(){}}),i.doAction("remove",t.target),t.target.is("tr")?f(t):u(t)};var u=function(t){var e=t.target,n=e.height(),i=e.width(),o=e.css("margin"),r=e.outerHeight(!0),a=e.attr("style")+"";e.wrap('<div class="acf-temp-remove" style="height:'+r+'px"></div>');var s=e.parent();e.css({height:n,width:i,margin:o,position:"absolute"}),setTimeout(function(){s.css({opacity:0,height:t.endHeight})},50),setTimeout(function(){e.attr("style",a),s.remove(),t.complete()},301)},f=function(e){var n=e.target,i=n.height(),o=n.children().length,r=t('<td class="acf-temp-remove" style="padding:0; height:'+i+'px" colspan="'+o+'"></td>');n.addClass("acf-remove-element"),setTimeout(function(){n.html(r)},251),setTimeout(function(){n.removeClass("acf-remove-element"),r.css({height:e.endHeight})},300),setTimeout(function(){n.remove(),e.complete()},451)};i.duplicate=function(e){e instanceof jQuery&&(e={target:e}),e=i.parseArgs(e,{target:!1,search:"",replace:"",rename:!0,before:function(t){},after:function(t,e){},append:function(t,e){t.after(e)}}),e.target=e.target||e.$el;var n=e.target;e.search=e.search||n.attr("data-id"),e.replace=e.replace||i.uniqid(),e.before(n),i.doAction("before_duplicate",n);var o=n.clone();return e.rename&&i.rename({target:o,search:e.search,replace:e.replace,replacer:"function"==typeof e.rename?e.rename:null}),o.removeClass("acf-clone"),o.find(".ui-sortable").removeClass("ui-sortable"),o.find("[data-select2-id]").removeAttr("data-select2-id"),o.find(".select2").remove(),o.find('.acf-is-subfields select[data-ui="1"]').each(function(){t(this).prop("id",t(this).prop("id").replace("acf_fields",i.uniqid("duplicated_")+"_acf_fields"))}),o.find(".acf-field-settings > .acf-tab-wrap").remove(),e.after(n,o),i.doAction("after_duplicate",n,o),e.append(n,o),i.doAction("duplicate",n,o),i.doAction("append",o),o},i.rename=function(t){t instanceof jQuery&&(t={target:t});var e=(t=i.parseArgs(t,{target:!1,destructive:!1,search:"",replace:"",replacer:null})).target;t.search||(t.search=e.attr("data-id")),t.replace||(t.replace=i.uniqid("acf")),t.replacer||(t.replacer=function(t,e,n,i){return e.replace(n,i)});var n=function(e){return function(n,i){return t.replacer(e,i,t.search,t.replace)}};if(t.destructive){var o=i.strReplace(t.search,t.replace,e.outerHTML());e.replaceWith(o)}else e.attr("data-id",t.replace),e.find('[id*="'+t.search+'"]').attr("id",n("id")),e.find('[for*="'+t.search+'"]').attr("for",n("for")),e.find('[name*="'+t.search+'"]').attr("name",n("name"));return e},i.prepareForAjax=function(t,e=!1){return(e||void 0===t.nonce)&&(t.nonce=i.get("nonce")),t.post_id=i.get("post_id"),i.has("language")&&(t.lang=i.get("language")),i.applyFilters("prepare_for_ajax",t)},i.startButtonLoading=function(t){t.prop("disabled",!0),t.after(' <i class="acf-loading"></i>')},i.stopButtonLoading=function(t){t.prop("disabled",!1),t.next(".acf-loading").remove()},i.showLoading=function(t){t.append('<div class="acf-loading-overlay"><i class="acf-loading"></i></div>')},i.hideLoading=function(t){t.children(".acf-loading-overlay").remove()},i.updateUserSetting=function(e,n){var o={action:"acf/ajax/user_setting",name:e,value:n};t.ajax({url:i.get("ajaxurl"),data:i.prepareForAjax(o),type:"post",dataType:"html"})},i.val=function(t,e,n){var i=t.val();return e!==i&&(t.val(e),t.is("select")&&null===t.val()?(t.val(i),!1):(!0!==n&&t.trigger("change"),!0))},i.show=function(t,e){return e&&i.unlock(t,"hidden",e),!i.isLocked(t,"hidden")&&!!t.hasClass("acf-hidden")&&(t.removeClass("acf-hidden"),!0)},i.hide=function(t,e){return e&&i.lock(t,"hidden",e),!t.hasClass("acf-hidden")&&(t.addClass("acf-hidden"),!0)},i.isHidden=function(t){return t.hasClass("acf-hidden")},i.isVisible=function(t){return!i.isHidden(t)};var d=function(t,e){return!(t.hasClass("acf-disabled")||(e&&i.unlock(t,"disabled",e),i.isLocked(t,"disabled")||!t.prop("disabled")||(t.prop("disabled",!1),0)))};i.enable=function(e,n){if(e.attr("name"))return d(e,n);var i=!1;return e.find("[name]").each(function(){d(t(this),n)&&(i=!0)}),i};var p=function(t,e){return e&&i.lock(t,"disabled",e),!t.prop("disabled")&&(t.prop("disabled",!0),!0)};i.disable=function(e,n){if(e.attr("name"))return p(e,n);var i=!1;return e.find("[name]").each(function(){p(t(this),n)&&(i=!0)}),i},i.isset=function(t){for(var e=1;e<arguments.length;e++){if(!t||!t.hasOwnProperty(arguments[e]))return!1;t=t[arguments[e]]}return!0},i.isget=function(t){for(var e=1;e<arguments.length;e++){if(!t||!t.hasOwnProperty(arguments[e]))return null;t=t[arguments[e]]}return t},i.getFileInputData=function(t,e){var n=t.val();if(!n)return!1;var o={url:n},r=!!t[0].files.length&&i.isget(t[0].files,0);if(r)if(o.size=r.size,o.type=r.type,r.type.indexOf("image")>-1){var a=window.URL||window.webkitURL,s=new Image;s.onload=function(){o.width=this.width,o.height=this.height,e(o)},s.src=a.createObjectURL(r)}else e(o);else e(o)},i.isAjaxSuccess=function(t){return t&&t.success},i.getAjaxMessage=function(t){return i.isget(t,"data","message")},i.getAjaxError=function(t){return i.isget(t,"data","error")},i.getXhrError=function(t){if(t.responseJSON){if(t.responseJSON.message)return t.responseJSON.message;if(t.responseJSON.data&&t.responseJSON.data.error)return t.responseJSON.data.error}else if(t.statusText)return t.statusText;return""},i.renderSelect=function(t,e){var n=t.val(),o=[],r=function(t){var e="";return t.map(function(t){var n=t.text||t.label||"",a=t.id||t.value||"";o.push(a),t.children?e+='<optgroup label="'+i.escAttr(n)+'">'+r(t.children)+"</optgroup>":e+='<option value="'+i.escAttr(a)+'"'+(t.disabled?' disabled="disabled"':"")+">"+i.strEscape(n)+"</option>"}),e};return t.html(r(e)),o.indexOf(n)>-1&&t.val(n),t.val()};var h,m,g,v,y,A=function(t,e){return t.data("acf-lock-"+e)||[]},T=function(t,e,n){t.data("acf-lock-"+e,n)};i.lock=function(t,e,n){var i=A(t,e);i.indexOf(n)<0&&(i.push(n),T(t,e,i))},i.unlock=function(t,e,n){var i=A(t,e),o=i.indexOf(n);return o>-1&&(i.splice(o,1),T(t,e,i)),0===i.length},i.isLocked=function(t,e){return A(t,e).length>0},i.isGutenberg=function(){return!!(window.wp&&wp.data&&wp.data.select&&wp.data.select("core/editor"))},i.isGutenbergPostEditor=function(){return!!(window.wp&&wp.data&&wp.data.select&&wp.data.select("core/edit-post"))},i.objectToArray=function(t){return Object.keys(t).map(function(e){return t[e]})},i.debounce=function(t,e){var n;return function(){var i=this,o=arguments;clearTimeout(n),n=setTimeout(function(){t.apply(i,o)},e)}},i.throttle=function(t,e){var n=!1;return function(){n||(n=!0,setTimeout(function(){n=!1},e),t.apply(this,arguments))}},i.isInView=function(t){t instanceof jQuery&&(t=t[0]);var e=t.getBoundingClientRect();return e.top!==e.bottom&&e.top>=0&&e.left>=0&&e.bottom<=(window.innerHeight||document.documentElement.clientHeight)&&e.right<=(window.innerWidth||document.documentElement.clientWidth)},i.onceInView=(h=[],m=0,g=function(){h.forEach(function(t){i.isInView(t.el)&&(t.callback.apply(this),y(t.id))})},v=i.debounce(g,300),y=function(e){(h=h.filter(function(t){return t.id!==e})).length||t(window).off("scroll resize",v).off("acfrefresh orientationchange",g)},function(e,n){e instanceof jQuery&&(e=e[0]),i.isInView(e)?n.apply(this):function(e,n){h.length||t(window).on("scroll resize",v).on("acfrefresh orientationchange",g),h.push({id:m++,el:e,callback:n})}(e,n)}),i.once=function(t){var n=0;return function(){return n++>0?t=e:t.apply(this,arguments)}},i.focusAttention=function(e){var n=1e3;e.addClass("acf-attention -focused"),i.isInView(e)||(t("body, html").animate({scrollTop:e.offset().top-t(window).height()/2},500),n+=500),setTimeout(function(){e.removeClass("-focused"),setTimeout(function(){e.removeClass("acf-attention")},250)},n)},i.onFocus=function(e,n){var i=!1,o=!1,r=function(){i=!0,setTimeout(function(){i=!1},1),s(!0)},a=function(){i||s(!1)},s=function(i){o!==i&&(i?(t(document).on("click",a),e.on("blur","input, select, textarea",a)):(t(document).off("click",a),e.off("blur","input, select, textarea",a)),o=i,n(i))};e.on("click",r),e.on("focus","input, select, textarea",r)},i.disableForm=function(t){t.submitter&&t.submitter.classList.add("disabled")},t.fn.exists=function(){return t(this).length>0},t.fn.outerHTML=function(){return t(this).get(0).outerHTML},Array.prototype.indexOf||(Array.prototype.indexOf=function(e){return t.inArray(e,this)}),i.isNumeric=function(t){return!isNaN(parseFloat(t))&&isFinite(t)},i.refresh=i.debounce(function(){t(window).trigger("acfrefresh"),i.doAction("refresh")},0),i.debug=function(){i.get("debug")&&console.log.apply(null,arguments)},t(document).ready(function(){i.doAction("ready")}),t(window).on("load",function(){setTimeout(function(){i.doAction("load")})}),t(window).on("beforeunload",function(){i.doAction("unload")}),t(window).on("resize",function(){i.doAction("resize")}),t(document).on("sortstart",function(t,e){i.doAction("sortstart",e.item,e.placeholder)}),t(document).on("sortstop",function(t,e){i.doAction("sortstop",e.item,e.placeholder)})}(jQuery)},6891:()=>{var t,e,n;t=jQuery,e=/^(\S+)\s*(.*)$/,n=acf.Model=function(){this.cid=acf.uniqueId("acf"),this.data=t.extend(!0,{},this.data),this.setup.apply(this,arguments),this.$el&&!this.$el.data("acf")&&this.$el.data("acf",this);var e=function(){this.initialize(),this.addEvents(),this.addActions(),this.addFilters()};this.wait&&!acf.didAction(this.wait)?this.addAction(this.wait,e):e.apply(this)},t.extend(n.prototype,{id:"",cid:"",$el:null,data:{},busy:!1,changed:!1,events:{},actions:{},filters:{},eventScope:"",wait:!1,priority:10,get:function(t){return this.data[t]},has:function(t){return null!=this.get(t)},set:function(t,e,n){var i=this.get(t);return i==e||(this.data[t]=e,n||(this.changed=!0,this.trigger("changed:"+t,[e,i]),this.trigger("changed",[t,e,i]))),this},inherit:function(e){return e instanceof jQuery&&(e=e.data()),t.extend(this.data,e),this},prop:function(){return this.$el.prop.apply(this.$el,arguments)},setup:function(e){t.extend(this,e)},initialize:function(){},addElements:function(t){if(!(t=t||this.elements||null)||!Object.keys(t).length)return!1;for(var e in t)this.addElement(e,t[e])},addElement:function(t,e){this["$"+t]=this.$(e)},addEvents:function(t){if(!(t=t||this.events||null))return!1;for(var n in t){var i=n.match(e);this.on(i[1],i[2],t[n])}},removeEvents:function(t){if(!(t=t||this.events||null))return!1;for(var n in t){var i=n.match(e);this.off(i[1],i[2],t[n])}},getEventTarget:function(e,n){return e||this.$el||t(document)},validateEvent:function(e){return!this.eventScope||t(e.target).closest(this.eventScope).is(this.$el)},proxyEvent:function(e){return this.proxy(function(n){if(this.validateEvent(n)){var i=acf.arrayArgs(arguments).slice(1),o=[n,t(n.currentTarget)].concat(i);e.apply(this,o)}})},on:function(t,e,n,i){var o,r,a,s,c;t instanceof jQuery?i?(o=t,r=e,a=n,s=i):(o=t,r=e,s=n):n?(r=t,a=e,s=n):(r=t,s=e),o=this.getEventTarget(o),"string"==typeof s&&(s=this.proxyEvent(this[s])),r=r+"."+this.cid,c=a?[r,a,s]:[r,s],o.on.apply(o,c)},off:function(t,e,n){var i,o,r,a;t instanceof jQuery?n?(i=t,o=e,r=n):(i=t,o=e):e?(o=t,r=e):o=t,i=this.getEventTarget(i),o=o+"."+this.cid,a=r?[o,r]:[o],i.off.apply(i,a)},trigger:function(t,e,n){var i=this.getEventTarget();return n?i.trigger.apply(i,arguments):i.triggerHandler.apply(i,arguments),this},addActions:function(t){if(!(t=t||this.actions||null))return!1;for(var e in t)this.addAction(e,t[e])},removeActions:function(t){if(!(t=t||this.actions||null))return!1;for(var e in t)this.removeAction(e,t[e])},addAction:function(t,e,n){n=n||this.priority,"string"==typeof e&&(e=this[e]),acf.addAction(t,e,n,this)},removeAction:function(t,e){acf.removeAction(t,this[e])},addFilters:function(t){if(!(t=t||this.filters||null))return!1;for(var e in t)this.addFilter(e,t[e])},addFilter:function(t,e,n){n=n||this.priority,"string"==typeof e&&(e=this[e]),acf.addFilter(t,e,n,this)},removeFilters:function(t){if(!(t=t||this.filters||null))return!1;for(var e in t)this.removeFilter(e,t[e])},removeFilter:function(t,e){acf.removeFilter(t,this[e])},$:function(t){return this.$el.find(t)},remove:function(){this.removeEvents(),this.removeActions(),this.removeFilters(),this.$el.remove()},setTimeout:function(t,e){return setTimeout(this.proxy(t),e)},time:function(){console.time(this.id||this.cid)},timeEnd:function(){console.timeEnd(this.id||this.cid)},show:function(){acf.show(this.$el)},hide:function(){acf.hide(this.$el)},proxy:function(e){return t.proxy(e,this)}}),n.extend=function(e){var n,i=this;return n=e&&e.hasOwnProperty("constructor")?e.constructor:function(){return i.apply(this,arguments)},t.extend(n,i),n.prototype=Object.create(i.prototype),t.extend(n.prototype,e),n.prototype.constructor=n,n},acf.models={},acf.getInstance=function(t){return t.data("acf")},acf.getInstances=function(e){var n=[];return e.each(function(){n.push(acf.getInstance(t(this)))}),n}},9340:()=>{jQuery,new acf.Model({events:{"click .acf-panel-title":"onClick"},onClick:function(t,e){t.preventDefault(),this.toggle(e.parent())},isOpen:function(t){return t.hasClass("-open")},toggle:function(t){this.isOpen(t)?this.close(t):this.open(t)},open:function(t){t.addClass("-open"),t.find(".acf-panel-title i").attr("class","dashicons dashicons-arrow-down")},close:function(t){t.removeClass("-open"),t.find(".acf-panel-title i").attr("class","dashicons dashicons-arrow-right")}})},9829:()=>{var t;t=jQuery,acf.copyable=new acf.Model({events:{"click .copyable":"copyCopyable"},wait:"ready",initialize:function(){if("undefined"==typeof pagenow)return;if(!["edit-acf-field-group","edit-acf-post-type","edit-acf-taxonomy","edit-acf-ui-options-page"].includes(pagenow))return;const e=t(document).find(".column-acf-key");e&&e.each(function(){t(this).html(acf.copyable.makeCopyable(t(this)))})},makeCopyable:function(t){const e=acf.strSanitize(t.text()),n=t.html();let i="copyable";return navigator.clipboard||(i=`${i} copy-unsupported`),n.replace(e,`<span class="${i}">${e}</span>`)},copyCopyable:function(e){if(e.stopPropagation(),!navigator.clipboard||t(e.target).is("input"))return;let n;n=t(e.target).hasClass("acf-input-wrap")?t(e.target).find("input").first().val():t(e.target).text().trim(),navigator.clipboard.writeText(n).then(()=>{t(e.target).closest(".copyable").addClass("copied"),setTimeout(function(){t(e.target).closest(".copyable").removeClass("copied")},2e3)})}})}},e={};function n(i){var o=e[i];if(void 0!==o)return o.exports;var r=e[i]={exports:{}};return t[i](r,r.exports,n),r.exports}n.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return n.d(e,{a:e}),e},n.d=(t,e)=>{for(var i in e)n.o(e,i)&&!n.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},n.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),(()=>{"use strict";n(6047),n(1018),n(6891),n(4204),n(5751),n(9340),n(2700),n(2177),n(9829)})()})();