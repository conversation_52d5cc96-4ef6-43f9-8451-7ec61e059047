# Custom Size Feature for WooCommerce

This implementation adds a custom size feature to WooCommerce product variations, allowing customers to specify custom measurements when standard sizes don't meet their needs.

## Features

- **Dynamic Form Display**: Custom size form appears only when "Custom Size" is selected from the size dropdown
- **Comprehensive Measurements**: Supports both shirt and trouser measurements with visual diagrams
- **Data Persistence**: Custom size data is saved and displayed in:
  - Cart items
  - Order details (admin and customer views)
  - Order confirmation emails
- **Validation**: Ensures at least one measurement is provided when custom size is selected
- **Responsive Design**: Works with both regular dropdowns and variation swatches

## Setup Instructions

### 1. Create Size Attribute
1. Go to **Products > Attributes** in WordPress admin
2. Create a new attribute called "Size" (slug: `pa_size`)
3. Add your standard size terms (S, M, L, XL, etc.)
4. Add a special term with slug `custom-size` and name "Custom Size"

### 2. Configure Products
1. Edit your variable products
2. Add the "Size" attribute to the product
3. Create variations for each size including the "Custom Size" option
4. Set appropriate pricing and stock for each variation

### 3. Test the Feature
1. Visit a product page with size variations
2. Select "Custom Size" from the size dropdown
3. The custom measurement form should appear
4. Fill in measurements and add to cart
5. Check cart and order details to verify data persistence

## Files Modified/Created

### Core Files
- `wp-content/themes/tahreemchaudhry/functions.php` - Backend functionality
- `wp-content/themes/tahreemchaudhry/woocommerce/content-single-product.php` - Frontend integration
- `wp-content/themes/tahreemchaudhry/template-parts/product/product-custom-size.php` - Custom size form

### Key Functions Added

#### Backend (functions.php)
- `woocommerce_dropdown_variation_attribute_options_html` filter - Adds "Custom Size" option
- `woocommerce_add_cart_item_data` filter - Saves custom measurements to cart
- `woocommerce_get_item_data` filter - Displays measurements in cart
- `woocommerce_add_order_item_meta` action - Saves measurements to orders

#### Frontend (content-single-product.php)
- JavaScript integration for showing/hiding custom size form
- Form validation to ensure measurements are provided
- Support for both dropdown and swatches variations

## Measurement Fields

### Shirt Measurements
- Shirt Length
- Shoulder
- Sleeve Length
- Wrist
- Chest
- Waist
- Hip
- Bottom
- Arm Hole

### Trouser Measurements
- Trouser Length
- Trouser Waist
- Trouser Hip
- Thigh
- Knee
- Trouser Bottom

## Customization

### Adding New Measurement Fields
1. Edit `template-parts/product/product-custom-size.php`
2. Add new input fields following the existing pattern
3. Update the field arrays in `functions.php` (lines 665-685)
4. Add corresponding JavaScript listeners for diagram labels

### Styling
Custom CSS is included in `content-single-product.php`. Modify the `<style>` section to match your theme's design.

### Validation Rules
Modify the validation function in `content-single-product.php` (lines 221-243) to add custom validation rules.

## Troubleshooting

### Custom Size Option Not Appearing
- Ensure the size attribute slug is exactly `pa_size`
- Verify "custom-size" term exists in the size attribute
- Check that the product has size variations configured

### Form Not Showing
- Verify JavaScript is loading correctly
- Check browser console for errors
- Ensure jQuery is available on the page

### Data Not Saving
- Check that form field names match the arrays in functions.php
- Verify WooCommerce hooks are firing correctly
- Test with different themes to rule out conflicts

## Browser Support
- Modern browsers with JavaScript enabled
- Responsive design works on mobile devices
- Graceful degradation for older browsers

## Performance Notes
- Form is loaded dynamically to avoid affecting page load times
- Measurements are only processed when custom size is selected
- Minimal impact on standard variation functionality

## Future Enhancements
- Add measurement unit selection (inches/cm)
- Include measurement guides/tooltips
- Add measurement validation ranges
- Support for additional garment types
- Integration with measurement conversion tools
