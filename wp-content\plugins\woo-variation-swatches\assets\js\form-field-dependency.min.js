var _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};!function(f){f.fn.FormFieldDependency=function(e){function c(e,t,a){if(null==e&&(e=[]),1==(a=void 0===a?!1:a))return e.sort().join(",").toLowerCase()==t.sort().join(",").toLowerCase();for(var s=0;s<e.length;s++)if(0<=t.indexOf(e[s]))return 1}function o(e,t){return 0<=f.inArray(e,t)&&f.isArray(t)}function u(e){if("null"==typeof e||void 0===e)return 1;if("string"==typeof e)return""==e.trim();if("object"==(void 0===e?"undefined":_typeof(e))){if(f.isArray(e)){var t=f.map(e,function(e,t){return""==e.trim()?null:e});return f.isEmptyObject(t)}return f.isEmptyObject(e)}}function p(t,a,s,e){if(void 0===e&&(e=!1),void 0===f(s).prop("tagName"))return!1;var n=f(s).prop("tagName").toLowerCase()+":"+f(s).prop("type").toLowerCase(),r=f(s).val().trim();switch(n){case"input:text":case"input:password":case"input:number":case"input:date":case"input:email":case"input:url":case"input:tel":case"textarea:textarea":var i=void 0===a.modifier?"":a.modifier;new RegExp(a.pattern,i).test(r)?f(t).show():f(t).hide()}e&&f(document.body).on("input",f(s),function(e){p(t,a,s,!1)})}function i(t,a,s,e){if(void 0===e&&(e=!1),void 0===f(s).prop("tagName"))return!1;var n=f(s).prop("tagName").toLowerCase()+":"+f(s).prop("type").toLowerCase(),r=f(s).val();switch(n){case"input:text":case"input:password":case"input:number":case"input:date":case"input:email":case"input:url":case"input:tel":case"textarea:textarea":case"select:select-one":""==r.trim()?f(t).show():f(t).hide();break;case"input:checkbox":f(s).is(":checked")&&""!=r.trim()?f(t).hide():f(t).show();break;case"select:select-multiple":u(r)?f(t).show():f(t).hide()}e&&f(document.body).on("input change",f(s),function(e){i(t,a,s,!1)})}function l(t,a,s,e){if(void 0===e&&(e=!1),void 0===f(s).prop("tagName"))return!1;var n=f(s).prop("tagName").toLowerCase()+":"+f(s).prop("type").toLowerCase(),r=f(s).val();switch(n){case"input:text":case"input:password":case"input:number":case"input:date":case"input:email":case"input:url":case"input:tel":case"textarea:textarea":case"select:select-one":""!=r.trim()?f(t).show():f(t).hide();break;case"input:checkbox":f(s).is(":checked")&&""!=r.trim()?f(t).show():f(t).hide();break;case"select:select-multiple":u(r)?f(t).hide():f(t).show()}e&&f(document.body).on("input change",f(s),function(e){l(t,a,s,!1)})}function h(t,a,s,e){if(void 0===e&&(e=!1),void 0===f(s).prop("tagName"))return!1;var n=f(s).prop("tagName").toLowerCase()+":"+f(s).prop("type").toLowerCase(),r=f(s).val(),i=void 0!==a.like;switch(a.empty=void 0!==a.empty&&a.empty,a.strict=void 0!==a.strict&&a.strict,i&&("input:checkbox"==(i=f(a.like).prop("tagName").toLowerCase()+":"+f(a.like).prop("type").toLowerCase())||"input:radio"==i?a.value=f(a.like+":checked").map(function(){return this.value}).get():(a.value=f(a.like).val(),showOnEmptyValue||(a.value=""==f(a.like).val().trim()?null:f(a.like).val()))),n){case"input:text":case"input:password":case"input:number":case"input:date":case"input:email":case"input:url":case"input:tel":case"textarea:textarea":case"select:select-one":r.trim()==a.value||o(r,a.value)||""==r.trim()&&a.empty?f(t).show():f(t).hide();break;case"input:checkbox":case"input:radio":(r=f(s+":checked").map(function(){return this.value}).get())==a.value||o(r,a.value)||c(r,a.value,a.strict)||u(r)&&a.empty?f(t).show():f(t).hide();break;case"select:select-multiple":c(r,a.value,a.strict)||null==r&&a.empty?f(t).show():f(t).hide()}e&&f(document.body).on("input change",f(s),function(e){h(t,a,s,!1)})}function d(t,a,s,e){if(void 0===e&&(e=!1),void 0===f(s).prop("tagName"))return!1;var n=f(s).prop("tagName").toLowerCase()+":"+f(s).prop("type").toLowerCase(),r=f(s).val(),i=void 0!==a.like;switch(a.strict=void 0!==a.strict&&a.strict,a.empty=void 0===a.empty||a.empty,i&&("input:checkbox"==(i=f(a.like).prop("tagName").toLowerCase()+":"+f(a.like).prop("type").toLowerCase())||"input:radio"==i?a.value=f(a.like+":checked").map(function(){return this.value}).get():(a.value=f(a.like).val(),showOnEmptyValue||(a.value=""==f(a.like).val().trim()?null:f(a.like).val()))),n){case"input:text":case"input:password":case"input:number":case"input:date":case"input:email":case"input:url":case"input:tel":case"textarea:textarea":case"select:select-one":r!=a.value&&!o(r,a.value)&&(""!=r.trim()||a.empty)?f(t).show():f(t).hide();break;case"input:checkbox":case"input:radio":r=f(s+":checked").map(function(){return this.value}).get(),void 0===a.strict&&(a.strict=!1),r==a.value||o(r,a.value)||c(r,a.value,a.strict)||u(r)&&!a.empty?f(t).hide():f(t).show();break;case"select:select-multiple":!c(r,a.value,a.strict)&&(null!=r||a.empty)?f(t).show():f(t).hide()}e&&f(document.body).on("input change",f(s),function(e){d(t,a,s,!1)})}function r(t,a,s,e){if(void 0===e&&(e=!1),void 0===f(s).prop("tagName"))return!1;f(s).prop("tagName").toLowerCase(),f(s).prop("type").toLowerCase();var n=parseInt(f(s).val());switch(a.value=parseInt(a.value),a.sign){case"<":case"lt":case"lessthen":case"less-then":case"LessThen":n<a.value?f(t).show():f(t).hide();break;case"<=":case"lteq":case"lessthenequal":case"less-then-equal":case"LessThenEqual":case"eqlt":n<=a.value?f(t).show():f(t).hide();break;case">=":case"gteq":case"greaterthenequal":case"greater-then-equal":case"GreaterThenEqual":case"eqgt":n>=a.value?f(t).show():f(t).hide();break;case">":case"gt":case"greaterthen":case"greater-then":case"GreaterThen":n>a.value?f(t).show():f(t).hide()}e&&f(document.body).on("input change",f(s),function(e){r(t,a,s,!1)})}function v(t,a,s,e){if(void 0===e&&(e=!1),void 0===f(s).prop("tagName"))return!1;f(s).prop("tagName").toLowerCase(),f(s).prop("type").toLowerCase();var n,r,i=parseInt(f(s).val());f.isArray(a.value)&&(n=parseInt(a.value[0]),r=parseInt(a.value[1])),void 0===a.value&&(n=parseInt(a.min),r=parseInt(a.max)),n<i&&i<r?f(t).show():f(t).hide(),e&&f(document.body).on("input change",f(s),function(e){v(t,a,s,!1)})}function m(t,a,s,e){if(void 0===e&&(e=!1),void 0===f(s).prop("tagName"))return!1;f(s).prop("tagName").toLowerCase(),f(s).prop("type").toLowerCase();var n=f(s).val().length;switch(a.value=parseInt(a.value),a.sign){case"<":case"lt":case"lessthen":case"less-then":case"LessThen":n<a.value?f(t).show():f(t).hide();break;case"<=":case"lteq":case"lessthenequal":case"less-then-equal":case"LessThenEqual":case"eqlt":n<=a.value?f(t).show():f(t).hide();break;case">=":case"gteq":case"greaterthenequal":case"greater-then-equal":case"GreaterThenEqual":case"eqgt":n>=a.value?f(t).show():f(t).hide();break;case">":case"gt":case"greaterthen":case"greater-then":case"GreaterThen":n>a.value?f(t).show():f(t).hide()}e&&f(document.body).on("input change",f(s),function(e){m(t,a,s,!1)})}function a(a,e){f.each(e,function(e,t){switch(t.type){case"empty":i(a,t,e,!0);break;case"notempty":case"not-empty":case"notEmpty":case"!empty":l(a,t,e,!0);break;case"equal":case"==":case"=":h(a,t,e,!0);break;case"!equal":case"notequal":case"!=":case"not-equal":case"notEqual":d(a,t,e,!0);break;case"regexp":case"expression":case"reg":case"exp":p(a,t,e,!0);break;case"compare":case"comp":r(a,t,e,!0);break;case"length":case"lng":m(a,t,e,!0);break;case"range":v(a,t,e,!0)}})}var t=f.extend({attribute:"wvsdepends",rules:{}},e);return e=t.rules,f.each(e,function(e,t){a(f(e),t)}),this.each(function(){var e=f(this).data(t.attribute.replace("data-","").trim());e&&(f(this).addClass("has-dependent-data"),f.each(e,function(e,t){a(f(this),t)}.bind(this)))})}}(jQuery);