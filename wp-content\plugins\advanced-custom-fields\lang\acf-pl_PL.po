# Advanced Custom Fields Translations are a combination of translate.wordpress.org contributions,
# combined with user contributed strings for the PRO version.
# Translations from translate.wordpress.org take priority over translations in this file.
# translate.wordpress.org contributions are synced at the time of each release.
#
# If you would like to contribute translations, please visit
# https://translate.wordpress.org/projects/wp-plugins/advanced-custom-fields/stable/
#
# For additional ACF PRO strings, please submit a pull request over on the ACF GitHub repo at
# http://github.com/advancedcustomfields/acf using the .pot (and any existing .po) files in /lang/pro/
#
# This file is distributed under the same license as Advanced Custom Fields.
msgid ""
msgstr ""
"PO-Revision-Date: 2025-07-21T18:29:30+00:00\n"
"Report-Msgid-Bugs-To: http://support.advancedcustomfields.com\n"
"Language: pl_PL\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: gettext\n"
"Project-Id-Version: Advanced Custom Fields\n"

#: includes/fields/class-acf-field-select.php:471
msgid ""
"Save created options back to the \"Choices\" setting in the field definition."
msgstr ""
"Zapisz utworzone opcje z powrotem w ustawieniu „Wybory” w definicji pola."

#: includes/fields/class-acf-field-select.php:470
msgid "Save Options"
msgstr "Zapisz opcje"

#: includes/fields/class-acf-field-select.php:448
msgid ""
"Allow content editors to create new options by typing in the Select input. "
"Multiple options can be created from a comma separated string."
msgstr ""
"Zezwól redaktorom treści na tworzenie nowych opcji poprzez wpisanie w polu "
"Wybierz. Wiele opcji można utworzyć z ciągu rozdzielonego przecinkami."

#: includes/fields/class-acf-field-select.php:447
msgid "Create Options"
msgstr "Utwórz opcje"

#: includes/admin/views/global/navigation.php:179
#: includes/admin/views/global/navigation.php:183
msgid "Edit ACF Field Groups"
msgstr "Edytuj grupy pól ACF"

#: includes/admin/views/global/navigation.php:100
msgid "Get 4 months free on any WP Engine plan"
msgstr "Otrzymaj 4 miesiące za darmo na dowolny plan WP Engine"

#: src/Site_Health/Site_Health.php:522
msgid "Number of Field Groups with Blocks and Other Locations"
msgstr "Liczba grup pól z blokami i innymi lokalizacjami"

#: src/Site_Health/Site_Health.php:517
msgid "Number of Field Groups with Multiple Block Locations"
msgstr "Liczba grup pól z wieloma lokalizacjami bloków"

#: src/Site_Health/Site_Health.php:512
msgid "Number of Field Groups with a Single Block Location"
msgstr "Liczba grup pól z pojedynczą lokalizacją bloku"

#: src/Site_Health/Site_Health.php:481
msgid "All Location Rules"
msgstr "Wszystkie zasady lokalizacji"

#: includes/validation.php:144
msgid "Learn more"
msgstr "Dowiedz się więcej"

#: includes/validation.php:133
msgid ""
"ACF was unable to perform validation because the provided nonce failed "
"verification."
msgstr ""
"ACF nie mógł wykonać walidacji, ponieważ podany kod jednorazowy nie "
"przeszedł weryfikacji."

#: includes/validation.php:131
msgid ""
"ACF was unable to perform validation because no nonce was received by the "
"server."
msgstr ""
"ACF nie mógł wykonać walidacji, ponieważ serwer nie otrzymał żadnego kodu "
"jednorazowego."

#. translators: This text is prepended by a link to ACF's website, and appended
#. by a link to WP Engine's website.
#: includes/admin/admin.php:324
msgid "are developed and maintained by"
msgstr "są rozwijane i utrzymywane przez"

#: src/Site_Health/Site_Health.php:295
msgid "Update Source"
msgstr "Zaktualizuj źródło"

#: includes/admin/views/acf-post-type/advanced-settings.php:850
#: includes/admin/views/acf-taxonomy/advanced-settings.php:810
msgid "By default only admin users can edit this setting."
msgstr "Domyślnie tylko administratorzy mogą edytować to ustawienie."

#: includes/admin/views/acf-post-type/advanced-settings.php:848
#: includes/admin/views/acf-taxonomy/advanced-settings.php:808
msgid "By default only super admin users can edit this setting."
msgstr "Domyślnie tylko superadministratorzy mogą edytować to ustawienie."

#: includes/admin/views/acf-field-group/field.php:322
msgid "Close and Add Field"
msgstr "Zamknij i dodaj pole"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:804
msgid ""
"A PHP function name to be called to handle the content of a meta box on your "
"taxonomy. For security, this callback will be executed in a special context "
"without access to any superglobals like $_POST or $_GET."
msgstr ""
"Nazwa funkcji PHP, która ma zostać wywołana w celu obsługi zawartości meta-"
"pola w Twojej taksonomii. Ze względów bezpieczeństwa to wywołanie zwrotne "
"zostanie wykonane w specjalnym kontekście bez dostępu do żadnych super "
"globalnych zmiennych, takich jak $_POST lub $_GET."

#: includes/admin/views/acf-post-type/advanced-settings.php:842
msgid ""
"A PHP function name to be called when setting up the meta boxes for the edit "
"screen. For security, this callback will be executed in a special context "
"without access to any superglobals like $_POST or $_GET."
msgstr ""
"Nazwa funkcji PHP, która ma zostać wywołana podczas konfigurowania pól meta "
"dla ekranu edycji. Ze względów bezpieczeństwa to wywołanie zwrotne zostanie "
"wykonane w specjalnym kontekście bez dostępu do żadnych super globalnych "
"zmiennych, takich jak $_POST lub $_GET."

#: src/Site_Health/Site_Health.php:296
msgid "wordpress.org"
msgstr "wordpress.org"

#: includes/fields/class-acf-field.php:359
msgid "Allow Access to Value in Editor UI"
msgstr "Zezwól na dostęp do wartości w interfejsie użytkownika edytora"

#: includes/fields/class-acf-field.php:341
msgid "Learn more."
msgstr "Dowiedz się więcej."

#. translators: %s A "Learn More" link to documentation explaining the setting
#. further.
#: includes/fields/class-acf-field.php:340
msgid ""
"Allow content editors to access and display the field value in the editor UI "
"using Block Bindings or the ACF Shortcode. %s"
msgstr ""
"Zezwalaj edytorom treści na dostęp i wyświetlanie wartości pola w "
"interfejsie użytkownika edytora za pomocą powiązań blokowych lub krótkiego "
"kodu ACF. %s"

#: src/Blocks/Bindings.php:67
msgid ""
"The requested ACF field type does not support output in Block Bindings or "
"the ACF shortcode."
msgstr ""
"Żądany rodzaj pola ACF nie obsługuje danych wyjściowych w powiązaniach "
"blokowych ani w krótkim kodzie ACF."

#: includes/api/api-template.php:1085 src/Blocks/Bindings.php:75
msgid ""
"The requested ACF field is not allowed to be output in bindings or the ACF "
"Shortcode."
msgstr ""
"Żądane pole ACF nie może być wyprowadzone w powiązaniach ani w krótkim "
"kodzie ACF."

#: includes/api/api-template.php:1077
msgid ""
"The requested ACF field type does not support output in bindings or the ACF "
"Shortcode."
msgstr ""
"Żądany rodzaj pola ACF nie obsługuje danych wyjściowych w powiązaniach ani w "
"krótkim kodzie ACF."

#: includes/api/api-template.php:1054
msgid "[The ACF shortcode cannot display fields from non-public posts]"
msgstr "[Krótki kod ACF nie może wyświetlać pól z wpisów niepublicznych]"

#: includes/api/api-template.php:1011
msgid "[The ACF shortcode is disabled on this site]"
msgstr "[Krótki kod ACF jest wyłączony na tej witrynie]"

#: includes/fields/class-acf-field-icon_picker.php:476
msgid "Businessman Icon"
msgstr "Ikonka biznesmena"

#: includes/fields/class-acf-field-icon_picker.php:468
msgid "Forums Icon"
msgstr "Ikonka forum"

#: includes/fields/class-acf-field-icon_picker.php:747
msgid "YouTube Icon"
msgstr "Ikonka YouTube"

#: includes/fields/class-acf-field-icon_picker.php:746
msgid "Yes (alt) Icon"
msgstr "Ikonka Yes (alt)"

#: includes/fields/class-acf-field-icon_picker.php:744
msgid "Xing Icon"
msgstr "Ikonka Xing"

#: includes/fields/class-acf-field-icon_picker.php:743
msgid "WordPress (alt) Icon"
msgstr "Ikonka WordPress (alt)"

#: includes/fields/class-acf-field-icon_picker.php:741
msgid "WhatsApp Icon"
msgstr "Ikonka WhatsApp"

#: includes/fields/class-acf-field-icon_picker.php:740
msgid "Write Blog Icon"
msgstr "Napisz ikonkę bloga"

#: includes/fields/class-acf-field-icon_picker.php:739
msgid "Widgets Menus Icon"
msgstr "Ikonka menu widżetów"

#: includes/fields/class-acf-field-icon_picker.php:738
msgid "View Site Icon"
msgstr "Zobacz ikonkę witryny"

#: includes/fields/class-acf-field-icon_picker.php:737
msgid "Learn More Icon"
msgstr "Ikonka Dowiedz się więcej"

#: includes/fields/class-acf-field-icon_picker.php:735
msgid "Add Page Icon"
msgstr "Dodaj ikonkę strony"

#: includes/fields/class-acf-field-icon_picker.php:732
msgid "Video (alt3) Icon"
msgstr "Ikonka filmu (alt3)"

#: includes/fields/class-acf-field-icon_picker.php:731
msgid "Video (alt2) Icon"
msgstr "Ikonka filmu (alt2)"

#: includes/fields/class-acf-field-icon_picker.php:730
msgid "Video (alt) Icon"
msgstr "Ikonka filmu (alt)"

#: includes/fields/class-acf-field-icon_picker.php:727
msgid "Update (alt) Icon"
msgstr "Ikonka aktualizacji (alt)"

#: includes/fields/class-acf-field-icon_picker.php:724
msgid "Universal Access (alt) Icon"
msgstr "Ikona uniwersalnego dostępu (alt)"

#: includes/fields/class-acf-field-icon_picker.php:721
msgid "Twitter (alt) Icon"
msgstr "Ikonka Twittera (alt)"

#: includes/fields/class-acf-field-icon_picker.php:719
msgid "Twitch Icon"
msgstr "Ikonka Twitch"

#: includes/fields/class-acf-field-icon_picker.php:716
msgid "Tide Icon"
msgstr "Ikonka fali"

#: includes/fields/class-acf-field-icon_picker.php:715
msgid "Tickets (alt) Icon"
msgstr "Ikonka Tickets (alt)"

#: includes/fields/class-acf-field-icon_picker.php:711
msgid "Text Page Icon"
msgstr "Ikonka strony tekstowej"

#: includes/fields/class-acf-field-icon_picker.php:705
msgid "Table Row Delete Icon"
msgstr "Ikonka usuwania wiersza tabeli"

#: includes/fields/class-acf-field-icon_picker.php:704
msgid "Table Row Before Icon"
msgstr "Ikonka wiersz tabeli przed"

#: includes/fields/class-acf-field-icon_picker.php:703
msgid "Table Row After Icon"
msgstr "Ikonka wiersz tabeli po"

#: includes/fields/class-acf-field-icon_picker.php:702
msgid "Table Col Delete Icon"
msgstr "Ikonka usuwania kolumny tabeli"

#: includes/fields/class-acf-field-icon_picker.php:701
msgid "Table Col Before Icon"
msgstr "Ikonka kolumna tabeli przed"

#: includes/fields/class-acf-field-icon_picker.php:700
msgid "Table Col After Icon"
msgstr "Ikonka kolumna tabeli po"

#: includes/fields/class-acf-field-icon_picker.php:699
msgid "Superhero (alt) Icon"
msgstr "Ikonka super bohatera (alt)"

#: includes/fields/class-acf-field-icon_picker.php:698
msgid "Superhero Icon"
msgstr "Ikonka superbohatera"

#: includes/fields/class-acf-field-icon_picker.php:692
msgid "Spotify Icon"
msgstr "Ikonka Spotify"

#: includes/fields/class-acf-field-icon_picker.php:686
msgid "Shortcode Icon"
msgstr "Ikonka krótkiego kodu"

#: includes/fields/class-acf-field-icon_picker.php:685
msgid "Shield (alt) Icon"
msgstr "Ikonka tarcza (alt)"

#: includes/fields/class-acf-field-icon_picker.php:683
msgid "Share (alt2) Icon"
msgstr "Ikonka udostępnij (alt2)"

#: includes/fields/class-acf-field-icon_picker.php:682
msgid "Share (alt) Icon"
msgstr "Ikonka udostępnij (alt)"

#: includes/fields/class-acf-field-icon_picker.php:677
msgid "Saved Icon"
msgstr "Ikonka zapisz"

#: includes/fields/class-acf-field-icon_picker.php:676
msgid "RSS Icon"
msgstr "Ikonka RSS"

#: includes/fields/class-acf-field-icon_picker.php:675
msgid "REST API Icon"
msgstr "Ikonka REST API"

#: includes/fields/class-acf-field-icon_picker.php:674
msgid "Remove Icon"
msgstr "Ikonka usuń"

#: includes/fields/class-acf-field-icon_picker.php:672
msgid "Reddit Icon"
msgstr "Ikonka Reddit"

#: includes/fields/class-acf-field-icon_picker.php:669
msgid "Privacy Icon"
msgstr "Ikonka prywatności"

#: includes/fields/class-acf-field-icon_picker.php:668
msgid "Printer Icon"
msgstr "Ikonka drukarki"

#: includes/fields/class-acf-field-icon_picker.php:664
msgid "Podio Icon"
msgstr "Ikonka Podio"

#: includes/fields/class-acf-field-icon_picker.php:663
msgid "Plus (alt2) Icon"
msgstr "Ikonka plus (alt2)"

#: includes/fields/class-acf-field-icon_picker.php:662
msgid "Plus (alt) Icon"
msgstr "Ikonka plus (alt)"

#: includes/fields/class-acf-field-icon_picker.php:660
msgid "Plugins Checked Icon"
msgstr "Ikonka zaznaczonych wtyczek"

#: includes/fields/class-acf-field-icon_picker.php:657
msgid "Pinterest Icon"
msgstr "Ikonka Pinterest"

#: includes/fields/class-acf-field-icon_picker.php:655
msgid "Pets Icon"
msgstr "Ikonka zwierząt domowych"

#: includes/fields/class-acf-field-icon_picker.php:653
msgid "PDF Icon"
msgstr "Ikonka PDF"

#: includes/fields/class-acf-field-icon_picker.php:651
msgid "Palm Tree Icon"
msgstr "Ikonka palmy"

#: includes/fields/class-acf-field-icon_picker.php:650
msgid "Open Folder Icon"
msgstr "Ikonka otwartego katalogu"

#: includes/fields/class-acf-field-icon_picker.php:649
msgid "No (alt) Icon"
msgstr "Ikonka brak (alt)"

#: includes/fields/class-acf-field-icon_picker.php:644
msgid "Money (alt) Icon"
msgstr "Ikonka pieniędzy (alt)"

#: includes/fields/class-acf-field-icon_picker.php:639
msgid "Menu (alt3) Icon"
msgstr "Ikonka menu (alt3)"

#: includes/fields/class-acf-field-icon_picker.php:638
msgid "Menu (alt2) Icon"
msgstr "Ikonka menu (alt2)"

#: includes/fields/class-acf-field-icon_picker.php:637
msgid "Menu (alt) Icon"
msgstr "Ikonka menu (alt)"

#: includes/fields/class-acf-field-icon_picker.php:632
msgid "Spreadsheet Icon"
msgstr "Ikonka arkusza kalkulacyjnego"

#: includes/fields/class-acf-field-icon_picker.php:631
msgid "Interactive Icon"
msgstr "Ikonka interaktywna"

#: includes/fields/class-acf-field-icon_picker.php:630
msgid "Document Icon"
msgstr "Ikonka dokumentu"

#: includes/fields/class-acf-field-icon_picker.php:629
msgid "Default Icon"
msgstr "Ikonka domyślnie"

#: includes/fields/class-acf-field-icon_picker.php:623
msgid "Location (alt) Icon"
msgstr "Ikonka lokalizacji (alt)"

#: includes/fields/class-acf-field-icon_picker.php:620
msgid "LinkedIn Icon"
msgstr "Ikonka LinkedIn"

#: includes/fields/class-acf-field-icon_picker.php:615
msgid "Instagram Icon"
msgstr "Ikonka Instagramu"

#: includes/fields/class-acf-field-icon_picker.php:614
msgid "Insert Before Icon"
msgstr "Ikonka wstaw przed"

#: includes/fields/class-acf-field-icon_picker.php:613
msgid "Insert After Icon"
msgstr "Ikonka wstaw po"

#: includes/fields/class-acf-field-icon_picker.php:612
msgid "Insert Icon"
msgstr "Ikonka wstaw"

#: includes/fields/class-acf-field-icon_picker.php:611
msgid "Info Outline Icon"
msgstr "Ikonka konturu informacji"

#: includes/fields/class-acf-field-icon_picker.php:608
msgid "Images (alt2) Icon"
msgstr "Ikonka obrazków (alt2)"

#: includes/fields/class-acf-field-icon_picker.php:607
msgid "Images (alt) Icon"
msgstr "Ikonka obrazków (alt)"

#: includes/fields/class-acf-field-icon_picker.php:606
msgid "Rotate Right Icon"
msgstr "Ikonka obróć w prawo"

#: includes/fields/class-acf-field-icon_picker.php:605
msgid "Rotate Left Icon"
msgstr "Ikonka obróć w lewo"

#: includes/fields/class-acf-field-icon_picker.php:604
msgid "Rotate Icon"
msgstr "Ikonka obróć"

#: includes/fields/class-acf-field-icon_picker.php:603
msgid "Flip Vertical Icon"
msgstr "Ikonka odwróć pionowo"

#: includes/fields/class-acf-field-icon_picker.php:602
msgid "Flip Horizontal Icon"
msgstr "Ikonka odwróć poziomo"

#: includes/fields/class-acf-field-icon_picker.php:600
msgid "Crop Icon"
msgstr "Ikonka przytnij"

#: includes/fields/class-acf-field-icon_picker.php:599
msgid "ID (alt) Icon"
msgstr "Ikonka ID (alt)"

#: includes/fields/class-acf-field-icon_picker.php:597
msgid "HTML Icon"
msgstr "Ikonka HTML"

#: includes/fields/class-acf-field-icon_picker.php:596
msgid "Hourglass Icon"
msgstr "Ikonka klepsydry"

#: includes/fields/class-acf-field-icon_picker.php:593
msgid "Heading Icon"
msgstr "Ikonka nagłówka"

#: includes/fields/class-acf-field-icon_picker.php:589
msgid "Google Icon"
msgstr "Ikonka Google"

#: includes/fields/class-acf-field-icon_picker.php:588
msgid "Games Icon"
msgstr "Ikonka gier"

#: includes/fields/class-acf-field-icon_picker.php:587
msgid "Fullscreen Exit (alt) Icon"
msgstr "Ikonka wyjścia z pełnego ekranu (alt)"

#: includes/fields/class-acf-field-icon_picker.php:586
msgid "Fullscreen (alt) Icon"
msgstr "Ikonka trybu pełnoekranowego (alt)"

#: includes/fields/class-acf-field-icon_picker.php:583
msgid "Status Icon"
msgstr "Ikonka statusu"

#: includes/fields/class-acf-field-icon_picker.php:581
msgid "Image Icon"
msgstr "Ikonka obrazka"

#: includes/fields/class-acf-field-icon_picker.php:580
msgid "Gallery Icon"
msgstr "Ikonka galerii"

#: includes/fields/class-acf-field-icon_picker.php:579
msgid "Chat Icon"
msgstr "Ikonka czatu"

#: includes/fields/class-acf-field-icon_picker.php:578
#: includes/fields/class-acf-field-icon_picker.php:627
msgid "Audio Icon"
msgstr "Ikonka dźwięku"

#: includes/fields/class-acf-field-icon_picker.php:577
msgid "Aside Icon"
msgstr "Ikonka obok"

#: includes/fields/class-acf-field-icon_picker.php:576
msgid "Food Icon"
msgstr "Ikonka żywności"

#: includes/fields/class-acf-field-icon_picker.php:569
msgid "Exit Icon"
msgstr "Ikonka wyjście"

#: includes/fields/class-acf-field-icon_picker.php:568
msgid "Excerpt View Icon"
msgstr "Ikonka widoku zajawki"

#: includes/fields/class-acf-field-icon_picker.php:567
msgid "Embed Video Icon"
msgstr "Ikonka osadzonego filmu"

#: includes/fields/class-acf-field-icon_picker.php:566
msgid "Embed Post Icon"
msgstr "Ikonka osadzonego wpisu"

#: includes/fields/class-acf-field-icon_picker.php:565
msgid "Embed Photo Icon"
msgstr "Ikonka osadzonego zdjęcia"

#: includes/fields/class-acf-field-icon_picker.php:564
msgid "Embed Generic Icon"
msgstr "Ikonka osadzenia ogólnego"

#: includes/fields/class-acf-field-icon_picker.php:563
msgid "Embed Audio Icon"
msgstr "Ikonka osadzonego dźwięku"

#: includes/fields/class-acf-field-icon_picker.php:562
msgid "Email (alt2) Icon"
msgstr "Ikonka e-maila (alt2)"

#: includes/fields/class-acf-field-icon_picker.php:559
msgid "Ellipsis Icon"
msgstr "Ikonka elipsy"

#: includes/fields/class-acf-field-icon_picker.php:555
msgid "Unordered List Icon"
msgstr "Ikonka listy nieuporządkowanej"

#: includes/fields/class-acf-field-icon_picker.php:550
msgid "RTL Icon"
msgstr "Ikonka RTL"

#: includes/fields/class-acf-field-icon_picker.php:543
msgid "Ordered List RTL Icon"
msgstr "Ikonka listy uporządkowanej RTL"

#: includes/fields/class-acf-field-icon_picker.php:542
msgid "Ordered List Icon"
msgstr "Ikonka listy uporządkowanej"

#: includes/fields/class-acf-field-icon_picker.php:541
msgid "LTR Icon"
msgstr "Ikonka LTR"

#: includes/fields/class-acf-field-icon_picker.php:533
msgid "Custom Character Icon"
msgstr "Ikonka własnej postaci"

#: includes/fields/class-acf-field-icon_picker.php:525
msgid "Edit Page Icon"
msgstr "Ikonka edycji strony"

#: includes/fields/class-acf-field-icon_picker.php:524
msgid "Edit Large Icon"
msgstr "Ikonka dużej edycji"

#: includes/fields/class-acf-field-icon_picker.php:522
msgid "Drumstick Icon"
msgstr "Ikonka pałeczek perkusyjnych"

#: includes/fields/class-acf-field-icon_picker.php:518
msgid "Database View Icon"
msgstr "Ikonka widoku bazy danych"

#: includes/fields/class-acf-field-icon_picker.php:517
msgid "Database Remove Icon"
msgstr "Ikonka usuwania bazy danych"

#: includes/fields/class-acf-field-icon_picker.php:516
msgid "Database Import Icon"
msgstr "Ikonka importowania bazy danych"

#: includes/fields/class-acf-field-icon_picker.php:515
msgid "Database Export Icon"
msgstr "Ikonka eksportowania bazy danych"

#: includes/fields/class-acf-field-icon_picker.php:514
msgid "Database Add Icon"
msgstr "Ikonka dodaj bazę danych"

#: includes/fields/class-acf-field-icon_picker.php:513
msgid "Database Icon"
msgstr "Ikonka baza danych"

#: includes/fields/class-acf-field-icon_picker.php:511
msgid "Cover Image Icon"
msgstr "Ikonka obrazek okładki"

#: includes/fields/class-acf-field-icon_picker.php:510
msgid "Volume On Icon"
msgstr "Ikonka włączenia głośności"

#: includes/fields/class-acf-field-icon_picker.php:509
msgid "Volume Off Icon"
msgstr "Ikonka wyłączenia głośności"

#: includes/fields/class-acf-field-icon_picker.php:508
msgid "Skip Forward Icon"
msgstr "Ikonka pomiń do przodu"

#: includes/fields/class-acf-field-icon_picker.php:507
msgid "Skip Back Icon"
msgstr "Ikonka pomiń wstecz"

#: includes/fields/class-acf-field-icon_picker.php:506
msgid "Repeat Icon"
msgstr "Ikonka powtórz"

#: includes/fields/class-acf-field-icon_picker.php:505
msgid "Play Icon"
msgstr "Ikonka odtwórz"

#: includes/fields/class-acf-field-icon_picker.php:504
msgid "Pause Icon"
msgstr "Ikonka pauza"

#: includes/fields/class-acf-field-icon_picker.php:503
msgid "Forward Icon"
msgstr "Ikonka do przodu"

#: includes/fields/class-acf-field-icon_picker.php:502
msgid "Back Icon"
msgstr "Ikonka cofnij"

#: includes/fields/class-acf-field-icon_picker.php:501
msgid "Columns Icon"
msgstr "Ikonka kolumny"

#: includes/fields/class-acf-field-icon_picker.php:500
msgid "Color Picker Icon"
msgstr "Ikonka selektor koloru"

#: includes/fields/class-acf-field-icon_picker.php:499
msgid "Coffee Icon"
msgstr "Ikonka kawa"

#: includes/fields/class-acf-field-icon_picker.php:498
msgid "Code Standards Icon"
msgstr "Ikonka kod standardowy"

#: includes/fields/class-acf-field-icon_picker.php:497
msgid "Cloud Upload Icon"
msgstr "Ikonka przesyłania do chmury"

#: includes/fields/class-acf-field-icon_picker.php:496
msgid "Cloud Saved Icon"
msgstr "Ikonka zapisana w chmurze"

#: includes/fields/class-acf-field-icon_picker.php:485
msgid "Car Icon"
msgstr "Ikonka samochodu"

#: includes/fields/class-acf-field-icon_picker.php:484
msgid "Camera (alt) Icon"
msgstr "Ikonka aparatu (alt)"

#: includes/fields/class-acf-field-icon_picker.php:480
msgid "Calculator Icon"
msgstr "Ikonka kalkulatora"

#: includes/fields/class-acf-field-icon_picker.php:479
msgid "Button Icon"
msgstr "Ikonka przycisku"

#: includes/fields/class-acf-field-icon_picker.php:477
msgid "Businessperson Icon"
msgstr "Ikonka przedsiębiorcy"

#: includes/fields/class-acf-field-icon_picker.php:474
msgid "Tracking Icon"
msgstr "Ikonka śledzenia"

#: includes/fields/class-acf-field-icon_picker.php:473
msgid "Topics Icon"
msgstr "Ikonka tematów"

#: includes/fields/class-acf-field-icon_picker.php:472
msgid "Replies Icon"
msgstr "Ikonka odpowiedzi"

#: includes/fields/class-acf-field-icon_picker.php:471
msgid "PM Icon"
msgstr "Ikonka PM"

#: includes/fields/class-acf-field-icon_picker.php:469
msgid "Friends Icon"
msgstr "Ikonka przyjaciół"

#: includes/fields/class-acf-field-icon_picker.php:467
msgid "Community Icon"
msgstr "Ikonka społeczności"

#: includes/fields/class-acf-field-icon_picker.php:466
msgid "BuddyPress Icon"
msgstr "Ikonka BuddyPress"

#: includes/fields/class-acf-field-icon_picker.php:465
msgid "bbPress Icon"
msgstr "Ikonka bbPress"

#: includes/fields/class-acf-field-icon_picker.php:464
msgid "Activity Icon"
msgstr "Ikonka aktywności"

#: includes/fields/class-acf-field-icon_picker.php:463
msgid "Book (alt) Icon"
msgstr "Ikonka książki (alt)"

#: includes/fields/class-acf-field-icon_picker.php:461
msgid "Block Default Icon"
msgstr "Ikonka domyślnego bloku"

#: includes/fields/class-acf-field-icon_picker.php:460
msgid "Bell Icon"
msgstr "Ikonka dzwonka"

#: includes/fields/class-acf-field-icon_picker.php:459
msgid "Beer Icon"
msgstr "Ikonka piwa"

#: includes/fields/class-acf-field-icon_picker.php:458
msgid "Bank Icon"
msgstr "Ikonka banku"

#: includes/fields/class-acf-field-icon_picker.php:454
msgid "Arrow Up (alt2) Icon"
msgstr "Ikonka strzałki w górę (alt2)"

#: includes/fields/class-acf-field-icon_picker.php:453
msgid "Arrow Up (alt) Icon"
msgstr "Ikonka strzałki w górę (alt)"

#: includes/fields/class-acf-field-icon_picker.php:451
msgid "Arrow Right (alt2) Icon"
msgstr "Ikonka strzałki w prawo (alt2)"

#: includes/fields/class-acf-field-icon_picker.php:450
msgid "Arrow Right (alt) Icon"
msgstr "Ikonka strzałki w prawo (alt)"

#: includes/fields/class-acf-field-icon_picker.php:448
msgid "Arrow Left (alt2) Icon"
msgstr "Ikonka strzałki w lewo (alt2)"

#: includes/fields/class-acf-field-icon_picker.php:447
msgid "Arrow Left (alt) Icon"
msgstr "Ikonka strzałki w lewo (alt)"

#: includes/fields/class-acf-field-icon_picker.php:445
msgid "Arrow Down (alt2) Icon"
msgstr "Ikonka strzałki w dół (alt2)"

#: includes/fields/class-acf-field-icon_picker.php:444
msgid "Arrow Down (alt) Icon"
msgstr "Ikonka strzałki w dół (alt)"

#: includes/fields/class-acf-field-icon_picker.php:440
msgid "Amazon Icon"
msgstr "Ikonka Amazon"

#: includes/fields/class-acf-field-icon_picker.php:439
msgid "Align Wide Icon"
msgstr "Ikonka wyrównaj szeroko"

#: includes/fields/class-acf-field-icon_picker.php:437
msgid "Align Pull Right Icon"
msgstr "Ikonka wyrównaj przyciąganie do prawej"

#: includes/fields/class-acf-field-icon_picker.php:436
msgid "Align Pull Left Icon"
msgstr "Ikonka wyrównaj przyciąganie do lewej"

#: includes/fields/class-acf-field-icon_picker.php:433
msgid "Align Full Width Icon"
msgstr "Ikonka wyrównaj na pełną szerokość"

#: includes/fields/class-acf-field-icon_picker.php:430
msgid "Airplane Icon"
msgstr "Ikonka samolot"

#: includes/fields/class-acf-field-icon_picker.php:427
msgid "Site (alt3) Icon"
msgstr "Ikonka witryna (alt3)"

#: includes/fields/class-acf-field-icon_picker.php:426
msgid "Site (alt2) Icon"
msgstr "Ikonka witryna (alt2)"

#: includes/fields/class-acf-field-icon_picker.php:425
msgid "Site (alt) Icon"
msgstr "Ikonka witryna (alt)"

#: includes/admin/views/options-page-preview.php:26
msgid "Upgrade to ACF PRO to create options pages in just a few clicks"
msgstr ""
"Zaktualizuj do wersji ACF PRO, aby tworzyć strony opcji za pomocą zaledwie "
"kilku kliknięć"

#: includes/ajax/class-acf-ajax-query-users.php:24
msgid "Invalid request args."
msgstr "Nieprawidłowe argumenty żądania."

#: includes/ajax/class-acf-ajax-check-screen.php:37
#: includes/ajax/class-acf-ajax-local-json-diff.php:37
#: includes/ajax/class-acf-ajax-query-users.php:33
#: includes/ajax/class-acf-ajax-upgrade.php:24
#: includes/ajax/class-acf-ajax-user-setting.php:38
msgid "Sorry, you do not have permission to do that."
msgstr "Przepraszamy, ale nie masz uprawnień, aby to zrobić."

#: src/Site_Health/Site_Health.php:720
msgid "Blocks Using Post Meta"
msgstr "Bloki używające meta wpisu"

#: includes/admin/views/acf-field-group/pro-features.php:25
#: includes/admin/views/acf-field-group/pro-features.php:27
#: includes/admin/views/global/header.php:27
msgid "ACF PRO logo"
msgstr "Logo ACF PRO"

#: includes/admin/views/acf-field-group/field.php:37
msgid "ACF PRO Logo"
msgstr "Logo ACF PRO"

#. translators: %s - field/param name
#: includes/fields/class-acf-field-icon_picker.php:813
msgid "%s requires a valid attachment ID when type is set to media_library."
msgstr ""
"%s wymaga poprawnego identyfikatora załącznika, gdy rodzaj jest ustawiony na "
"„media_library”."

#. translators: %s - field name
#: includes/fields/class-acf-field-icon_picker.php:797
msgid "%s is a required property of acf."
msgstr "%s jest wymaganą właściwością dla ACF."

#: includes/fields/class-acf-field-icon_picker.php:773
msgid "The value of icon to save."
msgstr "Wartość ikonki do zapisania."

#: includes/fields/class-acf-field-icon_picker.php:767
msgid "The type of icon to save."
msgstr "Rodzaj ikonki do zapisania."

#: includes/fields/class-acf-field-icon_picker.php:745
msgid "Yes Icon"
msgstr "Ikonka Yes"

#: includes/fields/class-acf-field-icon_picker.php:742
msgid "WordPress Icon"
msgstr "Ikonka WordPress"

#: includes/fields/class-acf-field-icon_picker.php:734
msgid "Warning Icon"
msgstr "Ikonka ostrzeżenia"

#: includes/fields/class-acf-field-icon_picker.php:733
msgid "Visibility Icon"
msgstr "Ikonka widoczności"

#: includes/fields/class-acf-field-icon_picker.php:729
msgid "Vault Icon"
msgstr "Ikonka sklepienia"

#: includes/fields/class-acf-field-icon_picker.php:728
msgid "Upload Icon"
msgstr "Ikonka przesyłania"

#: includes/fields/class-acf-field-icon_picker.php:726
msgid "Update Icon"
msgstr "Ikonka aktualizacji"

#: includes/fields/class-acf-field-icon_picker.php:725
msgid "Unlock Icon"
msgstr "Ikonka odblokowania"

#: includes/fields/class-acf-field-icon_picker.php:723
msgid "Universal Access Icon"
msgstr "Ikonka uniwersalnego dostępu"

#: includes/fields/class-acf-field-icon_picker.php:722
msgid "Undo Icon"
msgstr "Ikonka cofnij"

#: includes/fields/class-acf-field-icon_picker.php:720
msgid "Twitter Icon"
msgstr "Ikonka Twitter"

#: includes/fields/class-acf-field-icon_picker.php:718
msgid "Trash Icon"
msgstr "Ikonka kosza"

#: includes/fields/class-acf-field-icon_picker.php:717
msgid "Translation Icon"
msgstr "Ikonka tłumaczenia"

#: includes/fields/class-acf-field-icon_picker.php:714
msgid "Tickets Icon"
msgstr "Ikonka Tickets"

#: includes/fields/class-acf-field-icon_picker.php:713
msgid "Thumbs Up Icon"
msgstr "Ikonka kciuka w górę"

#: includes/fields/class-acf-field-icon_picker.php:712
msgid "Thumbs Down Icon"
msgstr "Ikonka kciuka w dół"

#: includes/fields/class-acf-field-icon_picker.php:633
#: includes/fields/class-acf-field-icon_picker.php:710
msgid "Text Icon"
msgstr "Ikonka tekst"

#: includes/fields/class-acf-field-icon_picker.php:709
msgid "Testimonial Icon"
msgstr "Ikonka referencji"

#: includes/fields/class-acf-field-icon_picker.php:708
msgid "Tagcloud Icon"
msgstr "Ikonka chmury znaczników"

#: includes/fields/class-acf-field-icon_picker.php:707
msgid "Tag Icon"
msgstr "Ikonka znacznika"

#: includes/fields/class-acf-field-icon_picker.php:706
msgid "Tablet Icon"
msgstr "Ikonka tabletu"

#: includes/fields/class-acf-field-icon_picker.php:697
msgid "Store Icon"
msgstr "Ikonka sklepu"

#: includes/fields/class-acf-field-icon_picker.php:696
msgid "Sticky Icon"
msgstr "Ikonka przypięcia"

#: includes/fields/class-acf-field-icon_picker.php:695
msgid "Star Half Icon"
msgstr "Ikonka połowy gwiazdki"

#: includes/fields/class-acf-field-icon_picker.php:694
msgid "Star Filled Icon"
msgstr "Ikonka wypełniona gwiazdka"

#: includes/fields/class-acf-field-icon_picker.php:693
msgid "Star Empty Icon"
msgstr "Ikonka pustej gwiazdki"

#: includes/fields/class-acf-field-icon_picker.php:691
msgid "Sos Icon"
msgstr "Ikonka Sos"

#: includes/fields/class-acf-field-icon_picker.php:690
msgid "Sort Icon"
msgstr "Ikonka sortuj"

#: includes/fields/class-acf-field-icon_picker.php:689
msgid "Smiley Icon"
msgstr "Ikonka uśmiech"

#: includes/fields/class-acf-field-icon_picker.php:688
msgid "Smartphone Icon"
msgstr "Ikonka smartfon"

#: includes/fields/class-acf-field-icon_picker.php:687
msgid "Slides Icon"
msgstr "Ikonka slajdów"

#: includes/fields/class-acf-field-icon_picker.php:684
msgid "Shield Icon"
msgstr "Ikonka tarcza"

#: includes/fields/class-acf-field-icon_picker.php:681
msgid "Share Icon"
msgstr "Ikonka udostępniania"

#: includes/fields/class-acf-field-icon_picker.php:680
msgid "Search Icon"
msgstr "Ikonka szukaj"

#: includes/fields/class-acf-field-icon_picker.php:679
msgid "Screen Options Icon"
msgstr "Ikonka opcji ekranu"

#: includes/fields/class-acf-field-icon_picker.php:678
msgid "Schedule Icon"
msgstr "Ikonka harmonogramu"

#: includes/fields/class-acf-field-icon_picker.php:673
msgid "Redo Icon"
msgstr "Ikonka ponów"

#: includes/fields/class-acf-field-icon_picker.php:671
msgid "Randomize Icon"
msgstr "Ikonka losuj"

#: includes/fields/class-acf-field-icon_picker.php:670
msgid "Products Icon"
msgstr "Ikonka produkty"

#: includes/fields/class-acf-field-icon_picker.php:667
msgid "Pressthis Icon"
msgstr "Ikonka naciśnij"

#: includes/fields/class-acf-field-icon_picker.php:666
msgid "Post Status Icon"
msgstr "Ikonka statusu wpisu"

#: includes/fields/class-acf-field-icon_picker.php:665
msgid "Portfolio Icon"
msgstr "Ikonka portfolio"

#: includes/fields/class-acf-field-icon_picker.php:661
msgid "Plus Icon"
msgstr "Ikonka plus"

#: includes/fields/class-acf-field-icon_picker.php:659
msgid "Playlist Video Icon"
msgstr "Ikonka listy odtwarzania filmów"

#: includes/fields/class-acf-field-icon_picker.php:658
msgid "Playlist Audio Icon"
msgstr "Ikonka listy odtwarzania dźwięku"

#: includes/fields/class-acf-field-icon_picker.php:656
msgid "Phone Icon"
msgstr "Ikonka telefonu"

#: includes/fields/class-acf-field-icon_picker.php:654
msgid "Performance Icon"
msgstr "Ikonka wydajności"

#: includes/fields/class-acf-field-icon_picker.php:652
msgid "Paperclip Icon"
msgstr "Ikonka spinacza"

#: includes/fields/class-acf-field-icon_picker.php:648
msgid "No Icon"
msgstr "Ikonka brak"

#: includes/fields/class-acf-field-icon_picker.php:647
msgid "Networking Icon"
msgstr "Ikonka sieciowa"

#: includes/fields/class-acf-field-icon_picker.php:646
msgid "Nametag Icon"
msgstr "Ikonka etykiety"

#: includes/fields/class-acf-field-icon_picker.php:645
msgid "Move Icon"
msgstr "Ikonka przesuń"

#: includes/fields/class-acf-field-icon_picker.php:643
msgid "Money Icon"
msgstr "Ikonka pieniędzy"

#: includes/fields/class-acf-field-icon_picker.php:642
msgid "Minus Icon"
msgstr "Ikonka minus"

#: includes/fields/class-acf-field-icon_picker.php:641
msgid "Migrate Icon"
msgstr "Ikonka migracji"

#: includes/fields/class-acf-field-icon_picker.php:640
msgid "Microphone Icon"
msgstr "Ikonka mikrofonu"

#: includes/fields/class-acf-field-icon_picker.php:635
msgid "Megaphone Icon"
msgstr "Ikonka megafonu"

#: includes/fields/class-acf-field-icon_picker.php:625
msgid "Marker Icon"
msgstr "Ikonka znacznika"

#: includes/fields/class-acf-field-icon_picker.php:624
msgid "Lock Icon"
msgstr "Ikonka kłódki"

#: includes/fields/class-acf-field-icon_picker.php:622
msgid "Location Icon"
msgstr "Ikonka lokalizacji"

#: includes/fields/class-acf-field-icon_picker.php:621
msgid "List View Icon"
msgstr "Ikonka widoku listy"

#: includes/fields/class-acf-field-icon_picker.php:619
msgid "Lightbulb Icon"
msgstr "Ikonka żarówki"

#: includes/fields/class-acf-field-icon_picker.php:618
msgid "Left Right Icon"
msgstr "Ikonka lewo prawo"

#: includes/fields/class-acf-field-icon_picker.php:617
msgid "Layout Icon"
msgstr "Ikonka układ"

#: includes/fields/class-acf-field-icon_picker.php:616
msgid "Laptop Icon"
msgstr "Ikonka laptop"

#: includes/fields/class-acf-field-icon_picker.php:610
msgid "Info Icon"
msgstr "Ikonka informacji"

#: includes/fields/class-acf-field-icon_picker.php:609
msgid "Index Card Icon"
msgstr "Ikonka karty indeksowej"

#: includes/fields/class-acf-field-icon_picker.php:598
msgid "ID Icon"
msgstr "Ikonka ID"

#: includes/fields/class-acf-field-icon_picker.php:595
msgid "Hidden Icon"
msgstr "Ikonka ukryj"

#: includes/fields/class-acf-field-icon_picker.php:594
msgid "Heart Icon"
msgstr "Ikonka serca"

#: includes/fields/class-acf-field-icon_picker.php:592
msgid "Hammer Icon"
msgstr "Ikonka młotka"

#: includes/fields/class-acf-field-icon_picker.php:470
#: includes/fields/class-acf-field-icon_picker.php:591
msgid "Groups Icon"
msgstr "Ikonka grup"

#: includes/fields/class-acf-field-icon_picker.php:590
msgid "Grid View Icon"
msgstr "Ikonka widoku siatki"

#: includes/fields/class-acf-field-icon_picker.php:585
msgid "Forms Icon"
msgstr "Ikonka formularzy"

#: includes/fields/class-acf-field-icon_picker.php:575
msgid "Flag Icon"
msgstr "Ikonka flagi"

#: includes/fields/class-acf-field-icon_picker.php:574
#: includes/fields/class-acf-field-icon_picker.php:601
msgid "Filter Icon"
msgstr "Ikonka filtra"

#: includes/fields/class-acf-field-icon_picker.php:573
msgid "Feedback Icon"
msgstr "Ikonka uwagi"

#: includes/fields/class-acf-field-icon_picker.php:572
msgid "Facebook (alt) Icon"
msgstr "Ikonka Facebooka (alt)"

#: includes/fields/class-acf-field-icon_picker.php:571
msgid "Facebook Icon"
msgstr "Ikonka Facebooka"

#: includes/fields/class-acf-field-icon_picker.php:570
msgid "External Icon"
msgstr "Ikonka zewnętrzny"

#: includes/fields/class-acf-field-icon_picker.php:561
msgid "Email (alt) Icon"
msgstr "Ikonka e-maila (alt)"

#: includes/fields/class-acf-field-icon_picker.php:560
msgid "Email Icon"
msgstr "Ikonka e-maila"

#: includes/fields/class-acf-field-icon_picker.php:558
#: includes/fields/class-acf-field-icon_picker.php:584
#: includes/fields/class-acf-field-icon_picker.php:634
msgid "Video Icon"
msgstr "Ikonka filmu"

#: includes/fields/class-acf-field-icon_picker.php:557
msgid "Unlink Icon"
msgstr "Ikonka odłączenia"

#: includes/fields/class-acf-field-icon_picker.php:556
msgid "Underline Icon"
msgstr "Ikonka podkreślenia"

#: includes/fields/class-acf-field-icon_picker.php:554
msgid "Text Color Icon"
msgstr "Ikonka koloru tekstu"

#: includes/fields/class-acf-field-icon_picker.php:553
msgid "Table Icon"
msgstr "Ikonka tabeli"

#: includes/fields/class-acf-field-icon_picker.php:552
msgid "Strikethrough Icon"
msgstr "Ikonka przekreślenia"

#: includes/fields/class-acf-field-icon_picker.php:551
msgid "Spellcheck Icon"
msgstr "Ikonka sprawdzania pisowni"

#: includes/fields/class-acf-field-icon_picker.php:549
msgid "Remove Formatting Icon"
msgstr "Ikonka usuwania formatowania"

#: includes/fields/class-acf-field-icon_picker.php:548
#: includes/fields/class-acf-field-icon_picker.php:582
msgid "Quote Icon"
msgstr "Ikonka cytatu"

#: includes/fields/class-acf-field-icon_picker.php:547
msgid "Paste Word Icon"
msgstr "Ikonka wklejenia słowa"

#: includes/fields/class-acf-field-icon_picker.php:546
msgid "Paste Text Icon"
msgstr "Ikonka wklejenia tekstu"

#: includes/fields/class-acf-field-icon_picker.php:545
msgid "Paragraph Icon"
msgstr "Ikonka akapitu"

#: includes/fields/class-acf-field-icon_picker.php:544
msgid "Outdent Icon"
msgstr "Ikonka zmniejszenia wcięcia"

#: includes/fields/class-acf-field-icon_picker.php:540
msgid "Kitchen Sink Icon"
msgstr "Ikonka zlewu kuchennego"

#: includes/fields/class-acf-field-icon_picker.php:539
msgid "Justify Icon"
msgstr "Ikonka wyjustowania"

#: includes/fields/class-acf-field-icon_picker.php:538
msgid "Italic Icon"
msgstr "Ikonka kursywy"

#: includes/fields/class-acf-field-icon_picker.php:537
msgid "Insert More Icon"
msgstr "Ikonka wstaw więcej"

#: includes/fields/class-acf-field-icon_picker.php:536
msgid "Indent Icon"
msgstr "Ikonka wcięcia"

#: includes/fields/class-acf-field-icon_picker.php:535
msgid "Help Icon"
msgstr "Ikonka pomocy"

#: includes/fields/class-acf-field-icon_picker.php:534
msgid "Expand Icon"
msgstr "Ikonka rozwinięcia"

#: includes/fields/class-acf-field-icon_picker.php:532
msgid "Contract Icon"
msgstr "Ikonka kontraktu"

#: includes/fields/class-acf-field-icon_picker.php:531
#: includes/fields/class-acf-field-icon_picker.php:628
msgid "Code Icon"
msgstr "Ikonka kodu"

#: includes/fields/class-acf-field-icon_picker.php:530
msgid "Break Icon"
msgstr "Ikonka przerwy"

#: includes/fields/class-acf-field-icon_picker.php:529
msgid "Bold Icon"
msgstr "Ikonka pogrubienia"

#: includes/fields/class-acf-field-icon_picker.php:523
msgid "Edit Icon"
msgstr "Ikonka edytuj"

#: includes/fields/class-acf-field-icon_picker.php:521
msgid "Download Icon"
msgstr "Ikonka pobierz"

#: includes/fields/class-acf-field-icon_picker.php:520
msgid "Dismiss Icon"
msgstr "Ikonka odrzuć"

#: includes/fields/class-acf-field-icon_picker.php:519
msgid "Desktop Icon"
msgstr "Ikonka komputera stacjonarnego"

#: includes/fields/class-acf-field-icon_picker.php:512
msgid "Dashboard Icon"
msgstr "Ikonka kokpit"

#: includes/fields/class-acf-field-icon_picker.php:495
msgid "Cloud Icon"
msgstr "Ikonka chmura"

#: includes/fields/class-acf-field-icon_picker.php:494
msgid "Clock Icon"
msgstr "Ikonka zegar"

#: includes/fields/class-acf-field-icon_picker.php:493
msgid "Clipboard Icon"
msgstr "Ikonka schowek"

#: includes/fields/class-acf-field-icon_picker.php:492
msgid "Chart Pie Icon"
msgstr "Ikonka wykres kołowy"

#: includes/fields/class-acf-field-icon_picker.php:491
msgid "Chart Line Icon"
msgstr "Ikonka wykres liniowy"

#: includes/fields/class-acf-field-icon_picker.php:490
msgid "Chart Bar Icon"
msgstr "Ikonka pasek wykresu"

#: includes/fields/class-acf-field-icon_picker.php:489
msgid "Chart Area Icon"
msgstr "Ikonka obszar wykresu"

#: includes/fields/class-acf-field-icon_picker.php:488
msgid "Category Icon"
msgstr "Ikonka kategorii"

#: includes/fields/class-acf-field-icon_picker.php:487
msgid "Cart Icon"
msgstr "Ikonka koszyka"

#: includes/fields/class-acf-field-icon_picker.php:486
msgid "Carrot Icon"
msgstr "Ikonka marchewki"

#: includes/fields/class-acf-field-icon_picker.php:483
msgid "Camera Icon"
msgstr "Ikonka aparatu"

#: includes/fields/class-acf-field-icon_picker.php:482
msgid "Calendar (alt) Icon"
msgstr "Ikonka kalendarza (alt)"

#: includes/fields/class-acf-field-icon_picker.php:481
msgid "Calendar Icon"
msgstr "Ikonka kalendarza"

#: includes/fields/class-acf-field-icon_picker.php:478
msgid "Businesswoman Icon"
msgstr "Ikonka bizneswoman"

#: includes/fields/class-acf-field-icon_picker.php:475
msgid "Building Icon"
msgstr "Ikonka budynku"

#: includes/fields/class-acf-field-icon_picker.php:462
msgid "Book Icon"
msgstr "Ikonka książki"

#: includes/fields/class-acf-field-icon_picker.php:457
msgid "Backup Icon"
msgstr "Ikonka kopii zapasowej"

#: includes/fields/class-acf-field-icon_picker.php:456
msgid "Awards Icon"
msgstr "Ikonka nagród"

#: includes/fields/class-acf-field-icon_picker.php:455
msgid "Art Icon"
msgstr "Ikonka sztuki"

#: includes/fields/class-acf-field-icon_picker.php:452
msgid "Arrow Up Icon"
msgstr "Ikonka strzałki w górę"

#: includes/fields/class-acf-field-icon_picker.php:449
msgid "Arrow Right Icon"
msgstr "Ikonka strzałki w prawo"

#: includes/fields/class-acf-field-icon_picker.php:446
msgid "Arrow Left Icon"
msgstr "Ikonka strzałki w lewo"

#: includes/fields/class-acf-field-icon_picker.php:443
msgid "Arrow Down Icon"
msgstr "Ikonka strzałki w dół"

#: includes/fields/class-acf-field-icon_picker.php:442
#: includes/fields/class-acf-field-icon_picker.php:626
msgid "Archive Icon"
msgstr "Ikonka archiwum"

#: includes/fields/class-acf-field-icon_picker.php:441
msgid "Analytics Icon"
msgstr "Ikonka analityki"

#: includes/fields/class-acf-field-icon_picker.php:438
#: includes/fields/class-acf-field-icon_picker.php:528
msgid "Align Right Icon"
msgstr "Ikonka wyrównaj do prawej"

#: includes/fields/class-acf-field-icon_picker.php:435
msgid "Align None Icon"
msgstr "Ikonka bez wyrównania"

#: includes/fields/class-acf-field-icon_picker.php:434
#: includes/fields/class-acf-field-icon_picker.php:527
msgid "Align Left Icon"
msgstr "Ikonka wyrównaj do lewej"

#: includes/fields/class-acf-field-icon_picker.php:432
#: includes/fields/class-acf-field-icon_picker.php:526
msgid "Align Center Icon"
msgstr "Ikonka wyśrodkuj"

#: includes/fields/class-acf-field-icon_picker.php:431
msgid "Album Icon"
msgstr "Ikonka albumu"

#: includes/fields/class-acf-field-icon_picker.php:429
msgid "Users Icon"
msgstr "Ikonka użytkownicy"

#: includes/fields/class-acf-field-icon_picker.php:428
msgid "Tools Icon"
msgstr "Ikonka narzędzia"

#: includes/fields/class-acf-field-icon_picker.php:424
msgid "Site Icon"
msgstr "Ikonka witryna"

#: includes/fields/class-acf-field-icon_picker.php:423
msgid "Settings Icon"
msgstr "Ikonka ustawienia"

#: includes/fields/class-acf-field-icon_picker.php:422
msgid "Post Icon"
msgstr "Ikonka wpis"

#: includes/fields/class-acf-field-icon_picker.php:421
msgid "Plugins Icon"
msgstr "Ikonka wtyczki"

#: includes/fields/class-acf-field-icon_picker.php:420
msgid "Page Icon"
msgstr "Ikonka strona"

#: includes/fields/class-acf-field-icon_picker.php:419
msgid "Network Icon"
msgstr "Ikonka sieć"

#: includes/fields/class-acf-field-icon_picker.php:418
msgid "Multisite Icon"
msgstr "Ikonka wielowitrynowa"

#: includes/fields/class-acf-field-icon_picker.php:417
msgid "Media Icon"
msgstr "Ikonka media"

#: includes/fields/class-acf-field-icon_picker.php:416
msgid "Links Icon"
msgstr "Ikonka odnośniki"

#: includes/fields/class-acf-field-icon_picker.php:415
msgid "Home Icon"
msgstr "Ikonka dom"

#: includes/fields/class-acf-field-icon_picker.php:413
msgid "Customizer Icon"
msgstr "Ikonka konfigurator"

#: includes/fields/class-acf-field-icon_picker.php:412
#: includes/fields/class-acf-field-icon_picker.php:736
msgid "Comments Icon"
msgstr "Ikonka komentarze"

#: includes/fields/class-acf-field-icon_picker.php:411
msgid "Collapse Icon"
msgstr "Ikonka zwinięcia"

#: includes/fields/class-acf-field-icon_picker.php:410
msgid "Appearance Icon"
msgstr "Ikonka wyglądu"

#: includes/fields/class-acf-field-icon_picker.php:414
msgid "Generic Icon"
msgstr "Ikonka ogólne"

#: includes/fields/class-acf-field-icon_picker.php:346
msgid "Icon picker requires a value."
msgstr "Selektor ikonki wymaga podania wartości."

#: includes/fields/class-acf-field-icon_picker.php:341
msgid "Icon picker requires an icon type."
msgstr "Selektor ikonki wymaga podania rodzaju ikonki."

#: includes/fields/class-acf-field-icon_picker.php:310
msgid ""
"The available icons matching your search query have been updated in the icon "
"picker below."
msgstr ""
"Dostępne ikonki odpowiadające Twojemu zapytaniu zostały zaktualizowane w "
"selektorze ikonki poniżej."

#: includes/fields/class-acf-field-icon_picker.php:309
msgid "No results found for that search term"
msgstr "Nie znaleziono wyników dla tej taksonomii wyszukiwania"

#: includes/fields/class-acf-field-icon_picker.php:291
msgid "Array"
msgstr "Tablica"

#: includes/fields/class-acf-field-icon_picker.php:290
msgid "String"
msgstr "Ciąg znaków"

#. translators: %s - link to documentation
#: includes/fields/class-acf-field-icon_picker.php:278
msgid "Specify the return format for the icon. %s"
msgstr "Określ format zwrotu dla ikonki. %s"

#: includes/fields/class-acf-field-icon_picker.php:263
msgid "Select where content editors can choose the icon from."
msgstr "Wybierz, skąd redaktorzy treści będą mogli wybrać ikonkę."

#: includes/fields/class-acf-field-icon_picker.php:224
msgid "The URL to the icon you'd like to use, or svg as Data URI"
msgstr ""
"Adres URL ikonki, której chcesz użyć, lub svg jako identyfikator URI danych"

#: includes/fields/class-acf-field-icon_picker.php:207
msgid "Browse Media Library"
msgstr "Przeglądaj bibliotekę mediów"

#: includes/fields/class-acf-field-icon_picker.php:198
msgid "The currently selected image preview"
msgstr "Aktualnie wybrany podgląd obrazka"

#: includes/fields/class-acf-field-icon_picker.php:189
msgid "Click to change the icon in the Media Library"
msgstr "Kliknij, aby zmienić ikonkę w multimediach"

#: includes/fields/class-acf-field-icon_picker.php:84
msgid "Search icons..."
msgstr "Szukaj ikonek..."

#: includes/fields/class-acf-field-icon_picker.php:53
msgid "Media Library"
msgstr "Multimedia"

#: includes/fields/class-acf-field-icon_picker.php:49
msgid "Dashicons"
msgstr "Dashicony"

#: includes/fields/class-acf-field-icon_picker.php:26
msgid ""
"An interactive UI for selecting an icon. Select from Dashicons, the media "
"library, or a standalone URL input."
msgstr ""
"Interaktywny interfejs użytkownika do wybierania ikonki. Wybierz z "
"Dashicons, multimediów lub samodzielnego wprowadzania adresu URL."

#: includes/fields/class-acf-field-icon_picker.php:23
msgid "Icon Picker"
msgstr "Wybór ikonki"

#: src/Site_Health/Site_Health.php:781
msgid "JSON Load Paths"
msgstr "Ścieżki wczytania JSON"

#: src/Site_Health/Site_Health.php:775
msgid "JSON Save Paths"
msgstr "Ścieżki zapisu JSON"

#: src/Site_Health/Site_Health.php:766
msgid "Registered ACF Forms"
msgstr "Zarejestrowane formularze ACF"

#: src/Site_Health/Site_Health.php:760
msgid "Shortcode Enabled"
msgstr "Krótki kod włączony"

#: src/Site_Health/Site_Health.php:752
msgid "Field Settings Tabs Enabled"
msgstr "Włączono karty ustawień pola"

#: src/Site_Health/Site_Health.php:744
msgid "Field Type Modal Enabled"
msgstr "Włączono modalny rodzaj pola"

#: src/Site_Health/Site_Health.php:736
msgid "Admin UI Enabled"
msgstr "Włączono UI administratora"

#: src/Site_Health/Site_Health.php:727
msgid "Block Preloading Enabled"
msgstr "Włączono wstępne ładowanie bloku"

#: src/Site_Health/Site_Health.php:715
msgid "Blocks Per ACF Block Version"
msgstr "Bloki na wersję bloku ACF"

#: src/Site_Health/Site_Health.php:710
msgid "Blocks Per API Version"
msgstr "Bloki na wersję API"

#: src/Site_Health/Site_Health.php:683
msgid "Registered ACF Blocks"
msgstr "Zarejestrowane bloki ACF"

#: src/Site_Health/Site_Health.php:677
msgid "Light"
msgstr "Jasny"

#: src/Site_Health/Site_Health.php:677
msgid "Standard"
msgstr "Standardowe"

#: src/Site_Health/Site_Health.php:676
msgid "REST API Format"
msgstr "Format REST API"

#: src/Site_Health/Site_Health.php:668
msgid "Registered Options Pages (PHP)"
msgstr "Zarejestrowane strony opcji (PHP)"

#: src/Site_Health/Site_Health.php:654
msgid "Registered Options Pages (JSON)"
msgstr "Zarejestrowane strony opcji (JSON)"

#: src/Site_Health/Site_Health.php:649
msgid "Registered Options Pages (UI)"
msgstr "Zarejestrowane strony opcji (Interfejs użytkownika)"

#: src/Site_Health/Site_Health.php:619
msgid "Options Pages UI Enabled"
msgstr "Strony opcji UI włączone"

#: src/Site_Health/Site_Health.php:611
msgid "Registered Taxonomies (JSON)"
msgstr "Zarejestrowane taksonomie (JSON)"

#: src/Site_Health/Site_Health.php:599
msgid "Registered Taxonomies (UI)"
msgstr "Zarejestrowane taksonomie (Interfejs użytkownika)"

#: src/Site_Health/Site_Health.php:587
msgid "Registered Post Types (JSON)"
msgstr "Zarejestrowane rodzaje wpisów (JSON)"

#: src/Site_Health/Site_Health.php:575
msgid "Registered Post Types (UI)"
msgstr "Zarejestrowane rodzaje wpisów (Interfejs użytkownika)"

#: src/Site_Health/Site_Health.php:562
msgid "Post Types and Taxonomies Enabled"
msgstr "Włączono rodzaje wpisów i taksonomie"

#: src/Site_Health/Site_Health.php:555
msgid "Number of Third Party Fields by Field Type"
msgstr "Liczba pól stron trzecich wg rodzaju pola"

#: src/Site_Health/Site_Health.php:550
msgid "Number of Fields by Field Type"
msgstr "Liczba pól wg rodzaju pola"

#: src/Site_Health/Site_Health.php:449
msgid "Field Groups Enabled for GraphQL"
msgstr "Włączono grupy pól dla GraphQL"

#: src/Site_Health/Site_Health.php:436
msgid "Field Groups Enabled for REST API"
msgstr "Włączono grupy pól dla interfejsu API REST"

#: src/Site_Health/Site_Health.php:424
msgid "Registered Field Groups (JSON)"
msgstr "Zarejestrowane grupy pól (JSON)"

#: src/Site_Health/Site_Health.php:412
msgid "Registered Field Groups (PHP)"
msgstr "Zarejestrowane grupy pól (PHP)"

#: src/Site_Health/Site_Health.php:400
msgid "Registered Field Groups (UI)"
msgstr "Zarejestrowane grupy pól (Interfejs użytkownika)"

#: src/Site_Health/Site_Health.php:388
msgid "Active Plugins"
msgstr "Wtyczki włączone"

#: src/Site_Health/Site_Health.php:362
msgid "Parent Theme"
msgstr "Motyw nadrzędny"

#: src/Site_Health/Site_Health.php:351
msgid "Active Theme"
msgstr "Włączony motyw"

#: src/Site_Health/Site_Health.php:342
msgid "Is Multisite"
msgstr "Jest wielowitrynowa"

#: src/Site_Health/Site_Health.php:337
msgid "MySQL Version"
msgstr "Wersja MySQL"

#: src/Site_Health/Site_Health.php:332
msgid "WordPress Version"
msgstr "Wersja WordPressa"

#: src/Site_Health/Site_Health.php:325
msgid "Subscription Expiry Date"
msgstr "Data wygaśnięcia subskrypcji"

#: src/Site_Health/Site_Health.php:317
msgid "License Status"
msgstr "Status licencji"

#: src/Site_Health/Site_Health.php:312
msgid "License Type"
msgstr "Rodzaj licencji"

#: src/Site_Health/Site_Health.php:307
msgid "Licensed URL"
msgstr "Adres URL licencji"

#: src/Site_Health/Site_Health.php:301
msgid "License Activated"
msgstr "Licencja została aktywowana"

#: src/Site_Health/Site_Health.php:290
msgid "Free"
msgstr "Darmowy"

#: src/Site_Health/Site_Health.php:289
msgid "Plugin Type"
msgstr "Rodzaj wtyczki"

#: src/Site_Health/Site_Health.php:284
msgid "Plugin Version"
msgstr "Wersja wtyczki"

#: src/Site_Health/Site_Health.php:255
msgid ""
"This section contains debug information about your ACF configuration which "
"can be useful to provide to support."
msgstr ""
"Ta sekcja zawiera informacje debugowania dotyczące konfiguracji ACF, które "
"mogą okazać się przydatne dla wsparcia technicznego."

#: includes/assets.php:373
msgid "An ACF Block on this page requires attention before you can save."
msgstr ""
"Przed zapisaniem pliku należy zwrócić uwagę na blok ACF na tej stronie."

#. translators: %s - The clear log button opening HTML tag. %s - The closing
#. HTML tag.
#: includes/admin/views/escaped-html-notice.php:63
msgid ""
"This data is logged as we detect values that have been changed during "
"output. %1$sClear log and dismiss%2$s after escaping the values in your "
"code. The notice will reappear if we detect changed values again."
msgstr ""
"Te dane są rejestrowane, gdy wykrywamy wartości, które zostały zmienione "
"podczas wyprowadzania. %1$sWyczyść dziennik i odrzuć%2$s po ucieczce z "
"wartości w kodzie. Powiadomienie pojawi się ponownie, jeśli ponownie "
"wykryjemy zmienione wartości."

#: includes/admin/views/escaped-html-notice.php:25
msgid "Dismiss permanently"
msgstr "Odrzuć na zawsze"

#: includes/admin/views/acf-field-group/field.php:220
msgid "Instructions for content editors. Shown when submitting data."
msgstr ""
"Instrukcje dla redaktorów treści. Wyświetlane podczas przesyłania danych."

#: includes/admin/post-types/admin-field-group.php:143
msgid "Has no term selected"
msgstr "Nie wybrano żadnej taksonomii"

#: includes/admin/post-types/admin-field-group.php:142
msgid "Has any term selected"
msgstr "Wybrano dowolną taksonomię"

#: includes/admin/post-types/admin-field-group.php:141
msgid "Terms do not contain"
msgstr "Taksonomia nie zawiera"

#: includes/admin/post-types/admin-field-group.php:140
msgid "Terms contain"
msgstr "Taksonomia zawiera"

#: includes/admin/post-types/admin-field-group.php:139
msgid "Term is not equal to"
msgstr "Taksonomia nie jest równa"

#: includes/admin/post-types/admin-field-group.php:138
msgid "Term is equal to"
msgstr "Taksonomia jest równa"

#: includes/admin/post-types/admin-field-group.php:137
msgid "Has no user selected"
msgstr "Nie wybrano żadnego użytkownika"

#: includes/admin/post-types/admin-field-group.php:136
msgid "Has any user selected"
msgstr "Wybrano dowolnego użytkownika"

#: includes/admin/post-types/admin-field-group.php:135
msgid "Users do not contain"
msgstr "Użytkownicy nie zawierają"

#: includes/admin/post-types/admin-field-group.php:134
msgid "Users contain"
msgstr "Użytkownicy zawierają"

#: includes/admin/post-types/admin-field-group.php:133
msgid "User is not equal to"
msgstr "Użytkownik nie jest równy"

#: includes/admin/post-types/admin-field-group.php:132
msgid "User is equal to"
msgstr "Użytkownik jest równy"

#: includes/admin/post-types/admin-field-group.php:131
msgid "Has no page selected"
msgstr "Nie wybrano żadnej strony"

#: includes/admin/post-types/admin-field-group.php:130
msgid "Has any page selected"
msgstr "Wybrano dowolną stronę"

#: includes/admin/post-types/admin-field-group.php:129
msgid "Pages do not contain"
msgstr "Strony nie zawierają"

#: includes/admin/post-types/admin-field-group.php:128
msgid "Pages contain"
msgstr "Strony zawierają"

#: includes/admin/post-types/admin-field-group.php:127
msgid "Page is not equal to"
msgstr "Strona nie jest równa"

#: includes/admin/post-types/admin-field-group.php:126
msgid "Page is equal to"
msgstr "Strona jest równa"

#: includes/admin/post-types/admin-field-group.php:125
msgid "Has no relationship selected"
msgstr "Nie wybrano żadnej relacji"

#: includes/admin/post-types/admin-field-group.php:124
msgid "Has any relationship selected"
msgstr "Wybrano dowolną relację"

#: includes/admin/post-types/admin-field-group.php:123
msgid "Has no post selected"
msgstr "Nie wybrano żadnego wpisu"

#: includes/admin/post-types/admin-field-group.php:122
msgid "Has any post selected"
msgstr "Wybrano dowolny wpis"

#: includes/admin/post-types/admin-field-group.php:121
msgid "Posts do not contain"
msgstr "Wpisy nie zawierają"

#: includes/admin/post-types/admin-field-group.php:120
msgid "Posts contain"
msgstr "Wpisy zawierają"

#: includes/admin/post-types/admin-field-group.php:119
msgid "Post is not equal to"
msgstr "Wpis nie jest równy"

#: includes/admin/post-types/admin-field-group.php:118
msgid "Post is equal to"
msgstr "Wpis jest równy"

#: includes/admin/post-types/admin-field-group.php:117
msgid "Relationships do not contain"
msgstr "Relacje nie zawierają"

#: includes/admin/post-types/admin-field-group.php:116
msgid "Relationships contain"
msgstr "Relacje zawierają"

#: includes/admin/post-types/admin-field-group.php:115
msgid "Relationship is not equal to"
msgstr "Relacje nie są równe"

#: includes/admin/post-types/admin-field-group.php:114
msgid "Relationship is equal to"
msgstr "Relacje są równe"

#: src/Blocks/Bindings.php:38
msgctxt "The core ACF block binding source name for fields on the current page"
msgid "ACF Fields"
msgstr "Pola ACF"

#: includes/admin/views/browse-fields-modal.php:14
msgid "ACF PRO Feature"
msgstr "Funkcja ACF PRO"

#: includes/admin/views/browse-fields-modal.php:10
msgid "Renew PRO to Unlock"
msgstr "Odnów PRO, aby odblokować"

#: includes/admin/views/browse-fields-modal.php:8
msgid "Renew PRO License"
msgstr "Odnów licencję PRO"

#: includes/admin/views/acf-field-group/field.php:41
msgid "PRO fields cannot be edited without an active license."
msgstr "Pól PRO nie można edytować bez aktywnej licencji."

#: includes/admin/admin-internal-post-type-list.php:232
msgid ""
"Please activate your ACF PRO license to edit field groups assigned to an ACF "
"Block."
msgstr ""
"Aby edytować grupy pól przypisane do bloku ACF, należy aktywować licencję "
"ACF PRO."

#: includes/admin/admin-internal-post-type-list.php:231
msgid "Please activate your ACF PRO license to edit this options page."
msgstr "Aby edytować tę stronę opcji, proszę aktywować licencję ACF PRO."

#: includes/api/api-template.php:385 includes/api/api-template.php:439
msgid ""
"Returning escaped HTML values is only possible when format_value is also "
"true. The field values have not been returned for security."
msgstr ""
"Zwrócenie zmienionych wartości HTML jest możliwe tylko wtedy, gdy parametr "
"format_value ma również wartość true. Wartości pól nie zostały zwrócone ze "
"względów bezpieczeństwa."

#: includes/api/api-template.php:46 includes/api/api-template.php:251
#: includes/api/api-template.php:947
msgid ""
"Returning an escaped HTML value is only possible when format_value is also "
"true. The field value has not been returned for security."
msgstr ""
"Zwrócenie wartości HTML w formie znaku ucieczki jest możliwe tylko wtedy, "
"gdy parametr format_value ma również wartość true. Wartość pola nie została "
"zwrócona ze względów bezpieczeństwa."

#. translators: %1$s - name of the ACF plugin. %2$s - Link to documentation.
#: includes/admin/views/escaped-html-notice.php:32
msgid ""
"%1$s ACF now automatically escapes unsafe HTML when rendered by "
"<code>the_field</code> or the ACF shortcode. We've detected the output of "
"some of your fields has been modified by this change, but this may not be a "
"breaking change. %2$s."
msgstr ""
"%1$s ACF teraz automatycznie ucieka z niebezpiecznego kodu HTML, gdy jest "
"renderowany przez <code>the_field</code> lub krótki kod ACF. Wykryliśmy, że "
"dane wyjściowe niektórych pól zostały zmodyfikowane przez tę zmianę, ale "
"może to nie być zmiana powodująca przerwanie działania. %2$s."

#: includes/admin/views/escaped-html-notice.php:27
msgid "Please contact your site administrator or developer for more details."
msgstr ""
"Aby uzyskać więcej szczegółów, skontaktuj się z administratorem witryny lub "
"twórcą witryny."

#: includes/admin/views/escaped-html-notice.php:5
msgid "Learn&nbsp;more"
msgstr "Dowiedz&nbsp;się&nbsp;więcej"

#: includes/admin/admin.php:63
msgid "Hide&nbsp;details"
msgstr "Ukryj&nbsp;szczegóły"

#: includes/admin/admin.php:62 includes/admin/views/escaped-html-notice.php:24
msgid "Show&nbsp;details"
msgstr "Pokaż&nbsp;szczegóły"

#. translators: %1$s - The selector used  %2$s The field name  3%$s The parent
#. function name
#: includes/admin/views/escaped-html-notice.php:49
msgid "%1$s (%2$s) - rendered via %3$s"
msgstr "%1$s (%2$s) - wyświetlane przez %3$s"

#: includes/admin/views/global/navigation.php:229
msgid "Renew ACF PRO License"
msgstr "Odnów licencję ACF PRO"

#: includes/admin/views/acf-field-group/pro-features.php:17
msgid "Renew License"
msgstr "Odnów licencję"

#: includes/admin/views/acf-field-group/pro-features.php:14
msgid "Manage License"
msgstr "Zarządzaj licencją"

#: includes/admin/views/acf-field-group/options.php:102
msgid "'High' position not supported in the Block Editor"
msgstr "Pozycja „Wysoka” nie jest obsługiwana w edytorze blokowym."

#: includes/admin/views/options-page-preview.php:30
msgid "Upgrade to ACF PRO"
msgstr "Kup ACF-a PRO"

#. translators: %s URL to ACF options pages documentation
#: includes/admin/views/options-page-preview.php:7
msgid ""
"ACF <a href=\"%s\" target=\"_blank\">options pages</a> are custom admin "
"pages for managing global settings via fields. You can create multiple pages "
"and sub-pages."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Strony opcji</a> ACF-a to własne strony "
"administracyjne umożliwiające zarządzanie globalnymi ustawieniami za pomocą "
"pól. Można tworzyć wiele stron i podstron."

#: includes/admin/views/global/header.php:35
msgid "Add Options Page"
msgstr "Dodaj stronę opcji"

#: includes/admin/views/acf-post-type/advanced-settings.php:708
msgid "In the editor used as the placeholder of the title."
msgstr "W edytorze, używane jako tekst zastępczy tytułu."

#: includes/admin/views/acf-post-type/advanced-settings.php:707
msgid "Title Placeholder"
msgstr "Placeholder (tekst zastępczy) tytułu"

#: includes/admin/views/global/navigation.php:97
msgid "4 Months Free"
msgstr "4 miesiące za darmo"

#. translators: %s - A singular label for a post type or taxonomy.
#: includes/admin/views/global/form-top.php:59
msgid "(Duplicated from %s)"
msgstr "(zduplikowane z %s)"

#: includes/admin/tools/class-acf-admin-tool-export.php:289
msgid "Select Options Pages"
msgstr "Wybierz strony opcji"

#: includes/admin/post-types/admin-taxonomy.php:107
msgid "Duplicate taxonomy"
msgstr "Duplikuj taksonomię"

#: includes/admin/post-types/admin-post-type.php:106
#: includes/admin/post-types/admin-taxonomy.php:106
msgid "Create taxonomy"
msgstr "Utwórz taksonomię"

#: includes/admin/post-types/admin-post-type.php:105
msgid "Duplicate post type"
msgstr "Duplikuj typ treści"

#: includes/admin/post-types/admin-post-type.php:104
#: includes/admin/post-types/admin-taxonomy.php:108
msgid "Create post type"
msgstr "Utwórz typ treści"

#: includes/admin/post-types/admin-post-type.php:103
#: includes/admin/post-types/admin-taxonomy.php:105
msgid "Link field groups"
msgstr "Powiąż grupy pól"

#: includes/admin/post-types/admin-post-type.php:102
#: includes/admin/post-types/admin-taxonomy.php:104
msgid "Add fields"
msgstr "Dodaj pola"

#: includes/admin/post-types/admin-field-group.php:147
msgid "This Field"
msgstr "To pole"

#: includes/admin/admin.php:361
msgid "ACF PRO"
msgstr "ACF PRO"

#: includes/admin/admin.php:359
msgid "Feedback"
msgstr "Uwagi"

#: includes/admin/admin.php:357
msgid "Support"
msgstr "Pomoc techniczna"

#. translators: This text is prepended by a link to ACF's website, and appended
#. by a link to WP Engine's website.
#: includes/admin/admin.php:332
msgid "is developed and maintained by"
msgstr "jest rozwijany i utrzymywany przez"

#. translators: %s - either "post type" or "taxonomy"
#: includes/admin/admin-internal-post-type.php:313
msgid "Add this %s to the location rules of the selected field groups."
msgstr "Dodaj ten element (%s) do reguł lokalizacji wybranych grup pól."

#. translators: %s the URL to ACF's bidirectional relationship documentation
#: includes/acf-bidirectional-functions.php:272
msgid ""
"Enabling the bidirectional setting allows you to update a value in the "
"target fields for each value selected for this field, adding or removing the "
"Post ID, Taxonomy ID or User ID of the item being updated. For more "
"information, please read the <a href=\"%s\" target=\"_blank\">documentation</"
"a>."
msgstr ""
"Włączenie opcji dwukierunkowości pozwala na aktualizowanie wartości pól "
"docelowych w każdym elemencie wybranym w tym polu, dodając lub usuwając "
"identyfikator aktualizowanego wpisu, terminu taksonomii lub użytkownika. Aby "
"uzyskać więcej informacji przeczytaj <a href=\"%s\" "
"target=\"_blank\">dokumentację</a>."

#: includes/acf-bidirectional-functions.php:248
msgid ""
"Select field(s) to store the reference back to the item being updated. You "
"may select this field. Target fields must be compatible with where this "
"field is being displayed. For example, if this field is displayed on a "
"Taxonomy, your target field should be of type Taxonomy"
msgstr ""
"Wybierz pole(-a), gdzie zapisywać odwołanie do aktualizowanego elementu. "
"Możesz wybrać to pole. Pola docelowe muszą być zgodne z miejscem "
"wyświetlania tego pola. Przykładowo, jeśli to pole jest wyświetlane w "
"terminie taksonomii, pole docelowe musi mieć rodzaj Taksonomia."

#: includes/acf-bidirectional-functions.php:247
msgid "Target Field"
msgstr "Pole docelowe"

#: includes/acf-bidirectional-functions.php:221
msgid "Update a field on the selected values, referencing back to this ID"
msgstr ""
"Zaktualizuj pole w wybranych elementach, odwołując się do tego identyfikatora"

#: includes/acf-bidirectional-functions.php:220
msgid "Bidirectional"
msgstr "Dwukierunkowe"

#. translators: %s A field type name, such as "Relationship"
#: includes/acf-bidirectional-functions.php:193
msgid "%s Field"
msgstr "Pole „%s”"

#: includes/fields/class-acf-field-page_link.php:498
#: includes/fields/class-acf-field-post_object.php:411
#: includes/fields/class-acf-field-select.php:378
#: includes/fields/class-acf-field-user.php:111
msgid "Select Multiple"
msgstr "Wielokrotny wybór"

#: includes/admin/views/global/navigation.php:241
msgid "WP Engine logo"
msgstr "Logo WP Engine"

#: includes/admin/views/acf-taxonomy/basic-settings.php:58
msgid "Lower case letters, underscores and dashes only, Max 32 characters."
msgstr "Tylko małe litery, myślniki i podkreślniki. Maksymalnie 32 znaki."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1156
msgid "The capability name for assigning terms of this taxonomy."
msgstr "Nazwa uprawnienia do przypisywania terminów tej taksonomii."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1155
msgid "Assign Terms Capability"
msgstr "Uprawnienie do przypisywania terminów"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1139
msgid "The capability name for deleting terms of this taxonomy."
msgstr "Nazwa uprawnienia do usuwania terminów tej taksonomii."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1138
msgid "Delete Terms Capability"
msgstr "Uprawnienie do usuwania terminów"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1122
msgid "The capability name for editing terms of this taxonomy."
msgstr "Nazwa uprawnienia do edytowania terminów tej taksonomii."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1121
msgid "Edit Terms Capability"
msgstr "Uprawnienie do edytowania terminów"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1105
msgid "The capability name for managing terms of this taxonomy."
msgstr "Nazwa uprawnienia do zarządzania terminami tej taksonomii."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1104
msgid "Manage Terms Capability"
msgstr "Uprawnienie do zarządzania terminami"

#: includes/admin/views/acf-post-type/advanced-settings.php:929
msgid ""
"Sets whether posts should be excluded from search results and taxonomy "
"archive pages."
msgstr ""
"Określa czy wpisy powinny być wykluczone z wyników wyszukiwania oraz stron "
"archiwów taksonomii."

#: includes/admin/views/acf-field-group/pro-features.php:78
msgid "More Tools from WP Engine"
msgstr "Więcej narzędzi od WP Engine"

#. translators: %s - WP Engine logo
#: includes/admin/views/acf-field-group/pro-features.php:73
msgid "Built for those that build with WordPress, by the team at %s"
msgstr ""
"Stworzone dla tych, których tworzą przy pomocy WordPressa, przez zespół %s"

#: includes/admin/views/acf-field-group/pro-features.php:6
msgid "View Pricing & Upgrade"
msgstr "Zobacz cennik i kup PRO"

#: includes/admin/views/acf-field-group/pro-features.php:3
#: includes/admin/views/options-page-preview.php:29
#: includes/fields/class-acf-field-icon_picker.php:273
msgid "Learn More"
msgstr "Dowiedz się więcej"

#: includes/admin/views/acf-field-group/pro-features.php:28
msgid ""
"Speed up your workflow and develop better websites with features like ACF "
"Blocks and Options Pages, and sophisticated field types like Repeater, "
"Flexible Content, Clone, and Gallery."
msgstr ""
"Przyśpiesz swoją pracę i twórz lepsze witryny przy pomocy funkcji takich jak "
"bloki ACF-a i strony opcji oraz wyrafinowanych rodzajów pól takich jak pole "
"powtarzalne, elastyczna treść, klon, czy galeria."

#: includes/admin/views/acf-field-group/pro-features.php:2
msgid "Unlock Advanced Features and Build Even More with ACF PRO"
msgstr "Odblokuj zaawansowane funkcje i zbuduj więcej przy pomocy ACF-a PRO"

#. translators: %s - singular label of post type/taxonomy, i.e. "Movie"/"Genre"
#: includes/admin/views/global/form-top.php:19
msgid "%s fields"
msgstr "Pola „%s”"

#: includes/admin/post-types/admin-taxonomies.php:267
msgid "No terms"
msgstr "Brak terminów"

#: includes/admin/post-types/admin-taxonomies.php:240
msgid "No post types"
msgstr "Brak typów treści"

#: includes/admin/post-types/admin-post-types.php:264
msgid "No posts"
msgstr "Brak wpisów"

#: includes/admin/post-types/admin-post-types.php:238
msgid "No taxonomies"
msgstr "Brak taksonomii"

#: includes/admin/post-types/admin-post-types.php:183
#: includes/admin/post-types/admin-taxonomies.php:182
msgid "No field groups"
msgstr "Brak grup pól"

#: includes/admin/post-types/admin-field-groups.php:255
msgid "No fields"
msgstr "Brak pól"

#: includes/admin/post-types/admin-field-groups.php:128
#: includes/admin/post-types/admin-post-types.php:147
#: includes/admin/post-types/admin-taxonomies.php:146
msgid "No description"
msgstr "Brak opisu"

#: includes/fields/class-acf-field-page_link.php:465
#: includes/fields/class-acf-field-post_object.php:374
#: includes/fields/class-acf-field-relationship.php:573
msgid "Any post status"
msgstr "Dowolny status wpisu"

#: includes/post-types/class-acf-taxonomy.php:288
msgid ""
"This taxonomy key is already in use by another taxonomy registered outside "
"of ACF and cannot be used."
msgstr ""
"Ten klucz taksonomii jest już używany przez inną taksonomię zarejestrowaną "
"poza ACF-em i nie może zostać użyty."

#: includes/post-types/class-acf-taxonomy.php:284
msgid ""
"This taxonomy key is already in use by another taxonomy in ACF and cannot be "
"used."
msgstr ""
"Ten klucz taksonomii jest już używany przez inną taksonomię w ACF-ie i nie "
"może zostać użyty."

#: includes/post-types/class-acf-taxonomy.php:256
msgid ""
"The taxonomy key must only contain lower case alphanumeric characters, "
"underscores or dashes."
msgstr ""
"Klucz taksonomii może zawierać wyłącznie małe litery, cyfry, myślniki i "
"podkreślniki."

#: includes/post-types/class-acf-taxonomy.php:251
msgid "The taxonomy key must be under 32 characters."
msgstr "Klucz taksonomii musi mieć mniej niż 32 znaków."

#: includes/post-types/class-acf-taxonomy.php:99
msgid "No Taxonomies found in Trash"
msgstr "Nie znaleziono żadnych taksonomii w koszu"

#: includes/post-types/class-acf-taxonomy.php:98
msgid "No Taxonomies found"
msgstr "Nie znaleziono żadnych taksonomii"

#: includes/post-types/class-acf-taxonomy.php:97
msgid "Search Taxonomies"
msgstr "Szukaj taksonomii"

#: includes/post-types/class-acf-taxonomy.php:96
msgid "View Taxonomy"
msgstr "Zobacz taksonomię"

#: includes/post-types/class-acf-taxonomy.php:95
msgid "New Taxonomy"
msgstr "Nowa taksonomia"

#: includes/post-types/class-acf-taxonomy.php:94
msgid "Edit Taxonomy"
msgstr "Edytuj taksonomię"

#: includes/post-types/class-acf-taxonomy.php:93
msgid "Add New Taxonomy"
msgstr "Utwórz taksonomię"

#: includes/post-types/class-acf-post-type.php:100
msgid "No Post Types found in Trash"
msgstr "Nie znaleziono żadnych typów treści w koszu"

#: includes/post-types/class-acf-post-type.php:99
msgid "No Post Types found"
msgstr "Nie znaleziono żadnych typów treści"

#: includes/post-types/class-acf-post-type.php:98
msgid "Search Post Types"
msgstr "Szukaj typów treści"

#: includes/post-types/class-acf-post-type.php:97
msgid "View Post Type"
msgstr "Zobacz typ treści"

#: includes/post-types/class-acf-post-type.php:96
msgid "New Post Type"
msgstr "Nowy typ treści"

#: includes/post-types/class-acf-post-type.php:95
msgid "Edit Post Type"
msgstr "Edytuj typ treści"

#: includes/post-types/class-acf-post-type.php:94
msgid "Add New Post Type"
msgstr "Utwórz typ treści"

#: includes/post-types/class-acf-post-type.php:366
msgid ""
"This post type key is already in use by another post type registered outside "
"of ACF and cannot be used."
msgstr ""
"Ten klucz typu treści jest już używany przez inny typ treści zarejestrowany "
"poza ACF-em i nie może zostać użyty."

#: includes/post-types/class-acf-post-type.php:361
msgid ""
"This post type key is already in use by another post type in ACF and cannot "
"be used."
msgstr ""
"Ten klucz typu treści jest już używany przez inny typ treści w ACF-ie i nie "
"może zostać użyty."

#. translators: %s a link to WordPress.org's Reserved Terms page
#: includes/post-types/class-acf-post-type.php:339
#: includes/post-types/class-acf-taxonomy.php:262
msgid ""
"This field must not be a WordPress <a href=\"%s\" target=\"_blank\">reserved "
"term</a>."
msgstr ""
"Pole nie może być <a href=\"%s\" target=\"_blank\">terminem zastrzeżonym</a> "
"przez WordPressa."

#: includes/post-types/class-acf-post-type.php:333
msgid ""
"The post type key must only contain lower case alphanumeric characters, "
"underscores or dashes."
msgstr ""
"Klucz typu treści może zawierać wyłącznie małe litery, cyfry, myślniki i "
"podkreślniki."

#: includes/post-types/class-acf-post-type.php:328
msgid "The post type key must be under 20 characters."
msgstr "Klucz typu treści musi mieć mniej niż 20 znaków."

#: includes/fields/class-acf-field-wysiwyg.php:24
msgid "We do not recommend using this field in ACF Blocks."
msgstr "Nie zalecamy używania tego pola w blokach ACF-a."

#: includes/fields/class-acf-field-wysiwyg.php:24
msgid ""
"Displays the WordPress WYSIWYG editor as seen in Posts and Pages allowing "
"for a rich text-editing experience that also allows for multimedia content."
msgstr ""
"Wyświetla edytor WYSIWYG WordPressa, taki jak we wpisach czy stronach, który "
"pozwala na formatowanie tekstu oraz użycie treści multimedialnych."

#: includes/fields/class-acf-field-wysiwyg.php:22
msgid "WYSIWYG Editor"
msgstr "Edytor WYSIWYG"

#: includes/fields/class-acf-field-user.php:17
msgid ""
"Allows the selection of one or more users which can be used to create "
"relationships between data objects."
msgstr ""
"Pozwala na wybór jednego lub kliku użytkowników do tworzenia relacji między "
"obiektami danych."

#: includes/fields/class-acf-field-url.php:20
msgid "A text input specifically designed for storing web addresses."
msgstr "Pole tekstowe przeznaczone do przechowywania adresów URL."

#: includes/fields/class-acf-field-icon_picker.php:56
#: includes/fields/class-acf-field-url.php:19
msgid "URL"
msgstr "Adres URL"

#: includes/fields/class-acf-field-true_false.php:24
msgid ""
"A toggle that allows you to pick a value of 1 or 0 (on or off, true or "
"false, etc). Can be presented as a stylized switch or checkbox."
msgstr ""
"Przełącznik pozwalający na wybranie wartości 1 lub 0 (włączone lub "
"wyłączone, prawda lub fałsz, itp.). Może być wyświetlany jako ostylowany "
"przełącznik lub pole zaznaczenia."

#: includes/fields/class-acf-field-time_picker.php:24
msgid ""
"An interactive UI for picking a time. The time format can be customized "
"using the field settings."
msgstr ""
"Interaktywny interfejs użytkownika do wybierania godziny. Zwracany format "
"czasu można dostosować przy użyciu ustawień pola."

#: includes/fields/class-acf-field-textarea.php:23
msgid "A basic textarea input for storing paragraphs of text."
msgstr "Prosty obszar tekstowy do przechowywania akapitów tekstu."

#: includes/fields/class-acf-field-text.php:23
msgid "A basic text input, useful for storing single string values."
msgstr ""
"Proste pole tekstowe, przydatne do przechowywania wartości pojedynczych "
"ciągów."

#: includes/fields/class-acf-field-taxonomy.php:22
msgid ""
"Allows the selection of one or more taxonomy terms based on the criteria and "
"options specified in the fields settings."
msgstr ""
"Pozwala na wybór jednego lub kliku terminów taksonomii w oparciu o kryteria "
"i opcje określone w ustawieniach pola."

#: includes/fields/class-acf-field-tab.php:25
msgid ""
"Allows you to group fields into tabbed sections in the edit screen. Useful "
"for keeping fields organized and structured."
msgstr ""
"Pozwala na grupowanie pól w zakładkach na ekranie edycji. Przydatne do "
"utrzymywania porządku i struktury pól."

#: includes/fields/class-acf-field-select.php:18
msgid "A dropdown list with a selection of choices that you specify."
msgstr "Lista rozwijana z wyborem określonych opcji."

#: includes/fields/class-acf-field-relationship.php:19
msgid ""
"A dual-column interface to select one or more posts, pages, or custom post "
"type items to create a relationship with the item that you're currently "
"editing. Includes options to search and filter."
msgstr ""
"Interfejs dwukolumnowy do wybierania jednego lub kilku wpisów, stron lub "
"elementów własnych typów treści, aby stworzyć relację z obecnie edytowanym "
"elementem. Posiada wyszukiwarkę i filtrowanie."

#: includes/fields/class-acf-field-range.php:23
msgid ""
"An input for selecting a numerical value within a specified range using a "
"range slider element."
msgstr ""
"Pole do wybierania wartości liczbowej z określonego zakresu za pomocą suwaka."

#: includes/fields/class-acf-field-radio.php:24
msgid ""
"A group of radio button inputs that allows the user to make a single "
"selection from values that you specify."
msgstr ""
"Grupa pól wyboru pozwalająca użytkownikowi na wybór jednej spośród "
"określonych wartości."

#: includes/fields/class-acf-field-post_object.php:17
msgid ""
"An interactive and customizable UI for picking one or many posts, pages or "
"post type items with the option to search. "
msgstr ""
"Interaktywny i konfigurowalny interfejs użytkownika do wybierania jednego "
"lub kilku wpisów, stron, elementów typów treści. Posiada wyszukiwarkę. "

#: includes/fields/class-acf-field-password.php:23
msgid "An input for providing a password using a masked field."
msgstr "Maskowane pole do wpisywania hasła."

#: includes/fields/class-acf-field-page_link.php:457
#: includes/fields/class-acf-field-post_object.php:366
#: includes/fields/class-acf-field-relationship.php:565
msgid "Filter by Post Status"
msgstr "Filtruj wg statusu wpisu"

#: includes/fields/class-acf-field-page_link.php:24
msgid ""
"An interactive dropdown to select one or more posts, pages, custom post type "
"items or archive URLs, with the option to search."
msgstr ""
"Interaktywna lista rozwijana do wybierania jednego lub kilku wpisów, stron, "
"elementów własnych typów treści lub adresów URL archiwów. Posiada "
"wyszukiwarkę."

#: includes/fields/class-acf-field-oembed.php:24
msgid ""
"An interactive component for embedding videos, images, tweets, audio and "
"other content by making use of the native WordPress oEmbed functionality."
msgstr ""
"Interaktywny element do osadzania filmów, obrazków, tweetów, audio i innych "
"treści przy użyciu natywnej funkcji oEmbed WordPressa."

#: includes/fields/class-acf-field-number.php:23
msgid "An input limited to numerical values."
msgstr "Pole ograniczone do wartości liczbowych."

#: includes/fields/class-acf-field-message.php:25
msgid ""
"Used to display a message to editors alongside other fields. Useful for "
"providing additional context or instructions around your fields."
msgstr ""
"Używane do wyświetlania wiadomości dla redaktorów obok innych pól. Przydatne "
"do przekazywania dodatkowego kontekstu lub instrukcji dotyczących pól."

#: includes/fields/class-acf-field-link.php:24
msgid ""
"Allows you to specify a link and its properties such as title and target "
"using the WordPress native link picker."
msgstr ""
"Pozwala na określenie odnośnika i jego właściwości, takich jak tytuł czy "
"cel, przy użyciu natywnego wyboru odnośników WordPressa."

#: includes/fields/class-acf-field-image.php:24
msgid "Uses the native WordPress media picker to upload, or choose images."
msgstr ""
"Używa natywnego wyboru mediów WordPressa do przesyłania lub wyboru obrazków."

#: includes/fields/class-acf-field-group.php:24
msgid ""
"Provides a way to structure fields into groups to better organize the data "
"and the edit screen."
msgstr ""
"Zapewnia sposób na uporządkowanie pól w grupy w celu lepszej organizacji "
"danych i ekranu edycji."

#: includes/fields/class-acf-field-google-map.php:24
msgid ""
"An interactive UI for selecting a location using Google Maps. Requires a "
"Google Maps API key and additional configuration to display correctly."
msgstr ""
"Interaktywny interfejs użytkownika do wybierania lokalizacji przy użyciu Map "
"Google. Do poprawnego wyświetlania wymagany jest klucz API Google Maps oraz "
"dodatkowa konfiguracja."

#: includes/fields/class-acf-field-file.php:24
msgid "Uses the native WordPress media picker to upload, or choose files."
msgstr ""
"Używa natywnego wyboru mediów WordPressa do przesyłania lub wyboru plików."

#: includes/fields/class-acf-field-email.php:23
msgid "A text input specifically designed for storing email addresses."
msgstr "Pole tekstowe przeznaczone do przechowywania adresów e-mail."

#: includes/fields/class-acf-field-date_time_picker.php:24
msgid ""
"An interactive UI for picking a date and time. The date return format can be "
"customized using the field settings."
msgstr ""
"Interaktywny interfejs użytkownika do wybierania daty i godziny. Zwracany "
"format daty można dostosować przy użyciu ustawień pola."

#: includes/fields/class-acf-field-date_picker.php:24
msgid ""
"An interactive UI for picking a date. The date return format can be "
"customized using the field settings."
msgstr ""
"Interaktywny interfejs użytkownika do wybierania daty. Zwracany format daty "
"można dostosować przy użyciu ustawień pola."

#: includes/fields/class-acf-field-color_picker.php:24
msgid "An interactive UI for selecting a color, or specifying a Hex value."
msgstr ""
"Interaktywny interfejs użytkownika do wybierania koloru lub określenia "
"wartości Hex."

#: includes/fields/class-acf-field-checkbox.php:24
msgid ""
"A group of checkbox inputs that allow the user to select one, or multiple "
"values that you specify."
msgstr ""
"Grupa pól zaznaczenia, która pozwala użytkownikowi na wybranie jednej lub "
"kilku określonych wartości."

#: includes/fields/class-acf-field-button-group.php:25
msgid ""
"A group of buttons with values that you specify, users can choose one option "
"from the values provided."
msgstr ""
"Grupa przycisków z określonymi wartościami, z których użytkownik może wybrać "
"jedną opcję."

#: includes/fields/class-acf-field-accordion.php:26
msgid ""
"Allows you to group and organize custom fields into collapsable panels that "
"are shown while editing content. Useful for keeping large datasets tidy."
msgstr ""
"Pozwala na grupowanie i organizowanie własnych pól w zwijanych panelach "
"wyświetlanych podczas edytowania treści. Przydatne do utrzymywania porządku "
"przy dużej ilości danych."

#: includes/fields.php:449
msgid ""
"This provides a solution for repeating content such as slides, team members, "
"and call-to-action tiles, by acting as a parent to a set of subfields which "
"can be repeated again and again."
msgstr ""
"Zapewnie rozwiązanie dla powtarzalnych treści, takich jak slajdy, członkowie "
"zespołu, czy kafelki. Działa jak element nadrzędny dla zestawu podpól, który "
"może być powtarzany wiele razy."

#: includes/fields.php:439
msgid ""
"This provides an interactive interface for managing a collection of "
"attachments. Most settings are similar to the Image field type. Additional "
"settings allow you to specify where new attachments are added in the gallery "
"and the minimum/maximum number of attachments allowed."
msgstr ""
"Zapewnia interaktywny interfejs do zarządzania zbiorem załączników. "
"Większość opcji jest podobna do rodzaju pola Obrazek. Dodatkowe opcje "
"pozwalają na określenie miejsca w galerii w którym nowe załączniki są "
"dodawane oraz minimalną i maksymalną liczbę dozwolonych załączników."

#: includes/fields.php:429
msgid ""
"This provides a simple, structured, layout-based editor. The Flexible "
"Content field allows you to define, create and manage content with total "
"control by using layouts and subfields to design the available blocks."
msgstr ""
"Zapewnia prosty, uporządkowany edytor oparty na układach. Pole elastycznej "
"treści pozwala na pełną kontrolę nad definiowaniem, tworzeniem i "
"zarządzaniem treścią przy użyciu układów i podpól w celu zaprojektowania "
"dostępnych bloków."

#: includes/fields.php:419
msgid ""
"This allows you to select and display existing fields. It does not duplicate "
"any fields in the database, but loads and displays the selected fields at "
"run-time. The Clone field can either replace itself with the selected fields "
"or display the selected fields as a group of subfields."
msgstr ""
"Pozwala na wybór i wyświetlanie istniejących pól. Zamiast powielania pól w "
"bazie danych, wczytuje i wyświetla wybrane pola w trakcie wykonywania kodu "
"strony. Pole klona może być zastąpione przez wybrane pola lub wyświetlać "
"wybrane pola jak grupa podpól."

#: includes/fields.php:416
msgctxt "noun"
msgid "Clone"
msgstr "Klon"

#: includes/admin/views/global/navigation.php:86 includes/fields.php:331
#: src/Site_Health/Site_Health.php:290
msgid "PRO"
msgstr "PRO"

#: includes/fields.php:329 includes/fields.php:386
msgid "Advanced"
msgstr "Zaawansowane"

#: includes/ajax/class-acf-ajax-local-json-diff.php:90
msgid "JSON (newer)"
msgstr "JSON (nowszy)"

#: includes/ajax/class-acf-ajax-local-json-diff.php:86
msgid "Original"
msgstr "Oryginalny"

#: includes/ajax/class-acf-ajax-local-json-diff.php:60
msgid "Invalid post ID."
msgstr "Nieprawidłowy identyfikator wpisu."

#: includes/ajax/class-acf-ajax-local-json-diff.php:52
msgid "Invalid post type selected for review."
msgstr "Wybrano nieprawidłowy typ treści do porównania."

#: includes/admin/views/global/navigation.php:192
msgid "More"
msgstr "Więcej"

#: includes/admin/views/browse-fields-modal.php:96
msgid "Tutorial"
msgstr "Poradnik"

#: includes/admin/views/browse-fields-modal.php:73
msgid "Select Field"
msgstr "Wybierz pole"

#. translators: %s: A link to the popular fields used in ACF
#: includes/admin/views/browse-fields-modal.php:60
msgid "Try a different search term or browse %s"
msgstr "Spróbuj innej frazy lub przejrzyj %s"

#: includes/admin/views/browse-fields-modal.php:57
msgid "Popular fields"
msgstr "Popularne pola"

#. translators: %s: The invalid search term
#: includes/admin/views/browse-fields-modal.php:50
#: includes/fields/class-acf-field-icon_picker.php:97
msgid "No search results for '%s'"
msgstr "Brak wyników wyszukiwania dla „%s”"

#: includes/admin/views/browse-fields-modal.php:23
msgid "Search fields..."
msgstr "Szukaj pól…"

#: includes/admin/views/browse-fields-modal.php:21
msgid "Select Field Type"
msgstr "Wybierz rodzaj pola"

#: includes/admin/views/browse-fields-modal.php:4
msgid "Popular"
msgstr "Popularne"

#: includes/admin/views/acf-taxonomy/list-empty.php:15
msgid "Add Taxonomy"
msgstr "Dodaj taksonomię"

#: includes/admin/views/acf-taxonomy/list-empty.php:14
msgid "Create custom taxonomies to classify post type content"
msgstr "Twórz własne taksonomie, aby klasyfikować typy treści"

#: includes/admin/views/acf-taxonomy/list-empty.php:13
msgid "Add Your First Taxonomy"
msgstr "Dodaj swoją pierwszą taksonomię"

#: includes/admin/views/acf-taxonomy/basic-settings.php:122
msgid "Hierarchical taxonomies can have descendants (like categories)."
msgstr ""
"Hierarchiczne taksonomie mogą posiadać elementy potomne (jak kategorie)."

#: includes/admin/views/acf-taxonomy/basic-settings.php:107
msgid "Makes a taxonomy visible on the frontend and in the admin dashboard."
msgstr ""
"Powoduje, że taksonomia jest widoczna w witrynie oraz w kokpicie "
"administratora."

#: includes/admin/views/acf-taxonomy/basic-settings.php:91
msgid "One or many post types that can be classified with this taxonomy."
msgstr ""
"Jeden lub klika typów wpisów, które będą klasyfikowane za pomocą tej "
"taksonomii."

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:60
msgid "genre"
msgstr "gatunek"

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:42
msgid "Genre"
msgstr "Gatunek"

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:25
msgid "Genres"
msgstr "Gatunki"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1231
msgid ""
"Optional custom controller to use instead of `WP_REST_Terms_Controller `."
msgstr ""
"Opcjonalny własny kontroler do użycia zamiast `WP_REST_Terms_Controller`."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1175
msgid "Expose this post type in the REST API."
msgstr "Pokaż ten typ treści w REST API."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1075
msgid "Customize the query variable name"
msgstr "Dostosuj nazwę zmiennej zapytania"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1048
msgid ""
"Terms can be accessed using the non-pretty permalink, e.g., {query_var}"
"={term_slug}."
msgstr ""
"Terminy są dostępne za pomocą nieprzyjaznych bezpośrednich odnośników, np. "
"{zmienna_zapytania}={uproszczona_nazwa_terminu}."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1001
msgid "Parent-child terms in URLs for hierarchical taxonomies."
msgstr ""
"Terminy nadrzędne i potomne w adresach URL dla hierarchicznych taksonomii."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:961
msgid "Customize the slug used in the URL"
msgstr "Dostosuj uproszczoną nazwę używaną w adresie URL"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:944
msgid "Permalinks for this taxonomy are disabled."
msgstr "Bezpośrednie odnośniki tej taksonomii są wyłączone."

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-taxonomy/advanced-settings.php:941
msgid ""
"Rewrite the URL using the taxonomy key as the slug. Your permalink structure "
"will be"
msgstr ""
"Przepisuj adres URL używając klucza taksonomii jako uproszczonej nazwy. "
"Struktura bezpośrednich odnośników będzie następująca"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:933
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1050
#: includes/admin/views/acf-taxonomy/basic-settings.php:57
msgid "Taxonomy Key"
msgstr "Klucz taksonomii"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:931
msgid "Select the type of permalink to use for this taxonomy."
msgstr "Wybierz rodzaj bezpośredniego odnośnika używanego dla tego taksonomii."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:916
msgid "Display a column for the taxonomy on post type listing screens."
msgstr "Wyświetl kolumnę taksonomii na ekranach list typów treści."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:915
msgid "Show Admin Column"
msgstr "Pokaż kolumnę administratora"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:902
msgid "Show the taxonomy in the quick/bulk edit panel."
msgstr "Pokaż taksonomię w panelu szybkiej/masowej edycji."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:901
msgid "Quick Edit"
msgstr "Szybka edycja"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:888
msgid "List the taxonomy in the Tag Cloud Widget controls."
msgstr "Pokaż taksonomię w ustawieniach widżetu Chmury tagów."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:887
msgid "Tag Cloud"
msgstr "Chmura tagów"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:842
msgid ""
"A PHP function name to be called for sanitizing taxonomy data saved from a "
"meta box."
msgstr ""
"Nazwa funkcji PHP, która będzie wzywana do oczyszczania danych taksonomii z "
"metaboksa."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:841
msgid "Meta Box Sanitization Callback"
msgstr "Funkcja zwrotna oczyszczania metaboksa"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:821
msgid "Register Meta Box Callback"
msgstr "Funkcja zwrotna rejestrowania metaboksa"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:752
msgid "No Meta Box"
msgstr "Brak metaboksa"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:751
msgid "Custom Meta Box"
msgstr "Własny metaboks"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:768
msgid ""
"Controls the meta box on the content editor screen. By default, the "
"Categories meta box is shown for hierarchical taxonomies, and the Tags meta "
"box is shown for non-hierarchical taxonomies."
msgstr ""
"Kontroluje metaboks na ekranie edytora treści. Domyślnie metaboks Kategorii "
"jest wyświetlany dla hierarchicznych taksonomii, a metaboks Tagów dla "
"taksonomii niehierarchicznych."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:767
msgid "Meta Box"
msgstr "Metaboks"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:746
#: includes/admin/views/acf-taxonomy/advanced-settings.php:773
msgid "Categories Meta Box"
msgstr "Metaboks kategorii"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:745
#: includes/admin/views/acf-taxonomy/advanced-settings.php:772
msgid "Tags Meta Box"
msgstr "Metaboks tagów"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:704
msgid "A link to a tag"
msgstr "Dodaj odnośnik do tagu"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:703
msgid "Describes a navigation link block variation used in the block editor."
msgstr "Opisuje wersję bloku odnośnika używanego w edytorze blokowym."

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:698
msgid "A link to a %s"
msgstr "Odnośnik do „%s”"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:683
msgid "Tag Link"
msgstr "Odnośnik tagu"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:682
msgid ""
"Assigns a title for navigation link block variation used in the block editor."
msgstr "Przypisuje tytuł wersji bloku odnośnika używanego w edytorze blokowym."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:663
msgid "← Go to tags"
msgstr "← Przejdź do tagów"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:662
msgid ""
"Assigns the text used to link back to the main index after updating a term."
msgstr ""
"Przypisuje tekst używany w odnośniku powrotu do głównego indeksu po "
"zaktualizowaniu terminu."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:661
msgid "Back To Items"
msgstr "Powrót do elementów"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:657
msgid "← Go to %s"
msgstr "← Przejdź do „%s”"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:642
msgid "Tags list"
msgstr "Lista tagów"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:641
msgid "Assigns text to the table hidden heading."
msgstr "Przypisuje tekst do ukrytego nagłówka tabeli."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:622
msgid "Tags list navigation"
msgstr "Nawigacja listy tagów"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:621
msgid "Assigns text to the table pagination hidden heading."
msgstr "Przypisuje tekst do ukrytego nagłówka stronicowania tabeli."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:597
msgid "Filter by category"
msgstr "Filtruj wg kategorii"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:596
msgid "Assigns text to the filter button in the posts lists table."
msgstr "Przypisuje tekst do przycisku filtrowania w tabeli listy wpisów."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:595
msgid "Filter By Item"
msgstr "Filtruj wg elementu"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:591
msgid "Filter by %s"
msgstr "Filtruj wg „%s”"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:575
#: includes/admin/views/acf-taxonomy/advanced-settings.php:576
msgid ""
"The description is not prominent by default; however, some themes may show "
"it."
msgstr ""
"Opis zwykle nie jest eksponowany, jednak niektóre motywy mogą go wyświetlać."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:574
msgid "Describes the Description field on the Edit Tags screen."
msgstr "Opisuje pole opisu na ekranie edycji tagów."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:573
msgid "Description Field Description"
msgstr "Opis pola opisu"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:554
#: includes/admin/views/acf-taxonomy/advanced-settings.php:555
msgid ""
"Assign a parent term to create a hierarchy. The term Jazz, for example, "
"would be the parent of Bebop and Big Band"
msgstr ""
"Przypisz pojęcie nadrzędne, aby utworzyć hierarchię. Na przykład pojęcie "
"Jazz byłoby rodzicem dla Bebop i Big Band"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:553
msgid "Describes the Parent field on the Edit Tags screen."
msgstr "Opisuje pole elementu nadrzędnego na ekranie edycji tagów."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:552
msgid "Parent Field Description"
msgstr "Opis pola nadrzędnego"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:538
#: includes/admin/views/acf-taxonomy/advanced-settings.php:539
msgid ""
"The \"slug\" is the URL-friendly version of the name. It is usually all "
"lower case and contains only letters, numbers, and hyphens."
msgstr ""
"„Uproszczona nazwa” jest przyjazną dla adresu URL wersją nazwy. Zwykle "
"składa się wyłącznie z małych liter, cyfr i myślników."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:537
msgid "Describes the Slug field on the Edit Tags screen."
msgstr "Opisuje pole uproszczonej nazwy na ekranie edycji tagów."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:536
msgid "Slug Field Description"
msgstr "Opis pola uproszczonej nazwy"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:522
#: includes/admin/views/acf-taxonomy/advanced-settings.php:523
msgid "The name is how it appears on your site"
msgstr "Nazwa jak pojawia się w witrynie"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:521
msgid "Describes the Name field on the Edit Tags screen."
msgstr "Opisuje pole nazwy na ekranie edycji tagów."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:520
msgid "Name Field Description"
msgstr "Opis pola nazwy"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:507
msgid "No tags"
msgstr "Brak tagów"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:506
msgid ""
"Assigns the text displayed in the posts and media list tables when no tags "
"or categories are available."
msgstr ""
"Przypisuje tekst wyświetlany w tabelach list wpisów i mediów gdy nie ma "
"żadnych tagów, ani kategorii."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:505
msgid "No Terms"
msgstr "Brak terminów"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:501
msgid "No %s"
msgstr "Brak „%s”"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:486
msgid "No tags found"
msgstr "Nie znaleziono żadnych tagów"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:485
msgid ""
"Assigns the text displayed when clicking the 'choose from most used' text in "
"the taxonomy meta box when no tags are available, and assigns the text used "
"in the terms list table when there are no items for a taxonomy."
msgstr ""
"Przypisuje tekst wyświetlany po kliknięciu „Wybierz z najczęściej używanych” "
"w metaboksie taksonomii gdy nie ma żadnych tagów, ani kategorii oraz tekst "
"używany w tabeli listy terminów gdy nie ma elementów z tej taksonomii."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:484
msgid "Not Found"
msgstr "Nie znaleziono"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:463
msgid "Assigns text to the Title field of the Most Used tab."
msgstr "Przypisuje tekst do pola Tytuł zakładki najczęściej używanych."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:462
#: includes/admin/views/acf-taxonomy/advanced-settings.php:464
#: includes/admin/views/acf-taxonomy/advanced-settings.php:465
msgid "Most Used"
msgstr "Najczęściej używane"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:444
msgid "Choose from the most used tags"
msgstr "Wybierz z najczęściej używanych tagów"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:443
msgid ""
"Assigns the 'choose from most used' text used in the meta box when "
"JavaScript is disabled. Only used on non-hierarchical taxonomies."
msgstr ""
"Przypisuje tekst „Wybierz z najczęściej używanych” w metaboksie, gdy "
"JavaScript jest wyłączony. Używane tylko w taksonomiach niehierarchicznych."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:442
msgid "Choose From Most Used"
msgstr "Wybierz z najczęściej używanych"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:438
msgid "Choose from the most used %s"
msgstr "Wybierz z najczęściej używanych „%s”"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:418
msgid "Add or remove tags"
msgstr "Dodaj lub usuń tagi"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:417
msgid ""
"Assigns the add or remove items text used in the meta box when JavaScript is "
"disabled. Only used on non-hierarchical taxonomies"
msgstr ""
"Przypisuje tekst dot. dodawania lub usuwania elementów w metaboksie, gdy "
"JavaScript jest wyłączony. Używane tylko w taksonomiach niehierarchicznych"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:416
msgid "Add Or Remove Items"
msgstr "Dodaj lub usuń elementy"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:412
msgid "Add or remove %s"
msgstr "Dodaj lub usuń %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:392
msgid "Separate tags with commas"
msgstr "Oddziel tagi przecinkami"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:391
msgid ""
"Assigns the separate item with commas text used in the taxonomy meta box. "
"Only used on non-hierarchical taxonomies."
msgstr ""
"Przypisuje tekst dot. oddzielania elementów przecinkami w metaboksie "
"taksonomii. Używane tylko w taksonomiach niehierarchicznych."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:390
msgid "Separate Items With Commas"
msgstr "Oddziel elementy przecinkami"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:386
msgid "Separate %s with commas"
msgstr "Oddziel %s przecinkami"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:366
msgid "Popular Tags"
msgstr "Popularne tagi"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:365
msgid "Assigns popular items text. Only used for non-hierarchical taxonomies."
msgstr ""
"Przypisuje tekst popularnych elementów. Używane tylko w taksonomiach "
"niehierarchicznych."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:364
msgid "Popular Items"
msgstr "Popularne elementy"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:361
msgid "Popular %s"
msgstr "Popularne %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:347
msgid "Search Tags"
msgstr "Szukaj tagów"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:346
msgid "Assigns search items text."
msgstr "Przypisuje tekst wyszukiwania elementów."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:323
msgid "Parent Category:"
msgstr "Kategoria nadrzędna:"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:322
msgid "Assigns parent item text, but with a colon (:) added to the end."
msgstr ""
"Przypisuje tekst elementu nadrzędnego, ale z dodanym na końcu dwukropkiem "
"(:)."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:321
msgid "Parent Item With Colon"
msgstr "Element nadrzędny z dwukropkiem"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:298
msgid "Parent Category"
msgstr "Kategoria nadrzędna"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:297
msgid "Assigns parent item text. Only used on hierarchical taxonomies."
msgstr ""
"Przypisuje tekst elementu nadrzędnego. Używane tylko w taksonomiach "
"hierarchicznych."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:296
msgid "Parent Item"
msgstr "Element nadrzędny"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:293
msgid "Parent %s"
msgstr "Element nadrzędny „%s”"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:278
msgid "New Tag Name"
msgstr "Nazwa nowego tagu"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:277
msgid "Assigns the new item name text."
msgstr "Przypisuje tekst nazwy nowego elementu."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:276
msgid "New Item Name"
msgstr "Nazwa nowego elementu"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:273
msgid "New %s Name"
msgstr "Nazwa nowego „%s”"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:258
msgid "Add New Tag"
msgstr "Utwórz tag"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:257
msgid "Assigns the add new item text."
msgstr "Przypisuje tekst dodania nowego elementu."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:238
msgid "Update Tag"
msgstr "Aktualizuj tag"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:237
msgid "Assigns the update item text."
msgstr "Przypisuje tekst zaktualizowania elementu."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:236
msgid "Update Item"
msgstr "Aktualizuj element"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:233
msgid "Update %s"
msgstr "Aktualizuj „%s”"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:218
msgid "View Tag"
msgstr "Zobacz tag"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:217
msgid "In the admin bar to view term during editing."
msgstr "Na pasku administratora do zobaczenia terminu podczas edycji."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:198
msgid "Edit Tag"
msgstr "Edytuj tag"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:197
msgid "At the top of the editor screen when editing a term."
msgstr "Na górze ekranu edycji podczas edytowania terminu."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:178
msgid "All Tags"
msgstr "Wszystkie tagi"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:177
msgid "Assigns the all items text."
msgstr "Przypisuje tekst wszystkich elementów."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:158
msgid "Assigns the menu name text."
msgstr "Przypisuje tekst nazwy menu."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:157
msgid "Menu Label"
msgstr "Etykieta menu"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:131
msgid "Active taxonomies are enabled and registered with WordPress."
msgstr "Włączone taksonomie są uruchamiane i rejestrowane razem z WordPressem."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:115
msgid "A descriptive summary of the taxonomy."
msgstr "Podsumowanie opisujące taksonomię."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:95
msgid "A descriptive summary of the term."
msgstr "Podsumowanie opisujące termin."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:94
msgid "Term Description"
msgstr "Opis terminu"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:76
msgid "Single word, no spaces. Underscores and dashes allowed."
msgstr "Pojedyncze słowo, bez spacji. Dozwolone są myślniki i podkreślniki."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:75
msgid "Term Slug"
msgstr "Uproszczona nazwa terminu"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:56
msgid "The name of the default term."
msgstr "Nazwa domyślnego terminu."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:55
msgid "Term Name"
msgstr "Nazwa terminu"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:41
msgid ""
"Create a term for the taxonomy that cannot be deleted. It will not be "
"selected for posts by default."
msgstr ""
"Utwórz termin taksonomii, którego nie będzie można usunąć. Nie będzie on "
"domyślnie wybrany dla wpisów."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:40
msgid "Default Term"
msgstr "Domyślny termin"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:28
msgid ""
"Whether terms in this taxonomy should be sorted in the order they are "
"provided to `wp_set_object_terms()`."
msgstr ""
"Czy terminy tej taksonomii powinny być posortowane w kolejności, w jakiej są "
"przekazywane do `wp_set_object_terms()`."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:27
msgid "Sort Terms"
msgstr "Sortuj terminy"

#: includes/admin/views/acf-post-type/list-empty.php:14
msgid "Add Post Type"
msgstr "Dodaj typ treści"

#: includes/admin/views/acf-post-type/list-empty.php:13
msgid ""
"Expand the functionality of WordPress beyond standard posts and pages with "
"custom post types."
msgstr ""
"Rozszerz funkcjonalność WordPressa ponad standardowe wpisy i strony za "
"pomocą własnych typów treści."

#: includes/admin/views/acf-post-type/list-empty.php:12
msgid "Add Your First Post Type"
msgstr "Dodaj swój pierwszy typ treści"

#: includes/admin/views/acf-post-type/basic-settings.php:136
#: includes/admin/views/acf-taxonomy/basic-settings.php:135
msgid "I know what I'm doing, show me all the options."
msgstr "Wiem co robię, pokaż mi wszystkie opcje."

#: includes/admin/views/acf-post-type/basic-settings.php:135
#: includes/admin/views/acf-taxonomy/basic-settings.php:134
msgid "Advanced Configuration"
msgstr "Zaawansowana konfiguracja"

#: includes/admin/views/acf-post-type/basic-settings.php:123
msgid "Hierarchical post types can have descendants (like pages)."
msgstr "Hierarchiczne typy treści mogą posiadać elementy potomne (jak strony)."

#: includes/admin/views/acf-post-type/basic-settings.php:122
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1000
#: includes/admin/views/acf-taxonomy/basic-settings.php:121
msgid "Hierarchical"
msgstr "Hierarchiczne"

#: includes/admin/views/acf-post-type/basic-settings.php:107
msgid "Visible on the frontend and in the admin dashboard."
msgstr "Widoczne w witrynie oraz w kokpicie administratora."

#: includes/admin/views/acf-post-type/basic-settings.php:106
#: includes/admin/views/acf-taxonomy/basic-settings.php:106
msgid "Public"
msgstr "Publiczne"

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:59
msgid "movie"
msgstr "film"

#: includes/admin/views/acf-post-type/basic-settings.php:57
msgid "Lower case letters, underscores and dashes only, Max 20 characters."
msgstr "Tylko małe litery, myślniki i podkreślniki. Maksymalnie 20 znaków."

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:41
msgid "Movie"
msgstr "Film"

#: includes/admin/views/acf-post-type/basic-settings.php:39
#: includes/admin/views/acf-taxonomy/basic-settings.php:40
msgid "Singular Label"
msgstr "Etykieta w liczbie pojedynczej"

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:24
msgid "Movies"
msgstr "Filmy"

#: includes/admin/views/acf-post-type/basic-settings.php:22
#: includes/admin/views/acf-taxonomy/basic-settings.php:23
msgid "Plural Label"
msgstr "Etykieta w liczbie mnogiej"

#: includes/admin/views/acf-post-type/advanced-settings.php:1313
msgid ""
"Optional custom controller to use instead of `WP_REST_Posts_Controller`."
msgstr ""
"Opcjonalny własny kontroler do użycia zamiast `WP_REST_Posts_Controller`."

#: includes/admin/views/acf-post-type/advanced-settings.php:1312
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1230
msgid "Controller Class"
msgstr "Klasa kontrolera"

#: includes/admin/views/acf-post-type/advanced-settings.php:1294
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1211
msgid "The namespace part of the REST API URL."
msgstr "Część przestrzeni nazw adresu URL REST API."

#: includes/admin/views/acf-post-type/advanced-settings.php:1293
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1210
msgid "Namespace Route"
msgstr "Ścieżka przestrzeni nazw"

#: includes/admin/views/acf-post-type/advanced-settings.php:1275
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1192
msgid "The base URL for the post type REST API URLs."
msgstr "Bazowy adres URL tego typu treści dla adresów URL REST API."

#: includes/admin/views/acf-post-type/advanced-settings.php:1274
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1191
msgid "Base URL"
msgstr "Bazowy adres URL"

#: includes/admin/views/acf-post-type/advanced-settings.php:1260
msgid ""
"Exposes this post type in the REST API. Required to use the block editor."
msgstr ""
"Pokaż ten typ treści w REST API. Wymagane, aby używać edytora blokowego."

#: includes/admin/views/acf-post-type/advanced-settings.php:1259
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1174
msgid "Show In REST API"
msgstr "Pokaż w REST API"

#: includes/admin/views/acf-post-type/advanced-settings.php:1238
msgid "Customize the query variable name."
msgstr "Dostosuj nazwę zmiennej zapytania."

#: includes/admin/views/acf-post-type/advanced-settings.php:1237
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1074
msgid "Query Variable"
msgstr "Zmienna zapytania"

#: includes/admin/views/acf-post-type/advanced-settings.php:1215
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1052
msgid "No Query Variable Support"
msgstr "Brak obsługi zmiennej zapytania"

#: includes/admin/views/acf-post-type/advanced-settings.php:1214
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1051
msgid "Custom Query Variable"
msgstr "Własna zmienna zapytania"

#: includes/admin/views/acf-post-type/advanced-settings.php:1211
msgid ""
"Items can be accessed using the non-pretty permalink, eg. {post_type}"
"={post_slug}."
msgstr ""
"Elementy są dostępne za pomocą nieprzyjaznych bezpośrednich odnośników, np. "
"{typ_treści}={uproszczona_nazwa_wpisu}."

#: includes/admin/views/acf-post-type/advanced-settings.php:1210
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1047
msgid "Query Variable Support"
msgstr "Obsługa zmiennej zapytania"

#: includes/admin/views/acf-post-type/advanced-settings.php:1185
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1023
msgid "URLs for an item and items can be accessed with a query string."
msgstr ""
"Adresy URL elementu i elementów są dostępne za pomocą ciągu znaków zapytania."

#: includes/admin/views/acf-post-type/advanced-settings.php:1184
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1022
msgid "Publicly Queryable"
msgstr "Publicznie dostępne"

#: includes/admin/views/acf-post-type/advanced-settings.php:1163
msgid "Custom slug for the Archive URL."
msgstr "Własna uproszczona nazwa dla adresu URL archiwum."

#: includes/admin/views/acf-post-type/advanced-settings.php:1162
msgid "Archive Slug"
msgstr "Uproszczona nazwa archiwum"

#: includes/admin/views/acf-post-type/advanced-settings.php:1149
msgid ""
"Has an item archive that can be customized with an archive template file in "
"your theme."
msgstr ""
"Posiada archiwum elementów, które można dostosować za pomocą szablonu "
"archiwum w motywie."

#: includes/admin/views/acf-post-type/advanced-settings.php:1148
msgid "Archive"
msgstr "Archiwum"

#: includes/admin/views/acf-post-type/advanced-settings.php:1128
msgid "Pagination support for the items URLs such as the archives."
msgstr "Obsługa stronicowania adresów URL elementów takich jak archiwa."

#: includes/admin/views/acf-post-type/advanced-settings.php:1127
msgid "Pagination"
msgstr "Stronicowanie"

#: includes/admin/views/acf-post-type/advanced-settings.php:1110
msgid "RSS feed URL for the post type items."
msgstr "Adres URL kanału RSS elementów tego typu treści."

#: includes/admin/views/acf-post-type/advanced-settings.php:1109
msgid "Feed URL"
msgstr "Adres URL kanału informacyjnego"

#: includes/admin/views/acf-post-type/advanced-settings.php:1091
#: includes/admin/views/acf-taxonomy/advanced-settings.php:981
msgid ""
"Alters the permalink structure to add the `WP_Rewrite::$front` prefix to "
"URLs."
msgstr ""
"Modyfikuje strukturę bezpośrednich odnośników dodając prefiks `WP_Rewrite::"
"$front` do adresów URL."

#: includes/admin/views/acf-post-type/advanced-settings.php:1090
#: includes/admin/views/acf-taxonomy/advanced-settings.php:980
msgid "Front URL Prefix"
msgstr "Prefiks adresu URL"

#: includes/admin/views/acf-post-type/advanced-settings.php:1071
msgid "Customize the slug used in the URL."
msgstr "Dostosuj uproszczoną nazwę używaną w adresie URL."

#: includes/admin/views/acf-post-type/advanced-settings.php:1070
#: includes/admin/views/acf-taxonomy/advanced-settings.php:960
msgid "URL Slug"
msgstr "Uproszczona nazwa w adresie URL"

#: includes/admin/views/acf-post-type/advanced-settings.php:1054
msgid "Permalinks for this post type are disabled."
msgstr "Bezpośrednie odnośniki tego typu treści są wyłączone."

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-post-type/advanced-settings.php:1053
#: includes/admin/views/acf-taxonomy/advanced-settings.php:943
msgid ""
"Rewrite the URL using a custom slug defined in the input below. Your "
"permalink structure will be"
msgstr ""
"Przepisuj adres URL używając własnej uproszczonej nazwy określonej w polu "
"poniżej. Struktura bezpośrednich odnośników będzie następująca"

#: includes/admin/views/acf-post-type/advanced-settings.php:1045
#: includes/admin/views/acf-taxonomy/advanced-settings.php:935
msgid "No Permalink (prevent URL rewriting)"
msgstr "Brak bezpośredniego odnośnika (wyłącz przepisywanie adresów URL)"

#: includes/admin/views/acf-post-type/advanced-settings.php:1044
#: includes/admin/views/acf-taxonomy/advanced-settings.php:934
msgid "Custom Permalink"
msgstr "Własny bezpośredni odnośnik"

#: includes/admin/views/acf-post-type/advanced-settings.php:1043
#: includes/admin/views/acf-post-type/advanced-settings.php:1213
#: includes/admin/views/acf-post-type/basic-settings.php:56
msgid "Post Type Key"
msgstr "Klucz typu treści"

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-post-type/advanced-settings.php:1041
#: includes/admin/views/acf-post-type/advanced-settings.php:1051
msgid ""
"Rewrite the URL using the post type key as the slug. Your permalink "
"structure will be"
msgstr ""
"Przepisuj adres URL używając klucza typu treści jako uproszczonej nazwy. "
"Struktura bezpośrednich odnośników będzie następująca"

#: includes/admin/views/acf-post-type/advanced-settings.php:1039
#: includes/admin/views/acf-taxonomy/advanced-settings.php:930
msgid "Permalink Rewrite"
msgstr "Przepisywanie bezpośrednich odnośników"

#: includes/admin/views/acf-post-type/advanced-settings.php:1025
msgid "Delete items by a user when that user is deleted."
msgstr "Usuwaj elementy należące do użytkownika podczas jego usuwania."

#: includes/admin/views/acf-post-type/advanced-settings.php:1024
msgid "Delete With User"
msgstr "Usuń wraz z użytkownikiem"

#: includes/admin/views/acf-post-type/advanced-settings.php:1010
msgid "Allow the post type to be exported from 'Tools' > 'Export'."
msgstr ""
"Zezwól na eksportowanie tego typu treści na ekranie „Narzędzia > Eksport”."

#: includes/admin/views/acf-post-type/advanced-settings.php:1009
msgid "Can Export"
msgstr "Można eksportować"

#: includes/admin/views/acf-post-type/advanced-settings.php:978
msgid "Optionally provide a plural to be used in capabilities."
msgstr "Opcjonalnie podaj liczbę mnogą, aby użyć jej w uprawnieniach."

#: includes/admin/views/acf-post-type/advanced-settings.php:977
msgid "Plural Capability Name"
msgstr "Nazwa uprawnienia w liczbie mnogiej"

#: includes/admin/views/acf-post-type/advanced-settings.php:959
msgid "Choose another post type to base the capabilities for this post type."
msgstr ""
"Wybierz inny typ treści, aby bazować na jego uprawnieniach dla tego typu "
"treści."

#: includes/admin/views/acf-post-type/advanced-settings.php:958
msgid "Singular Capability Name"
msgstr "Nazwa uprawnienia w liczbie pojedynczej"

#: includes/admin/views/acf-post-type/advanced-settings.php:944
msgid ""
"By default the capabilities of the post type will inherit the 'Post' "
"capability names, eg. edit_post, delete_posts. Enable to use post type "
"specific capabilities, eg. edit_{singular}, delete_{plural}."
msgstr ""
"Domyślnie nazwy uprawnienia tego typu treści zostaną odziedziczone z "
"uprawnień typu „Wpis”, np. edit_post, delete_posts. Włącz, aby użyć w tym "
"typie treści własnych uprawnień, np. edit_{liczba pojedyncza}, "
"delete_{liczba mnoga}."

#: includes/admin/views/acf-post-type/advanced-settings.php:943
msgid "Rename Capabilities"
msgstr "Zmień nazwy uprawnień"

#: includes/admin/views/acf-post-type/advanced-settings.php:928
msgid "Exclude From Search"
msgstr "Wyklucz z wyszukiwania"

#: includes/admin/views/acf-post-type/advanced-settings.php:915
#: includes/admin/views/acf-taxonomy/advanced-settings.php:874
msgid ""
"Allow items to be added to menus in the 'Appearance' > 'Menus' screen. Must "
"be turned on in 'Screen options'."
msgstr ""
"Zezwól na dodawanie elementów do menu na ekranie „Wygląd > Menu”. Należy je "
"włączyć w „Opcjach ekranu”."

#: includes/admin/views/acf-post-type/advanced-settings.php:914
#: includes/admin/views/acf-taxonomy/advanced-settings.php:873
msgid "Appearance Menus Support"
msgstr "Obsługa menu wyglądu"

#: includes/admin/views/acf-post-type/advanced-settings.php:896
msgid "Appears as an item in the 'New' menu in the admin bar."
msgstr "Pojawia się jako element w menu „Utwórz” na pasku administratora."

#: includes/admin/views/acf-post-type/advanced-settings.php:895
msgid "Show In Admin Bar"
msgstr "Pokaż na pasku administratora"

#: includes/admin/views/acf-post-type/advanced-settings.php:861
msgid "Custom Meta Box Callback"
msgstr "Własna funkcja zwrotna metaboksa"

#: includes/admin/views/acf-post-type/advanced-settings.php:822
#: includes/fields/class-acf-field-icon_picker.php:636
msgid "Menu Icon"
msgstr "Ikonka menu"

#: includes/admin/views/acf-post-type/advanced-settings.php:778
msgid "The position in the sidebar menu in the admin dashboard."
msgstr "Pozycja w menu w panelu bocznym w kokpicie administratora."

#: includes/admin/views/acf-post-type/advanced-settings.php:777
msgid "Menu Position"
msgstr "Pozycja menu"

#: includes/admin/views/acf-post-type/advanced-settings.php:759
msgid ""
"By default the post type will get a new top level item in the admin menu. If "
"an existing top level item is supplied here, the post type will be added as "
"a submenu item under it."
msgstr ""
"Domyślnie typ treści otrzyma nowy element najwyższego poziomu w menu "
"administratora. Jeżeli zostanie tu podany istniejący element najwyższego "
"poziomu, typ treści zostanie dodany do niego jako element podmenu."

#: includes/admin/views/acf-post-type/advanced-settings.php:758
msgid "Admin Menu Parent"
msgstr "Element nadrzędny menu administratora"

#: includes/admin/views/acf-post-type/advanced-settings.php:739
#: includes/admin/views/acf-taxonomy/advanced-settings.php:734
msgid "Admin editor navigation in the sidebar menu."
msgstr "Nawigacja w menu w panelu bocznym kokpitu administratora."

#: includes/admin/views/acf-post-type/advanced-settings.php:738
#: includes/admin/views/acf-taxonomy/advanced-settings.php:733
msgid "Show In Admin Menu"
msgstr "Pokaż w menu administratora"

#: includes/admin/views/acf-post-type/advanced-settings.php:725
#: includes/admin/views/acf-taxonomy/advanced-settings.php:719
msgid "Items can be edited and managed in the admin dashboard."
msgstr "Elementy mogą być edytowanie i zarządzane w kokpicie administratora."

#: includes/admin/views/acf-post-type/advanced-settings.php:724
#: includes/admin/views/acf-taxonomy/advanced-settings.php:718
msgid "Show In UI"
msgstr "Pokaż w interfejsie użytkownika"

#: includes/admin/views/acf-post-type/advanced-settings.php:694
msgid "A link to a post."
msgstr "Odnośnik do wpisu."

#: includes/admin/views/acf-post-type/advanced-settings.php:693
msgid "Description for a navigation link block variation."
msgstr "Opis wersji bloku odnośnika."

#: includes/admin/views/acf-post-type/advanced-settings.php:692
#: includes/admin/views/acf-taxonomy/advanced-settings.php:702
msgid "Item Link Description"
msgstr "Opis odnośnika elementu"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:688
msgid "A link to a %s."
msgstr "Odnośnik do „%s”."

#: includes/admin/views/acf-post-type/advanced-settings.php:673
msgid "Post Link"
msgstr "Odnośnik wpisu"

#: includes/admin/views/acf-post-type/advanced-settings.php:672
msgid "Title for a navigation link block variation."
msgstr "Tytuł wersji bloku odnośnika."

#: includes/admin/views/acf-post-type/advanced-settings.php:671
#: includes/admin/views/acf-taxonomy/advanced-settings.php:681
msgid "Item Link"
msgstr "Odnośnik elementu"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:668
#: includes/admin/views/acf-taxonomy/advanced-settings.php:678
msgid "%s Link"
msgstr "Odnośnik „%s”"

#: includes/admin/views/acf-post-type/advanced-settings.php:653
msgid "Post updated."
msgstr "Wpis został zaktualizowany."

#: includes/admin/views/acf-post-type/advanced-settings.php:652
msgid "In the editor notice after an item is updated."
msgstr "Powiadomienie w edytorze po zaktualizowaniu elementu."

#: includes/admin/views/acf-post-type/advanced-settings.php:651
msgid "Item Updated"
msgstr "Element został zaktualizowany"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:648
msgid "%s updated."
msgstr "„%s” został zaktualizowany."

#: includes/admin/views/acf-post-type/advanced-settings.php:633
msgid "Post scheduled."
msgstr "Zaplanowano publikację wpisu."

#: includes/admin/views/acf-post-type/advanced-settings.php:632
msgid "In the editor notice after scheduling an item."
msgstr "Powiadomienie w edytorze po zaplanowaniu publikacji elementu."

#: includes/admin/views/acf-post-type/advanced-settings.php:631
msgid "Item Scheduled"
msgstr "Zaplanowano publikację elementu"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:628
msgid "%s scheduled."
msgstr "Zaplanowano publikację „%s”."

#: includes/admin/views/acf-post-type/advanced-settings.php:613
msgid "Post reverted to draft."
msgstr "Wpis zamieniony w szkic."

#: includes/admin/views/acf-post-type/advanced-settings.php:612
msgid "In the editor notice after reverting an item to draft."
msgstr "Powiadomienie w edytorze po zamienieniu elementu w szkic."

#: includes/admin/views/acf-post-type/advanced-settings.php:611
msgid "Item Reverted To Draft"
msgstr "Element zamieniony w szkic"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:608
msgid "%s reverted to draft."
msgstr "„%s” zamieniony w szkic."

#: includes/admin/views/acf-post-type/advanced-settings.php:593
msgid "Post published privately."
msgstr "Wpis został opublikowany jako prywatny."

#: includes/admin/views/acf-post-type/advanced-settings.php:592
msgid "In the editor notice after publishing a private item."
msgstr "Powiadomienie w edytorze po opublikowaniu elementu jako prywatny."

#: includes/admin/views/acf-post-type/advanced-settings.php:591
msgid "Item Published Privately"
msgstr "Element został opublikowany jako prywatny"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:588
msgid "%s published privately."
msgstr "„%s” został opublikowany jako prywatny."

#: includes/admin/views/acf-post-type/advanced-settings.php:573
msgid "Post published."
msgstr "Wpis został opublikowany."

#: includes/admin/views/acf-post-type/advanced-settings.php:572
msgid "In the editor notice after publishing an item."
msgstr "Powiadomienie w edytorze po opublikowaniu elementu."

#: includes/admin/views/acf-post-type/advanced-settings.php:571
msgid "Item Published"
msgstr "Element został opublikowany"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:568
msgid "%s published."
msgstr "„%s” został opublikowany."

#: includes/admin/views/acf-post-type/advanced-settings.php:553
msgid "Posts list"
msgstr "Lista wpisów"

#: includes/admin/views/acf-post-type/advanced-settings.php:552
msgid "Used by screen readers for the items list on the post type list screen."
msgstr ""
"Używane przez czytniki ekranu do listy elementów na ekranie listy wpisów "
"tego typu treści."

#: includes/admin/views/acf-post-type/advanced-settings.php:551
#: includes/admin/views/acf-taxonomy/advanced-settings.php:640
msgid "Items List"
msgstr "Lista elementów"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:548
#: includes/admin/views/acf-taxonomy/advanced-settings.php:637
msgid "%s list"
msgstr "Lista „%s”"

#: includes/admin/views/acf-post-type/advanced-settings.php:533
msgid "Posts list navigation"
msgstr "Nawigacja listy wpisów"

#: includes/admin/views/acf-post-type/advanced-settings.php:532
msgid ""
"Used by screen readers for the filter list pagination on the post type list "
"screen."
msgstr ""
"Używane przez czytniki ekranu do stronicowania na liście filtrów na ekranie "
"listy wpisów tego typu treści."

#: includes/admin/views/acf-post-type/advanced-settings.php:531
#: includes/admin/views/acf-taxonomy/advanced-settings.php:620
msgid "Items List Navigation"
msgstr "Nawigacja listy elementów"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:528
#: includes/admin/views/acf-taxonomy/advanced-settings.php:617
msgid "%s list navigation"
msgstr "Nawigacja listy „%s”"

#: includes/admin/views/acf-post-type/advanced-settings.php:512
msgid "Filter posts by date"
msgstr "Filtruj wpisy wg daty"

#: includes/admin/views/acf-post-type/advanced-settings.php:511
msgid ""
"Used by screen readers for the filter by date heading on the post type list "
"screen."
msgstr ""
"Używane przez czytniki ekranu do nagłówka filtrowania wg daty na ekranie "
"listy wpisów tego typu treści."

#: includes/admin/views/acf-post-type/advanced-settings.php:510
msgid "Filter Items By Date"
msgstr "Filtruj elementy wg daty"

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:506
msgid "Filter %s by date"
msgstr "Filtruj %s wg daty"

#: includes/admin/views/acf-post-type/advanced-settings.php:491
msgid "Filter posts list"
msgstr "Filtrowanie listy wpisów"

#: includes/admin/views/acf-post-type/advanced-settings.php:490
msgid ""
"Used by screen readers for the filter links heading on the post type list "
"screen."
msgstr ""
"Używane przez czytniki ekranu do nagłówka odnośników filtrowania na ekranie "
"listy wpisów tego typu treści."

#: includes/admin/views/acf-post-type/advanced-settings.php:489
msgid "Filter Items List"
msgstr "Filtrowanie listy elementów"

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:485
msgid "Filter %s list"
msgstr "Filtrowanie listy „%s”"

#: includes/admin/views/acf-post-type/advanced-settings.php:469
msgid "In the media modal showing all media uploaded to this item."
msgstr "W oknie mediów wyświetlającym wszystkie media wgrane do tego elementu."

#: includes/admin/views/acf-post-type/advanced-settings.php:468
msgid "Uploaded To This Item"
msgstr "Wgrane do elementu"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:464
msgid "Uploaded to this %s"
msgstr "Wgrane do „%s”"

#: includes/admin/views/acf-post-type/advanced-settings.php:449
msgid "Insert into post"
msgstr "Wstaw do wpisu"

#: includes/admin/views/acf-post-type/advanced-settings.php:448
msgid "As the button label when adding media to content."
msgstr "Jako etykieta przycisku podczas dodawania mediów do treści."

#: includes/admin/views/acf-post-type/advanced-settings.php:447
msgid "Insert Into Media Button"
msgstr "Przycisk wstawiania mediów"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:443
msgid "Insert into %s"
msgstr "Wstaw do „%s”"

#: includes/admin/views/acf-post-type/advanced-settings.php:428
msgid "Use as featured image"
msgstr "Użyj jako obrazek wyróżniający"

#: includes/admin/views/acf-post-type/advanced-settings.php:427
msgid ""
"As the button label for selecting to use an image as the featured image."
msgstr ""
"Jako etykieta przycisku podczas wybierania obrazka do użycia jako obrazka "
"wyróżniającego."

#: includes/admin/views/acf-post-type/advanced-settings.php:426
msgid "Use Featured Image"
msgstr "Użyj obrazka wyróżniającego"

#: includes/admin/views/acf-post-type/advanced-settings.php:413
msgid "Remove featured image"
msgstr "Usuń obrazek wyróżniający"

#: includes/admin/views/acf-post-type/advanced-settings.php:412
msgid "As the button label when removing the featured image."
msgstr "Jako etykieta przycisku podczas usuwania obrazka wyróżniającego."

#: includes/admin/views/acf-post-type/advanced-settings.php:411
msgid "Remove Featured Image"
msgstr "Usuń obrazek wyróżniający"

#: includes/admin/views/acf-post-type/advanced-settings.php:398
msgid "Set featured image"
msgstr "Ustaw obrazek wyróżniający"

#: includes/admin/views/acf-post-type/advanced-settings.php:397
msgid "As the button label when setting the featured image."
msgstr "Jako etykieta przycisku podczas ustawiania obrazka wyróżniającego."

#: includes/admin/views/acf-post-type/advanced-settings.php:396
msgid "Set Featured Image"
msgstr "Ustaw obrazek wyróżniający"

#: includes/admin/views/acf-post-type/advanced-settings.php:383
msgid "Featured image"
msgstr "Obrazek wyróżniający"

#: includes/admin/views/acf-post-type/advanced-settings.php:382
msgid "In the editor used for the title of the featured image meta box."
msgstr "W edytorze, używane jako tytuł metaboksa obrazka wyróżniającego."

#: includes/admin/views/acf-post-type/advanced-settings.php:381
msgid "Featured Image Meta Box"
msgstr "Metaboks obrazka wyróżniającego"

#: includes/admin/views/acf-post-type/advanced-settings.php:368
msgid "Post Attributes"
msgstr "Atrybuty wpisu"

#: includes/admin/views/acf-post-type/advanced-settings.php:367
msgid "In the editor used for the title of the post attributes meta box."
msgstr "W edytorze, używane jako tytuł metaboksa atrybutów wpisu."

#: includes/admin/views/acf-post-type/advanced-settings.php:366
msgid "Attributes Meta Box"
msgstr "Metaboks atrybutów"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:363
msgid "%s Attributes"
msgstr "Atrybuty „%s”"

#: includes/admin/views/acf-post-type/advanced-settings.php:348
msgid "Post Archives"
msgstr "Archiwa wpisów"

#: includes/admin/views/acf-post-type/advanced-settings.php:347
msgid ""
"Adds 'Post Type Archive' items with this label to the list of posts shown "
"when adding items to an existing menu in a CPT with archives enabled. Only "
"appears when editing menus in 'Live Preview' mode and a custom archive slug "
"has been provided."
msgstr ""
"Dodaje element „Archiwa typu treści”, o tej etykiecie, do listy wpisów "
"własnych typów treści z włączonymi archiwami, wyświetlanych podczas "
"dodawania elementów do istniejącego menu. Pojawia się podczas edytowania "
"menu w trybie „Podgląd na żywo”, gdy podano własną uproszczoną nazwę "
"archiwów."

#: includes/admin/views/acf-post-type/advanced-settings.php:346
msgid "Archives Nav Menu"
msgstr "Menu nawigacyjne archiwów"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:343
msgid "%s Archives"
msgstr "Archiwa „%s”"

#: includes/admin/views/acf-post-type/advanced-settings.php:328
msgid "No posts found in Trash"
msgstr "Nie znaleziono żadnych wpisów w koszu"

#: includes/admin/views/acf-post-type/advanced-settings.php:327
msgid ""
"At the top of the post type list screen when there are no posts in the trash."
msgstr "Na górze ekranu listy wpisów typu treści gdy brak wpisów w koszu."

#: includes/admin/views/acf-post-type/advanced-settings.php:326
msgid "No Items Found in Trash"
msgstr "Nie znaleziono żadnych elementów w koszu"

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:322
msgid "No %s found in Trash"
msgstr "Nie znaleziono żadnych „%s” w koszu"

#: includes/admin/views/acf-post-type/advanced-settings.php:307
msgid "No posts found"
msgstr "Nie znaleziono żadnych wpisów"

#: includes/admin/views/acf-post-type/advanced-settings.php:306
msgid ""
"At the top of the post type list screen when there are no posts to display."
msgstr ""
"Na górze ekranu listy wpisów typu treści gdy brak wpisów do wyświetlenia."

#: includes/admin/views/acf-post-type/advanced-settings.php:305
msgid "No Items Found"
msgstr "Nie znaleziono żadnych elementów"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:301
#: includes/admin/views/acf-taxonomy/advanced-settings.php:480
msgid "No %s found"
msgstr "Nie znaleziono żadnych „%s”"

#: includes/admin/views/acf-post-type/advanced-settings.php:286
msgid "Search Posts"
msgstr "Szukaj wpisów"

#: includes/admin/views/acf-post-type/advanced-settings.php:285
msgid "At the top of the items screen when searching for an item."
msgstr "Na górze ekranu elementów podczas szukania elementu."

#: includes/admin/views/acf-post-type/advanced-settings.php:284
#: includes/admin/views/acf-taxonomy/advanced-settings.php:345
msgid "Search Items"
msgstr "Szukaj elementów"

#. translators: %s Singular form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:281
#: includes/admin/views/acf-taxonomy/advanced-settings.php:342
msgid "Search %s"
msgstr "Szukaj „%s”"

#: includes/admin/views/acf-post-type/advanced-settings.php:266
msgid "Parent Page:"
msgstr "Strona nadrzędna:"

#: includes/admin/views/acf-post-type/advanced-settings.php:265
msgid "For hierarchical types in the post type list screen."
msgstr "Dla hierarchicznych typów na ekranie listy wpisów tego typu treści."

#: includes/admin/views/acf-post-type/advanced-settings.php:264
msgid "Parent Item Prefix"
msgstr "Prefiks elementu nadrzędnego"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:261
#: includes/admin/views/acf-taxonomy/advanced-settings.php:318
msgid "Parent %s:"
msgstr "Nadrzędny „%s”:"

#: includes/admin/views/acf-post-type/advanced-settings.php:246
msgid "New Post"
msgstr "Nowy wpis"

#: includes/admin/views/acf-post-type/advanced-settings.php:244
msgid "New Item"
msgstr "Nowy element"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:241
msgid "New %s"
msgstr "Nowy „%s”"

#: includes/admin/views/acf-post-type/advanced-settings.php:206
#: includes/admin/views/acf-post-type/advanced-settings.php:226
msgid "Add New Post"
msgstr "Utwórz wpis"

#: includes/admin/views/acf-post-type/advanced-settings.php:205
msgid "At the top of the editor screen when adding a new item."
msgstr "Na górze ekranu edycji podczas dodawania nowego elementu."

#: includes/admin/views/acf-post-type/advanced-settings.php:204
#: includes/admin/views/acf-taxonomy/advanced-settings.php:256
msgid "Add New Item"
msgstr "Utwórz element"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:201
#: includes/admin/views/acf-post-type/advanced-settings.php:221
#: includes/admin/views/acf-taxonomy/advanced-settings.php:253
msgid "Add New %s"
msgstr "Utwórz „%s”"

#: includes/admin/views/acf-post-type/advanced-settings.php:186
msgid "View Posts"
msgstr "Zobacz wpisy"

#: includes/admin/views/acf-post-type/advanced-settings.php:185
msgid ""
"Appears in the admin bar in the 'All Posts' view, provided the post type "
"supports archives and the home page is not an archive of that post type."
msgstr ""
"Pojawia się na pasku administratora w widoku „Wszystkie wpisy”, pod "
"warunkiem, że typ treści obsługuje archiwa, a strona główna nie jest "
"archiwum tego typu treści."

#: includes/admin/views/acf-post-type/advanced-settings.php:184
msgid "View Items"
msgstr "Zobacz elementy"

#: includes/admin/views/acf-post-type/advanced-settings.php:166
msgid "View Post"
msgstr "Zobacz wpis"

#: includes/admin/views/acf-post-type/advanced-settings.php:165
msgid "In the admin bar to view item when editing it."
msgstr "Na pasku administratora do zobaczenia elementu podczas jego edycji."

#: includes/admin/views/acf-post-type/advanced-settings.php:164
#: includes/admin/views/acf-taxonomy/advanced-settings.php:216
msgid "View Item"
msgstr "Zobacz element"

#. translators: %s Singular form of post type name
#. translators: %s Plural form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:161
#: includes/admin/views/acf-post-type/advanced-settings.php:181
#: includes/admin/views/acf-taxonomy/advanced-settings.php:213
msgid "View %s"
msgstr "Zobacz „%s”"

#: includes/admin/views/acf-post-type/advanced-settings.php:146
msgid "Edit Post"
msgstr "Edytuj wpis"

#: includes/admin/views/acf-post-type/advanced-settings.php:145
msgid "At the top of the editor screen when editing an item."
msgstr "Na górze ekranu edycji podczas edytowania elementu."

#: includes/admin/views/acf-post-type/advanced-settings.php:144
#: includes/admin/views/acf-taxonomy/advanced-settings.php:196
msgid "Edit Item"
msgstr "Edytuj element"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:141
#: includes/admin/views/acf-taxonomy/advanced-settings.php:193
msgid "Edit %s"
msgstr "Edytuj „%s”"

#: includes/admin/views/acf-post-type/advanced-settings.php:126
msgid "All Posts"
msgstr "Wszystkie wpisy"

#: includes/admin/views/acf-post-type/advanced-settings.php:125
#: includes/admin/views/acf-post-type/advanced-settings.php:225
#: includes/admin/views/acf-post-type/advanced-settings.php:245
msgid "In the post type submenu in the admin dashboard."
msgstr "W podmenu typu treści w kokpicie administratora."

#: includes/admin/views/acf-post-type/advanced-settings.php:124
#: includes/admin/views/acf-taxonomy/advanced-settings.php:176
msgid "All Items"
msgstr "Wszystkie elementy"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:121
#: includes/admin/views/acf-taxonomy/advanced-settings.php:173
msgid "All %s"
msgstr "Wszystkie %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:105
msgid "Admin menu name for the post type."
msgstr "Nazwa menu administracyjnego tego typu treści."

#: includes/admin/views/acf-post-type/advanced-settings.php:104
msgid "Menu Name"
msgstr "Nazwa menu"

#: includes/admin/views/acf-post-type/advanced-settings.php:90
#: includes/admin/views/acf-taxonomy/advanced-settings.php:142
msgid "Regenerate all labels using the Singular and Plural labels"
msgstr ""
"Odnów wszystkie etykiety używając etykiet w liczbie pojedynczej i mnogiej"

#: includes/admin/views/acf-post-type/advanced-settings.php:88
#: includes/admin/views/acf-taxonomy/advanced-settings.php:140
msgid "Regenerate"
msgstr "Odnów"

#: includes/admin/views/acf-post-type/advanced-settings.php:79
msgid "Active post types are enabled and registered with WordPress."
msgstr ""
"Włączone typy treści są uruchamiane i rejestrowane razem z WordPressem."

#: includes/admin/views/acf-post-type/advanced-settings.php:63
msgid "A descriptive summary of the post type."
msgstr "Podsumowanie opisujące typ treści."

#: includes/admin/views/acf-post-type/advanced-settings.php:48
msgid "Add Custom"
msgstr "Dodaj własną"

#: includes/admin/views/acf-post-type/advanced-settings.php:42
msgid "Enable various features in the content editor."
msgstr "Włącz różne funkcje w edytorze treści."

#: includes/admin/views/acf-post-type/advanced-settings.php:31
msgid "Post Formats"
msgstr "Formaty wpisów"

#: includes/admin/views/acf-post-type/advanced-settings.php:25
msgid "Editor"
msgstr "Edytor"

#: includes/admin/views/acf-post-type/advanced-settings.php:24
msgid "Trackbacks"
msgstr "Trackbacki"

#: includes/admin/views/acf-post-type/basic-settings.php:87
msgid "Select existing taxonomies to classify items of the post type."
msgstr "Wybierz istniejące taksonomie, aby klasyfikować ten typ treści."

#: includes/admin/views/acf-field-group/field.php:158
msgid "Browse Fields"
msgstr "Przeglądaj pola"

#: includes/admin/tools/class-acf-admin-tool-import.php:287
msgid "Nothing to import"
msgstr "Brak danych do importu"

#: includes/admin/tools/class-acf-admin-tool-import.php:282
msgid ". The Custom Post Type UI plugin can be deactivated."
msgstr ". Wtyczka „Custom Post Type UI” może zostać wyłączona."

#. translators: %d - number of items imported from CPTUI
#: includes/admin/tools/class-acf-admin-tool-import.php:273
msgid "Imported %d item from Custom Post Type UI -"
msgid_plural "Imported %d items from Custom Post Type UI -"
msgstr[0] "Zaimportowano %d element z „Custom Post Type UI” -"
msgstr[1] "Zaimportowano %d elementy z „Custom Post Type UI” -"
msgstr[2] "Zaimportowano %d elementów z „Custom Post Type UI” -"

#: includes/admin/tools/class-acf-admin-tool-import.php:257
msgid "Failed to import taxonomies."
msgstr "Nie udało się zaimportować taksonomii."

#: includes/admin/tools/class-acf-admin-tool-import.php:239
msgid "Failed to import post types."
msgstr "Nie udało się zaimportować typów treści."

#: includes/admin/tools/class-acf-admin-tool-import.php:228
msgid "Nothing from Custom Post Type UI plugin selected for import."
msgstr "Nie wybrano niczego do zaimportowania z wtyczki „Custom Post Type UI”."

#: includes/admin/tools/class-acf-admin-tool-import.php:204
msgid "Imported 1 item"
msgid_plural "Imported %s items"
msgstr[0] "Zaimportowano 1 element"
msgstr[1] "Zaimportowano %s elementy"
msgstr[2] "Zaimportowano %s elementów"

#: includes/admin/tools/class-acf-admin-tool-import.php:119
msgid ""
"Importing a Post Type or Taxonomy with the same key as one that already "
"exists will overwrite the settings for the existing Post Type or Taxonomy "
"with those of the import."
msgstr ""
"Zaimportowanie typu treści lub taksonomii, z tym samym kluczem co już "
"istniejący element, nadpisze ustawienia istniejącego typu treści lub "
"taksonomii używając zaimportowanych danych."

#: includes/admin/tools/class-acf-admin-tool-import.php:108
#: includes/admin/tools/class-acf-admin-tool-import.php:124
msgid "Import from Custom Post Type UI"
msgstr "Importuj z „Custom Post Type UI”"

#: includes/admin/tools/class-acf-admin-tool-export.php:354
msgid ""
"The following code can be used to register a local version of the selected "
"items. Storing field groups, post types, or taxonomies locally can provide "
"many benefits such as faster load times, version control & dynamic fields/"
"settings. Simply copy and paste the following code to your theme's "
"functions.php file or include it within an external file, then deactivate or "
"delete the items from the ACF admin."
msgstr ""
"Następujący kod może zostać użyty do zarejestrowania lokalnej wersji "
"wybranych elementów. Przechowywanie grup pól, typów treści czy taksonomii "
"lokalnie może pozytywnie wpłynąć na szybsze czasy wczytywania, kontrolę "
"wersji i dynamiczne pola oraz ustawienia. Skopiuj i wklej następujący kod do "
"pliku function.php w twoim motywie lub załącz go w oddzielnym pliku, a "
"następnie wyłącz lub usuń te elementy z ACF-a."

#: includes/admin/tools/class-acf-admin-tool-export.php:353
msgid "Export - Generate PHP"
msgstr "Eksport - wygeneruj PHP"

#: includes/admin/tools/class-acf-admin-tool-export.php:330
msgid "Export"
msgstr "Eksportuj"

#: includes/admin/tools/class-acf-admin-tool-export.php:264
msgid "Select Taxonomies"
msgstr "Wybierz taksonomie"

#: includes/admin/tools/class-acf-admin-tool-export.php:239
msgid "Select Post Types"
msgstr "Wybierz typy treści"

#: includes/admin/tools/class-acf-admin-tool-export.php:155
msgid "Exported 1 item."
msgid_plural "Exported %s items."
msgstr[0] "Wyeksportowano 1 element."
msgstr[1] "Wyeksportowano %s elementy."
msgstr[2] "Wyeksportowano %s elementów."

#: includes/admin/post-types/admin-taxonomy.php:127
msgid "Category"
msgstr "Kategoria"

#: includes/admin/post-types/admin-taxonomy.php:125
msgid "Tag"
msgstr "Tag"

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:82
msgid "%s taxonomy created"
msgstr "Taksonomia %s została utworzona"

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:76
msgid "%s taxonomy updated"
msgstr "Taksonomia %s została zaktualizowana"

#: includes/admin/post-types/admin-taxonomy.php:56
msgid "Taxonomy draft updated."
msgstr "Szkic taksonomii został zaktualizowany."

#: includes/admin/post-types/admin-taxonomy.php:55
msgid "Taxonomy scheduled for."
msgstr "Publikacja taksonomii została zaplanowana."

#: includes/admin/post-types/admin-taxonomy.php:54
msgid "Taxonomy submitted."
msgstr "Taksonomia została dodana."

#: includes/admin/post-types/admin-taxonomy.php:53
msgid "Taxonomy saved."
msgstr "Taksonomia została zapisana."

#: includes/admin/post-types/admin-taxonomy.php:49
msgid "Taxonomy deleted."
msgstr "Taksonomia została usunięta."

#: includes/admin/post-types/admin-taxonomy.php:48
msgid "Taxonomy updated."
msgstr "Taksonomia została zaktualizowana."

#: includes/admin/post-types/admin-taxonomies.php:351
#: includes/admin/post-types/admin-taxonomy.php:153
msgid ""
"This taxonomy could not be registered because its key is in use by another "
"taxonomy registered by another plugin or theme."
msgstr ""
"Nie można było zarejestrować tej taksonomii, ponieważ jej klucz jest już "
"używany przez inną taksonomię zarejestrowaną przez inną wtyczkę lub motyw."

#. translators: %s number of taxonomies synchronized
#: includes/admin/post-types/admin-taxonomies.php:333
msgid "Taxonomy synchronized."
msgid_plural "%s taxonomies synchronized."
msgstr[0] "Taksonomia została zsynchronizowana."
msgstr[1] "%s taksonomie zostały zsynchronizowane."
msgstr[2] "%s taksonomii zostało zsynchronizowanych."

#. translators: %s number of taxonomies duplicated
#: includes/admin/post-types/admin-taxonomies.php:326
msgid "Taxonomy duplicated."
msgid_plural "%s taxonomies duplicated."
msgstr[0] "Taksonomia została zduplikowana."
msgstr[1] "%s taksonomie zostały zduplikowane."
msgstr[2] "%s taksonomii zostało zduplikowanych."

#. translators: %s number of taxonomies deactivated
#: includes/admin/post-types/admin-taxonomies.php:319
msgid "Taxonomy deactivated."
msgid_plural "%s taxonomies deactivated."
msgstr[0] "Taksonomia została wyłączona."
msgstr[1] "%s taksonomie zostały wyłączone."
msgstr[2] "%s taksonomii zostało wyłączonych."

#. translators: %s number of taxonomies activated
#: includes/admin/post-types/admin-taxonomies.php:312
msgid "Taxonomy activated."
msgid_plural "%s taxonomies activated."
msgstr[0] "Taksonomia została włączona."
msgstr[1] "%s taksonomie zostały włączone."
msgstr[2] "%s taksonomii zostało włączonych."

#: includes/admin/post-types/admin-taxonomies.php:113
msgid "Terms"
msgstr "Terminy"

#. translators: %s number of post types synchronized
#: includes/admin/post-types/admin-post-types.php:327
msgid "Post type synchronized."
msgid_plural "%s post types synchronized."
msgstr[0] "Typ treści został zsynchronizowany."
msgstr[1] "%s typy treści zostały zsynchronizowane."
msgstr[2] "%s typów treści zostało zsynchronizowanych."

#. translators: %s number of post types duplicated
#: includes/admin/post-types/admin-post-types.php:320
msgid "Post type duplicated."
msgid_plural "%s post types duplicated."
msgstr[0] "Typ treści został zduplikowany."
msgstr[1] "%s typy treści zostały zduplikowane."
msgstr[2] "%s typów treści zostało zduplikowanych."

#. translators: %s number of post types deactivated
#: includes/admin/post-types/admin-post-types.php:313
msgid "Post type deactivated."
msgid_plural "%s post types deactivated."
msgstr[0] "Typ treści został wyłączony."
msgstr[1] "%s typy treści zostały wyłączone."
msgstr[2] "%s typów treści zostało wyłączonych."

#. translators: %s number of post types activated
#: includes/admin/post-types/admin-post-types.php:306
msgid "Post type activated."
msgid_plural "%s post types activated."
msgstr[0] "Typ treści został włączony."
msgstr[1] "%s typy treści zostały włączone."
msgstr[2] "%s typów treści zostało włączonych."

#: includes/admin/post-types/admin-post-types.php:87
#: includes/admin/post-types/admin-taxonomies.php:111
#: includes/admin/tools/class-acf-admin-tool-import.php:79
#: includes/admin/views/acf-taxonomy/basic-settings.php:82
#: includes/post-types/class-acf-post-type.php:91
msgid "Post Types"
msgstr "Typy treści"

#: includes/admin/post-types/admin-post-type.php:158
#: includes/admin/post-types/admin-taxonomy.php:160
msgid "Advanced Settings"
msgstr "Ustawienia zaawansowane"

#: includes/admin/post-types/admin-post-type.php:157
#: includes/admin/post-types/admin-taxonomy.php:159
msgid "Basic Settings"
msgstr "Ustawienia podstawowe"

#: includes/admin/post-types/admin-post-type.php:151
#: includes/admin/post-types/admin-post-types.php:345
msgid ""
"This post type could not be registered because its key is in use by another "
"post type registered by another plugin or theme."
msgstr ""
"Nie można było zarejestrować tego typu treści, ponieważ jego klucz jest już "
"używany przez inny typ treści zarejestrowany przez inną wtyczkę lub motyw."

#: includes/admin/post-types/admin-post-type.php:126
msgid "Pages"
msgstr "Strony"

#: includes/admin/admin-internal-post-type.php:347
msgid "Link Existing Field Groups"
msgstr "Powiąż istniejące grupy pól"

#. translators: %s post type name
#: includes/admin/post-types/admin-post-type.php:80
msgid "%s post type created"
msgstr "Typ treści %s został utworzony"

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:78
msgid "Add fields to %s"
msgstr "Dodaj pola do „%s”"

#. translators: %s post type name
#: includes/admin/post-types/admin-post-type.php:76
msgid "%s post type updated"
msgstr "Typ treści %s został zaktualizowany"

#: includes/admin/post-types/admin-post-type.php:56
msgid "Post type draft updated."
msgstr "Szkic typu treści został zaktualizowany."

#: includes/admin/post-types/admin-post-type.php:55
msgid "Post type scheduled for."
msgstr "Publikacja typu treści została zaplanowana."

#: includes/admin/post-types/admin-post-type.php:54
msgid "Post type submitted."
msgstr "Typ teści został dodany."

#: includes/admin/post-types/admin-post-type.php:53
msgid "Post type saved."
msgstr "Typ treści został zapisany."

#: includes/admin/post-types/admin-post-type.php:50
msgid "Post type updated."
msgstr "Typ treści został zaktualizowany."

#: includes/admin/post-types/admin-post-type.php:49
msgid "Post type deleted."
msgstr "Typ treści został usunięty."

#: includes/admin/post-types/admin-field-group.php:146
msgid "Type to search..."
msgstr "Zacznij pisać, aby wyszukać…"

#: includes/admin/post-types/admin-field-group.php:101
msgid "PRO Only"
msgstr "Tylko w PRO"

#: includes/admin/post-types/admin-field-group.php:93
msgid "Field groups linked successfully."
msgstr "Grupy pól zostały powiązane."

#. translators: %s - URL to ACF tools page.
#: includes/admin/admin.php:199
msgid ""
"Import Post Types and Taxonomies registered with Custom Post Type UI and "
"manage them with ACF. <a href=\"%s\">Get Started</a>."
msgstr ""
"Importuj typy treści i taksonomie zarejestrowane za pomocą wtyczki „Custom "
"Post Type UI” i zarządzaj nimi w ACF-ie. <a href=\"%s\">Rozpocznij</a>."

#: includes/admin/admin.php:46 includes/admin/admin.php:361
#: src/Site_Health/Site_Health.php:254
msgid "ACF"
msgstr "ACF"

#: includes/admin/admin-internal-post-type.php:314
msgid "taxonomy"
msgstr "taksonomia"

#: includes/admin/admin-internal-post-type.php:314
msgid "post type"
msgstr "typ treści"

#: includes/admin/admin-internal-post-type.php:338
msgid "Done"
msgstr "Gotowe"

#: includes/admin/admin-internal-post-type.php:324
msgid "Field Group(s)"
msgstr "Grupa(-y) pól"

#: includes/admin/admin-internal-post-type.php:323
msgid "Select one or many field groups..."
msgstr "Wybierz jedną lub klika grup pól…"

#: includes/admin/admin-internal-post-type.php:322
msgid "Please select the field groups to link."
msgstr "Proszę wybrać grupy pól do powiązania."

#: includes/admin/admin-internal-post-type.php:280
msgid "Field group linked successfully."
msgid_plural "Field groups linked successfully."
msgstr[0] "Grupa pól została powiązana."
msgstr[1] "Grupy pól zostały powiązane."
msgstr[2] "Grupy pól zostały powiązane."

#: includes/admin/admin-internal-post-type-list.php:277
#: includes/admin/post-types/admin-post-types.php:346
#: includes/admin/post-types/admin-taxonomies.php:352
msgctxt "post status"
msgid "Registration Failed"
msgstr "Rejestracja nieudana"

#: includes/admin/admin-internal-post-type-list.php:276
msgid ""
"This item could not be registered because its key is in use by another item "
"registered by another plugin or theme."
msgstr ""
"Nie można było zarejestrować tego elementu, ponieważ jego klucz jest już "
"używany przez inny element zarejestrowany przez inną wtyczkę lub motyw."

#: includes/acf-internal-post-type-functions.php:509
#: includes/acf-internal-post-type-functions.php:538
msgid "REST API"
msgstr "REST API"

#: includes/acf-internal-post-type-functions.php:508
#: includes/acf-internal-post-type-functions.php:537
#: includes/acf-internal-post-type-functions.php:564
msgid "Permissions"
msgstr "Uprawnienia"

#: includes/acf-internal-post-type-functions.php:507
#: includes/acf-internal-post-type-functions.php:536
msgid "URLs"
msgstr "Adresy URL"

#: includes/acf-internal-post-type-functions.php:506
#: includes/acf-internal-post-type-functions.php:535
#: includes/acf-internal-post-type-functions.php:562
msgid "Visibility"
msgstr "Widoczność"

#: includes/acf-internal-post-type-functions.php:505
#: includes/acf-internal-post-type-functions.php:534
#: includes/acf-internal-post-type-functions.php:563
msgid "Labels"
msgstr "Etykiety"

#: includes/admin/post-types/admin-field-group.php:279
msgid "Field Settings Tabs"
msgstr "Ustawienia pól w zakładkach"

#. Author URI of the plugin
#: acf.php
msgid ""
"https://wpengine.com/?"
"utm_source=wordpress.org&utm_medium=referral&utm_campaign=plugin_directory&utm_content=advanced_custom_fields"
msgstr ""
"https://wpengine.com/?"
"utm_source=wordpress.org&utm_medium=referral&utm_campaign=plugin_directory&utm_content=advanced_custom_fields"

#: includes/api/api-template.php:1027
msgid "[ACF shortcode value disabled for preview]"
msgstr "[Wartość shortcode'u ACF-a wyłączona podczas podglądu]"

#: includes/admin/admin-internal-post-type.php:290
#: includes/admin/post-types/admin-field-group.php:572
msgid "Close Modal"
msgstr "Zamknij okno"

#: includes/admin/post-types/admin-field-group.php:92
msgid "Field moved to other group"
msgstr "Pole zostało przeniesione do innej grupy"

#: includes/admin/post-types/admin-field-group.php:91
msgid "Close modal"
msgstr "Zamknij okno"

#: includes/fields/class-acf-field-tab.php:122
msgid "Start a new group of tabs at this tab."
msgstr "Rozpocznij od tej zakładki nową grupę zakładek."

#: includes/fields/class-acf-field-tab.php:121
msgid "New Tab Group"
msgstr "Nowa grupa zakładek"

#: includes/fields/class-acf-field-select.php:421
#: includes/fields/class-acf-field-true_false.php:188
msgid "Use a stylized checkbox using select2"
msgstr "Użyj ostylowanego pola select2"

#: includes/fields/class-acf-field-radio.php:250
msgid "Save Other Choice"
msgstr "Zapisz inny wybór"

#: includes/fields/class-acf-field-radio.php:239
msgid "Allow Other Choice"
msgstr "Zezwól na inny wybór"

#: includes/fields/class-acf-field-checkbox.php:420
msgid "Add Toggle All"
msgstr "Dodaj „Przełącz wszystko”"

#: includes/fields/class-acf-field-checkbox.php:379
msgid "Save Custom Values"
msgstr "Zapisz własne wartości"

#: includes/fields/class-acf-field-checkbox.php:368
msgid "Allow Custom Values"
msgstr "Zezwól na własne wartości"

#: includes/fields/class-acf-field-checkbox.php:134
msgid "Checkbox custom values cannot be empty. Uncheck any empty values."
msgstr ""
"Własne wartości pola zaznaczenia nie mogą być puste. Odznacz wszystkie puste "
"wartości."

#: includes/admin/views/global/navigation.php:256
msgid "Updates"
msgstr "Aktualizacje"

#: includes/admin/views/global/navigation.php:180
#: includes/admin/views/global/navigation.php:184
msgid "Advanced Custom Fields logo"
msgstr "Logo Advanced Custom Fields"

#: includes/admin/views/global/form-top.php:92
msgid "Save Changes"
msgstr "Zapisz zmiany"

#: includes/admin/views/global/form-top.php:79
msgid "Field Group Title"
msgstr "Tytuł grupy pól"

#: includes/admin/views/acf-post-type/advanced-settings.php:709
#: includes/admin/views/global/form-top.php:3
msgid "Add title"
msgstr "Dodaj tytuł"

#. translators: %s url to getting started guide
#: includes/admin/views/acf-field-group/list-empty.php:30
#: includes/admin/views/acf-post-type/list-empty.php:20
#: includes/admin/views/acf-taxonomy/list-empty.php:21
#: includes/admin/views/options-page-preview.php:13
msgid ""
"New to ACF? Take a look at our <a href=\"%s\" target=\"_blank\">getting "
"started guide</a>."
msgstr ""
"Nie znasz ACF-a? Zapoznaj się z naszym <a href=\"%s\" "
"target=\"_blank\">przewodnikiem jak zacząć</a>."

#: includes/admin/views/acf-field-group/list-empty.php:24
msgid "Add Field Group"
msgstr "Dodaj grupę pól"

#. translators: %s url to creating a field group page
#: includes/admin/views/acf-field-group/list-empty.php:18
msgid ""
"ACF uses <a href=\"%s\" target=\"_blank\">field groups</a> to group custom "
"fields together, and then attach those fields to edit screens."
msgstr ""
"ACF używa <a href=\"%s\" target=\"_blank\">grup pól</a> do łączenia własnych "
"pól, a następnie dołączania tych pól do ekranów edycji."

#: includes/admin/views/acf-field-group/list-empty.php:12
msgid "Add Your First Field Group"
msgstr "Dodaj swoją pierwszą grupę pól"

#: includes/admin/admin-options-pages-preview.php:28
#: includes/admin/views/acf-field-group/pro-features.php:58
#: includes/admin/views/global/navigation.php:86
#: includes/admin/views/global/navigation.php:258
msgid "Options Pages"
msgstr "Strony opcji"

#: includes/admin/views/acf-field-group/pro-features.php:54
msgid "ACF Blocks"
msgstr "Bloki ACF-a"

#: includes/admin/views/acf-field-group/pro-features.php:62
msgid "Gallery Field"
msgstr "Pole galerii"

#: includes/admin/views/acf-field-group/pro-features.php:42
msgid "Flexible Content Field"
msgstr "Pole elastycznej treści"

#: includes/admin/views/acf-field-group/pro-features.php:46
msgid "Repeater Field"
msgstr "Pole powtarzalne"

#: includes/admin/views/global/navigation.php:218
msgid "Unlock Extra Features with ACF PRO"
msgstr "Odblokuj dodatkowe funkcje przy pomocy ACF-a PRO"

#: includes/admin/views/acf-field-group/options.php:267
msgid "Delete Field Group"
msgstr "Usuń grupę pól"

#. translators: 1: Post creation date 2: Post creation time
#: includes/admin/views/acf-field-group/options.php:261
msgid "Created on %1$s at %2$s"
msgstr "Utworzono %1$s o %2$s"

#: includes/acf-field-group-functions.php:497
msgid "Group Settings"
msgstr "Ustawienia grupy"

#: includes/acf-field-group-functions.php:495
msgid "Location Rules"
msgstr "Reguły lokalizacji"

#. translators: %s url to field types list
#: includes/admin/views/acf-field-group/fields.php:73
msgid ""
"Choose from over 30 field types. <a href=\"%s\" target=\"_blank\">Learn "
"more</a>."
msgstr ""
"Wybierz spośród ponad 30 rodzajów pól. <a href=\"%s\" "
"target=\"_blank\">Dowiedz się więcej</a>."

#: includes/admin/views/acf-field-group/fields.php:65
msgid ""
"Get started creating new custom fields for your posts, pages, custom post "
"types and other WordPress content."
msgstr ""
"Zacznij tworzyć nowe własne pola dla swoich wpisów, stron, własnych typów "
"treści i innych treści WordPressa."

#: includes/admin/views/acf-field-group/fields.php:64
msgid "Add Your First Field"
msgstr "Dodaj swoje pierwsze pole"

#. translators: A symbol (or text, if not available in your locale) meaning
#. "Order Number", in terms of positional placement.
#: includes/admin/views/acf-field-group/fields.php:43
msgid "#"
msgstr "#"

#: includes/admin/views/acf-field-group/fields.php:33
#: includes/admin/views/acf-field-group/fields.php:67
#: includes/admin/views/acf-field-group/fields.php:101
#: includes/admin/views/global/form-top.php:88
msgid "Add Field"
msgstr "Dodaj pole"

#: includes/acf-field-group-functions.php:496 includes/fields.php:384
msgid "Presentation"
msgstr "Prezentacja"

#: includes/fields.php:383
msgid "Validation"
msgstr "Walidacja"

#: includes/acf-internal-post-type-functions.php:504
#: includes/acf-internal-post-type-functions.php:533 includes/fields.php:382
msgid "General"
msgstr "Ogólne"

#: includes/admin/tools/class-acf-admin-tool-import.php:67
msgid "Import JSON"
msgstr "Importuj JSON"

#: includes/admin/tools/class-acf-admin-tool-export.php:338
msgid "Export As JSON"
msgstr "Eksportuj jako JSON"

#. translators: %s number of field groups deactivated
#: includes/admin/post-types/admin-field-groups.php:360
msgid "Field group deactivated."
msgid_plural "%s field groups deactivated."
msgstr[0] "Grupa pól została wyłączona."
msgstr[1] "%s grupy pól zostały wyłączone."
msgstr[2] "%s grup pól zostało wyłączonych."

#. translators: %s number of field groups activated
#: includes/admin/post-types/admin-field-groups.php:353
msgid "Field group activated."
msgid_plural "%s field groups activated."
msgstr[0] "Grupa pól została włączona."
msgstr[1] "%s grupy pól zostały włączone."
msgstr[2] "%s grup pól zostało włączonych."

#: includes/admin/admin-internal-post-type-list.php:470
#: includes/admin/admin-internal-post-type-list.php:496
msgid "Deactivate"
msgstr "Wyłącz"

#: includes/admin/admin-internal-post-type-list.php:470
msgid "Deactivate this item"
msgstr "Wyłącz ten element"

#: includes/admin/admin-internal-post-type-list.php:466
#: includes/admin/admin-internal-post-type-list.php:495
msgid "Activate"
msgstr "Włącz"

#: includes/admin/admin-internal-post-type-list.php:466
msgid "Activate this item"
msgstr "Włącz ten element"

#: includes/admin/post-types/admin-field-group.php:88
msgid "Move field group to trash?"
msgstr "Przenieść grupę pól do kosza?"

#: acf.php:520 includes/admin/admin-internal-post-type-list.php:264
#: includes/admin/post-types/admin-field-group.php:304
#: includes/admin/post-types/admin-post-type.php:282
#: includes/admin/post-types/admin-taxonomy.php:284
msgctxt "post status"
msgid "Inactive"
msgstr "Wyłączone"

#. Author of the plugin
#: acf.php includes/admin/views/global/navigation.php:240
msgid "WP Engine"
msgstr "WP Engine"

#: acf.php:578
msgid ""
"Advanced Custom Fields and Advanced Custom Fields PRO should not be active "
"at the same time. We've automatically deactivated Advanced Custom Fields PRO."
msgstr ""
"Wtyczki Advanced Custom Fields i Advanced Custom Fields PRO nie powinny być "
"włączone jednocześnie. Automatycznie wyłączyliśmy Advanced Custom Fields PRO."

#: acf.php:576
msgid ""
"Advanced Custom Fields and Advanced Custom Fields PRO should not be active "
"at the same time. We've automatically deactivated Advanced Custom Fields."
msgstr ""
"Wtyczki Advanced Custom Fields i Advanced Custom Fields PRO nie powinny być "
"włączone jednocześnie. Automatycznie wyłączyliśmy Advanced Custom Fields."

#: includes/fields/class-acf-field-user.php:578
msgid "%1$s must have a user with the %2$s role."
msgid_plural "%1$s must have a user with one of the following roles: %2$s"
msgstr[0] "%1$s musi mieć użytkownika z rolą %2$s."
msgstr[1] "%1$s musi mieć użytkownika z jedną z następujących ról: %2$s"
msgstr[2] "%1$s musi mieć użytkownika z jedną z następujących ról: %2$s"

#: includes/fields/class-acf-field-user.php:569
msgid "%1$s must have a valid user ID."
msgstr "%1$s musi mieć prawidłowy identyfikator użytkownika."

#: includes/fields/class-acf-field-user.php:408
msgid "Invalid request."
msgstr "Nieprawidłowe żądanie."

#: includes/fields/class-acf-field-select.php:689
msgid "%1$s is not one of %2$s"
msgstr "%1$s nie zawiera się w %2$s"

#: includes/fields/class-acf-field-post_object.php:660
msgid "%1$s must have term %2$s."
msgid_plural "%1$s must have one of the following terms: %2$s"
msgstr[0] "%1$s musi należeć do taksonomii %2$s."
msgstr[1] "%1$s musi należeć do jednej z następujących taksonomii: %2$s"
msgstr[2] "%1$s musi należeć do jednej z następujących taksonomii: %2$s"

#: includes/fields/class-acf-field-post_object.php:644
msgid "%1$s must be of post type %2$s."
msgid_plural "%1$s must be of one of the following post types: %2$s"
msgstr[0] "%1$s musi należeć do typu treści %2$s."
msgstr[1] "%1$s musi należeć do jednego z następujących typów treści: %2$s"
msgstr[2] "%1$s musi należeć do jednego z następujących typów treści: %2$s"

#: includes/fields/class-acf-field-post_object.php:635
msgid "%1$s must have a valid post ID."
msgstr "%1$s musi mieć prawidłowy identyfikator wpisu."

#: includes/fields/class-acf-field-file.php:447
msgid "%s requires a valid attachment ID."
msgstr "%s wymaga prawidłowego identyfikatora załącznika."

#: includes/admin/views/acf-field-group/options.php:233
msgid "Show in REST API"
msgstr "Pokaż w REST API"

#: includes/fields/class-acf-field-color_picker.php:156
msgid "Enable Transparency"
msgstr "Włącz przezroczystość"

#: includes/fields/class-acf-field-color_picker.php:175
msgid "RGBA Array"
msgstr "Tablica RGBA"

#: includes/fields/class-acf-field-color_picker.php:92
msgid "RGBA String"
msgstr "Ciąg RGBA"

#: includes/fields/class-acf-field-color_picker.php:91
#: includes/fields/class-acf-field-color_picker.php:174
msgid "Hex String"
msgstr "Ciąg Hex"

#: includes/admin/views/browse-fields-modal.php:12
msgid "Upgrade to PRO"
msgstr "Kup PRO"

#: includes/admin/post-types/admin-field-group.php:304
#: includes/admin/post-types/admin-post-type.php:282
#: includes/admin/post-types/admin-taxonomy.php:284
msgctxt "post status"
msgid "Active"
msgstr "Włączone"

#: includes/fields/class-acf-field-email.php:166
msgid "'%s' is not a valid email address"
msgstr "„%s” nie jest poprawnym adresem e-mail"

#: includes/fields/class-acf-field-color_picker.php:70
msgid "Color value"
msgstr "Kolor"

#: includes/fields/class-acf-field-color_picker.php:68
msgid "Select default color"
msgstr "Wybierz domyślny kolor"

#: includes/fields/class-acf-field-color_picker.php:66
msgid "Clear color"
msgstr "Wyczyść kolor"

#: includes/acf-wp-functions.php:90
msgid "Blocks"
msgstr "Bloki"

#: includes/acf-wp-functions.php:86
msgid "Options"
msgstr "Opcje"

#: includes/acf-wp-functions.php:82
msgid "Users"
msgstr "Użytkownicy"

#: includes/acf-wp-functions.php:78
msgid "Menu items"
msgstr "Elementy menu"

#: includes/acf-wp-functions.php:70
msgid "Widgets"
msgstr "Widżety"

#: includes/acf-wp-functions.php:62
msgid "Attachments"
msgstr "Załączniki"

#: includes/acf-wp-functions.php:57
#: includes/admin/post-types/admin-post-types.php:112
#: includes/admin/post-types/admin-taxonomies.php:86
#: includes/admin/tools/class-acf-admin-tool-import.php:90
#: includes/admin/views/acf-post-type/basic-settings.php:86
#: includes/post-types/class-acf-taxonomy.php:90
#: includes/post-types/class-acf-taxonomy.php:91
msgid "Taxonomies"
msgstr "Taksonomie"

#: includes/acf-wp-functions.php:44
#: includes/admin/post-types/admin-post-type.php:124
#: includes/admin/post-types/admin-post-types.php:114
#: includes/admin/views/acf-post-type/advanced-settings.php:106
msgid "Posts"
msgstr "Wpisy"

#: includes/ajax/class-acf-ajax-local-json-diff.php:81
msgid "Last updated: %s"
msgstr "Ostatnia aktualizacja: %s"

#: includes/ajax/class-acf-ajax-local-json-diff.php:75
msgid "Sorry, this post is unavailable for diff comparison."
msgstr "Przepraszamy, ten wpis jest niedostępny dla porównania różnic."

#: includes/ajax/class-acf-ajax-local-json-diff.php:47
msgid "Invalid field group parameter(s)."
msgstr "Nieprawidłowy parametr(y) grupy pól."

#: includes/admin/admin-internal-post-type-list.php:429
msgid "Awaiting save"
msgstr "Oczekiwanie na zapis"

#: includes/admin/admin-internal-post-type-list.php:426
msgid "Saved"
msgstr "Zapisana"

#: includes/admin/admin-internal-post-type-list.php:422
#: includes/admin/tools/class-acf-admin-tool-import.php:46
msgid "Import"
msgstr "Importuj"

#: includes/admin/admin-internal-post-type-list.php:418
msgid "Review changes"
msgstr "Przejrzyj zmiany"

#: includes/admin/admin-internal-post-type-list.php:394
msgid "Located in: %s"
msgstr "Znajduje się w: %s"

#: includes/admin/admin-internal-post-type-list.php:391
msgid "Located in plugin: %s"
msgstr "Znalezione we wtyczce: %s"

#: includes/admin/admin-internal-post-type-list.php:388
msgid "Located in theme: %s"
msgstr "Znalezione w motywie: %s"

#: includes/admin/post-types/admin-field-groups.php:235
msgid "Various"
msgstr "Różne"

#: includes/admin/admin-internal-post-type-list.php:230
#: includes/admin/admin-internal-post-type-list.php:503
msgid "Sync changes"
msgstr "Synchronizuj zmiany"

#: includes/admin/admin-internal-post-type-list.php:229
msgid "Loading diff"
msgstr "Ładowanie różnic"

#: includes/admin/admin-internal-post-type-list.php:228
msgid "Review local JSON changes"
msgstr "Przegląd lokalnych zmian JSON"

#: includes/admin/admin.php:174
msgid "Visit website"
msgstr "Odwiedź stronę"

#: includes/admin/admin.php:173
msgid "View details"
msgstr "Zobacz szczegóły"

#: includes/admin/admin.php:172
msgid "Version %s"
msgstr "Wersja %s"

#: includes/admin/admin.php:171
msgid "Information"
msgstr "Informacje"

#: includes/admin/admin.php:162
msgid ""
"<a href=\"%s\" target=\"_blank\">Help Desk</a>. The support professionals on "
"our Help Desk will assist with your more in depth, technical challenges."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Pomoc</a>. Nasi pracownicy pomocy "
"technicznej pomogą w bardziej dogłębnych wyzwaniach technicznych."

#: includes/admin/admin.php:158
msgid ""
"<a href=\"%s\" target=\"_blank\">Discussions</a>. We have an active and "
"friendly community on our Community Forums who may be able to help you "
"figure out the 'how-tos' of the ACF world."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Dyskusje</a>. Mamy aktywną i przyjazną "
"społeczność na naszych forach społecznościowych, która pomoże Ci poznać "
"tajniki świata ACF-a."

#: includes/admin/admin.php:154
msgid ""
"<a href=\"%s\" target=\"_blank\">Documentation</a>. Our extensive "
"documentation contains references and guides for most situations you may "
"encounter."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Dokumentacja</a>. Nasza obszerna "
"dokumentacja zawiera opisy i przewodniki dotyczące większości sytuacji, "
"które możesz napotkać."

#: includes/admin/admin.php:151
msgid ""
"We are fanatical about support, and want you to get the best out of your "
"website with ACF. If you run into any difficulties, there are several places "
"you can find help:"
msgstr ""
"Jesteśmy fanatyczni, jeśli chodzi o wsparcie i chcemy, abyś w pełni "
"wykorzystał swoją stronę internetową dzięki ACF-owi. Jeśli napotkasz "
"jakiekolwiek trudności, jest kilka miejsc, w których możesz znaleźć pomoc:"

#: includes/admin/admin.php:148 includes/admin/admin.php:150
msgid "Help & Support"
msgstr "Pomoc i wsparcie"

#: includes/admin/admin.php:139
msgid ""
"Please use the Help & Support tab to get in touch should you find yourself "
"requiring assistance."
msgstr ""
"Skorzystaj z zakładki Pomoc i wsparcie, aby skontaktować się, jeśli "
"potrzebujesz pomocy."

#: includes/admin/admin.php:136
msgid ""
"Before creating your first Field Group, we recommend first reading our <a "
"href=\"%s\" target=\"_blank\">Getting started</a> guide to familiarize "
"yourself with the plugin's philosophy and best practises."
msgstr ""
"Przed utworzeniem pierwszej grupy pól zalecamy najpierw przeczytanie naszego "
"przewodnika <a href=\"%s\" target=\"_blank\">Pierwsze kroki</a>, aby "
"zapoznać się z filozofią wtyczki i sprawdzonymi metodami."

#: includes/admin/admin.php:134
msgid ""
"The Advanced Custom Fields plugin provides a visual form builder to "
"customize WordPress edit screens with extra fields, and an intuitive API to "
"display custom field values in any theme template file."
msgstr ""
"Wtyczka Advanced Custom Fields zapewnia wizualny kreator formularzy do "
"dostosowywania ekranów edycji WordPress z dodatkowymi polami oraz intuicyjny "
"interfejs API do wyświetlania niestandardowych wartości pól w dowolnym pliku "
"szablonu motywu."

#: includes/admin/admin.php:131 includes/admin/admin.php:133
msgid "Overview"
msgstr "Przegląd"

#. translators: %s the name of the location type
#: includes/locations.php:38
msgid "Location type \"%s\" is already registered."
msgstr "Typ lokalizacji „%s” jest już zarejestrowany."

#. translators: %s class name for a location that could not be found
#: includes/locations.php:26
msgid "Class \"%s\" does not exist."
msgstr "Klasa „%s” nie istnieje."

#: includes/ajax/class-acf-ajax-query-users.php:43
#: includes/ajax/class-acf-ajax.php:157
msgid "Invalid nonce."
msgstr "Nieprawidłowy kod jednorazowy."

#: includes/fields/class-acf-field-user.php:400
msgid "Error loading field."
msgstr "Błąd ładowania pola."

#: includes/forms/form-user.php:328
msgid "<strong>Error</strong>: %s"
msgstr "<strong>Błąd</strong>: %s"

#: includes/locations/class-acf-location-widget.php:22
msgid "Widget"
msgstr "Widżet"

#: includes/locations/class-acf-location-user-role.php:24
msgid "User Role"
msgstr "Rola użytkownika"

#: includes/locations/class-acf-location-comment.php:22
msgid "Comment"
msgstr "Komentarz"

#: includes/locations/class-acf-location-post-format.php:22
msgid "Post Format"
msgstr "Format wpisu"

#: includes/locations/class-acf-location-nav-menu-item.php:22
msgid "Menu Item"
msgstr "Element menu"

#: includes/locations/class-acf-location-post-status.php:22
msgid "Post Status"
msgstr "Status wpisu"

#: includes/acf-wp-functions.php:74
#: includes/locations/class-acf-location-nav-menu.php:89
msgid "Menus"
msgstr "Menu"

#: includes/locations/class-acf-location-nav-menu.php:80
msgid "Menu Locations"
msgstr "Położenia menu"

#: includes/locations/class-acf-location-nav-menu.php:22
msgid "Menu"
msgstr "Menu"

#: includes/locations/class-acf-location-post-taxonomy.php:22
msgid "Post Taxonomy"
msgstr "Taksonomia wpisu"

#: includes/locations/class-acf-location-page-type.php:114
msgid "Child Page (has parent)"
msgstr "Strona potomna (ma stronę nadrzędną)"

#: includes/locations/class-acf-location-page-type.php:113
msgid "Parent Page (has children)"
msgstr "Strona nadrzędna (ma strony potomne)"

#: includes/locations/class-acf-location-page-type.php:112
msgid "Top Level Page (no parent)"
msgstr "Strona najwyższego poziomu (bez strony nadrzędnej)"

#: includes/locations/class-acf-location-page-type.php:111
msgid "Posts Page"
msgstr "Strona z wpisami"

#: includes/locations/class-acf-location-page-type.php:110
msgid "Front Page"
msgstr "Strona główna"

#: includes/locations/class-acf-location-page-type.php:22
msgid "Page Type"
msgstr "Typ strony"

#: includes/locations/class-acf-location-current-user.php:73
msgid "Viewing back end"
msgstr "Wyświetla kokpit administratora"

#: includes/locations/class-acf-location-current-user.php:72
msgid "Viewing front end"
msgstr "Wyświetla witrynę"

#: includes/locations/class-acf-location-current-user.php:71
msgid "Logged in"
msgstr "Zalogowany"

#: includes/locations/class-acf-location-current-user.php:22
msgid "Current User"
msgstr "Bieżący użytkownik"

#: includes/locations/class-acf-location-page-template.php:22
msgid "Page Template"
msgstr "Szablon strony"

#: includes/locations/class-acf-location-user-form.php:74
msgid "Register"
msgstr "Zarejestruj się"

#: includes/locations/class-acf-location-user-form.php:73
msgid "Add / Edit"
msgstr "Dodaj / Edytuj"

#: includes/locations/class-acf-location-user-form.php:22
msgid "User Form"
msgstr "Formularz użytkownika"

#: includes/locations/class-acf-location-page-parent.php:22
msgid "Page Parent"
msgstr "Strona nadrzędna"

#: includes/locations/class-acf-location-current-user-role.php:77
msgid "Super Admin"
msgstr "Superadministrator"

#: includes/locations/class-acf-location-current-user-role.php:22
msgid "Current User Role"
msgstr "Rola bieżącego użytkownika"

#: includes/locations/class-acf-location-page-template.php:73
#: includes/locations/class-acf-location-post-template.php:85
msgid "Default Template"
msgstr "Domyślny szablon"

#: includes/locations/class-acf-location-post-template.php:22
msgid "Post Template"
msgstr "Szablon wpisu"

#: includes/locations/class-acf-location-post-category.php:22
msgid "Post Category"
msgstr "Kategoria wpisu"

#: includes/locations/class-acf-location-attachment.php:84
msgid "All %s formats"
msgstr "Wszystkie formaty %s"

#: includes/locations/class-acf-location-attachment.php:22
msgid "Attachment"
msgstr "Załącznik"

#: includes/validation.php:323
msgid "%s value is required"
msgstr "%s wartość jest wymagana"

#: includes/admin/views/acf-field-group/conditional-logic.php:64
msgid "Show this field if"
msgstr "Pokaż to pole jeśli"

#: includes/admin/views/acf-field-group/conditional-logic.php:25
#: includes/admin/views/acf-field-group/field.php:122 includes/fields.php:385
msgid "Conditional Logic"
msgstr "Wyświetlanie warunkowe"

#: includes/admin/views/acf-field-group/conditional-logic.php:169
#: includes/admin/views/acf-field-group/location-rule.php:84
msgid "and"
msgstr "oraz"

#: includes/admin/post-types/admin-field-groups.php:97
#: includes/admin/post-types/admin-post-types.php:118
#: includes/admin/post-types/admin-taxonomies.php:117
msgid "Local JSON"
msgstr "Lokalny JSON"

#: includes/admin/views/acf-field-group/pro-features.php:50
msgid "Clone Field"
msgstr "Pole klona"

#. translators: %s a list of plugin
#: includes/admin/views/upgrade/notice.php:32
msgid ""
"Please also check all premium add-ons (%s) are updated to the latest version."
msgstr ""
"Proszę również sprawdzić, czy wszystkie dodatki premium (%s) są "
"zaktualizowane do najnowszej wersji."

#: includes/admin/views/upgrade/notice.php:29
msgid ""
"This version contains improvements to your database and requires an upgrade."
msgstr "Ta wersja zawiera ulepszenia bazy danych i wymaga uaktualnienia."

#. translators: %1 plugin name, %2 version number
#: includes/admin/views/upgrade/notice.php:28
msgid "Thank you for updating to %1$s v%2$s!"
msgstr "Dziękujemy za aktualizację %1$s do wersji %2$s!"

#: includes/admin/views/upgrade/notice.php:26
msgid "Database Upgrade Required"
msgstr "Wymagana jest aktualizacja bazy danych"

#: includes/admin/post-types/admin-field-group.php:159
#: includes/admin/views/upgrade/notice.php:17
msgid "Options Page"
msgstr "Strona opcji"

#: includes/admin/views/upgrade/notice.php:14 includes/fields.php:436
msgid "Gallery"
msgstr "Galeria"

#: includes/admin/views/upgrade/notice.php:11 includes/fields.php:426
msgid "Flexible Content"
msgstr "Elastyczna treść"

#: includes/admin/views/upgrade/notice.php:8 includes/fields.php:446
msgid "Repeater"
msgstr "Pole powtarzalne"

#: includes/admin/views/tools/tools.php:16
msgid "Back to all tools"
msgstr "Wróć do wszystkich narzędzi"

#: includes/admin/views/acf-field-group/options.php:195
msgid ""
"If multiple field groups appear on an edit screen, the first field group's "
"options will be used (the one with the lowest order number)"
msgstr ""
"Jeśli na stronie edycji znajduje się kilka grup pól, zostaną zastosowane "
"ustawienia pierwszej z nich. (pierwsza grupa pól to ta, która ma najniższy "
"numer w kolejności)"

#: includes/admin/views/acf-field-group/options.php:195
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr "<b>Wybierz</b> elementy, które chcesz <b>ukryć</b> na stronie edycji."

#: includes/admin/views/acf-field-group/options.php:194
msgid "Hide on screen"
msgstr "Ukryj na ekranie"

#: includes/admin/views/acf-field-group/options.php:186
msgid "Send Trackbacks"
msgstr "Wyślij trackbacki"

#: includes/admin/post-types/admin-taxonomy.php:126
#: includes/admin/views/acf-field-group/options.php:185
#: includes/admin/views/acf-taxonomy/advanced-settings.php:159
msgid "Tags"
msgstr "Tagi"

#: includes/admin/post-types/admin-taxonomy.php:128
#: includes/admin/views/acf-field-group/options.php:184
msgid "Categories"
msgstr "Kategorie"

#: includes/admin/views/acf-field-group/options.php:182
#: includes/admin/views/acf-post-type/advanced-settings.php:28
msgid "Page Attributes"
msgstr "Atrybuty strony"

#: includes/admin/views/acf-field-group/options.php:181
msgid "Format"
msgstr "Format"

#: includes/admin/views/acf-field-group/options.php:180
#: includes/admin/views/acf-post-type/advanced-settings.php:22
msgid "Author"
msgstr "Autor"

#: includes/admin/views/acf-field-group/options.php:179
msgid "Slug"
msgstr "Uproszczona nazwa"

#: includes/admin/views/acf-field-group/options.php:178
#: includes/admin/views/acf-post-type/advanced-settings.php:27
msgid "Revisions"
msgstr "Wersje"

#: includes/acf-wp-functions.php:66
#: includes/admin/views/acf-field-group/options.php:177
#: includes/admin/views/acf-post-type/advanced-settings.php:23
msgid "Comments"
msgstr "Komentarze"

#: includes/admin/views/acf-field-group/options.php:176
msgid "Discussion"
msgstr "Dyskusja"

#: includes/admin/views/acf-field-group/options.php:174
#: includes/admin/views/acf-post-type/advanced-settings.php:26
msgid "Excerpt"
msgstr "Zajawka"

#: includes/admin/views/acf-field-group/options.php:173
msgid "Content Editor"
msgstr "Edytor treści"

#: includes/admin/views/acf-field-group/options.php:172
msgid "Permalink"
msgstr "Bezpośredni odnośnik"

#: includes/admin/views/acf-field-group/options.php:250
msgid "Shown in field group list"
msgstr "Wyświetlany na liście grup pól"

#: includes/admin/views/acf-field-group/options.php:157
msgid "Field groups with a lower order will appear first"
msgstr "Grupy pól z niższym numerem pojawią się pierwsze"

#: includes/admin/views/acf-field-group/options.php:156
msgid "Order No."
msgstr "Nr w kolejności."

#: includes/admin/views/acf-field-group/options.php:147
msgid "Below fields"
msgstr "Pod polami"

#: includes/admin/views/acf-field-group/options.php:146
msgid "Below labels"
msgstr "Pod etykietami"

#: includes/admin/views/acf-field-group/options.php:139
msgid "Instruction Placement"
msgstr "Położenie instrukcji"

#: includes/admin/views/acf-field-group/options.php:122
msgid "Label Placement"
msgstr "Położenie etykiety"

#: includes/admin/views/acf-field-group/options.php:110
msgid "Side"
msgstr "Boczna"

#: includes/admin/views/acf-field-group/options.php:109
msgid "Normal (after content)"
msgstr "Normalna (pod treścią)"

#: includes/admin/views/acf-field-group/options.php:108
msgid "High (after title)"
msgstr "Wysoka (pod tytułem)"

#: includes/admin/views/acf-field-group/options.php:101
msgid "Position"
msgstr "Pozycja"

#: includes/admin/views/acf-field-group/options.php:92
msgid "Seamless (no metabox)"
msgstr "Bezpodziałowy (brak metaboksa)"

#: includes/admin/views/acf-field-group/options.php:91
msgid "Standard (WP metabox)"
msgstr "Standardowy (metabox WP)"

#: includes/admin/views/acf-field-group/options.php:84
msgid "Style"
msgstr "Styl"

#: includes/admin/views/acf-field-group/fields.php:55
msgid "Type"
msgstr "Rodzaj"

#: includes/admin/post-types/admin-field-groups.php:91
#: includes/admin/post-types/admin-post-types.php:111
#: includes/admin/post-types/admin-taxonomies.php:110
#: includes/admin/views/acf-field-group/fields.php:54
msgid "Key"
msgstr "Klucz"

#. translators: Hidden accessibility text for the positional order number of
#. the field.
#: includes/admin/views/acf-field-group/fields.php:48
msgid "Order"
msgstr "Kolejność"

#: includes/admin/views/acf-field-group/field.php:321
msgid "Close Field"
msgstr "Zamknij pole"

#: includes/admin/views/acf-field-group/field.php:252
msgid "id"
msgstr "id"

#: includes/admin/views/acf-field-group/field.php:236
msgid "class"
msgstr "class"

#: includes/admin/views/acf-field-group/field.php:278
msgid "width"
msgstr "szerokość"

#: includes/admin/views/acf-field-group/field.php:272
msgid "Wrapper Attributes"
msgstr "Atrybuty kontenera"

#: includes/fields/class-acf-field.php:312
msgid "Required"
msgstr "Wymagane"

#: includes/admin/views/acf-field-group/field.php:219
msgid "Instructions"
msgstr "Instrukcje"

#: includes/admin/views/acf-field-group/field.php:142
msgid "Field Type"
msgstr "Rodzaj pola"

#: includes/admin/views/acf-field-group/field.php:183
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr "Pojedyncze słowo, bez spacji. Dozwolone są myślniki i podkreślniki"

#: includes/admin/views/acf-field-group/field.php:182
msgid "Field Name"
msgstr "Nazwa pola"

#: includes/admin/views/acf-field-group/field.php:170
msgid "This is the name which will appear on the EDIT page"
msgstr "Ta nazwa będzie widoczna na stronie edycji"

#: includes/admin/views/acf-field-group/field.php:169
#: includes/admin/views/browse-fields-modal.php:69
msgid "Field Label"
msgstr "Etykieta pola"

#: includes/admin/views/acf-field-group/field.php:94
msgid "Delete"
msgstr "Usuń"

#: includes/admin/views/acf-field-group/field.php:94
msgid "Delete field"
msgstr "Usuń pole"

#: includes/admin/views/acf-field-group/field.php:92
msgid "Move"
msgstr "Przenieś"

#: includes/admin/views/acf-field-group/field.php:92
msgid "Move field to another group"
msgstr "Przenieś pole do innej grupy"

#: includes/admin/views/acf-field-group/field.php:90
msgid "Duplicate field"
msgstr "Duplikuj to pole"

#: includes/admin/views/acf-field-group/field.php:86
#: includes/admin/views/acf-field-group/field.php:89
msgid "Edit field"
msgstr "Edytuj pole"

#: includes/admin/views/acf-field-group/field.php:82
msgid "Drag to reorder"
msgstr "Przeciągnij aby zmienić kolejność"

#: includes/admin/post-types/admin-field-group.php:99
#: includes/admin/views/acf-field-group/location-group.php:3
msgid "Show this field group if"
msgstr "Pokaż tę grupę pól jeśli"

#: includes/admin/views/upgrade/upgrade.php:93
#: includes/ajax/class-acf-ajax-upgrade.php:34
msgid "No updates available."
msgstr "Brak dostępnych aktualizacji."

#. translators: %s the url to the field group page.
#: includes/admin/views/upgrade/upgrade.php:32
msgid "Database upgrade complete. <a href=\"%s\">See what's new</a>"
msgstr ""
"Aktualizacja bazy danych zakończona. <a href=\"%s\">Zobacz co nowego</a>"

#: includes/admin/views/upgrade/upgrade.php:27
msgid "Reading upgrade tasks..."
msgstr "Czytam zadania aktualizacji…"

#: includes/admin/views/upgrade/network.php:165
#: includes/admin/views/upgrade/upgrade.php:64
msgid "Upgrade failed."
msgstr "Aktualizacja nie powiodła się."

#: includes/admin/views/upgrade/network.php:162
msgid "Upgrade complete."
msgstr "Aktualizacja zakończona."

#. translators: %s the version being upgraded to.
#. translators: %s the new ACF version
#: includes/admin/views/upgrade/network.php:148
#: includes/admin/views/upgrade/upgrade.php:29
msgid "Upgrading data to version %s"
msgstr "Aktualizowanie danych do wersji %s"

#: includes/admin/views/upgrade/network.php:120
#: includes/admin/views/upgrade/notice.php:46
msgid ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"
msgstr ""
"Zdecydowanie zaleca się wykonanie kopii zapasowej bazy danych przed "
"kontynuowaniem. Czy na pewno chcesz teraz uruchomić aktualizacje?"

#: includes/admin/views/upgrade/network.php:116
msgid "Please select at least one site to upgrade."
msgstr "Proszę wybrać co najmniej jedną witrynę do uaktualnienia."

#. translators: %s admin dashboard url page
#: includes/admin/views/upgrade/network.php:96
msgid ""
"Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr ""
"Aktualizacja bazy danych zakończona. <a href=\"%s\">Wróć do kokpitu sieci</a>"

#: includes/admin/views/upgrade/network.php:79
msgid "Site is up to date"
msgstr "Oprogramowanie witryny jest aktualne"

#. translators: %1 current db version, %2 available db version
#: includes/admin/views/upgrade/network.php:77
msgid "Site requires database upgrade from %1$s to %2$s"
msgstr "Witryna wymaga aktualizacji bazy danych z %1$s do %2$s"

#: includes/admin/views/upgrade/network.php:34
#: includes/admin/views/upgrade/network.php:45
msgid "Site"
msgstr "Witryna"

#. translators: %s The button label name, translated seperately
#: includes/admin/views/upgrade/network.php:24
#: includes/admin/views/upgrade/network.php:25
#: includes/admin/views/upgrade/network.php:94
msgid "Upgrade Sites"
msgstr "Zaktualizuj witryny"

#. translators: %s The button label name, translated seperately
#: includes/admin/views/upgrade/network.php:24
msgid ""
"The following sites require a DB upgrade. Check the ones you want to update "
"and then click %s."
msgstr ""
"Następujące witryny wymagają aktualizacji bazy danych. Zaznacz te, które "
"chcesz zaktualizować i kliknij %s."

#: includes/admin/views/acf-field-group/conditional-logic.php:184
#: includes/admin/views/acf-field-group/locations.php:37
msgid "Add rule group"
msgstr "Dodaj grupę warunków"

#: includes/admin/views/acf-field-group/locations.php:10
msgid ""
"Create a set of rules to determine which edit screens will use these "
"advanced custom fields"
msgstr ""
"Utwórz zestaw warunków, które określą w których miejscach będą wykorzystane "
"zdefiniowane tutaj własne pola"

#: includes/admin/views/acf-field-group/locations.php:9
msgid "Rules"
msgstr "Reguły"

#: includes/admin/tools/class-acf-admin-tool-export.php:449
msgid "Copied"
msgstr "Skopiowano"

#: includes/admin/tools/class-acf-admin-tool-export.php:425
msgid "Copy to clipboard"
msgstr "Skopiuj do schowka"

#: includes/admin/tools/class-acf-admin-tool-export.php:331
msgid ""
"Select the items you would like to export and then select your export "
"method. Export As JSON to export to a .json file which you can then import "
"to another ACF installation. Generate PHP to export to PHP code which you "
"can place in your theme."
msgstr ""
"Wybierz elementy, które chcesz wyeksportować, a następnie wybierz metodę "
"eksportu. Użyj „Eksportuj jako JSON” aby wyeksportować do pliku .json, który "
"można następnie zaimportować do innej instalacji ACF. Użyj „Utwórz PHP” do "
"wyeksportowania ustawień do kodu PHP, który można umieścić w motywie."

#: includes/admin/tools/class-acf-admin-tool-export.php:215
msgid "Select Field Groups"
msgstr "Wybierz grupy pól"

#: includes/admin/tools/class-acf-admin-tool-export.php:88
#: includes/admin/tools/class-acf-admin-tool-export.php:121
msgid "No field groups selected"
msgstr "Nie zaznaczono żadnej grupy pól"

#: includes/admin/tools/class-acf-admin-tool-export.php:38
#: includes/admin/tools/class-acf-admin-tool-export.php:339
#: includes/admin/tools/class-acf-admin-tool-export.php:363
msgid "Generate PHP"
msgstr "Utwórz PHP"

#: includes/admin/tools/class-acf-admin-tool-export.php:34
msgid "Export Field Groups"
msgstr "Eksportuj grupy pól"

#: includes/admin/tools/class-acf-admin-tool-import.php:172
msgid "Import file empty"
msgstr "Importowany plik jest pusty"

#: includes/admin/tools/class-acf-admin-tool-import.php:163
msgid "Incorrect file type"
msgstr "Błędny typ pliku"

#: includes/admin/tools/class-acf-admin-tool-import.php:158
msgid "Error uploading file. Please try again"
msgstr "Błąd przesyłania pliku. Proszę spróbować ponownie"

#: includes/admin/tools/class-acf-admin-tool-import.php:47
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the items in that file."
msgstr ""
"Wybierz plik JSON Advanced Custom Fields, który chcesz zaimportować. Po "
"kliknięciu przycisku importu poniżej, ACF zaimportuje elementy z tego pliku."

#: includes/admin/tools/class-acf-admin-tool-import.php:26
msgid "Import Field Groups"
msgstr "Importuj grupy pól"

#: includes/admin/admin-internal-post-type-list.php:417
msgid "Sync"
msgstr "Synchronizuj"

#. translators: %s: field group title
#: includes/admin/admin-internal-post-type-list.php:960
msgid "Select %s"
msgstr "Wybierz %s"

#: includes/admin/admin-internal-post-type-list.php:460
#: includes/admin/admin-internal-post-type-list.php:492
#: includes/admin/views/acf-field-group/field.php:90
msgid "Duplicate"
msgstr "Duplikuj"

#: includes/admin/admin-internal-post-type-list.php:460
msgid "Duplicate this item"
msgstr "Duplikuj ten element"

#: includes/admin/views/acf-post-type/advanced-settings.php:41
msgid "Supports"
msgstr "Obsługuje"

#: includes/admin/admin.php:355
#: includes/admin/views/browse-fields-modal.php:102
msgid "Documentation"
msgstr "Dokumentacja"

#: includes/admin/post-types/admin-field-groups.php:90
#: includes/admin/post-types/admin-post-types.php:110
#: includes/admin/post-types/admin-taxonomies.php:109
#: includes/admin/views/acf-field-group/options.php:249
#: includes/admin/views/acf-post-type/advanced-settings.php:62
#: includes/admin/views/acf-taxonomy/advanced-settings.php:114
#: includes/admin/views/upgrade/network.php:36
#: includes/admin/views/upgrade/network.php:47
msgid "Description"
msgstr "Opis"

#: includes/admin/admin-internal-post-type-list.php:414
#: includes/admin/admin-internal-post-type-list.php:832
msgid "Sync available"
msgstr "Synchronizacja jest dostępna"

#. translators: %s number of field groups synchronized
#: includes/admin/post-types/admin-field-groups.php:374
msgid "Field group synchronized."
msgid_plural "%s field groups synchronized."
msgstr[0] "Grupa pól została zsynchronizowana."
msgstr[1] "%s grupy pól zostały zsynchronizowane."
msgstr[2] "%s grup pól zostało zsynchronizowanych."

#. translators: %s number of field groups duplicated
#: includes/admin/post-types/admin-field-groups.php:367
msgid "Field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] "Grupa pól została zduplikowana."
msgstr[1] "%s grupy pól zostały zduplikowane."
msgstr[2] "%s grup pól zostało zduplikowanych."

#: includes/admin/admin-internal-post-type-list.php:155
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] "Włączone <span class=\"count\">(%s)</span>"
msgstr[1] "Włączone <span class=\"count\">(%s)</span>"
msgstr[2] "Włączone <span class=\"count\">(%s)</span>"

#: includes/admin/admin-upgrade.php:251
msgid "Review sites & upgrade"
msgstr "Strona opinii i aktualizacji"

#: includes/admin/admin-upgrade.php:59 includes/admin/admin-upgrade.php:90
#: includes/admin/admin-upgrade.php:91 includes/admin/admin-upgrade.php:227
#: includes/admin/views/upgrade/network.php:21
#: includes/admin/views/upgrade/upgrade.php:23
msgid "Upgrade Database"
msgstr "Aktualizuj bazę danych"

#: includes/admin/views/acf-field-group/options.php:175
#: includes/admin/views/acf-post-type/advanced-settings.php:30
msgid "Custom Fields"
msgstr "Własne pola"

#: includes/admin/post-types/admin-field-group.php:609
msgid "Move Field"
msgstr "Przenieś pole"

#: includes/admin/post-types/admin-field-group.php:602
#: includes/admin/post-types/admin-field-group.php:606
msgid "Please select the destination for this field"
msgstr "Proszę wybrać miejsce przeznaczenia dla tego pola"

#. translators: Confirmation message once a field has been moved to a different
#. field group.
#: includes/admin/post-types/admin-field-group.php:568
msgid "The %1$s field can now be found in the %2$s field group"
msgstr "Pole %1$s znajduje się teraz w grupie pól %2$s"

#: includes/admin/post-types/admin-field-group.php:565
msgid "Move Complete."
msgstr "Przenoszenie zakończone."

#: includes/admin/views/acf-field-group/field.php:52
#: includes/admin/views/acf-field-group/options.php:217
#: includes/admin/views/acf-post-type/advanced-settings.php:78
#: includes/admin/views/acf-taxonomy/advanced-settings.php:130
msgid "Active"
msgstr "Włączone"

#: includes/admin/post-types/admin-field-group.php:276
msgid "Field Keys"
msgstr "Klucze pola"

#: includes/admin/post-types/admin-field-group.php:180
msgid "Settings"
msgstr "Ustawienia"

#: includes/admin/post-types/admin-field-groups.php:92
msgid "Location"
msgstr "Lokalizacja"

#: includes/admin/post-types/admin-field-group.php:100
msgid "Null"
msgstr "Pusty"

#: includes/admin/post-types/admin-field-group.php:97
#: includes/class-acf-internal-post-type.php:728
#: includes/post-types/class-acf-field-group.php:345
msgid "copy"
msgstr "kopia"

#: includes/admin/post-types/admin-field-group.php:96
msgid "(this field)"
msgstr "(to pole)"

#: includes/admin/post-types/admin-field-group.php:94
msgid "Checked"
msgstr "Zaznaczone"

#: includes/admin/post-types/admin-field-group.php:90
msgid "Move Custom Field"
msgstr "Przenieś pole"

#: includes/admin/post-types/admin-field-group.php:89
msgid "No toggle fields available"
msgstr "Brak dostępnych pól"

#: includes/admin/post-types/admin-field-group.php:87
msgid "Field group title is required"
msgstr "Tytuł grupy pól jest wymagany"

#: includes/admin/post-types/admin-field-group.php:86
msgid "This field cannot be moved until its changes have been saved"
msgstr "To pole nie może zostać przeniesione zanim zmiany nie zostaną zapisane"

#: includes/admin/post-types/admin-field-group.php:85
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr "Ciąg znaków „field_” nie może zostać użyty na początku nazwy pola"

#: includes/admin/post-types/admin-field-group.php:69
msgid "Field group draft updated."
msgstr "Szkic grupy pól został zaktualizowany."

#: includes/admin/post-types/admin-field-group.php:68
msgid "Field group scheduled for."
msgstr "Publikacja grupy pól została zaplanowana."

#: includes/admin/post-types/admin-field-group.php:67
msgid "Field group submitted."
msgstr "Grupa pól została dodana."

#: includes/admin/post-types/admin-field-group.php:66
msgid "Field group saved."
msgstr "Grupa pól została zapisana."

#: includes/admin/post-types/admin-field-group.php:65
msgid "Field group published."
msgstr "Grupa pól została opublikowana."

#: includes/admin/post-types/admin-field-group.php:62
msgid "Field group deleted."
msgstr "Grupa pól została usunięta."

#: includes/admin/post-types/admin-field-group.php:60
#: includes/admin/post-types/admin-field-group.php:61
#: includes/admin/post-types/admin-field-group.php:63
msgid "Field group updated."
msgstr "Grupa pól została zaktualizowana."

#: includes/admin/admin-tools.php:107
#: includes/admin/views/global/navigation.php:254
#: includes/admin/views/tools/tools.php:14
msgid "Tools"
msgstr "Narzędzia"

#: includes/locations/abstract-acf-location.php:105
msgid "is not equal to"
msgstr "nie jest równe"

#: includes/locations/abstract-acf-location.php:104
msgid "is equal to"
msgstr "jest równe"

#: includes/locations.php:104
msgid "Forms"
msgstr "Formularze"

#: includes/admin/post-types/admin-post-type.php:125 includes/locations.php:102
#: includes/locations/class-acf-location-page.php:22
msgid "Page"
msgstr "Strona"

#: includes/admin/post-types/admin-post-type.php:123 includes/locations.php:101
#: includes/locations/class-acf-location-post.php:22
msgid "Post"
msgstr "Wpis"

#: includes/fields.php:328
msgid "Relational"
msgstr "Relacyjne"

#: includes/fields.php:327
msgid "Choice"
msgstr "Wybór"

#: includes/fields.php:325
msgid "Basic"
msgstr "Podstawowe"

#: includes/fields.php:276
msgid "Unknown"
msgstr "Nieznany"

#: includes/fields.php:276
msgid "Field type does not exist"
msgstr "Rodzaj pola nie istnieje"

#: includes/forms/form-front.php:220
msgid "Spam Detected"
msgstr "Wykryto spam"

#: includes/forms/form-front.php:103
msgid "Post updated"
msgstr "Wpis został zaktualizowany"

#: includes/forms/form-front.php:102
msgid "Update"
msgstr "Aktualizuj"

#: includes/forms/form-front.php:63
msgid "Validate Email"
msgstr "Potwierdź e-mail"

#: includes/fields.php:326 includes/forms/form-front.php:55
msgid "Content"
msgstr "Treść"

#: includes/admin/views/acf-post-type/advanced-settings.php:21
#: includes/forms/form-front.php:46
msgid "Title"
msgstr "Tytuł"

#: includes/assets.php:376 includes/forms/form-comment.php:140
msgid "Edit field group"
msgstr "Edytuj grupę pól"

#: includes/admin/post-types/admin-field-group.php:113
msgid "Selection is less than"
msgstr "Wybór jest mniejszy niż"

#: includes/admin/post-types/admin-field-group.php:112
msgid "Selection is greater than"
msgstr "Wybór jest większy niż"

#: includes/admin/post-types/admin-field-group.php:111
msgid "Value is less than"
msgstr "Wartość jest mniejsza niż"

#: includes/admin/post-types/admin-field-group.php:110
msgid "Value is greater than"
msgstr "Wartość jest większa niż"

#: includes/admin/post-types/admin-field-group.php:109
msgid "Value contains"
msgstr "Wartość zawiera"

#: includes/admin/post-types/admin-field-group.php:108
msgid "Value matches pattern"
msgstr "Wartość musi pasować do wzoru"

#: includes/admin/post-types/admin-field-group.php:107
msgid "Value is not equal to"
msgstr "Wartość nie jest równa"

#: includes/admin/post-types/admin-field-group.php:106
msgid "Value is equal to"
msgstr "Wartość jest równa"

#: includes/admin/post-types/admin-field-group.php:105
msgid "Has no value"
msgstr "Nie ma wartości"

#: includes/admin/post-types/admin-field-group.php:104
msgid "Has any value"
msgstr "Ma dowolną wartość"

#: includes/admin/admin-internal-post-type.php:337
#: includes/admin/views/browse-fields-modal.php:72 includes/assets.php:354
msgid "Cancel"
msgstr "Anuluj"

#: includes/assets.php:350
msgid "Are you sure?"
msgstr "Czy na pewno?"

#: includes/assets.php:370
msgid "%d fields require attention"
msgstr "%d pola(-ól) wymaga uwagi"

#: includes/assets.php:369
msgid "1 field requires attention"
msgstr "1 pole wymaga uwagi"

#: includes/assets.php:368 includes/validation.php:257
#: includes/validation.php:265
msgid "Validation failed"
msgstr "Walidacja nie powiodła się"

#: includes/assets.php:367
msgid "Validation successful"
msgstr "Walidacja zakończona sukcesem"

#: includes/media.php:54
msgid "Restricted"
msgstr "Ograniczone"

#: includes/media.php:53
msgid "Collapse Details"
msgstr "Zwiń szczegóły"

#: includes/media.php:52
msgid "Expand Details"
msgstr "Rozwiń szczegóły"

#: includes/admin/views/acf-post-type/advanced-settings.php:470
#: includes/media.php:51
msgid "Uploaded to this post"
msgstr "Wgrane do wpisu"

#: includes/media.php:50
msgctxt "verb"
msgid "Update"
msgstr "Aktualizuj"

#: includes/media.php:49
msgctxt "verb"
msgid "Edit"
msgstr "Edytuj"

#: includes/assets.php:364
msgid "The changes you made will be lost if you navigate away from this page"
msgstr ""
"Wprowadzone przez Ciebie zmiany przepadną jeśli przejdziesz do innej strony"

#: includes/api/api-helpers.php:3000
msgid "File type must be %s."
msgstr "Wymagany typ pliku to %s."

#: includes/admin/post-types/admin-field-group.php:98
#: includes/admin/views/acf-field-group/conditional-logic.php:64
#: includes/admin/views/acf-field-group/conditional-logic.php:182
#: includes/admin/views/acf-field-group/location-group.php:3
#: includes/admin/views/acf-field-group/locations.php:35
#: includes/api/api-helpers.php:2997
msgid "or"
msgstr "lub"

#: includes/api/api-helpers.php:2973
msgid "File size must not exceed %s."
msgstr "Rozmiar pliku nie może przekraczać %s."

#: includes/api/api-helpers.php:2969
msgid "File size must be at least %s."
msgstr "Rozmiar pliku musi wynosić co najmniej %s."

#: includes/api/api-helpers.php:2956
msgid "Image height must not exceed %dpx."
msgstr "Wysokość obrazka nie może przekraczać %dpx."

#: includes/api/api-helpers.php:2952
msgid "Image height must be at least %dpx."
msgstr "Obrazek musi mieć co najmniej %dpx wysokości."

#: includes/api/api-helpers.php:2940
msgid "Image width must not exceed %dpx."
msgstr "Szerokość obrazka nie może przekraczać %dpx."

#: includes/api/api-helpers.php:2936
msgid "Image width must be at least %dpx."
msgstr "Obrazek musi mieć co najmniej %dpx szerokości."

#: includes/api/api-helpers.php:1425 includes/api/api-term.php:140
msgid "(no title)"
msgstr "(brak tytułu)"

#: includes/api/api-helpers.php:781
msgid "Full Size"
msgstr "Pełny rozmiar"

#: includes/api/api-helpers.php:746
msgid "Large"
msgstr "Duży"

#: includes/api/api-helpers.php:745
msgid "Medium"
msgstr "Średni"

#: includes/api/api-helpers.php:744
msgid "Thumbnail"
msgstr "Miniatura"

#: includes/acf-field-functions.php:854
#: includes/admin/post-types/admin-field-group.php:95
msgid "(no label)"
msgstr "(brak etykiety)"

#: includes/fields/class-acf-field-textarea.php:135
msgid "Sets the textarea height"
msgstr "Określa wysokość obszaru tekstowego"

#: includes/fields/class-acf-field-textarea.php:134
msgid "Rows"
msgstr "Wiersze"

#: includes/fields/class-acf-field-textarea.php:22
msgid "Text Area"
msgstr "Obszar tekstowy"

#: includes/fields/class-acf-field-checkbox.php:421
msgid "Prepend an extra checkbox to toggle all choices"
msgstr "Dołącz dodatkowe pole, aby grupowo włączać/wyłączać wszystkie wybory"

#: includes/fields/class-acf-field-checkbox.php:383
msgid "Save 'custom' values to the field's choices"
msgstr "Dopisz własne wartości do wyborów pola"

#: includes/fields/class-acf-field-checkbox.php:372
msgid "Allow 'custom' values to be added"
msgstr "Zezwól na dodawanie własnych wartości"

#: includes/fields/class-acf-field-checkbox.php:35
msgid "Add new choice"
msgstr "Dodaj nowy wybór"

#: includes/fields/class-acf-field-checkbox.php:157
msgid "Toggle All"
msgstr "Przełącz wszystko"

#: includes/fields/class-acf-field-page_link.php:487
msgid "Allow Archives URLs"
msgstr "Zezwól na adresy URL archiwów"

#: includes/fields/class-acf-field-page_link.php:196
msgid "Archives"
msgstr "Archiwa"

#: includes/fields/class-acf-field-page_link.php:22
msgid "Page Link"
msgstr "Odnośnik do strony"

#: includes/fields/class-acf-field-taxonomy.php:884
#: includes/locations/class-acf-location-user-form.php:72
msgid "Add"
msgstr "Dodaj"

#: includes/admin/views/acf-field-group/fields.php:53
#: includes/fields/class-acf-field-taxonomy.php:854
msgid "Name"
msgstr "Nazwa"

#: includes/fields/class-acf-field-taxonomy.php:839
msgid "%s added"
msgstr "Dodano %s"

#: includes/fields/class-acf-field-taxonomy.php:803
msgid "%s already exists"
msgstr "%s już istnieje"

#: includes/fields/class-acf-field-taxonomy.php:791
msgid "User unable to add new %s"
msgstr "Użytkownik nie może dodać nowych „%s”"

#: includes/fields/class-acf-field-taxonomy.php:678
msgid "Term ID"
msgstr "Identyfikator terminu"

#: includes/fields/class-acf-field-taxonomy.php:677
msgid "Term Object"
msgstr "Obiekt terminu"

#: includes/fields/class-acf-field-taxonomy.php:662
msgid "Load value from posts terms"
msgstr "Wczytaj wartości z terminów taksonomii z wpisu"

#: includes/fields/class-acf-field-taxonomy.php:661
msgid "Load Terms"
msgstr "Wczytaj terminy taksonomii"

#: includes/fields/class-acf-field-taxonomy.php:651
msgid "Connect selected terms to the post"
msgstr "Przypisz wybrane terminy taksonomii do wpisu"

#: includes/fields/class-acf-field-taxonomy.php:650
msgid "Save Terms"
msgstr "Zapisz terminy taksonomii"

#: includes/fields/class-acf-field-taxonomy.php:640
msgid "Allow new terms to be created whilst editing"
msgstr "Zezwól na tworzenie nowych terminów taksonomii podczas edycji"

#: includes/fields/class-acf-field-taxonomy.php:639
msgid "Create Terms"
msgstr "Tworzenie terminów taksonomii"

#: includes/fields/class-acf-field-taxonomy.php:698
msgid "Radio Buttons"
msgstr "Pola wyboru"

#: includes/fields/class-acf-field-taxonomy.php:697
msgid "Single Value"
msgstr "Pojedyncza wartość"

#: includes/fields/class-acf-field-taxonomy.php:695
msgid "Multi Select"
msgstr "Wybór wielokrotny"

#: includes/fields/class-acf-field-checkbox.php:22
#: includes/fields/class-acf-field-taxonomy.php:694
msgid "Checkbox"
msgstr "Pole zaznaczenia"

#: includes/fields/class-acf-field-taxonomy.php:693
msgid "Multiple Values"
msgstr "Wiele wartości"

#: includes/fields/class-acf-field-taxonomy.php:688
msgid "Select the appearance of this field"
msgstr "Określ wygląd tego pola"

#: includes/fields/class-acf-field-taxonomy.php:687
msgid "Appearance"
msgstr "Wygląd"

#: includes/fields/class-acf-field-taxonomy.php:629
msgid "Select the taxonomy to be displayed"
msgstr "Wybierz taksonomię do wyświetlenia"

#: includes/fields/class-acf-field-taxonomy.php:593
msgctxt "No Terms"
msgid "No %s"
msgstr "Brak „%s”"

#: includes/fields/class-acf-field-number.php:240
msgid "Value must be equal to or lower than %d"
msgstr "Wartość musi być równa lub niższa od %d"

#: includes/fields/class-acf-field-number.php:235
msgid "Value must be equal to or higher than %d"
msgstr "Wartość musi być równa lub wyższa od %d"

#: includes/fields/class-acf-field-number.php:223
msgid "Value must be a number"
msgstr "Wartość musi być liczbą"

#: includes/fields/class-acf-field-number.php:22
msgid "Number"
msgstr "Liczba"

#: includes/fields/class-acf-field-radio.php:254
msgid "Save 'other' values to the field's choices"
msgstr "Dopisz wartości wyboru „inne” do wyborów pola"

#: includes/fields/class-acf-field-radio.php:243
msgid "Add 'other' choice to allow for custom values"
msgstr "Dodaj wybór „inne”, aby zezwolić na własne wartości"

#: includes/admin/views/global/navigation.php:202
msgid "Other"
msgstr "Inne"

#: includes/fields/class-acf-field-radio.php:22
msgid "Radio Button"
msgstr "Pole wyboru"

#: includes/fields/class-acf-field-accordion.php:106
msgid ""
"Define an endpoint for the previous accordion to stop. This accordion will "
"not be visible."
msgstr ""
"Zdefiniuj punkt końcowy dla zatrzymania poprzedniego panelu zwijanego. Ten "
"panel zwijany nie będzie widoczny."

#: includes/fields/class-acf-field-accordion.php:95
msgid "Allow this accordion to open without closing others."
msgstr "Zezwól, aby ten zwijany panel otwierał się bez zamykania innych."

#: includes/fields/class-acf-field-accordion.php:94
msgid "Multi-Expand"
msgstr "Multi-expand"

#: includes/fields/class-acf-field-accordion.php:84
msgid "Display this accordion as open on page load."
msgstr "Pokaż ten zwijany panel jako otwarty po załadowaniu strony."

#: includes/fields/class-acf-field-accordion.php:83
msgid "Open"
msgstr "Otwórz"

#: includes/fields/class-acf-field-accordion.php:24
msgid "Accordion"
msgstr "Zwijany panel"

#: includes/fields/class-acf-field-file.php:253
#: includes/fields/class-acf-field-file.php:265
msgid "Restrict which files can be uploaded"
msgstr "Określ jakie pliki mogą być przesyłane"

#: includes/fields/class-acf-field-file.php:207
msgid "File ID"
msgstr "Identyfikator pliku"

#: includes/fields/class-acf-field-file.php:206
msgid "File URL"
msgstr "Adres URL pliku"

#: includes/fields/class-acf-field-file.php:205
msgid "File Array"
msgstr "Tablica pliku"

#: includes/fields/class-acf-field-file.php:176
msgid "Add File"
msgstr "Dodaj plik"

#: includes/admin/tools/class-acf-admin-tool-import.php:151
#: includes/fields/class-acf-field-file.php:176
msgid "No file selected"
msgstr "Nie wybrano pliku"

#: includes/fields/class-acf-field-file.php:140
msgid "File name"
msgstr "Nazwa pliku"

#: includes/fields/class-acf-field-file.php:57
msgid "Update File"
msgstr "Aktualizuj plik"

#: includes/fields/class-acf-field-file.php:56
msgid "Edit File"
msgstr "Edytuj plik"

#: includes/admin/tools/class-acf-admin-tool-import.php:55
#: includes/fields/class-acf-field-file.php:55
msgid "Select File"
msgstr "Wybierz plik"

#: includes/fields/class-acf-field-file.php:22
msgid "File"
msgstr "Plik"

#: includes/fields/class-acf-field-password.php:22
msgid "Password"
msgstr "Hasło"

#: includes/fields/class-acf-field-select.php:363
msgid "Specify the value returned"
msgstr "Określ zwracaną wartość"

#: includes/fields/class-acf-field-select.php:431
msgid "Use AJAX to lazy load choices?"
msgstr "Używaj AJAX do wczytywania wyborów?"

#: includes/fields/class-acf-field-checkbox.php:333
#: includes/fields/class-acf-field-select.php:352
msgid "Enter each default value on a new line"
msgstr "Wpisz każdą domyślną wartość w osobnej linii"

#: includes/fields/class-acf-field-select.php:217 includes/media.php:48
msgctxt "verb"
msgid "Select"
msgstr "Wybierz"

#: includes/fields/class-acf-field-select.php:95
msgctxt "Select2 JS load_fail"
msgid "Loading failed"
msgstr "Wczytywanie zakończone niepowodzeniem"

#: includes/fields/class-acf-field-select.php:94
msgctxt "Select2 JS searching"
msgid "Searching&hellip;"
msgstr "Szukam&hellip;"

#: includes/fields/class-acf-field-select.php:93
msgctxt "Select2 JS load_more"
msgid "Loading more results&hellip;"
msgstr "Wczytuję więcej wyników&hellip;"

#. translators: %d - maximum number of items that can be selected in the select
#. field
#: includes/fields/class-acf-field-select.php:92
msgctxt "Select2 JS selection_too_long_n"
msgid "You can only select %d items"
msgstr "Możesz wybrać tylko %d elementy(-tów)"

#: includes/fields/class-acf-field-select.php:90
msgctxt "Select2 JS selection_too_long_1"
msgid "You can only select 1 item"
msgstr "Możesz wybrać tylko 1 element"

#. translators: %d - number of characters that should be removed from select
#. field
#: includes/fields/class-acf-field-select.php:89
msgctxt "Select2 JS input_too_long_n"
msgid "Please delete %d characters"
msgstr "Proszę usunąć %d znaki(-ów)"

#: includes/fields/class-acf-field-select.php:87
msgctxt "Select2 JS input_too_long_1"
msgid "Please delete 1 character"
msgstr "Proszę usunąć 1 znak"

#. translators: %d - number of characters to enter into select field input
#: includes/fields/class-acf-field-select.php:86
msgctxt "Select2 JS input_too_short_n"
msgid "Please enter %d or more characters"
msgstr "Wpisz %d lub więcej znaków"

#: includes/fields/class-acf-field-select.php:84
msgctxt "Select2 JS input_too_short_1"
msgid "Please enter 1 or more characters"
msgstr "Wpisz 1 lub więcej znaków"

#: includes/fields/class-acf-field-select.php:83
msgctxt "Select2 JS matches_0"
msgid "No matches found"
msgstr "Brak pasujących wyników"

#. translators: %d - number of results available in select field
#: includes/fields/class-acf-field-select.php:82
msgctxt "Select2 JS matches_n"
msgid "%d results are available, use up and down arrow keys to navigate."
msgstr "Dostępnych wyników: %d. Użyj strzałek w górę i w dół, aby nawigować."

#: includes/fields/class-acf-field-select.php:80
msgctxt "Select2 JS matches_1"
msgid "One result is available, press enter to select it."
msgstr "Dostępny jest jeden wynik. Aby go wybrać, naciśnij Enter."

#: includes/fields/class-acf-field-select.php:16
#: includes/fields/class-acf-field-taxonomy.php:699
msgctxt "noun"
msgid "Select"
msgstr "Lista wyboru"

#: includes/fields/class-acf-field-user.php:102
msgid "User ID"
msgstr "Identyfikator użytkownika"

#: includes/fields/class-acf-field-user.php:101
msgid "User Object"
msgstr "Obiekt użytkownika"

#: includes/fields/class-acf-field-user.php:100
msgid "User Array"
msgstr "Tablica użytkownika"

#: includes/fields/class-acf-field-user.php:88
msgid "All user roles"
msgstr "Wszystkie role użytkownika"

#: includes/fields/class-acf-field-user.php:80
msgid "Filter by Role"
msgstr "Filtruj wg roli"

#: includes/fields/class-acf-field-user.php:15 includes/locations.php:103
msgid "User"
msgstr "Użytkownik"

#: includes/fields/class-acf-field-separator.php:22
msgid "Separator"
msgstr "Separator"

#: includes/fields/class-acf-field-color_picker.php:69
msgid "Select Color"
msgstr "Wybierz kolor"

#: includes/admin/post-types/admin-post-type.php:127
#: includes/admin/post-types/admin-taxonomy.php:129
#: includes/fields/class-acf-field-color_picker.php:67
msgid "Default"
msgstr "Domyślne"

#: includes/admin/views/acf-post-type/advanced-settings.php:89
#: includes/admin/views/acf-taxonomy/advanced-settings.php:141
#: includes/fields/class-acf-field-color_picker.php:65
msgid "Clear"
msgstr "Wyczyść"

#: includes/fields/class-acf-field-color_picker.php:22
msgid "Color Picker"
msgstr "Wybór koloru"

#: includes/fields/class-acf-field-date_time_picker.php:82
msgctxt "Date Time Picker JS pmTextShort"
msgid "P"
msgstr "P"

#: includes/fields/class-acf-field-date_time_picker.php:81
msgctxt "Date Time Picker JS pmText"
msgid "PM"
msgstr "PM"

#: includes/fields/class-acf-field-date_time_picker.php:78
msgctxt "Date Time Picker JS amTextShort"
msgid "A"
msgstr "A"

#: includes/fields/class-acf-field-date_time_picker.php:77
msgctxt "Date Time Picker JS amText"
msgid "AM"
msgstr "AM"

#: includes/fields/class-acf-field-date_time_picker.php:75
msgctxt "Date Time Picker JS selectText"
msgid "Select"
msgstr "Wybierz"

#: includes/fields/class-acf-field-date_time_picker.php:74
msgctxt "Date Time Picker JS closeText"
msgid "Done"
msgstr "Gotowe"

#: includes/fields/class-acf-field-date_time_picker.php:73
msgctxt "Date Time Picker JS currentText"
msgid "Now"
msgstr "Teraz"

#: includes/fields/class-acf-field-date_time_picker.php:72
msgctxt "Date Time Picker JS timezoneText"
msgid "Time Zone"
msgstr "Strefa czasowa"

#: includes/fields/class-acf-field-date_time_picker.php:71
msgctxt "Date Time Picker JS microsecText"
msgid "Microsecond"
msgstr "Mikrosekunda"

#: includes/fields/class-acf-field-date_time_picker.php:70
msgctxt "Date Time Picker JS millisecText"
msgid "Millisecond"
msgstr "Milisekunda"

#: includes/fields/class-acf-field-date_time_picker.php:69
msgctxt "Date Time Picker JS secondText"
msgid "Second"
msgstr "Sekunda"

#: includes/fields/class-acf-field-date_time_picker.php:68
msgctxt "Date Time Picker JS minuteText"
msgid "Minute"
msgstr "Minuta"

#: includes/fields/class-acf-field-date_time_picker.php:67
msgctxt "Date Time Picker JS hourText"
msgid "Hour"
msgstr "Godzina"

#: includes/fields/class-acf-field-date_time_picker.php:66
msgctxt "Date Time Picker JS timeText"
msgid "Time"
msgstr "Czas"

#: includes/fields/class-acf-field-date_time_picker.php:65
msgctxt "Date Time Picker JS timeOnlyTitle"
msgid "Choose Time"
msgstr "Określ czas"

#: includes/fields/class-acf-field-date_time_picker.php:22
msgid "Date Time Picker"
msgstr "Wybór daty i godziny"

#: includes/fields/class-acf-field-accordion.php:105
msgid "Endpoint"
msgstr "Punkt końcowy"

#: includes/admin/views/acf-field-group/options.php:130
#: includes/fields/class-acf-field-tab.php:112
msgid "Left aligned"
msgstr "Wyrównanie do lewej"

#: includes/admin/views/acf-field-group/options.php:129
#: includes/fields/class-acf-field-tab.php:111
msgid "Top aligned"
msgstr "Wyrównanie do góry"

#: includes/fields/class-acf-field-tab.php:107
msgid "Placement"
msgstr "Położenie"

#: includes/fields/class-acf-field-tab.php:23
msgid "Tab"
msgstr "Zakładka"

#: includes/fields/class-acf-field-url.php:138
msgid "Value must be a valid URL"
msgstr "Wartość musi być poprawnym adresem URL"

#: includes/fields/class-acf-field-link.php:153
msgid "Link URL"
msgstr "Adres URL odnośnika"

#: includes/fields/class-acf-field-link.php:152
msgid "Link Array"
msgstr "Tablica odnośnika"

#: includes/fields/class-acf-field-link.php:124
msgid "Opens in a new window/tab"
msgstr "Otwiera się w nowym oknie/karcie"

#: includes/fields/class-acf-field-link.php:119
msgid "Select Link"
msgstr "Wybierz odnośnik"

#: includes/fields/class-acf-field-link.php:22
msgid "Link"
msgstr "Odnośnik"

#: includes/fields/class-acf-field-email.php:22
msgid "Email"
msgstr "E-mail"

#: includes/fields/class-acf-field-number.php:173
#: includes/fields/class-acf-field-range.php:206
msgid "Step Size"
msgstr "Wielkość kroku"

#: includes/fields/class-acf-field-number.php:143
#: includes/fields/class-acf-field-range.php:184
msgid "Maximum Value"
msgstr "Wartość maksymalna"

#: includes/fields/class-acf-field-number.php:133
#: includes/fields/class-acf-field-range.php:173
msgid "Minimum Value"
msgstr "Wartość minimalna"

#: includes/fields/class-acf-field-range.php:22
msgid "Range"
msgstr "Zakres"

#: includes/fields/class-acf-field-button-group.php:165
#: includes/fields/class-acf-field-checkbox.php:350
#: includes/fields/class-acf-field-radio.php:210
#: includes/fields/class-acf-field-select.php:370
msgid "Both (Array)"
msgstr "Oba (tablica)"

#: includes/admin/views/acf-field-group/fields.php:52
#: includes/fields/class-acf-field-button-group.php:164
#: includes/fields/class-acf-field-checkbox.php:349
#: includes/fields/class-acf-field-radio.php:209
#: includes/fields/class-acf-field-select.php:369
msgid "Label"
msgstr "Etykieta"

#: includes/fields/class-acf-field-button-group.php:163
#: includes/fields/class-acf-field-checkbox.php:348
#: includes/fields/class-acf-field-radio.php:208
#: includes/fields/class-acf-field-select.php:368
msgid "Value"
msgstr "Wartość"

#: includes/fields/class-acf-field-button-group.php:211
#: includes/fields/class-acf-field-checkbox.php:411
#: includes/fields/class-acf-field-radio.php:282
msgid "Vertical"
msgstr "Pionowy"

#: includes/fields/class-acf-field-button-group.php:210
#: includes/fields/class-acf-field-checkbox.php:412
#: includes/fields/class-acf-field-radio.php:283
msgid "Horizontal"
msgstr "Poziomy"

#: includes/fields/class-acf-field-button-group.php:138
#: includes/fields/class-acf-field-checkbox.php:323
#: includes/fields/class-acf-field-radio.php:183
#: includes/fields/class-acf-field-select.php:341
msgid "red : Red"
msgstr "czerwony : Czerwony"

#: includes/fields/class-acf-field-button-group.php:138
#: includes/fields/class-acf-field-checkbox.php:323
#: includes/fields/class-acf-field-radio.php:183
#: includes/fields/class-acf-field-select.php:341
msgid "For more control, you may specify both a value and label like this:"
msgstr ""
"Aby uzyskać większą kontrolę, można określić wartość i etykietę w niniejszy "
"sposób:"

#: includes/fields/class-acf-field-button-group.php:138
#: includes/fields/class-acf-field-checkbox.php:323
#: includes/fields/class-acf-field-radio.php:183
#: includes/fields/class-acf-field-select.php:341
msgid "Enter each choice on a new line."
msgstr "Wpisz każdy z wyborów w osobnej linii."

#: includes/fields/class-acf-field-button-group.php:137
#: includes/fields/class-acf-field-checkbox.php:322
#: includes/fields/class-acf-field-radio.php:182
#: includes/fields/class-acf-field-select.php:340
msgid "Choices"
msgstr "Wybory"

#: includes/fields/class-acf-field-button-group.php:23
msgid "Button Group"
msgstr "Grupa przycisków"

#: includes/fields/class-acf-field-button-group.php:183
#: includes/fields/class-acf-field-page_link.php:519
#: includes/fields/class-acf-field-post_object.php:432
#: includes/fields/class-acf-field-radio.php:228
#: includes/fields/class-acf-field-select.php:399
#: includes/fields/class-acf-field-taxonomy.php:708
#: includes/fields/class-acf-field-user.php:132
msgid "Allow Null"
msgstr "Zezwól na pusty"

#: includes/fields/class-acf-field-page_link.php:273
#: includes/fields/class-acf-field-post_object.php:254
#: includes/fields/class-acf-field-taxonomy.php:872
msgid "Parent"
msgstr "Element nadrzędny"

#: includes/fields/class-acf-field-wysiwyg.php:367
msgid "TinyMCE will not be initialized until field is clicked"
msgstr ""
"TinyMCE nie zostanie zainicjowany, dopóki to pole nie zostanie kliknięte"

#: includes/fields/class-acf-field-wysiwyg.php:366
msgid "Delay Initialization"
msgstr "Opóźnij inicjowanie"

#: includes/fields/class-acf-field-wysiwyg.php:355
msgid "Show Media Upload Buttons"
msgstr "Pokaż przycisk dodawania mediów"

#: includes/fields/class-acf-field-wysiwyg.php:339
msgid "Toolbar"
msgstr "Pasek narzędzi"

#: includes/fields/class-acf-field-wysiwyg.php:331
msgid "Text Only"
msgstr "Tylko tekstowa"

#: includes/fields/class-acf-field-wysiwyg.php:330
msgid "Visual Only"
msgstr "Tylko wizualna"

#: includes/fields/class-acf-field-wysiwyg.php:329
msgid "Visual & Text"
msgstr "Wizualna i tekstowa"

#: includes/fields/class-acf-field-icon_picker.php:262
#: includes/fields/class-acf-field-wysiwyg.php:324
msgid "Tabs"
msgstr "Zakładki"

#: includes/fields/class-acf-field-wysiwyg.php:268
msgid "Click to initialize TinyMCE"
msgstr "Kliknij, aby zainicjować TinyMCE"

#: includes/fields/class-acf-field-wysiwyg.php:262
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr "Tekstowy"

#: includes/fields/class-acf-field-wysiwyg.php:261
msgid "Visual"
msgstr "Wizualny"

#: includes/fields/class-acf-field-text.php:181
#: includes/fields/class-acf-field-textarea.php:217
msgid "Value must not exceed %d characters"
msgstr "Wartość nie może przekraczać %d znaków"

#: includes/fields/class-acf-field-text.php:116
#: includes/fields/class-acf-field-textarea.php:114
msgid "Leave blank for no limit"
msgstr "Pozostaw puste w przypadku braku limitu"

#: includes/fields/class-acf-field-text.php:115
#: includes/fields/class-acf-field-textarea.php:113
msgid "Character Limit"
msgstr "Limit znaków"

#: includes/fields/class-acf-field-email.php:144
#: includes/fields/class-acf-field-number.php:194
#: includes/fields/class-acf-field-password.php:95
#: includes/fields/class-acf-field-range.php:228
#: includes/fields/class-acf-field-text.php:156
msgid "Appears after the input"
msgstr "Pojawia się za polem"

#: includes/fields/class-acf-field-email.php:143
#: includes/fields/class-acf-field-number.php:193
#: includes/fields/class-acf-field-password.php:94
#: includes/fields/class-acf-field-range.php:227
#: includes/fields/class-acf-field-text.php:155
msgid "Append"
msgstr "Za polem (sufiks)"

#: includes/fields/class-acf-field-email.php:134
#: includes/fields/class-acf-field-number.php:184
#: includes/fields/class-acf-field-password.php:85
#: includes/fields/class-acf-field-range.php:218
#: includes/fields/class-acf-field-text.php:146
msgid "Appears before the input"
msgstr "Pojawia się przed polem"

#: includes/fields/class-acf-field-email.php:133
#: includes/fields/class-acf-field-number.php:183
#: includes/fields/class-acf-field-password.php:84
#: includes/fields/class-acf-field-range.php:217
#: includes/fields/class-acf-field-text.php:145
msgid "Prepend"
msgstr "Przed polem (prefiks)"

#: includes/fields/class-acf-field-email.php:124
#: includes/fields/class-acf-field-number.php:164
#: includes/fields/class-acf-field-password.php:75
#: includes/fields/class-acf-field-text.php:136
#: includes/fields/class-acf-field-textarea.php:146
#: includes/fields/class-acf-field-url.php:105
msgid "Appears within the input"
msgstr "Pojawia się w polu"

#: includes/fields/class-acf-field-email.php:123
#: includes/fields/class-acf-field-number.php:163
#: includes/fields/class-acf-field-password.php:74
#: includes/fields/class-acf-field-text.php:135
#: includes/fields/class-acf-field-textarea.php:145
#: includes/fields/class-acf-field-url.php:104
msgid "Placeholder Text"
msgstr "Placeholder (tekst zastępczy)"

#: includes/fields/class-acf-field-button-group.php:148
#: includes/fields/class-acf-field-email.php:104
#: includes/fields/class-acf-field-number.php:114
#: includes/fields/class-acf-field-radio.php:193
#: includes/fields/class-acf-field-range.php:154
#: includes/fields/class-acf-field-text.php:96
#: includes/fields/class-acf-field-textarea.php:94
#: includes/fields/class-acf-field-url.php:85
#: includes/fields/class-acf-field-wysiwyg.php:292
msgid "Appears when creating a new post"
msgstr "Wyświetlana podczas tworzenia nowego wpisu"

#: includes/fields/class-acf-field-text.php:22
msgid "Text"
msgstr "Tekst"

#: includes/fields/class-acf-field-relationship.php:753
msgid "%1$s requires at least %2$s selection"
msgid_plural "%1$s requires at least %2$s selections"
msgstr[0] "%1$s wymaga co najmniej %2$s wyboru"
msgstr[1] "%1$s wymaga co najmniej %2$s wyborów"
msgstr[2] "%1$s wymaga co najmniej %2$s wyborów"

#: includes/fields/class-acf-field-post_object.php:402
#: includes/fields/class-acf-field-relationship.php:616
msgid "Post ID"
msgstr "Identyfikator wpisu"

#: includes/fields/class-acf-field-post_object.php:15
#: includes/fields/class-acf-field-post_object.php:401
#: includes/fields/class-acf-field-relationship.php:615
msgid "Post Object"
msgstr "Obiekt wpisu"

#: includes/fields/class-acf-field-relationship.php:648
msgid "Maximum Posts"
msgstr "Maksimum wpisów"

#: includes/fields/class-acf-field-relationship.php:638
msgid "Minimum Posts"
msgstr "Minimum wpisów"

#: includes/admin/views/acf-field-group/options.php:183
#: includes/admin/views/acf-post-type/advanced-settings.php:29
#: includes/fields/class-acf-field-relationship.php:673
msgid "Featured Image"
msgstr "Obrazek wyróżniający"

#: includes/fields/class-acf-field-relationship.php:669
msgid "Selected elements will be displayed in each result"
msgstr "Wybrane elementy będą wyświetlone przy każdym wyniku"

#: includes/fields/class-acf-field-relationship.php:668
msgid "Elements"
msgstr "Elementy"

#: includes/fields/class-acf-field-relationship.php:602
#: includes/fields/class-acf-field-taxonomy.php:20
#: includes/fields/class-acf-field-taxonomy.php:628
#: includes/locations/class-acf-location-taxonomy.php:22
msgid "Taxonomy"
msgstr "Taksonomia"

#: includes/fields/class-acf-field-relationship.php:601
#: includes/locations/class-acf-location-post-type.php:22
#: includes/post-types/class-acf-post-type.php:92
msgid "Post Type"
msgstr "Typ treści"

#: includes/fields/class-acf-field-relationship.php:595
msgid "Filters"
msgstr "Filtry"

#: includes/fields/class-acf-field-page_link.php:480
#: includes/fields/class-acf-field-post_object.php:389
#: includes/fields/class-acf-field-relationship.php:588
msgid "All taxonomies"
msgstr "Wszystkie taksonomie"

#: includes/fields/class-acf-field-page_link.php:472
#: includes/fields/class-acf-field-post_object.php:381
#: includes/fields/class-acf-field-relationship.php:580
msgid "Filter by Taxonomy"
msgstr "Filtruj wg taksonomii"

#: includes/fields/class-acf-field-page_link.php:450
#: includes/fields/class-acf-field-post_object.php:359
#: includes/fields/class-acf-field-relationship.php:558
msgid "All post types"
msgstr "Wszystkie typy treści"

#: includes/fields/class-acf-field-page_link.php:442
#: includes/fields/class-acf-field-post_object.php:351
#: includes/fields/class-acf-field-relationship.php:550
msgid "Filter by Post Type"
msgstr "Filtruj wg typu treści"

#: includes/fields/class-acf-field-relationship.php:450
msgid "Search..."
msgstr "Wyszukiwanie…"

#: includes/fields/class-acf-field-relationship.php:380
msgid "Select taxonomy"
msgstr "Wybierz taksonomię"

#: includes/fields/class-acf-field-relationship.php:372
msgid "Select post type"
msgstr "Wybierz typ treści"

#: includes/fields/class-acf-field-relationship.php:78
msgid "No matches found"
msgstr "Brak pasujących wyników"

#: includes/fields/class-acf-field-relationship.php:77
msgid "Loading"
msgstr "Wczytywanie"

#: includes/fields/class-acf-field-relationship.php:76
msgid "Maximum values reached ( {max} values )"
msgstr "Maksymalna liczba wartości została przekroczona ( {max} wartości )"

#: includes/fields/class-acf-field-relationship.php:17
msgid "Relationship"
msgstr "Relacja"

#: includes/fields/class-acf-field-file.php:277
#: includes/fields/class-acf-field-image.php:307
msgid "Comma separated list. Leave blank for all types"
msgstr "Lista oddzielona przecinkami. Pozostaw puste dla wszystkich typów"

#: includes/fields/class-acf-field-file.php:276
#: includes/fields/class-acf-field-image.php:306
msgid "Allowed File Types"
msgstr "Dozwolone typy plików"

#: includes/fields/class-acf-field-file.php:264
#: includes/fields/class-acf-field-image.php:270
msgid "Maximum"
msgstr "Maksimum"

#: includes/fields/class-acf-field-file.php:144
#: includes/fields/class-acf-field-file.php:256
#: includes/fields/class-acf-field-file.php:268
#: includes/fields/class-acf-field-image.php:261
#: includes/fields/class-acf-field-image.php:297
msgid "File size"
msgstr "Wielkość pliku"

#: includes/fields/class-acf-field-image.php:235
#: includes/fields/class-acf-field-image.php:271
msgid "Restrict which images can be uploaded"
msgstr "Określ jakie obrazy mogą być przesyłane"

#: includes/fields/class-acf-field-file.php:252
#: includes/fields/class-acf-field-image.php:234
msgid "Minimum"
msgstr "Minimum"

#: includes/fields/class-acf-field-file.php:222
#: includes/fields/class-acf-field-image.php:200
msgid "Uploaded to post"
msgstr "Wgrane do wpisu"

#: includes/fields/class-acf-field-file.php:221
#: includes/fields/class-acf-field-image.php:199
#: includes/locations/class-acf-location-attachment.php:73
#: includes/locations/class-acf-location-comment.php:61
#: includes/locations/class-acf-location-nav-menu.php:74
#: includes/locations/class-acf-location-taxonomy.php:63
#: includes/locations/class-acf-location-user-form.php:71
#: includes/locations/class-acf-location-user-role.php:78
#: includes/locations/class-acf-location-widget.php:65
msgid "All"
msgstr "Wszystkie"

#: includes/fields/class-acf-field-file.php:216
#: includes/fields/class-acf-field-image.php:194
msgid "Limit the media library choice"
msgstr "Ogranicz wybór do biblioteki mediów"

#: includes/fields/class-acf-field-file.php:215
#: includes/fields/class-acf-field-image.php:193
msgid "Library"
msgstr "Biblioteka"

#: includes/fields/class-acf-field-image.php:326
msgid "Preview Size"
msgstr "Rozmiar podglądu"

#: includes/fields/class-acf-field-image.php:185
msgid "Image ID"
msgstr "Identyfikator obrazka"

#: includes/fields/class-acf-field-image.php:184
msgid "Image URL"
msgstr "Adres URL obrazka"

#: includes/fields/class-acf-field-image.php:183
msgid "Image Array"
msgstr "Tablica obrazków"

#: includes/fields/class-acf-field-button-group.php:158
#: includes/fields/class-acf-field-checkbox.php:343
#: includes/fields/class-acf-field-file.php:200
#: includes/fields/class-acf-field-link.php:147
#: includes/fields/class-acf-field-radio.php:203
msgid "Specify the returned value on front end"
msgstr "Określ wartość zwracaną w witrynie"

#: includes/fields/class-acf-field-button-group.php:157
#: includes/fields/class-acf-field-checkbox.php:342
#: includes/fields/class-acf-field-file.php:199
#: includes/fields/class-acf-field-link.php:146
#: includes/fields/class-acf-field-radio.php:202
#: includes/fields/class-acf-field-taxonomy.php:672
msgid "Return Value"
msgstr "Zwracana wartość"

#: includes/fields/class-acf-field-image.php:155
msgid "Add Image"
msgstr "Dodaj obrazek"

#: includes/fields/class-acf-field-image.php:155
msgid "No image selected"
msgstr "Nie wybrano obrazka"

#: includes/assets.php:353 includes/fields/class-acf-field-file.php:152
#: includes/fields/class-acf-field-image.php:135
#: includes/fields/class-acf-field-link.php:124
msgid "Remove"
msgstr "Usuń"

#: includes/admin/views/acf-field-group/field.php:89
#: includes/fields/class-acf-field-file.php:150
#: includes/fields/class-acf-field-image.php:133
#: includes/fields/class-acf-field-link.php:124
msgid "Edit"
msgstr "Edytuj"

#: includes/fields/class-acf-field-image.php:63 includes/media.php:55
msgid "All images"
msgstr "Wszystkie obrazki"

#: includes/fields/class-acf-field-image.php:62
msgid "Update Image"
msgstr "Aktualizuj obrazek"

#: includes/fields/class-acf-field-image.php:61
msgid "Edit Image"
msgstr "Edytuj obrazek"

#: includes/fields/class-acf-field-image.php:60
msgid "Select Image"
msgstr "Wybierz obrazek"

#: includes/fields/class-acf-field-image.php:22
msgid "Image"
msgstr "Obrazek"

#: includes/fields/class-acf-field-message.php:113
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr ""
"Zezwól aby znaczniki HTML były wyświetlane jako widoczny tekst, a nie "
"renderowane"

#: includes/fields/class-acf-field-message.php:112
msgid "Escape HTML"
msgstr "Dodawaj znaki ucieczki do HTML (escape HTML)"

#: includes/fields/class-acf-field-message.php:104
#: includes/fields/class-acf-field-textarea.php:162
msgid "No Formatting"
msgstr "Brak formatowania"

#: includes/fields/class-acf-field-message.php:103
#: includes/fields/class-acf-field-textarea.php:161
msgid "Automatically add &lt;br&gt;"
msgstr "Automatycznie dodaj &lt;br&gt;"

#: includes/fields/class-acf-field-message.php:102
#: includes/fields/class-acf-field-textarea.php:160
msgid "Automatically add paragraphs"
msgstr "Automatycznie twórz akapity"

#: includes/fields/class-acf-field-message.php:98
#: includes/fields/class-acf-field-textarea.php:156
msgid "Controls how new lines are rendered"
msgstr "Kontroluje jak są renderowane nowe linie"

#: includes/fields/class-acf-field-message.php:97
#: includes/fields/class-acf-field-textarea.php:155
msgid "New Lines"
msgstr "Nowe linie"

#: includes/fields/class-acf-field-date_picker.php:221
#: includes/fields/class-acf-field-date_time_picker.php:208
msgid "Week Starts On"
msgstr "Pierwszy dzień tygodnia"

#: includes/fields/class-acf-field-date_picker.php:190
msgid "The format used when saving a value"
msgstr "Format używany podczas zapisywania wartości"

#: includes/fields/class-acf-field-date_picker.php:189
msgid "Save Format"
msgstr "Zapisz format"

#: includes/fields/class-acf-field-date_picker.php:61
msgctxt "Date Picker JS weekHeader"
msgid "Wk"
msgstr "Tydz"

#: includes/fields/class-acf-field-date_picker.php:60
msgctxt "Date Picker JS prevText"
msgid "Prev"
msgstr "Wstecz"

#: includes/fields/class-acf-field-date_picker.php:59
msgctxt "Date Picker JS nextText"
msgid "Next"
msgstr "Dalej"

#: includes/fields/class-acf-field-date_picker.php:58
msgctxt "Date Picker JS currentText"
msgid "Today"
msgstr "Dzisiaj"

#: includes/fields/class-acf-field-date_picker.php:57
msgctxt "Date Picker JS closeText"
msgid "Done"
msgstr "Gotowe"

#: includes/fields/class-acf-field-date_picker.php:22
msgid "Date Picker"
msgstr "Wybór daty"

#: includes/fields/class-acf-field-image.php:238
#: includes/fields/class-acf-field-image.php:274
#: includes/fields/class-acf-field-oembed.php:241
msgid "Width"
msgstr "Szerokość"

#: includes/fields/class-acf-field-oembed.php:238
#: includes/fields/class-acf-field-oembed.php:250
msgid "Embed Size"
msgstr "Rozmiar osadzenia"

#: includes/fields/class-acf-field-oembed.php:198
msgid "Enter URL"
msgstr "Wpisz adres URL"

#: includes/fields/class-acf-field-oembed.php:22
msgid "oEmbed"
msgstr "oEmbed"

#: includes/fields/class-acf-field-true_false.php:172
msgid "Text shown when inactive"
msgstr "Tekst wyświetlany, gdy jest wyłączone"

#: includes/fields/class-acf-field-true_false.php:171
msgid "Off Text"
msgstr "Tekst, gdy wyłączone"

#: includes/fields/class-acf-field-true_false.php:156
msgid "Text shown when active"
msgstr "Tekst wyświetlany, gdy jest włączone"

#: includes/fields/class-acf-field-true_false.php:155
msgid "On Text"
msgstr "Tekst, gdy włączone"

#: includes/fields/class-acf-field-select.php:420
#: includes/fields/class-acf-field-true_false.php:187
msgid "Stylized UI"
msgstr "Ostylowany interfejs użytkownika"

#: includes/fields/class-acf-field-button-group.php:147
#: includes/fields/class-acf-field-checkbox.php:332
#: includes/fields/class-acf-field-color_picker.php:144
#: includes/fields/class-acf-field-email.php:103
#: includes/fields/class-acf-field-number.php:113
#: includes/fields/class-acf-field-radio.php:192
#: includes/fields/class-acf-field-range.php:153
#: includes/fields/class-acf-field-select.php:351
#: includes/fields/class-acf-field-text.php:95
#: includes/fields/class-acf-field-textarea.php:93
#: includes/fields/class-acf-field-true_false.php:135
#: includes/fields/class-acf-field-url.php:84
#: includes/fields/class-acf-field-wysiwyg.php:291
msgid "Default Value"
msgstr "Wartość domyślna"

#: includes/fields/class-acf-field-true_false.php:126
msgid "Displays text alongside the checkbox"
msgstr "Wyświetla tekst obok pola"

#: includes/fields/class-acf-field-message.php:23
#: includes/fields/class-acf-field-message.php:87
#: includes/fields/class-acf-field-true_false.php:125
msgid "Message"
msgstr "Wiadomość"

#: includes/assets.php:352 includes/fields/class-acf-field-true_false.php:79
#: includes/fields/class-acf-field-true_false.php:175
#: src/Site_Health/Site_Health.php:281 src/Site_Health/Site_Health.php:343
msgid "No"
msgstr "Nie"

#: includes/assets.php:351 includes/fields/class-acf-field-true_false.php:76
#: includes/fields/class-acf-field-true_false.php:159
#: src/Site_Health/Site_Health.php:280 src/Site_Health/Site_Health.php:343
msgid "Yes"
msgstr "Tak"

#: includes/fields/class-acf-field-true_false.php:22
msgid "True / False"
msgstr "Prawda / Fałsz"

#: includes/fields/class-acf-field-group.php:415
msgid "Row"
msgstr "Wiersz"

#: includes/fields/class-acf-field-group.php:414
msgid "Table"
msgstr "Tabela"

#: includes/admin/post-types/admin-field-group.php:158
#: includes/fields/class-acf-field-group.php:413
msgid "Block"
msgstr "Blok"

#: includes/fields/class-acf-field-group.php:408
msgid "Specify the style used to render the selected fields"
msgstr "Określ style stosowane to renderowania wybranych pól"

#: includes/fields.php:330 includes/fields/class-acf-field-button-group.php:204
#: includes/fields/class-acf-field-checkbox.php:405
#: includes/fields/class-acf-field-group.php:407
#: includes/fields/class-acf-field-radio.php:276
msgid "Layout"
msgstr "Układ"

#: includes/fields/class-acf-field-group.php:391
msgid "Sub Fields"
msgstr "Pola podrzędne"

#: includes/fields/class-acf-field-group.php:22
msgid "Group"
msgstr "Grupa"

#: includes/fields/class-acf-field-google-map.php:222
msgid "Customize the map height"
msgstr "Dostosuj wysokość mapy"

#: includes/fields/class-acf-field-google-map.php:221
#: includes/fields/class-acf-field-image.php:249
#: includes/fields/class-acf-field-image.php:285
#: includes/fields/class-acf-field-oembed.php:253
msgid "Height"
msgstr "Wysokość"

#: includes/fields/class-acf-field-google-map.php:210
msgid "Set the initial zoom level"
msgstr "Ustaw początkowe powiększenie"

#: includes/fields/class-acf-field-google-map.php:209
msgid "Zoom"
msgstr "Powiększenie"

#: includes/fields/class-acf-field-google-map.php:183
#: includes/fields/class-acf-field-google-map.php:196
msgid "Center the initial map"
msgstr "Wyśrodkuj początkową mapę"

#: includes/fields/class-acf-field-google-map.php:182
#: includes/fields/class-acf-field-google-map.php:195
msgid "Center"
msgstr "Wyśrodkowanie"

#: includes/fields/class-acf-field-google-map.php:154
msgid "Search for address..."
msgstr "Szukaj adresu…"

#: includes/fields/class-acf-field-google-map.php:151
msgid "Find current location"
msgstr "Znajdź aktualną lokalizację"

#: includes/fields/class-acf-field-google-map.php:150
msgid "Clear location"
msgstr "Wyczyść lokalizację"

#: includes/fields/class-acf-field-google-map.php:149
#: includes/fields/class-acf-field-relationship.php:600
msgid "Search"
msgstr "Szukaj"

#: includes/fields/class-acf-field-google-map.php:57
msgid "Sorry, this browser does not support geolocation"
msgstr "Przepraszamy, ta przeglądarka nie obsługuje geolokalizacji"

#: includes/fields/class-acf-field-google-map.php:22
msgid "Google Map"
msgstr "Mapa Google"

#: includes/fields/class-acf-field-date_picker.php:201
#: includes/fields/class-acf-field-date_time_picker.php:189
#: includes/fields/class-acf-field-time_picker.php:122
msgid "The format returned via template functions"
msgstr "Wartość zwracana przez funkcje w szablonie"

#: includes/fields/class-acf-field-color_picker.php:168
#: includes/fields/class-acf-field-date_picker.php:200
#: includes/fields/class-acf-field-date_time_picker.php:188
#: includes/fields/class-acf-field-icon_picker.php:285
#: includes/fields/class-acf-field-image.php:177
#: includes/fields/class-acf-field-post_object.php:396
#: includes/fields/class-acf-field-relationship.php:610
#: includes/fields/class-acf-field-select.php:362
#: includes/fields/class-acf-field-time_picker.php:121
#: includes/fields/class-acf-field-user.php:95
msgid "Return Format"
msgstr "Zwracany format"

#: includes/fields/class-acf-field-date_picker.php:179
#: includes/fields/class-acf-field-date_picker.php:210
#: includes/fields/class-acf-field-date_time_picker.php:180
#: includes/fields/class-acf-field-date_time_picker.php:198
#: includes/fields/class-acf-field-time_picker.php:113
#: includes/fields/class-acf-field-time_picker.php:129
msgid "Custom:"
msgstr "Własny:"

#: includes/fields/class-acf-field-date_picker.php:171
#: includes/fields/class-acf-field-date_time_picker.php:171
#: includes/fields/class-acf-field-time_picker.php:106
msgid "The format displayed when editing a post"
msgstr "Format wyświetlany przy edycji wpisu"

#: includes/fields/class-acf-field-date_picker.php:170
#: includes/fields/class-acf-field-date_time_picker.php:170
#: includes/fields/class-acf-field-time_picker.php:105
msgid "Display Format"
msgstr "Format wyświetlania"

#: includes/fields/class-acf-field-time_picker.php:22
msgid "Time Picker"
msgstr "Wybór godziny"

#. translators: counts for inactive field groups
#: acf.php:526
msgid "Inactive <span class=\"count\">(%s)</span>"
msgid_plural "Inactive <span class=\"count\">(%s)</span>"
msgstr[0] "Wyłączone <span class=\"count\">(%s)</span>"
msgstr[1] "Wyłączone <span class=\"count\">(%s)</span>"
msgstr[2] "Wyłączone <span class=\"count\">(%s)</span>"

#: acf.php:487
msgid "No Fields found in Trash"
msgstr "Nie znaleziono żadnych pól w koszu"

#: acf.php:486
msgid "No Fields found"
msgstr "Nie znaleziono żadnych pól"

#: acf.php:485
msgid "Search Fields"
msgstr "Szukaj pól"

#: acf.php:484
msgid "View Field"
msgstr "Zobacz pole"

#: acf.php:483 includes/admin/views/acf-field-group/fields.php:113
msgid "New Field"
msgstr "Nowe pole"

#: acf.php:482
msgid "Edit Field"
msgstr "Edytuj pole"

#: acf.php:481
msgid "Add New Field"
msgstr "Dodaj nowe pole"

#: acf.php:479
msgid "Field"
msgstr "Pole"

#: acf.php:478 includes/admin/post-types/admin-field-group.php:179
#: includes/admin/post-types/admin-field-groups.php:93
#: includes/admin/views/acf-field-group/fields.php:32
msgid "Fields"
msgstr "Pola"

#: acf.php:453
msgid "No Field Groups found in Trash"
msgstr "Nie znaleziono żadnych grup pól w koszu"

#: acf.php:452
msgid "No Field Groups found"
msgstr "Nie znaleziono żadnych grup pól"

#: acf.php:451
msgid "Search Field Groups"
msgstr "Szukaj grup pól"

#: acf.php:450
msgid "View Field Group"
msgstr "Zobacz grupę pól"

#: acf.php:449
msgid "New Field Group"
msgstr "Nowa grupa pól"

#: acf.php:448
msgid "Edit Field Group"
msgstr "Edytuj grupę pól"

#: acf.php:447
msgid "Add New Field Group"
msgstr "Dodaj nową grupę pól"

#: acf.php:446 acf.php:480
#: includes/admin/views/acf-post-type/advanced-settings.php:224
#: includes/post-types/class-acf-post-type.php:93
#: includes/post-types/class-acf-taxonomy.php:92
msgid "Add New"
msgstr "Dodaj"

#: acf.php:445
msgid "Field Group"
msgstr "Grupa pól"

#: acf.php:444 includes/admin/post-types/admin-field-groups.php:55
#: includes/admin/post-types/admin-post-types.php:113
#: includes/admin/post-types/admin-taxonomies.php:112
msgid "Field Groups"
msgstr "Grupy pól"

#. Description of the plugin
#: acf.php
msgid "Customize WordPress with powerful, professional and intuitive fields."
msgstr ""
"Dostosuj WordPressa za pomocą wszechstronnych, profesjonalnych i "
"intuicyjnych pól."

#. Plugin URI of the plugin
#: acf.php
msgid "https://www.advancedcustomfields.com"
msgstr "https://www.advancedcustomfields.com"

#. Plugin Name of the plugin
#: acf.php acf.php:290
msgid "Advanced Custom Fields"
msgstr "Advanced Custom Fields"

#: pro/acf-pro.php:27
msgid "Advanced Custom Fields PRO"
msgstr "Advanced Custom Fields PRO"

#: pro/blocks.php:170
msgid "Block type name is required."
msgstr "Nazwa typu bloku jest wymagana."

#. translators: The name of the block type
#: pro/blocks.php:178
msgid "Block type \"%s\" is already registered."
msgstr "Typ bloku \"%s\" jest już zarejestrowany."

#: pro/blocks.php:726
msgid "Switch to Edit"
msgstr "Przejdź do Edytuj"

#: pro/blocks.php:727
msgid "Switch to Preview"
msgstr "Przejdź do Podglądu"

#: pro/blocks.php:728
msgid "Change content alignment"
msgstr "Zmień wyrównanie treści"

#. translators: %s: Block type title
#: pro/blocks.php:731
msgid "%s settings"
msgstr "Ustawienia  %s"

#: pro/blocks.php:936
msgid "This block contains no editable fields."
msgstr "Ten blok nie zawiera żadnych edytowalnych pól."

#. translators: %s: an admin URL to the field group edit screen
#: pro/blocks.php:942
msgid ""
"Assign a <a href=\"%s\" target=\"_blank\">field group</a> to add fields to "
"this block."
msgstr ""
"Przypisz <a href=\"%s\" target=\"_blank\">grupę pól</a>, aby dodać pola do "
"tego bloku."

#: pro/options-page.php:78
msgid "Options Updated"
msgstr "Ustawienia zostały zaktualizowane"

#: pro/updates.php:99
msgid ""
"To enable updates, please enter your license key on the <a "
"href=\"%1$s\">Updates</a> page. If you don't have a licence key, please see "
"<a href=\"%2$s\" target=\"_blank\">details & pricing</a>."
msgstr ""
"Aby włączyć aktualizacje, należy wprowadzić klucz licencyjny na stronie <a "
"href=\"%1$s\">Aktualizacje</a>. Jeśli nie posiadasz klucza licencyjnego, "
"zapoznaj się z <a href=\"%2$s\" target=\"_blank\">informacjami szczegółowymi "
"i cenami</a>."

#: pro/updates.php:159
msgid ""
"<b>ACF Activation Error</b>. Your defined license key has changed, but an "
"error occurred when deactivating your old licence"
msgstr ""
"<b>Błąd aktywacji ACF</b>. Zdefiniowany przez Państwa klucz licencyjny uległ "
"zmianie, ale podczas dezaktywacji starej licencji wystąpił błąd"

#: pro/updates.php:154
msgid ""
"<b>ACF Activation Error</b>. Your defined license key has changed, but an "
"error occurred when connecting to activation server"
msgstr ""
"<b>Błąd aktywacji ACF</b>. Twój zdefiniowany klucz licencyjny uległ zmianie, "
"ale wystąpił błąd podczas łączenia się z serwerem aktywacyjnym"

#: pro/updates.php:192
msgid "<b>ACF Activation Error</b>"
msgstr "<b>ACF Błąd aktywacji</b>"

#: pro/updates.php:187
msgid ""
"<b>ACF Activation Error</b>. An error occurred when connecting to activation "
"server"
msgstr ""
"<b>Błąd aktywacji ACF</b>. Wystąpił błąd podczas łączenia się z serwerem "
"aktywacyjnym"

#: pro/updates.php:279
msgid "Check Again"
msgstr "Sprawdź ponownie"

#: pro/updates.php:593
msgid "<b>ACF Activation Error</b>. Could not connect to activation server"
msgstr ""
"<b>Błąd aktywacji ACF</b>. Nie można połączyć się z serwerem aktywacyjnym"

#: pro/admin/admin-options-page.php:195
msgid "Publish"
msgstr "Opublikuj"

#: pro/admin/admin-options-page.php:199
msgid ""
"No Custom Field Groups found for this options page. <a href=\"%s\">Create a "
"Custom Field Group</a>"
msgstr ""
"Żadna grupa pól nie została dodana do tej strony opcji. <a "
"href=\"%s\">Utwórz grupę własnych pól</a>"

#: pro/admin/admin-updates.php:52
msgid "<b>Error</b>. Could not connect to update server"
msgstr "<b>Błąd</b>. Nie można połączyć z serwerem aktualizacji"

#: pro/admin/admin-updates.php:212
msgid ""
"<b>Error</b>. Could not authenticate update package. Please check again or "
"deactivate and reactivate your ACF PRO license."
msgstr ""
"<b>Error</b>. Nie można uwierzytelnić pakietu aktualizacyjnego. Proszę "
"sprawdzić ponownie lub dezaktywować i ponownie uaktywnić licencję ACF PRO."

#: pro/admin/admin-updates.php:199
msgid ""
"<b>Error</b>. Your license for this site has expired or been deactivated. "
"Please reactivate your ACF PRO license."
msgstr ""
"<b>Błąd</b>. Twoja licencja dla tej strony wygasła lub została "
"dezaktywowana. Proszę ponownie aktywować licencję ACF PRO."

#: pro/fields/class-acf-field-clone.php:27,
#: pro/fields/class-acf-field-repeater.php:31
msgid ""
"Allows you to select and display existing fields. It does not duplicate any "
"fields in the database, but loads and displays the selected fields at run-"
"time. The Clone field can either replace itself with the selected fields or "
"display the selected fields as a group of subfields."
msgstr ""
"Umożliwia wybranie i wyświetlenie istniejących pól. Nie duplikuje żadnych "
"pól w bazie danych, ale ładuje i wyświetla wybrane pola w czasie "
"wykonywania. Pole Klonuj może zastąpić się wybranymi polami lub wyświetlić "
"wybrane pola jako grupę podpól."

#: pro/fields/class-acf-field-clone.php:819
msgid "Select one or more fields you wish to clone"
msgstr "Wybierz jedno lub więcej pól które chcesz sklonować"

#: pro/fields/class-acf-field-clone.php:838
msgid "Display"
msgstr "Wyświetl"

#: pro/fields/class-acf-field-clone.php:839
msgid "Specify the style used to render the clone field"
msgstr "Określ styl wykorzystywany do stosowania w klonowanych polach"

#: pro/fields/class-acf-field-clone.php:844
msgid "Group (displays selected fields in a group within this field)"
msgstr "Grupuj (wyświetla wybrane pola w grupie)"

#: pro/fields/class-acf-field-clone.php:845
msgid "Seamless (replaces this field with selected fields)"
msgstr "Ujednolicenie (zastępuje to pole wybranymi polami)"

#: pro/fields/class-acf-field-clone.php:868
msgid "Labels will be displayed as %s"
msgstr "Etykiety będą wyświetlane jako %s"

#: pro/fields/class-acf-field-clone.php:873
msgid "Prefix Field Labels"
msgstr "Prefiks Etykiet Pól"

#: pro/fields/class-acf-field-clone.php:883
msgid "Values will be saved as %s"
msgstr "Wartości będą zapisane jako %s"

#: pro/fields/class-acf-field-clone.php:888
msgid "Prefix Field Names"
msgstr "Prefiks Nazw Pól"

#: pro/fields/class-acf-field-clone.php:1005
msgid "Unknown field"
msgstr "Nieznane pole"

#: pro/fields/class-acf-field-clone.php:1042
msgid "Unknown field group"
msgstr "Nieznana grupa pól"

#: pro/fields/class-acf-field-clone.php:1046
msgid "All fields from %s field group"
msgstr "Wszystkie pola z grupy pola %s"

#: pro/fields/class-acf-field-flexible-content.php:27
msgid ""
"Allows you to define, create and manage content with total control by "
"creating layouts that contain subfields that content editors can choose from."
msgstr ""
"Umożliwia definiowanie, tworzenie i zarządzanie treścią z pełną kontrolą "
"poprzez tworzenie układów zawierających podpola, które edytorzy treści mogą "
"wybierać."

#: pro/fields/class-acf-field-flexible-content.php:36,
#: pro/fields/class-acf-field-repeater.php:103,
#: pro/fields/class-acf-field-repeater.php:297
msgid "Add Row"
msgstr "Dodaj wiersz"

#: pro/fields/class-acf-field-flexible-content.php:76,
#: pro/fields/class-acf-field-flexible-content.php:943,
#: pro/fields/class-acf-field-flexible-content.php:1022
msgid "layout"
msgid_plural "layouts"
msgstr[0] "układ"
msgstr[1] "układy"
msgstr[2] "układów"

#: pro/fields/class-acf-field-flexible-content.php:77
msgid "layouts"
msgstr "układy"

#: pro/fields/class-acf-field-flexible-content.php:81,
#: pro/fields/class-acf-field-flexible-content.php:942,
#: pro/fields/class-acf-field-flexible-content.php:1021
msgid "This field requires at least {min} {label} {identifier}"
msgstr "To pole wymaga przynajmniej {min} {label} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:82
msgid "This field has a limit of {max} {label} {identifier}"
msgstr "To pole ma ograniczenie {max} {label} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:85
msgid "{available} {label} {identifier} available (max {max})"
msgstr "{available} {label} {identifier} dostępne (max {max})"

#: pro/fields/class-acf-field-flexible-content.php:86
msgid "{required} {label} {identifier} required (min {min})"
msgstr "{required} {label} {identifier} wymagane (min {min})"

#: pro/fields/class-acf-field-flexible-content.php:89
msgid "Flexible Content requires at least 1 layout"
msgstr "Elastyczne pole wymaga przynajmniej 1 układu"

#: pro/fields/class-acf-field-flexible-content.php:282
msgid "Click the \"%s\" button below to start creating your layout"
msgstr "Kliknij przycisk \"%s\" poniżej, aby zacząć tworzyć nowy układ"

#: pro/fields/class-acf-field-flexible-content.php:423
msgid "Add layout"
msgstr "Dodaj układ"

#: pro/fields/class-acf-field-flexible-content.php:424
msgid "Duplicate layout"
msgstr "Powiel układ"

#: pro/fields/class-acf-field-flexible-content.php:425
msgid "Remove layout"
msgstr "Usuń układ"

#: pro/fields/class-acf-field-flexible-content.php:426,
#: pro/fields/class-acf-repeater-table.php:382
msgid "Click to toggle"
msgstr "Kliknij, aby przełączyć"

#: pro/fields/class-acf-field-flexible-content.php:562
msgid "Delete Layout"
msgstr "Usuń układ"

#: pro/fields/class-acf-field-flexible-content.php:563
msgid "Duplicate Layout"
msgstr "Duplikuj układ"

#: pro/fields/class-acf-field-flexible-content.php:564
msgid "Add New Layout"
msgstr "Dodaj nowy układ"

#: pro/fields/class-acf-field-flexible-content.php:564
msgid "Add Layout"
msgstr "Dodaj układ"

#: pro/fields/class-acf-field-flexible-content.php:647
msgid "Min"
msgstr "Min"

#: pro/fields/class-acf-field-flexible-content.php:662
msgid "Max"
msgstr "Max"

#: pro/fields/class-acf-field-flexible-content.php:705
msgid "Minimum Layouts"
msgstr "Minimalna liczba układów"

#: pro/fields/class-acf-field-flexible-content.php:716
msgid "Maximum Layouts"
msgstr "Maksymalna liczba układów"

#: pro/fields/class-acf-field-flexible-content.php:727,
#: pro/fields/class-acf-field-repeater.php:293
msgid "Button Label"
msgstr "Etykieta przycisku"

#: pro/fields/class-acf-field-flexible-content.php:1710,
#: pro/fields/class-acf-field-repeater.php:918
msgid "%s must be of type array or null."
msgstr "%s musi być typu tablicy lub null."

#: pro/fields/class-acf-field-flexible-content.php:1721
msgid "%1$s must contain at least %2$s %3$s layout."
msgid_plural "%1$s must contain at least %2$s %3$s layouts."
msgstr[0] "%1$s musi zawierać co najmniej %2$s %3$s układ."
msgstr[1] "%1$s musi zawierać co najmniej %2$s %3$s układy."
msgstr[2] "%1$s musi zawierać co najmniej %2$s %3$s układów."

#: pro/fields/class-acf-field-flexible-content.php:1737
msgid "%1$s must contain at most %2$s %3$s layout."
msgid_plural "%1$s must contain at most %2$s %3$s layouts."
msgstr[0] "%1$s musi zawierać co najwyżej %2$s %3$s układ."
msgstr[1] "%1$s musi zawierać co najwyżej %2$s %3$s układy."
msgstr[2] "%1$s musi zawierać co najwyżej %2$s %3$s układów."

#: pro/fields/class-acf-field-gallery.php:27
msgid ""
"An interactive interface for managing a collection of attachments, such as "
"images."
msgstr ""
"Interaktywny interfejs do zarządzania kolekcją załączników, takich jak "
"obrazy."

#: pro/fields/class-acf-field-gallery.php:77
msgid "Add Image to Gallery"
msgstr "Dodaj obraz do galerii"

#: pro/fields/class-acf-field-gallery.php:78
msgid "Maximum selection reached"
msgstr "Maksimum ilości wyborów osiągnięte"

#: pro/fields/class-acf-field-gallery.php:324
msgid "Length"
msgstr "Długość"

#: pro/fields/class-acf-field-gallery.php:368
msgid "Caption"
msgstr "Etykieta"

#: pro/fields/class-acf-field-gallery.php:380
msgid "Alt Text"
msgstr "Tekst alternatywny"

#: pro/fields/class-acf-field-gallery.php:504
msgid "Add to gallery"
msgstr "Dodaj do galerii"

#: pro/fields/class-acf-field-gallery.php:508
msgid "Bulk actions"
msgstr "Działania na wielu"

#: pro/fields/class-acf-field-gallery.php:509
msgid "Sort by date uploaded"
msgstr "Sortuj po dacie przesłania"

#: pro/fields/class-acf-field-gallery.php:510
msgid "Sort by date modified"
msgstr "Sortuj po dacie modyfikacji"

#: pro/fields/class-acf-field-gallery.php:511
msgid "Sort by title"
msgstr "Sortuj po tytule"

#: pro/fields/class-acf-field-gallery.php:512
msgid "Reverse current order"
msgstr "Odwróć aktualną kolejność"

#: pro/fields/class-acf-field-gallery.php:524
msgid "Close"
msgstr "Zamknij"

#: pro/fields/class-acf-field-gallery.php:615
msgid "Minimum Selection"
msgstr "Minimalna liczba wybranych elementów"

#: pro/fields/class-acf-field-gallery.php:625
msgid "Maximum Selection"
msgstr "Maksymalna liczba wybranych elementów"

#: pro/fields/class-acf-field-gallery.php:707
msgid "Allowed file types"
msgstr "Dozwolone typy plików"

#: pro/fields/class-acf-field-gallery.php:727
msgid "Insert"
msgstr "Wstaw"

#: pro/fields/class-acf-field-gallery.php:728
msgid "Specify where new attachments are added"
msgstr "Określ gdzie są dodawane nowe załączniki"

#: pro/fields/class-acf-field-gallery.php:732
msgid "Append to the end"
msgstr "Dodaj na końcu"

#: pro/fields/class-acf-field-gallery.php:733
msgid "Prepend to the beginning"
msgstr "Dodaj do początku"

#: pro/fields/class-acf-field-repeater.php:66,
#: pro/fields/class-acf-field-repeater.php:463
msgid "Minimum rows not reached ({min} rows)"
msgstr "Nie osiągnięto minimalnej liczby wierszy ({min} wierszy)"

#: pro/fields/class-acf-field-repeater.php:67
msgid "Maximum rows reached ({max} rows)"
msgstr "Osiągnięto maksimum liczby wierszy ( {max} wierszy )"

#: pro/fields/class-acf-field-repeater.php:68
msgid "Error loading page"
msgstr "Błąd ładowania strony"

#: pro/fields/class-acf-field-repeater.php:69
msgid "Order will be assigned upon save"
msgstr "Kolejność zostanie przydzielona po zapisaniu"

#: pro/fields/class-acf-field-repeater.php:196
msgid "Useful for fields with a large number of rows."
msgstr "Przydatne dla pól z dużą liczbą wierszy."

#: pro/fields/class-acf-field-repeater.php:207
msgid "Rows Per Page"
msgstr "Wiersze na stronę"

#: pro/fields/class-acf-field-repeater.php:208
msgid "Set the number of rows to be displayed on a page."
msgstr "Ustawienie liczby wierszy, które mają być wyświetlane na stronie."

#: pro/fields/class-acf-field-repeater.php:240
msgid "Minimum Rows"
msgstr "Minimalna liczba wierszy"

#: pro/fields/class-acf-field-repeater.php:251
msgid "Maximum Rows"
msgstr "Maksymalna liczba wierszy"

#: pro/fields/class-acf-field-repeater.php:281
msgid "Collapsed"
msgstr "Zwinięty"

#: pro/fields/class-acf-field-repeater.php:282
msgid "Select a sub field to show when row is collapsed"
msgstr ""
"Wybierz pole podrzędne, które mają być pokazane kiedy wiersz jest zwinięty"

#: pro/fields/class-acf-field-repeater.php:1060
msgid "Invalid field key or name."
msgstr "Nieprawidłowy klucz lub nazwa pola."

#: pro/fields/class-acf-field-repeater.php:1069
msgid "There was an error retrieving the field."
msgstr "Wystąpił błąd przy pobieraniu pola."

#: pro/fields/class-acf-repeater-table.php:369
msgid "Click to reorder"
msgstr "Kliknij, aby zmienić kolejność"

#: pro/fields/class-acf-repeater-table.php:402
msgid "Add row"
msgstr "Dodaj wiersz"

#: pro/fields/class-acf-repeater-table.php:403
msgid "Duplicate row"
msgstr "Powiel wiersz"

#: pro/fields/class-acf-repeater-table.php:404
msgid "Remove row"
msgstr "Usuń wiersz"

#: pro/fields/class-acf-repeater-table.php:448,
#: pro/fields/class-acf-repeater-table.php:465,
#: pro/fields/class-acf-repeater-table.php:466
msgid "Current Page"
msgstr "Bieżąca strona"

#: pro/fields/class-acf-repeater-table.php:456,
#: pro/fields/class-acf-repeater-table.php:457
msgid "First Page"
msgstr "Pierwsza strona"

#: pro/fields/class-acf-repeater-table.php:460,
#: pro/fields/class-acf-repeater-table.php:461
msgid "Previous Page"
msgstr "Poprzednia strona"

#. translators: 1: Current page, 2: Total pages.
#: pro/fields/class-acf-repeater-table.php:470
msgctxt "paging"
msgid "%1$s of %2$s"
msgstr "%1$s z %2$s"

#: pro/fields/class-acf-repeater-table.php:477,
#: pro/fields/class-acf-repeater-table.php:478
msgid "Next Page"
msgstr "Następna strona"

#: pro/fields/class-acf-repeater-table.php:481,
#: pro/fields/class-acf-repeater-table.php:482
msgid "Last Page"
msgstr "Ostatnia strona"

#: pro/locations/class-acf-location-block.php:71
msgid "No block types exist"
msgstr "Nie istnieją żadne typy bloków"

#: pro/locations/class-acf-location-options-page.php:70
msgid "No options pages exist"
msgstr "Strona opcji nie istnieje"

#: pro/admin/views/html-settings-updates.php:6
msgid "Deactivate License"
msgstr "Deaktywuj licencję"

#: pro/admin/views/html-settings-updates.php:6
msgid "Activate License"
msgstr "Aktywuj licencję"

#: pro/admin/views/html-settings-updates.php:16
msgid "License Information"
msgstr "Informacje o licencji"

#: pro/admin/views/html-settings-updates.php:34
msgid ""
"To unlock updates, please enter your license key below. If you don't have a "
"licence key, please see <a href=\"%s\" target=\"_blank\">details & pricing</"
"a>."
msgstr ""
"Żeby odblokować aktualizacje proszę podać swój klucz licencyjny poniżej. "
"Jeśli nie posiadasz klucza prosimy zapoznać się ze <a href=\"%s\" "
"target=\"_blank\">szczegółami i cennikiem</a>."

#: pro/admin/views/html-settings-updates.php:37
msgid "License Key"
msgstr "Klucz licencyjny"

#: pro/admin/views/html-settings-updates.php:22
msgid "Your license key is defined in wp-config.php."
msgstr "Twój klucz licencyjny jest zdefiniowany w pliku wp-config.php."

#: pro/admin/views/html-settings-updates.php:29
msgid "Retry Activation"
msgstr "Ponów próbę aktywacji"

#: pro/admin/views/html-settings-updates.php:61
msgid "Update Information"
msgstr "Informacje o aktualizacji"

#: pro/admin/views/html-settings-updates.php:68
msgid "Current Version"
msgstr "Zainstalowana wersja"

#: pro/admin/views/html-settings-updates.php:76
msgid "Latest Version"
msgstr "Najnowsza wersja"

#: pro/admin/views/html-settings-updates.php:84
msgid "Update Available"
msgstr "Dostępna aktualizacja"

#: pro/admin/views/html-settings-updates.php:98
msgid "Upgrade Notice"
msgstr "Informacje o aktualizacji"

#: pro/admin/views/html-settings-updates.php:126
msgid "Check For Updates"
msgstr "Sprawdź dostępność aktualizacji"

#: pro/admin/views/html-settings-updates.php:121
msgid "Enter your license key to unlock updates"
msgstr "Wprowadź klucz licencyjny, aby odblokować aktualizacje"

#: pro/admin/views/html-settings-updates.php:119
msgid "Update Plugin"
msgstr "Aktualizuj wtyczkę"

#: pro/admin/views/html-settings-updates.php:117
msgid "Please reactivate your license to unlock updates"
msgstr ""
"Proszę wpisać swój klucz licencyjny powyżej aby odblokować aktualizacje"
