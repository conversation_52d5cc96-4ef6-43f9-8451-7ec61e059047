function wli_refresh_cart_count(){var e=0;jQuery(".sb_woo_mini_cart ul li").length&&jQuery(".sb_woo_mini_cart ul li").each(function(){var o=jQuery(this).children(".quantity").text().split(" "),o=parseInt(o[0]);e+=o}),e<=0&&(e=""),jQuery(".sb_woo_prod_cart_container .et-cart-info span").text(e)}function sb_woo_popup_notice(o){jQuery("html, body").scrollTop(0),jQuery.colorbox({html:o,width:"50%",className:"woocommerce"})}function sb_woo_maybe_submit_checkout_coupon(){jQuery(this).keypress(function(o){13==o.which&&sb_woo_submit_checkout_coupon()})}function sb_woo_submit_checkout_coupon(){var o;return jQuery(".coupon-module").length&&(jQuery(".coupon-module").parent().removeClass("woocommerce-invalid").removeClass("woocommerce-validated"),""!=(o=jQuery(".coupon-module").val())?(jQuery("#coupon_code").val(o),jQuery(".checkout_coupon").submit()):jQuery(".coupon-module").parent().addClass("woocommerce-invalid").removeClass("woocommerce-validated")),!1}function sb_woo_variation_image(){var r,c,t,o=jQuery(".single-product .cart.variations_form").data("product_variations"),u=[],_=0,n=0,m=[];jQuery(".single-product .cart.variations_form .variations .value select").each(function(o,e){r=jQuery(this).val(),c=jQuery(this).attr("id"),t="attribute_"+c,r&&(u.push([t,r]),_++)}),0<_&&(jQuery(o).each(function(o,e){var r=e.attributes;n=0,jQuery(r).each(function(o,e){jQuery(e).each(function(o,r){jQuery(u).each(function(o,e){r[e[0]]!=e[1]&&""!=r[e[0]]||n++})})}),_<=n&&(m=e)}),sb_woo_product_thumb_replace_by_url(void 0!==m.image?m.image.full_src:jQuery(".sb_woo_product_thumb_col_num_1 a").data("large_image"),jQuery(".sb_woo_product_image_container")))}function sb_woo_product_thumb_replace_by_url(o,e){var r;jQuery(".single-product .sb_woo_product_image img").attr("src")==o||(r=0==(r=e.closest(".sb_woo_product_image_container")).length?jQuery(".sb_woo_product_image_container"):r).length&&(r.find(".sb_woo_product_image img").trigger("zoom.destroy"),r.find(".sb_woo_product_image img.zoomImg").remove(),e=r.find(".sb_woo_product_image img").height(),r.find(".sb_woo_product_image").css("height",e+"px"),r.find(".sb_woo_product_image img").fadeOut(400,function(){r.find(".sb_woo_product_image img").attr("src",o),r.find(".sb_woo_product_image").imagesLoaded(function(){var o=r.find(".sb_woo_product_image img").height();r.find(".sb_woo_product_image").css("height",o+"px"),r.find(".sb_woo_product_image img").fadeIn(400,function(){r.find(".sb_woo_product_image").hasClass("sb_woo_image_disable_zoom")?r.find(".sb_woo_product_image").hasClass("sb_woo_image_disable_lightbox")||jQuery(".sb_woo_product_image").colorbox({href:r.find(".sb_woo_product_image img").attr("src")}):r.find(".sb_woo_product_image").zoom({callback:function(){r.find(".sb_woo_product_image").hasClass("sb_woo_image_disable_lightbox")||jQuery(this).colorbox({href:r.find(".sb_woo_product_image img").attr("src")})}})})})}))}function sb_woo_product_thumb_replace(o){sb_woo_product_thumb_replace_by_url(o.data("large_image"),o)}jQuery(document).ready(function(){var o;jQuery(".single-product .sb_woo_product_image").length&&(jQuery(".single-product .sb_woo_product_image").hasClass("sb_woo_image_disable_zoom")?jQuery(".single-product .sb_woo_product_image").hasClass("sb_woo_image_disable_lightbox")||jQuery(".sb_woo_product_image").colorbox({href:jQuery(".single-product .sb_woo_product_image img").attr("src")}):jQuery(".single-product .sb_woo_product_image").zoom({callback:function(){jQuery(".single-product .sb_woo_product_image").hasClass("sb_woo_image_disable_lightbox")||jQuery(this).colorbox({href:jQuery(".single-product .sb_woo_product_image img").attr("src")})}})),jQuery(".single-product .cart.variations_form")&&jQuery(".single-product .cart.variations_form .variations .value select").each(function(o,e){jQuery(this).change(function(){sb_woo_variation_image()})}),jQuery(".woocommerce-remove-coupon").length&&jQuery(".et_pb_woo_checkout_coupon").slideUp(),jQuery(document.body).on("wc_fragments_refreshed",function(){}),jQuery("body").hasClass("wli_injected")&&(0<jQuery(".wpcf7").length&&((o=document.body.className.match(/(^|\s)postid-(\d+)(\s|$)/))&&(jQuery("input[name='_wpcf7_container_post']").val(o[2]),jQuery(".wpcf7-submit").addClass("button"),jQuery(".wpcf7-form > p").addClass("form-row"),jQuery(".wpcf7-form > p .wpcf7-form-control-wrap input").addClass("input-text"))),jQuery(document.body).on("added_to_cart",function(){setTimeout(function(){jQuery(".added_to_cart").addClass("button")},50)}),jQuery(document.body).on("wc_fragments_loaded",function(){wli_refresh_cart_count()}),jQuery(document.body).on("wc_fragments_refreshed",function(){wli_refresh_cart_count()}),jQuery(document.body).on("checkout_error",function(){jQuery(".woocommerce-NoticeGroup").length&&(sb_woo_popup_notice(jQuery(".woocommerce-NoticeGroup").html()),setTimeout(function(){jQuery(".woocommerce-NoticeGroup").remove()},250))}),jQuery(document.body).on("updated_wc_div",function(){jQuery(".woocommerce .woocommerce-error").length&&(sb_woo_popup_notice(jQuery(".woocommerce .woocommerce-error")),jQuery(".entry-content .woocommerce .woocommerce-error").remove()),jQuery(".woocommerce .woocommerce-message").length&&(sb_woo_popup_notice(jQuery(".woocommerce .woocommerce-message").clone().wrap("<div />")),jQuery(".entry-content .woocommerce .woocommerce-message").remove()),0<jQuery(".cart-empty").length&&jQuery(".et_pb_woo_cart_totals").remove()}),jQuery(document.body).on("applied_coupon",function(){jQuery(".woocommerce .woocommerce-error").length&&(sb_woo_popup_notice(jQuery(".woocommerce .woocommerce-error")),jQuery(".entry-content .woocommerce .woocommerce-error").remove())}),jQuery(document.body).on("removed_coupon",function(){jQuery(".woocommerce .woocommerce-message").length&&(sb_woo_popup_notice(jQuery(".woocommerce .woocommerce-message").clone().wrap("<div />")),jQuery(".entry-content .woocommerce .woocommerce-message").remove())}),jQuery(document.body).on("update_checkout",function(){jQuery(".wli_wrapper_checkout-form-coupon .woocommerce-error").length?(sb_woo_popup_notice(jQuery(".wli_wrapper_checkout-form-coupon .woocommerce-error").clone().wrap("<div />")),jQuery(".et_pb_woo_checkout_coupon").slideDown()):jQuery(".wli_wrapper_checkout-form-coupon .woocommerce-message").length?(sb_woo_popup_notice(jQuery(".wli_wrapper_checkout-form-coupon .woocommerce-message").clone().wrap("<div />")),jQuery(".coupon-module").val(""),jQuery(".woocommerce-remove-coupon").length?jQuery(".et_pb_woo_checkout_coupon").slideDown():jQuery(".et_pb_woo_checkout_coupon").slideUp()):jQuery(".woocommerce .woocommerce-message").length&&(sb_woo_popup_notice(jQuery(".woocommerce .woocommerce-message").clone().wrap("<div />")),setTimeout(function(){jQuery(".entry-content > .woocommerce > .woocommerce-message").remove()},250))}))});