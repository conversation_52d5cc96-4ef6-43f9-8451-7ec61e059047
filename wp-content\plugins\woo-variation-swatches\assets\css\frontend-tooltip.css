/*!
 * Variation Swatches for WooCommerce v1.1.19 
 * 
 * Author: <PERSON><PERSON> ( <EMAIL> ) 
 * Date: 07/09/2021
 * Released under the GPLv3 license.
 */
.woo-variation-swatches .radio-variable-item[data-wvstooltip] {
  position: relative;
}

.woo-variation-swatches .radio-variable-item[data-wvstooltip]::before,
.woo-variation-swatches .radio-variable-item[data-wvstooltip]::after {
  left: 8px;
  bottom: 100%;
}

.woo-variation-swatches .radio-variable-item .image-tooltip-wrapper {
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  left: 8px;
}

.woo-variation-swatches.wvs-archive-align-center .wvs-archive-variation-wrapper .radio-variable-item[data-wvstooltip]::before,
.woo-variation-swatches.wvs-archive-align-center .wvs-archive-variation-wrapper .radio-variable-item[data-wvstooltip]::after {
  left: 50%;
}

.woo-variation-swatches.wvs-archive-align-center .wvs-archive-variation-wrapper .radio-variable-item .image-tooltip-wrapper {
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  left: 50%;
}

.woo-variation-swatches.wvs-archive-align-right .wvs-archive-variation-wrapper .radio-variable-item[data-wvstooltip]::before,
.woo-variation-swatches.wvs-archive-align-right .wvs-archive-variation-wrapper .radio-variable-item[data-wvstooltip]::after {
  left: 100%;
}

.woo-variation-swatches.wvs-archive-align-right .wvs-archive-variation-wrapper .radio-variable-item .image-tooltip-wrapper {
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  left: 100%;
}

[data-wvstooltip],
.wvs-has-image-tooltip {
  position: relative;
  cursor: pointer;
  outline: none;
}

[data-wvstooltip]:before,
[data-wvstooltip]:after,
.wvs-has-image-tooltip:before,
.wvs-has-image-tooltip:after {
  visibility: hidden;
  opacity: 0;
  pointer-events: none;
  box-sizing: inherit;
  position: absolute;
  bottom: 130%;
  left: 50%;
  z-index: 999;
  -webkit-transform: translateZ(0);
          transform: translateZ(0);
  box-shadow: 0 7px 15px rgba(0, 0, 0, 0.3);
  transition: opacity 300ms linear, bottom 300ms linear;
}

[data-wvstooltip]:before,
.wvs-has-image-tooltip:before {
  margin-bottom: 5px;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  padding: 7px;
  border-radius: 3px;
  background-color: #000;
  background-color: rgba(51, 51, 51, 0.9);
  color: #fff;
  text-align: center;
  font-size: 14px;
  line-height: 1.2;
}

[data-wvstooltip]:before {
  min-width: 100px;
  content: attr(data-wvstooltip);
}

.wvs-has-image-tooltip:before {
  content: attr(data-title);
  background-image: var(--tooltip-background);
  background-repeat: no-repeat;
  width: var(--tooltip-width);
  height: calc( var(--tooltip-height) + 20px);
  background-size: contain;
  border: 2px solid;
  background-position: center top;
  padding: 0;
  line-height: 20px;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  font-size: 12px;
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
}

[data-wvstooltip]:after,
.wvs-has-image-tooltip:after {
  margin-left: -5px;
  width: 0;
  border-top: 5px solid #000000;
  border-top: 5px solid rgba(51, 51, 51, 0.9);
  border-right: 5px solid transparent;
  border-left: 5px solid transparent;
  content: " ";
  font-size: 0;
  line-height: 0;
}

[data-wvstooltip]:hover:before,
[data-wvstooltip]:hover:after,
.wvs-has-image-tooltip:hover:before,
.wvs-has-image-tooltip:hover:after {
  bottom: 120%;
  visibility: visible;
  opacity: 1;
}