<?php
/**
 * Tahreem Chaudhry functions and definitions
 *
 * @link https://developer.wordpress.org/themes/basics/theme-functions/
 *
 * @package Tahreem_Chaudhry
 */

if ( ! defined( '_S_VERSION' ) ) {
	// Replace the version number of the theme on each release.
	define( '_S_VERSION', '1.0.0' );
}

function cc_mime_types($mimes)
{
	$mimes['svg'] = 'image/svg+xml';
	return $mimes;
}
add_filter('upload_mimes', 'cc_mime_types');

/**
 * Register Home Slider custom post type.
 */
function bts_brands_homeslider()
{
	$labels = array(
		'name'               => _x('Home Slide', 'post type general name'),
		'singular_name'      => _x('Home Slide', 'post type singular name'),
		'add_new'            => _x('Add New', 'slide'),
		'add_new_item'       => __('Add New Slide'),
		'edit_item'          => __('Edit Slide'),
		'new_item'           => __('New Slide'),
		'all_items'          => __('All Slides'),
		'view_item'          => __('View This Post'),
		'search_items'       => __('Search Slide'),
		'not_found'          => __('No Slide found'),
		'not_found_in_trash' => __('No Slide found in the Trash'),
		'menu_name'          => 'Home Slider',
	);
	$args = array(
		'labels'             => $labels,
		'public'             => true,
		'show_ui'            => true,
		'show_in_menu'       => true,
		'query_var'          => true,
		'description'        => 'Holds home slide on the website',
		'menu_position'      => 11,
		'capability_type'    => 'post',
		'supports'           => array('title', 'editor', 'thumbnail'),
		'has_archive'        => false,
		'publicly_queryable' => false,
		'show_in_rest'       => true,
		'menu_icon'          => 'dashicons-images-alt2',
    'taxonomies' => array('category'),
	);
	register_post_type('homeslider', $args);
}
add_action('init', 'bts_brands_homeslider');

function register_announcement_post_type() {
    $labels = array(
        'name'               => 'Announcements',
        'singular_name'      => 'Announcement',
        'add_new'            => 'Add New',
        'add_new_item'       => 'Add New Announcement',
        'edit_item'          => 'Edit Announcement',
        'new_item'           => 'New Announcement',
        'all_items'          => 'All Announcements',
        'menu_name'          => 'Announcements'
    );

    $args = array(
        'labels'             => $labels,
        'public'             => false,
        'show_ui'            => true,
        'show_in_menu'       => true,
        'supports'           => array('title'),
        'exclude_from_search'=> true,
        'publicly_queryable' => false,
        'has_archive'        => false,
        'rewrite'            => false,
        'capability_type'    => 'post',
    );

    register_post_type('announcement', $args);
}
add_action('init', 'register_announcement_post_type');

/**
 * Sets up theme defaults and registers support for various WordPress features.
 *
 * Note that this function is hooked into the after_setup_theme hook, which
 * runs before the init hook. The init hook is too late for some features, such
 * as indicating support for post thumbnails.
 */
function tahreemchaudhry_setup() {
	/*
		* Make theme available for translation.
		* Translations can be filed in the /languages/ directory.
		* If you're building a theme based on Tahreem Chaudhry, use a find and replace
		* to change 'tahreemchaudhry' to the name of your theme in all the template files.
		*/
	load_theme_textdomain( 'tahreemchaudhry', get_template_directory() . '/languages' );

	// Add default posts and comments RSS feed links to head.
	add_theme_support( 'automatic-feed-links' );

	/*
		* Let WordPress manage the document title.
		* By adding theme support, we declare that this theme does not use a
		* hard-coded <title> tag in the document head, and expect WordPress to
		* provide it for us.
		*/
	add_theme_support( 'title-tag' );

	/*
		* Enable support for Post Thumbnails on posts and pages.
		*
		* @link https://developer.wordpress.org/themes/functionality/featured-images-post-thumbnails/
		*/
	add_theme_support( 'post-thumbnails' );

	// This theme uses wp_nav_menu() in one location.
	register_nav_menus(
		array(
			'primary' => esc_html__('Primary Menu', 'tehreemchaudhry'),
			'footer' => esc_html__('Footer Menu', 'tehreemchaudhry'),
		)
	);

	/*
		* Switch default core markup for search form, comment form, and comments
		* to output valid HTML5.
		*/
	add_theme_support(
		'html5',
		array(
			'search-form',
			'comment-form',
			'comment-list',
			'gallery',
			'caption',
			'style',
			'script',
		)
	);

	// Set up the WordPress core custom background feature.
	add_theme_support(
		'custom-background',
		apply_filters(
			'tahreemchaudhry_custom_background_args',
			array(
				'default-color' => 'ffffff',
				'default-image' => '',
			)
		)
	);

	// Add theme support for selective refresh for widgets.
	add_theme_support( 'customize-selective-refresh-widgets' );

	/**
	 * Add support for core custom logo.
	 *
	 * @link https://codex.wordpress.org/Theme_Logo
	 */
	add_theme_support(
		'custom-logo',
		array(
			'height'      => 250,
			'width'       => 250,
			'flex-width'  => true,
			'flex-height' => true,
		)
	);
}
add_action( 'after_setup_theme', 'tahreemchaudhry_setup' );

/**
 * Set the content width in pixels, based on the theme's design and stylesheet.
 *
 * Priority 0 to make it available to lower priority callbacks.
 *
 * @global int $content_width
 */
function tahreemchaudhry_content_width() {
	$GLOBALS['content_width'] = apply_filters( 'tahreemchaudhry_content_width', 640 );
}
add_action( 'after_setup_theme', 'tahreemchaudhry_content_width', 0 );

/**
 * Register widget area.
 *
 * @link https://developer.wordpress.org/themes/functionality/sidebars/#registering-a-sidebar
 */
function tahreemchaudhry_widgets_init() {
	register_sidebar(
		array(
			'name'          => esc_html__( 'Sidebar', 'tahreemchaudhry' ),
			'id'            => 'sidebar-1',
			'description'   => esc_html__( 'Add widgets here.', 'tahreemchaudhry' ),
			'before_widget' => '<section id="%1$s" class="widget %2$s">',
			'after_widget'  => '</section>',
			'before_title'  => '<h2 class="widget-title">',
			'after_title'   => '</h2>',
		)
	);
}
add_action( 'widgets_init', 'tahreemchaudhry_widgets_init' );

/**
 * Enqueue scripts and styles.
 */
function tahreemchaudhry_scripts() {
	wp_enqueue_style('tahreemchaudhry-bs-custom-styles', get_template_directory_uri() . '/assets/scss/customstyles.min.css');
	wp_enqueue_style( 'tahreemchaudhry-style', get_stylesheet_uri(), array(), _S_VERSION );
	wp_style_add_data( 'tahreemchaudhry-style', 'rtl', 'replace' );

	$file = get_template_directory() . '/woocommerce.css';
	wp_enqueue_style(
		'custom-woocommerce',
		get_template_directory_uri() . '/woocommerce.css',
		array(),
		file_exists($file) ? filemtime($file) : null
	);

	wp_enqueue_script( 'tahreemchaudhry-navigation', get_template_directory_uri() . '/js/navigation.js', array(), _S_VERSION, true );

	if ( is_singular() && comments_open() && get_option( 'thread_comments' ) ) {
		wp_enqueue_script( 'comment-reply' );
	}
}
add_action( 'wp_enqueue_scripts', 'tahreemchaudhry_scripts' );

/**
 * Implement the Custom Header feature.
 */
require get_template_directory() . '/inc/custom-header.php';

/**
 * Custom template tags for this theme.
 */
require get_template_directory() . '/inc/template-tags.php';

/**
 * Functions which enhance the theme by hooking into WordPress.
 */
require get_template_directory() . '/inc/template-functions.php';

/**
 * Customizer additions.
 */
require get_template_directory() . '/inc/customizer.php';

/**
 * Load Jetpack compatibility file.
 */
if ( defined( 'JETPACK__VERSION' ) ) {
	require get_template_directory() . '/inc/jetpack.php';
}

// Enable WooCommerce support

// add_filter('woocommerce_enqueue_styles', '__return_false');

// add_action('after_setup_theme', function() {
//     add_theme_support('woocommerce');
// });

add_action('after_setup_theme', 'tahreemchaudhry_add_woocommerce_support');
function tahreemchaudhry_add_woocommerce_support() {
    add_theme_support('woocommerce');
}

add_theme_support('wc-product-gallery-zoom');
add_theme_support('wc-product-gallery-lightbox');
add_theme_support('wc-product-gallery-slider');

remove_action( 'woocommerce_before_main_content', 'woocommerce_breadcrumb', 20 );
remove_action( 'woocommerce_sidebar', 'woocommerce_get_sidebar', 10 );
remove_action( 'woocommerce_single_product_summary', 'woocommerce_template_single_excerpt', 20 );

add_filter('woocommerce_product_tabs', 'remove_woocommerce_product_tabs', 98);
function remove_woocommerce_product_tabs($tabs) {
    unset($tabs['description']);             // Remove the description tab
    unset($tabs['additional_information']);  // Remove the additional information tab
    // unset($tabs['reviews']);             // Optionally remove reviews tab
    return $tabs;
}

function remove_default_widgets() {
    unregister_widget('WP_Widget_Recent_Posts');
    unregister_widget('WP_Widget_Recent_Comments');
    unregister_widget('WP_Widget_Archives');
    unregister_widget('WP_Widget_Categories');
    unregister_widget('WP_Widget_Search');
}
add_action('widgets_init', 'remove_default_widgets', 11);


// Include the custom walker class
// require_once get_template_directory() . '/inc/bootstrap_5_nav_walker.php';

// function enqueue_latest_jquery()
// {
// 	wp_enqueue_script('jquery');
// }
// add_action('wp_enqueue_scripts', 'enqueue_latest_jquery');

/*****************************************
 *  Disable Comments 
 * 
 *****************************************/

add_action('admin_init', function () {
	// Redirect any user trying to access the comments page
	global $pagenow;

	if ($pagenow === 'edit-comments.php') {
		wp_redirect(admin_url());
		exit;
	}

	// Remove comments metabox from dashboard
	remove_meta_box('dashboard_recent_comments', 'dashboard', 'normal');

	// Disable support for comments and trackbacks in post types
	foreach (get_post_types() as $post_type) {
		if (post_type_supports($post_type, 'comments')) {
			remove_post_type_support($post_type, 'comments');
			remove_post_type_support($post_type, 'trackbacks');
		}
	}
});

// Close comments on the front-end
add_filter('comments_open', '__return_false', 20, 2);
add_filter('pings_open', '__return_false', 20, 2);

// Hide existing comments
add_filter('comments_array', '__return_empty_array', 10, 2);

// Remove comments page in menu
add_action('admin_menu', function () {
	remove_menu_page('edit-comments.php');
});

// Remove comments links from admin bar
add_action('init', function () {
	if (is_admin_bar_showing()) {
		remove_action('admin_bar_menu', 'wp_admin_bar_comments_menu', 60);
	}
});

/*****************************************
 *  Contact Form 7 
 * 
 *****************************************/
add_filter('wpcf7_autop_or_not', '__return_false');
/*****************Contact Form 7 Spam filters************************/
add_filter('wpcf7_validate_text', 'no_urls_allowed', 10, 3);
add_filter('wpcf7_validate_text*', 'no_urls_allowed', 10, 3);
add_filter('wpcf7_validate_textarea', 'no_urls_allowed', 10, 3);
add_filter('wpcf7_validate_textarea*', 'no_urls_allowed', 10, 3);
function no_urls_allowed($result, $tag)
{

	$tag = new WPCF7_Shortcode($tag);

	$type = $tag->type;
	$name = $tag->name;

	$value = isset($_POST[$name])
		? trim(wp_unslash(strtr((string) $_POST[$name], "\n", " ")))
		: '';

	// If this is meant to be a URL field, do nothing
	if ('url' == $tag->basetype || stristr($name, 'url')) {
		return $result;
	}

	// Check for URLs
	$value = $_POST[$name];
	$not_allowed = array(
		'http://', 'https://', 'www.', '[url', '<a ', 'girl', 'seo', 'marketing ', 'rank', 'tinyurl ', 'website', 'ads', 'google', 'search', 'url', 'baby', 'sex', 'here', 'female body', 'erotic', 'photo', 'photos', '.gd', 'sexy girls', 'viagra', 'I want sex',
		'http', 'www', '.com', '.mx', '.org', '.net', '.co.uk', '.jp', '.ch', '.info', '.me', '.mobi', '.us', '.biz', '.ca', '.ws', '.ag', '.com.co', '.net.co', '.com.ag', '.net.ag', '.it', '.fr', '.tv', '.am',
		'.asia', '.at', '.be', '.cc', '.de', '.es', '.com.es', '.eu', '.fm', '.in', '.tk', '.com.mx', '.nl', '.nu', '.tw', '.vg', 'sex', 'porn', 'fuck', 'buy', 'free', 'dating', 'viagra', 'money', 'dollars', 'payment',
		'website', 'games', 'toys', 'poker', 'cheap',

		'э', 'л', 'й', 'д', 'ж', '|', '}', '.ru', 'б', 'в', 'г', 'д', 'ж', 'з', 'к', 'л', 'м', 'н', 'п', 'т', 'ф', 'х', 'ц', 'ч', 'ш', 'щ', 'я', 'ю', 'и', 'Б', 'Г', 'Д', 'Ж', 'П', 'Ф', 'И',

		'#1', '100% more', '100% free', '100% satisfied', 'Additional income', 'Be your own boss', 'Best price', 'Big bucks', 'Billion', 'Cash bonus', 'Cents on the dollar', 'Consolidate debt', 'Double your cash', 'Double your income',
		'Earn extra cash', 'Earn money', 'Eliminate bad credit', 'Extra cash', 'Extra income', 'Expect to earn', 'Fast cash', 'Financial freedom', 'Free access', 'Free consultation', 'Free gift', 'Free hosting', 'Free info', 'Free investment', 'Free membership',
		'Free money', 'Free preview', 'Free quote', 'Free trial', 'Full refund', 'Get out of debt', 'Get paid', 'Giveaway', 'Guaranteed', 'Increase sales', 'Increase traffic', 'Incredible deal', 'Lower rates', 'Lowest price', 'Make money',
		'Million dollars', 'Miracle', 'Money back', 'Once in a lifetime', 'One time', 'Pennies a day', 'Potential earnings', 'Prize', 'Promise', 'Pure profit', 'Risk-free', 'Satisfaction guaranteed', 'Save big money', 'Save up to', 'Special promotion',

	);
	foreach ($not_allowed as $na) {
		if (stristr($value, $na)) {
			$result->invalidate($tag, 'Access Dined');
			return $result;
		}
	}
	return $result;
}

/*
* Product Category Custom Template Starts
*/
function force_custom_woocommerce_category_template( $template ) {
    if ( is_tax( 'product_cat' ) ) {
        $custom_template = get_stylesheet_directory() . '/woocommerce-custom-category.php';
        if ( file_exists( $custom_template ) ) {
            return $custom_template;
        }
    }
    return $template;
}
add_filter( 'template_include', 'force_custom_woocommerce_category_template', 50 );
/*
* Product Category Custom Template Ends
*/

/*
* Search Function Starts
*/
add_action('wp_enqueue_scripts', function () {
    wp_enqueue_script('jquery');
    wp_localize_script('jquery', 'ajaxurl', admin_url('admin-ajax.php'));
});

// AJAX handler
add_action('wp_ajax_search_products', 'ajax_search_products');
add_action('wp_ajax_nopriv_search_products', 'ajax_search_products');

function ajax_search_products() {
    $term = sanitize_text_field($_GET['term'] ?? '');
    $results = [];

    if (!empty($term)) {
        $args = [
            'post_type' => 'product',
            'post_status' => 'publish',
            'posts_per_page' => 10,
            's' => $term,
        ];

        $query = new WP_Query($args);

        if ($query->have_posts()) {
            foreach ($query->posts as $post) {
                $product = wc_get_product($post->ID);
                $image = get_the_post_thumbnail_url($post->ID, 'thumbnail') ?: wc_placeholder_img_src();
                $price = $product->get_price_html();
                $cats = get_the_terms($post->ID, 'product_cat');
                $cat_name = $cats && !is_wp_error($cats) ? $cats[0]->name : 'Uncategorized';

                $results[] = [
                    'title' => get_the_title($post),
                    'link' => get_permalink($post),
                    'category' => $cat_name,
                    'image' => $image,
                    'price' => $price,
                ];
            }
        }
    }

    wp_send_json($results);
}

/*
* Search Function Ends
*/

/*
* Shop Page & Category Page Starts
*/
add_action('wp_ajax_load_shop_products', 'load_shop_products_callback');
add_action('wp_ajax_nopriv_load_shop_products', 'load_shop_products_callback');

function load_shop_products_callback() {
  require get_template_directory() . '/shop-product-loop.php';
  wp_die();
}

add_action('wp_ajax_custom_sort_products', 'handle_custom_sort_products');
add_action('wp_ajax_nopriv_custom_sort_products', 'handle_custom_sort_products');

function handle_custom_sort_products() {
  get_template_part('template-parts/custom-product-loop');
  wp_die(); // required
}
/*
* Shop Page & Category Page Ends
*/


add_action('woocommerce_before_add_to_cart_button', 'show_product_disclaimer', 5);
function show_product_disclaimer() {
    get_template_part('template-parts/product/product-description-disclaimer');
}

// 2. Gift wrap shown second
add_action('woocommerce_before_add_to_cart_button', 'add_gift_wrap_checkbox', 10);
function add_gift_wrap_checkbox() {
    global $product;

    $label = get_field('gift_wrap_label', $product->get_id());
    $price = get_field('gift_wrap_price', $product->get_id());

    if ($label && $price !== '') {
        echo '<div class="gift-wrap-option">';
		echo '<label>';
		echo '<input type="checkbox" name="gift_wrap" value="1" id="giftWrapCheckbox" />';
		echo '<span id="giftWrapLabel">' . esc_html($label) . ' (+Rs ' . wc_price($price) . ')</span>';
		echo '</label>';
		echo '</div>';
    }
}

// 3. Delivery notice shown last
add_action('woocommerce_before_add_to_cart_button', 'show_delivery_notice', 15);
function show_delivery_notice() {
    global $product;

    $notice = get_field('delivery_notice', $product->get_id());

    if (!empty($notice)) {
        echo '<div class="product-delivery-notice" style="margin-top: 10px; font-size: 15px; color: #444;">';
        echo esc_html($notice);
        echo '</div>';
    }
}

add_filter('woocommerce_add_cart_item_data', 'add_gift_wrap_cart_item_data', 10, 2);
function add_gift_wrap_cart_item_data($cart_item_data, $product_id) {
    if (!empty($_POST['gift_wrap'])) {
        $label = get_field('gift_wrap_label', $product_id);
        $price = get_field('gift_wrap_price', $product_id);
        $cart_item_data['gift_wrap'] = [
            'label' => $label,
            'price' => (float) $price,
        ];
    }
    return $cart_item_data;
}

add_filter('woocommerce_get_item_data', 'display_gift_wrap_cart_data', 10, 2);
function display_gift_wrap_cart_data($item_data, $cart_item) {
    if (!empty($cart_item['gift_wrap'])) {
        $item_data[] = [
            'name' => 'Gift Wrap',
            'value' => esc_html($cart_item['gift_wrap']['label']),
        ];
    }
    return $item_data;
}

add_action('woocommerce_before_calculate_totals', 'add_gift_wrap_price_to_total', 10, 1);
function add_gift_wrap_price_to_total($cart) {
    if (is_admin() && !defined('DOING_AJAX')) return;

    foreach ($cart->get_cart() as $cart_item) {
        if (!empty($cart_item['gift_wrap'])) {
            $qty = $cart_item['quantity'];
            $extra = $cart_item['gift_wrap']['price'] * $qty;
            $cart_item['data']->set_price($cart_item['data']->get_price() + ($extra / $qty));
        }
    }
}

add_action('woocommerce_checkout_create_order_line_item', 'add_gift_wrap_to_order_items', 10, 4);
function add_gift_wrap_to_order_items($item, $cart_item_key, $values, $order) {
    if (!empty($values['gift_wrap'])) {
        $item->add_meta_data('Gift Wrap', $values['gift_wrap']['label']);
    }
}





add_action('woocommerce_after_single_product', function () {
    ?>
    <script>
    jQuery(document).ready(function($) {
        // Insert after swatches
        const swatchWrapper = $('.color-variable-items-wrapper');
        const label = $('<label id="customColorLabel" style="display:none; margin-top:10px;">Pick your custom color:</label>');
        const colorPicker = $('<input type="color" name="custom_color_value" id="customColorPicker" style="display:none; margin-top:5px;">');

        swatchWrapper.after(label, colorPicker);

        // Listen for changes in the hidden select
        $('#pa_color').on('change', function () {
            const selected = $(this).val();
            if (selected === 'custom-color') {
                $('#customColorLabel').show();
                $('#customColorPicker').show();
            } else {
                $('#customColorLabel').hide();
                $('#customColorPicker').hide();
            }
        });

        // Trigger once on load (in case custom is pre-selected)
        $('#pa_color').trigger('change');
    });
    </script>
    <?php
});

add_filter('woocommerce_add_cart_item_data', function($cart_item_data, $product_id) {
    if (!empty($_POST['custom_color_value'])) {
        $cart_item_data['custom_color_value'] = sanitize_hex_color($_POST['custom_color_value']);
    }
    return $cart_item_data;
}, 10, 2);

add_filter('woocommerce_get_item_data', function($item_data, $cart_item) {
    if (!empty($cart_item['custom_color_value'])) {
        $item_data[] = [
            'key'   => 'Custom Color',
            'value' => '<div style="display:inline-block;width:20px;height:20px;background:' . esc_attr($cart_item['custom_color_value']) . ';border:1px solid #ccc;"></div> ' . esc_html($cart_item['custom_color_value']),
        ];
    }
    return $item_data;
}, 10, 2);

add_action('woocommerce_add_order_item_meta', function($item_id, $values, $cart_item_key) {
    if (!empty($values['custom_color_value'])) {
        wc_add_order_item_meta($item_id, 'Custom Color', $values['custom_color_value']);
    }
}, 10, 3);

// Custom Size Functionality
// Add "Custom Size" option to size variations
add_filter('woocommerce_dropdown_variation_attribute_options_html', function($html, $args) {
    if (isset($args['attribute']) && $args['attribute'] === 'pa_size') {
        // Add custom size option to the dropdown
        $custom_option = '<option value="custom-size">Custom Size</option>';
        $html = str_replace('</select>', $custom_option . '</select>', $html);
    }
    return $html;
}, 10, 2);

// Ensure custom-size is treated as a valid variation option
add_filter('woocommerce_variation_is_active', function($active, $variation) {
    if (isset($_POST['attribute_pa_size']) && $_POST['attribute_pa_size'] === 'custom-size') {
        return true;
    }
    return $active;
}, 10, 2);

// Save custom size data to cart
add_filter('woocommerce_add_cart_item_data', function($cart_item_data, $product_id) {
    // Check if custom size is selected
    if (!empty($_POST['attribute_pa_size']) && $_POST['attribute_pa_size'] === 'custom-size') {
        $custom_size_data = array();

        // Shirt measurements
        $shirt_fields = array(
            'shirtLength' => 'Shirt Length',
            'sholuderLength' => 'Shoulder',
            'sleevLength' => 'Sleeve Length',
            'wrist' => 'Wrist',
            'chest' => 'Chest',
            'waist' => 'Waist',
            'shirtHip' => 'Hip',
            'shirtBottom' => 'Bottom',
            'armHole' => 'Arm Hole'
        );

        // Trouser measurements
        $trouser_fields = array(
            'trouserLength' => 'Trouser Length',
            'trouserWaist' => 'Trouser Waist',
            'trouserHip' => 'Trouser Hip',
            'trouserThigh' => 'Thigh',
            'trouserKnee' => 'Knee',
            'trouserBottom' => 'Trouser Bottom'
        );

        // Collect shirt measurements
        foreach ($shirt_fields as $field => $label) {
            if (!empty($_POST[$field])) {
                $custom_size_data['shirt'][$field] = array(
                    'label' => $label,
                    'value' => sanitize_text_field($_POST[$field])
                );
            }
        }

        // Collect trouser measurements
        foreach ($trouser_fields as $field => $label) {
            if (!empty($_POST[$field])) {
                $custom_size_data['trouser'][$field] = array(
                    'label' => $label,
                    'value' => sanitize_text_field($_POST[$field])
                );
            }
        }

        if (!empty($custom_size_data)) {
            $cart_item_data['custom_size_data'] = $custom_size_data;
        }
    }

    return $cart_item_data;
}, 10, 2);

// Display custom size data in cart
add_filter('woocommerce_get_item_data', function($item_data, $cart_item) {
    if (!empty($cart_item['custom_size_data'])) {
        $custom_size = $cart_item['custom_size_data'];

        // Display shirt measurements
        if (!empty($custom_size['shirt'])) {
            $item_data[] = array(
                'key'   => 'Custom Size - Shirt',
                'value' => '',
                'display' => ''
            );

            foreach ($custom_size['shirt'] as $field => $data) {
                $item_data[] = array(
                    'key'   => '&nbsp;&nbsp;' . $data['label'],
                    'value' => $data['value'],
                    'display' => $data['value']
                );
            }
        }

        // Display trouser measurements
        if (!empty($custom_size['trouser'])) {
            $item_data[] = array(
                'key'   => 'Custom Size - Trouser',
                'value' => '',
                'display' => ''
            );

            foreach ($custom_size['trouser'] as $field => $data) {
                $item_data[] = array(
                    'key'   => '&nbsp;&nbsp;' . $data['label'],
                    'value' => $data['value'],
                    'display' => $data['value']
                );
            }
        }
    }
    return $item_data;
}, 10, 2);

// Save custom size data to order
add_action('woocommerce_add_order_item_meta', function($item_id, $values, $cart_item_key) {
    if (!empty($values['custom_size_data'])) {
        $custom_size = $values['custom_size_data'];

        // Save shirt measurements
        if (!empty($custom_size['shirt'])) {
            foreach ($custom_size['shirt'] as $field => $data) {
                wc_add_order_item_meta($item_id, 'Custom Size - ' . $data['label'], $data['value']);
            }
        }

        // Save trouser measurements
        if (!empty($custom_size['trouser'])) {
            foreach ($custom_size['trouser'] as $field => $data) {
                wc_add_order_item_meta($item_id, 'Custom Size - ' . $data['label'], $data['value']);
            }
        }
    }
}, 10, 3);


// AJAX handler for custom size form
add_action('wp_ajax_get_custom_size_form', 'get_custom_size_form_callback');
add_action('wp_ajax_nopriv_get_custom_size_form', 'get_custom_size_form_callback');

function get_custom_size_form_callback() {
    $custom_fields = array(); // Initialize for template
    ob_start();
    ?>
    <div id="customSizeForm" style="display:none; margin-top:20px; border: 1px solid #ddd; padding: 20px; background: #f9f9f9;">
        <h4 style="margin-bottom:15px;">Custom Size Measurements</h4>
        <div class="custom-size-fields mt-3">
            <div class="row">
                <div class="col-md-6">
                    <h5>Shirt Measurements</h5>
                    <div class="form-floating mb-2">
                        <input type="text" class="form-control" name="shirtLength" placeholder="Shirt Length">
                        <label>Shirt Length</label>
                    </div>
                    <div class="form-floating mb-2">
                        <input type="text" class="form-control" name="chest" placeholder="Chest">
                        <label>Chest</label>
                    </div>
                    <div class="form-floating mb-2">
                        <input type="text" class="form-control" name="waist" placeholder="Waist">
                        <label>Waist</label>
                    </div>
                </div>
                <div class="col-md-6">
                    <h5>Trouser Measurements</h5>
                    <div class="form-floating mb-2">
                        <input type="text" class="form-control" name="trouserLength" placeholder="Length">
                        <label>Length</label>
                    </div>
                    <div class="form-floating mb-2">
                        <input type="text" class="form-control" name="trouserWaist" placeholder="Waist">
                        <label>Waist</label>
                    </div>
                    <div class="form-floating mb-2">
                        <input type="text" class="form-control" name="trouserHip" placeholder="Hip">
                        <label>Hip</label>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php
    $html = ob_get_clean();
    echo $html;
    wp_die();
}

// Add custom size JavaScript to single product pages
add_action('wp_footer', function() {
    if (is_product()) {
        ?>
        <script>
        jQuery(document).ready(function($) {
            // Add custom size form after variations table
            function addCustomSizeForm() {
                const variationsTable = $('.variations');
                if (variationsTable.length > 0) {
                    if ($('#customSizeWrapper').length === 0) {
                        variationsTable.after('<div id="customSizeWrapper"></div>');
                    }

                    // Load form via AJAX
                    $.get('<?php echo admin_url('admin-ajax.php'); ?>?action=get_custom_size_form', function(data) {
                        $('#customSizeWrapper').html(data);
                        bindEvents();
                    });
                }
            }

            function bindEvents() {
                // Listen for size changes
                $(document).on('change', 'select[name*="pa_size"]', function() {
                    checkSizeSelection();
                });

                // Check initial state
                setTimeout(checkSizeSelection, 1000);
            }

            function checkSizeSelection() {
                const sizeSelect = $('select[name*="pa_size"]');
                const selectedValue = sizeSelect.val();

                console.log('Size dropdown found:', sizeSelect.length > 0);
                console.log('Size selected:', selectedValue);
                console.log('Custom form exists:', $('#customSizeForm').length > 0);

                if (selectedValue === 'custom-size') {
                    $('#customSizeForm').slideDown();
                } else {
                    $('#customSizeForm').slideUp();
                }
            }

            // Initialize
            addCustomSizeForm();
        });
        </script>
        <?php
    }
});

// Add custom size CSS
add_action('wp_head', function() {
    if (is_product()) {
        ?>
        <style>
        #customSizeForm {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            background-color: #f9f9f9;
            margin-top: 20px;
        }
        #customSizeForm h4 {
            margin-bottom: 20px;
            color: #333;
            font-weight: 600;
        }
        #customSizeForm h5 {
            margin-bottom: 15px;
            color: #555;
            font-weight: 500;
        }
        .form-floating {
            margin-bottom: 10px;
        }
        .form-control {
            border: 1px solid #ccc;
            border-radius: 4px;
            padding: 10px;
            font-size: 14px;
            width: 100%;
        }
        .form-control:focus {
            border-color: #000;
            box-shadow: 0 0 0 0.2rem rgba(0, 0, 0, 0.1);
        }
        .row {
            display: flex;
            gap: 20px;
        }
        .col-md-6 {
            flex: 1;
        }
        @media (max-width: 768px) {
            .row {
                flex-direction: column;
                gap: 10px;
            }
        }
        </style>
        <?php
    }
});

// Admin notice for custom size setup
add_action('admin_notices', function() {
    if (current_user_can('manage_options') && isset($_GET['post_type']) && $_GET['post_type'] === 'product') {
        echo '<div class="notice notice-info is-dismissible">';
        echo '<p><strong>Custom Size Feature:</strong> To enable custom size for products, create a "Size" attribute (pa_size) and add "custom-size" as one of the terms. The custom size form will appear when customers select "Custom Size".</p>';
        echo '</div>';
    }
});

// Save custom picked color
// add_filter('woocommerce_add_cart_item_data', function($cart_item_data, $product_id) {
//     if (!empty($_POST['custom_color_value'])) {
//         $cart_item_data['custom_color_value'] = sanitize_hex_color($_POST['custom_color_value']);
//     }
//     return $cart_item_data;
// }, 10, 2);
